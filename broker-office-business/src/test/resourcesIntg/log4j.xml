<?xml version="1.0"?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">

<log4j:configuration debug="false" xmlns:log4j="http://jakarta.apache.org/log4j/">

    <!-- CONSOLE APPENDER -->
	<appender name="ConsoleAppender" class="org.apache.log4j.ConsoleAppender">
		<param name="Threshold" value="DEBUG" />
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%p %x %l: %m %n"/>
    	</layout>
	</appender>

	
	<logger name="oracle.jdbc">
    	<level value="ERROR" />
 	</logger> 
	<logger name="org.hibernate">
    	<level value="DEBUG" />
	</logger>
	<logger name="org.hibernate.sql">
    		<level value="DEBUG" />
	</logger>
	<logger name="org.hibernate.type">
    		<level value="DEBUG" />
	</logger>
	
	<logger name="com.ing.canada.plp">
    	<level value="ERROR" />
 	</logger> 

    <logger name="org.springframework">
		<level value="ERROR" />
	</logger>

	<logger name="com.ing.canada.common">
    	<level value="DEBUG" />
	</logger>

  	<root>
    	<level value="DEBUG" />
    	<appender-ref ref="ConsoleAppender"/>
    </root>

</log4j:configuration>