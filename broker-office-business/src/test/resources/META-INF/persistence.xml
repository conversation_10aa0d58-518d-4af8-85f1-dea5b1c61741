<?xml version="1.0" encoding="UTF-8"?>
<persistence xmlns="https://jakarta.ee/xml/ns/persistence"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="https://jakarta.ee/xml/ns/persistence https://jakarta.ee/xml/ns/persistence/persistence_3_0.xsd"
	version="3.0">

	<persistence-unit name="plpolicy_persistence_unit" transaction-type="RESOURCE_LOCAL">
		
		<class>com.ing.canada.plp.domain.billing.Account</class>
		<class>com.ing.canada.plp.domain.billing.Billing</class>
		<class>com.ing.canada.plp.domain.billing.PaymentSchedule</class>
		<class>com.ing.canada.plp.domain.ManufacturingContext</class>
		<class>com.ing.canada.plp.domain.businesstransaction.BusinessTransaction</class>
		<class>com.ing.canada.plp.domain.businesstransaction.BusinessTransactionActivity</class>
		<class>com.ing.canada.plp.domain.businesstransaction.BusinessTransactionSubActivity</class>				
		<class>com.ing.canada.plp.domain.businesstransaction.BusinessTransactionSubActivityComplementInfo</class>
		<class>com.ing.canada.plp.domain.businesstransaction.MessageRepositoryEntry</class>
		<class>com.ing.canada.plp.domain.businesstransaction.TransactionalMessage</class>
		<class>com.ing.canada.plp.domain.businesstransaction.TransactionalMessageElement</class>
		<class>com.ing.canada.plp.domain.coverage.Coverage</class>
		<class>com.ing.canada.plp.domain.coverage.CoveragePremium</class>
		<class>com.ing.canada.plp.domain.coverage.CoverageRepositoryEntry</class>
		<class>com.ing.canada.plp.domain.coverage.SubCoveragePremium</class>
		<class>com.ing.canada.plp.domain.coverage.BasicCoverageBasePremium</class>
		<class>com.ing.canada.plp.domain.driver.AffinityGroupRepositoryEntry</class>
		<class>com.ing.canada.plp.domain.driver.AffinityGroupSpecialConditionRepositoryEntry</class>
		<class>com.ing.canada.plp.domain.driver.Conviction</class>
		<class>com.ing.canada.plp.domain.driver.DriverComplementInfo</class>
		<class>com.ing.canada.plp.domain.driver.DriverLicenseClass</class>
		<class>com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy</class>
		<class>com.ing.canada.plp.domain.insurancepolicy.InsurancePolicyNote</class>
		<class>com.ing.canada.plp.domain.insurancerisk.AdditionalInterestRole</class>
		<class>com.ing.canada.plp.domain.insurancerisk.AdditionalInterestRepositoryEntry</class>
		<class>com.ing.canada.plp.domain.insurancerisk.AdditionalInterestRepositoryManufacturingContext</class>
		<class>com.ing.canada.plp.domain.insurancerisk.Claim</class>
		<class>com.ing.canada.plp.domain.insurancerisk.InsuranceRisk</class>
		<class>com.ing.canada.plp.domain.insurancerisk.KindOfLoss</class>
		<class>com.ing.canada.plp.domain.insurancerisk.MultiplicativeRatingFactorFromBasicCoverage</class>
		<class>com.ing.canada.plp.domain.insurancerisk.MultiplicativeRatingFactorFromNonBasicCoverage</class>
		<class>com.ing.canada.plp.domain.insurancerisk.RatingRisk</class>
		<class>com.ing.canada.plp.domain.insurancerisk.Trailer</class>
		<class>com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer</class>
		<class>com.ing.canada.plp.domain.insuranceriskoffer.CoveragePremiumOffer</class>
		<class>com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer</class>
		<class>com.ing.canada.plp.domain.insuranceriskoffer.RatingRiskOffer</class>
		<class>com.ing.canada.plp.domain.insuranceriskoffer.SubCoveragePremiumOffer</class>
		<class>com.ing.canada.plp.domain.insuranceriskoffer.PolicyOfferRating</class>
		<class>com.ing.canada.plp.domain.party.Address</class>
		<class>com.ing.canada.plp.domain.party.CarrierRepositoryEntry</class>
		<class>com.ing.canada.plp.domain.party.Consent</class>
		<class>com.ing.canada.plp.domain.party.CreditScore</class>
		<class>com.ing.canada.plp.domain.party.GroupRepositoryEntry</class>
		<class>com.ing.canada.plp.domain.party.MunicipalityDetailSpecification</class>
		<class>com.ing.canada.plp.domain.party.MunicipalityRepositoryEntry</class>
		<class>com.ing.canada.plp.domain.party.Party</class>
		<class>com.ing.canada.plp.domain.party.PartyGroup</class>
		<class>com.ing.canada.plp.domain.party.PartyRoleInRisk</class>
		<class>com.ing.canada.plp.domain.party.Phone</class>
		<class>com.ing.canada.plp.domain.policyversion.ProducerRepositoryEntry</class>
		<class>com.ing.canada.plp.domain.policyversion.Producer</class>
		<class>com.ing.canada.plp.domain.policyversion.PolicyAdditionalCoverage</class>
		<class>com.ing.canada.plp.domain.policyversion.PolicyHolder</class>
		<class>com.ing.canada.plp.domain.policyversion.PolicyVersion</class>
		<class>com.ing.canada.plp.domain.policyversion.ReferenceDate</class>
		<class>com.ing.canada.plp.domain.policyversion.PriorCarrierPolicyInfo</class>
		<class>com.ing.canada.plp.domain.policyversion.RelatedInsurancePolicy</class>
		<class>com.ing.canada.plp.domain.vehicle.AntiTheftDevice</class>
		<class>com.ing.canada.plp.domain.vehicle.AntiTheftDeviceModelRepositoryEntry</class>
		<class>com.ing.canada.plp.domain.vehicle.VehicleDetailSpecificationRepositoryEntry</class>
		<class>com.ing.canada.plp.domain.vehicle.Vehicle</class>
		<class>com.ing.canada.plp.domain.vehicle.VehicleEquipment</class>
		<class>com.ing.canada.plp.domain.vehicle.VehicleRepositoryEntry</class>
		<class>com.ing.canada.plp.domain.diagnostics.DiagnosticAutomatedAdvice</class>
		<class>com.ing.canada.plp.domain.diagnostics.DiagnosticAutomatedAdviceRepositoryEntry</class>
		<class>com.ing.canada.plp.domain.subbrokerassignment.SubBrokerAssignment</class>
		<class>com.ing.canada.plp.report.SubBrokerAssignmentInfo</class>
		<class>com.ing.canada.plp.report.insurancePolicy.InsurancePolicyBrokerInfo</class>
		<class>com.ing.canada.plp.report.insurancePolicy.InsurancePolicyNoteInfo</class>
		<class>com.ing.canada.plp.report.performancemetric.PerformanceMetricInfo</class>
		
		<exclude-unlisted-classes></exclude-unlisted-classes>
		
		<properties>
			<!--property name="hibernate.default_batch_fetch_size" value="5" /-->
			<property name="hibernate.max_fetch_depth" value="3" />
			<property name="hibernate.default_schema" value="plpadmin" />
			<property name="hibernate.ejb.interceptor" value="com.ing.canada.plp.dao.interceptor.UppercaseStringInterceptor" />
			<property name="hibernate.ejb.event.pre-update" value="com.ing.canada.plp.lock.InsurancePolicyLockPreUpdateEventListener" /> 
			<property name="hibernate.ejb.event.flush-entity" value="com.ing.canada.plp.lock.InsurancePolicyLockFlushEntityEventListener" /> 
		</properties>
		<shared-cache-mode>NONE</shared-cache-mode>

	</persistence-unit>

</persistence>
