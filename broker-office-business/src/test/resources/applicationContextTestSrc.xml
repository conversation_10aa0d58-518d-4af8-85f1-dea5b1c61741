<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.0.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.0.xsd

	http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context-3.0.xsd" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:context="http://www.springframework.org/schema/context">

<context:annotation-config/>
	<context:component-scan base-package="com.intact.brokeroffice.business"/>
	<context:component-scan base-package="com.ing.canada.plp.helper"/>
	<context:component-scan base-package="com.ing.canada.plp.dao"/>
	<context:component-scan base-package="com.ing.canada.plp.report.dao"/>
	<context:component-scan base-package="com.ing.canada.plp.service"/>
	<context:component-scan base-package="com.ing.canada.plp.report.service"/>
	<context:component-scan base-package="com.intact.upv.intact.services.impl"/>
	<context:component-scan base-package="com.intact.upv.common.services.impl"/>
	
	<import resource="classpath:capi-java-services-beans.xml"/>
	<import resource="classpath:capi-pega-beans.xml"/>
		
	<bean id="transactionManager" class="org.springframework.orm.jpa.JpaTransactionManager">
		<property name="entityManagerFactory" ref="entityManagerFactory"/>
		<property name="dataSource" ref="dataSource"/>
	</bean>

	<bean id="cif-dataSource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
		<property name="driverClassName" value="org.hsqldb.jdbcDriver" />
		<property name="url" value="jdbc:hsqldb:mem:test" />
		<property name="username" value="sa" />
		<property name="password" value="" />
	</bean>

	<bean id="brm-dataSource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
		<property name="driverClassName" value="org.hsqldb.jdbcDriver" />
		<property name="url" value="jdbc:hsqldb:mem:test" />
		<property name="username" value="sa" />
		<property name="password" value="" />
	</bean>

	<bean id="dw-dataSource" class="org.apache.commons.dbcp.BasicDataSource" destroy-method="close">
		<property name="driverClassName" value="org.hsqldb.jdbcDriver" />
		<property name="url" value="jdbc:hsqldb:mem:test" />
		<property name="username" value="sa" />
		<property name="password" value="" />
	</bean>

	<tx:annotation-driven transaction-manager="transactionManager"/>

	<bean id="entityManagerFactory" class="org.springframework.orm.jpa.LocalContainerEntityManagerFactoryBean">
		<property name="persistenceUnitName" value="plpolicy_persistence_unit"/>
		<property name="dataSource" ref="dataSource"/>

		<property name="jpaVendorAdapter">

			<bean class="org.springframework.orm.jpa.vendor.HibernateJpaVendorAdapter">
				<property name="showSql" value="false"/>
                <property name="databasePlatform" value="org.hibernate.dialect.OracleDialect"/>
			</bean>

		</property>

	</bean>

	<!-- JdbcTemplate for DAOs requiring JDBC access -->
	<bean id="jdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
		<property name="dataSource" ref="dataSource"/>
	</bean>

	<bean id="dataSource" class="org.apache.commons.dbcp.BasicDataSource">

		<property name="driverClassName" value="oracle.jdbc.driver.OracleDriver"/>
		<!--<property name="url" value="*****************************************" />
		<property name="username" value="PLPadmin" />
		<property name="password" value="PLPadmin" />-->
		<property name="url" value="****************************************"/>
		<property name="username" value="posingplp"/>
		<property name="password" value="posingplp"/>
	<!--	<property name="url" value="****************************************" />
		<property name="username" value="posingplp" />
		<property name="password" value="newland05" />-->

	</bean>

	<bean id="mrktJdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate">
		<property name="dataSource" ref="mrkDataSource"/>
	</bean>

	<!-- Datasource -->
	<bean id="mrkDataSource" class="org.apache.commons.dbcp.BasicDataSource">
		<property name="driverClassName" value="oracle.jdbc.driver.OracleDriver" />
		<property name="url" value="****************************************" />
		<property name="username" value="dw_report_intact1" />
		<property name="password" value="tom4$poc" />
	</bean>

	<bean id="plp-ingAesCipher" class="com.ing.canada.plp.crypto.IngAesCipher" scope="prototype"/>

	<!-- spring application context -->
	<bean id="applicationContextHolder" class="com.ing.canada.plp.stereotype.ApplicationContextHolder" scope="singleton"/>
	
	<bean id="testProxyInterceptor" class="com.ing.canada.common.util.test.TestProxyInterceptor"/>
	
	<bean class="org.springframework.aop.framework.ProxyFactoryBean">
		<property name="proxyInterfaces" value="com.ing.canada.cif.service.IClientContactHistsService"/>
		<property name="interceptorNames" value="testProxyInterceptor"/>
	</bean>
	<bean class="org.springframework.aop.framework.ProxyFactoryBean">
		<property name="proxyInterfaces" value="com.ing.canada.cif.service.IClientService"/>
		<property name="interceptorNames" value="testProxyInterceptor"/>
	</bean>
	<bean class="org.springframework.aop.framework.ProxyFactoryBean">
		<property name="proxyInterfaces" value="com.ing.canada.cif.service.IBrokerService"/>
		<property name="interceptorNames" value="testProxyInterceptor"/>
	</bean>
	
	<bean class="org.springframework.aop.framework.ProxyFactoryBean">
		<property name="proxyInterfaces" value="com.ing.canada.cif.service.ISubBrokersService"/>
		<property name="interceptorNames" value="testProxyInterceptor"/>
	</bean>
	
	<bean id="dataMediatorServiceFactory" class="org.springframework.beans.factory.config.ServiceLocatorFactoryBean">
		<property name="serviceLocatorInterface" value="com.intact.common.datamediator.DataMediatorToSOMServiceFactory"/>
	</bean>	    
    
    <bean id="getUpdateClassicAutoPolicyService9x00" class="com.ing.canada.common.services.impl.ilservices.base.GenericDelegate">
		<property name="componentName"  value="AGREEMENT"/>
		<property name="serviceName"    value="UPDATE_CLASSIC_AUTO_POLICY"/>		
		<property name="serviceVersion" value="9.00"/>	
	</bean>	
	
	<bean id="getCreateClassicAccountService3x00" class="com.ing.canada.common.services.impl.ilservices.base.GenericDelegate">
		<property name="componentName" value="ACCOUNT"/>
		<property name="serviceName" value="CREATE_CLASSIC_ACCOUNT"/>
		<property name="serviceVersion" value="3.00"/>
	</bean>
</beans>