package com.intact.brokeroffice.business.quotes.impl;

import java.util.Calendar;
import java.util.Date;
import java.util.GregorianCalendar;
import java.util.List;
import junit.framework.JUnit4TestAdapter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit38.AbstractJUnit38SpringContextTests;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.report.insurancePolicy.InsurancePolicyBrokerInfoSP;
import com.ing.canada.plp.report.insurancePolicy.criteria.QuoteSearchCriteria;
import com.intact.brokeroffice.business.quote.IQuoteSearchBusinessProcess;

@ContextConfiguration(locations = { "/applicationContextTestSrc.xml" })
public class QuoteSearchBusinessProcessTest extends AbstractJUnit38SpringContextTests{
	
	@Autowired	
	private IQuoteSearchBusinessProcess quoteSearchBusinessProcess;		
		
	@Test
	public void testFullSearchCriteria()
	{
		/* ------------------------------------------------- 
		 * Recherche avec MasterBroker, dateFrom , dateTo and status 
			t_selected_master           	VARCHAR2  	-->	0028
			t_selected_point_of_sale     	VARCHAR2  	-->	0028
			t_quote_ref_number           	VARCHAR2  	-->	QA111137285
			t_quote_ref_legacy_number   	VARCHAR2  	-->	null
			t_postal_code               	VARCHAR2  	--> J3X1Z3
			t_telephone                  	VARCHAR2  	-->	null
			t_last_name                 	VARCHAR2  	-->	FASDF
			t_first_name                	VARCHAR2  	-->	AS
			t_last_update_from           	VARCHAR2  	-->	2010/01/01
			t_last_update_to            	VARCHAR2  	--> today
			t_inception_date_from        	VARCHAR2  	-->	null
			t_inception_date_to          	VARCHAR2  	-->	null
			t_email                     	VARCHAR2    --> null
			t_synchro_discount          	VARCHAR2  	-->	null
			t_selected_client_followUp   	VARCHAR2  	-->	null	
			t_selected_quote_status    		VARCHAR2  	-->	EXP
		
		---------------------------------------------------*/
		
		QuoteSearchCriteria searchCriteria = new QuoteSearchCriteria();
		searchCriteria.setSelectedMaster("0028");
		searchCriteria.setSelectedPointOfSale("0028");
		searchCriteria.setAgreementNumber("QA111137285");
		searchCriteria.setAgreementLegacyNumber(false);
		searchCriteria.setPostalCode("J3X1Z3");
		searchCriteria.setTelephone(null);
		searchCriteria.setLastName("FASDF");
		searchCriteria.setFirstName("AS");		
		
		GregorianCalendar gCalendar =  new GregorianCalendar();
		gCalendar.set(Calendar.YEAR, 2010);
		gCalendar.set(Calendar.MONTH, 0);
		gCalendar.set(Calendar.DATE, 1);
		
		searchCriteria.setFrom(gCalendar.getTime());
		searchCriteria.setTo(new Date());
		
		searchCriteria.setSelectedQuoteStatus("EXP");			
		
		List<InsurancePolicyBrokerInfoSP> quotesFound = this.quoteSearchBusinessProcess.getInsurancePolicyBrokerListSP(searchCriteria, ProvinceCodeEnum.QUEBEC,
				CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber(),
				ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION);
		assertNotNull(quotesFound);			
		//assertEquals(quotesFound.size(), 1);
	}
	
	@Test
	public void testFirstSearchCriteria()
	{
		/* --------------------------------------------------
		 *  
		 * Recherche avec MasterBroker, dateFrom & dateTo 
			t_selected_master           VARCHAR2  	-->	0028
			t_selected_point_of_sale    VARCHAR2  	-->	null
			t_quote_ref_number          VARCHAR2  	-->	null	
			t_quote_ref_legacy_number   VARCHAR2  	-->	null
			t_postal_code               VARCHAR2  	--> null
			t_telephone                 VARCHAR2  	-->	null
			t_last_name                 VARCHAR2  	-->	null
			t_first_name                VARCHAR2  	-->	null
			t_last_update_from          VARCHAR2  	-->	2010/01/01
			t_last_update_to            VARCHAR2  	-->	today
			t_inception_date_from       VARCHAR2  	-->	null
			t_inception_date_to         VARCHAR2  	-->	null
			t_email                     VARCHAR2    --> null
			t_synchro_discount          VARCHAR2  	-->	null
			t_selected_client_followUp  VARCHAR2  	-->	null	
			t_selected_quote_status    	VARCHAR2  	-->	null
		
		-----------------------------------------------------*/
		//reset(this.insurancePolicyBrokerService);
		
		QuoteSearchCriteria searchCriteria = new QuoteSearchCriteria();
		searchCriteria.setSelectedMaster("0028");
		
		GregorianCalendar gCalendar =  new GregorianCalendar();
		gCalendar.set(Calendar.YEAR, 2010);
		gCalendar.set(Calendar.MONTH, 0);
		gCalendar.set(Calendar.DATE, 1);
		
		searchCriteria.setFrom(gCalendar.getTime());
		searchCriteria.setTo(new Date());
		
		List<InsurancePolicyBrokerInfoSP> quotesFound = this.quoteSearchBusinessProcess.getInsurancePolicyBrokerListSP(searchCriteria, ProvinceCodeEnum.QUEBEC,
				CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber(),
				ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION);
		assertNotNull(quotesFound);	
		//assertEquals(quotesFound.size(), 11);		
	}
	
	@Test
	public void testSecondSearchCriteria()
	{
		/* ------------------------------------------------- 
		 * Recherche avec MasterBroker, Postal code, dateFrom, dateTo. 
			t_selected_master           	VARCHAR2  	-->	0028
			t_selected_point_of_sale     	VARCHAR2  	-->	null
			t_quote_ref_number           	VARCHAR2  	-->	null	
			t_quote_ref_legacy_number   	VARCHAR2  	-->	null
			t_postal_code               	VARCHAR2  	--> J3X1Z3
			t_telephone                  	VARCHAR2  	-->	null
			t_last_name                 	VARCHAR2  	-->	null
			t_first_name                	VARCHAR2  	-->	null
			t_last_update_from           	VARCHAR2  	-->	2010/01/01
			t_last_update_to            	VARCHAR2  	-->	today
			t_inception_date_from        	VARCHAR2  	-->	null
			t_inception_date_to          	VARCHAR2  	-->	null
			t_email                     	VARCHAR2    --> null
			t_synchro_discount          	VARCHAR2  	-->	null
			t_selected_client_followUp   	VARCHAR2  	-->	null	
			t_selected_quote_status    		VARCHAR2  	-->	null
		
		---------------------------------------------------*/
		
		QuoteSearchCriteria searchCriteria = new QuoteSearchCriteria();
		searchCriteria.setSelectedMaster("0028");
		searchCriteria.setPostalCode("J3X1Z3");	
		
		GregorianCalendar gCalendar =  new GregorianCalendar();
		gCalendar.set(Calendar.YEAR, 2010);
		gCalendar.set(Calendar.MONTH, 0);
		gCalendar.set(Calendar.DATE, 1);
		
		searchCriteria.setFrom(gCalendar.getTime());
		searchCriteria.setTo(new Date());
		
		List<InsurancePolicyBrokerInfoSP> quotesFound = this.quoteSearchBusinessProcess.getInsurancePolicyBrokerListSP(searchCriteria, ProvinceCodeEnum.QUEBEC,
				CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber(),
				ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION);
		assertNotNull(quotesFound);
	}
	
	@Test
	public void testThirdSearchCriteria()
	{
		/* ------------------------------------------------- 
		 * Recherche avec MasterBroker, dateFrom , dateTo and status 
			t_selected_master           	VARCHAR2  	-->	0028
			t_selected_point_of_sale     	VARCHAR2  	-->	null
			t_quote_ref_number           	VARCHAR2  	-->	null
			t_quote_ref_legacy_number   	VARCHAR2  	-->	null
			t_postal_code               	VARCHAR2  	--> null
			t_telephone                  	VARCHAR2  	-->	null
			t_last_name                 	VARCHAR2  	-->	null
			t_first_name                	VARCHAR2  	-->	null
			t_last_update_from           	VARCHAR2  	-->	2010/01/01
			t_last_update_to            	VARCHAR2  	-->	today
			t_inception_date_from        	VARCHAR2  	-->	null
			t_inception_date_to          	VARCHAR2  	-->	null
			t_email                     	VARCHAR2    --> null
			t_synchro_discount          	VARCHAR2  	-->	null
			t_selected_client_followUp   	VARCHAR2  	-->	null	
			t_selected_quote_status    		VARCHAR2  	-->	QIN
		
		---------------------------------------------------*/
		
		QuoteSearchCriteria searchCriteria = new QuoteSearchCriteria();
		searchCriteria.setSelectedMaster("0028");
		searchCriteria.setSelectedQuoteStatus("QIN");
		
		GregorianCalendar gCalendar =  new GregorianCalendar();
		gCalendar.set(Calendar.YEAR, 2010);
		gCalendar.set(Calendar.MONTH, 0);
		gCalendar.set(Calendar.DATE, 1);
		
		searchCriteria.setFrom(gCalendar.getTime());
		searchCriteria.setTo(new Date());
						
		List<InsurancePolicyBrokerInfoSP> quotesFound = this.quoteSearchBusinessProcess.getInsurancePolicyBrokerListSP(searchCriteria, ProvinceCodeEnum.QUEBEC,
				CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber(),
				ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION);
		assertNotNull(quotesFound);			
		//assertEquals(quotesFound.size(), 0);
	}	
	
	/**
	 * Utility method need to run this test case in Maven 1.
	 * 
	 * @return Test suite
	 */
	public static junit.framework.Test suite() {
		return new JUnit4TestAdapter(QuoteSearchBusinessProcessTest.class);
	}
	
}
