package com.intact.brokeroffice.business.common.impl;


import java.util.ArrayList;
import java.util.List;

import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit38.AbstractJUnit38SpringContextTests;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.transaction.annotation.Transactional;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.plp.domain.enums.AgreementFollowUpStatusEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicyNote;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.report.insurancePolicy.InsurancePolicyBrokerInfo;
import com.intact.brokeroffice.business.common.ICommandBusinessProcess;
import com.intact.brokeroffice.business.quote.IQuoteBusinessProcess;

@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(locations = { "/applicationContextTest.xml" })
@Transactional
public class CommandBusinessProcessTest extends AbstractJUnit38SpringContextTests  {

	
	@Autowired
	private ICommandBusinessProcess commandBusinessProcess;
	
	@Autowired
	private IQuoteBusinessProcess quoteBusinessProcess;
	

	@Test
	@Transactional
	@Rollback
	public void testInsurancePolicyBrokerForParamMastersResultSet() {
		List<String> masters = new ArrayList<String>();
		masters.add("0028");
		List<InsurancePolicyBrokerInfo> list = this.quoteBusinessProcess.getInsurancePolicyBrokerList(masters, 31, ProvinceCodeEnum.QUEBEC, CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber(), ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION, false);
		for(InsurancePolicyBrokerInfo key : list){
			System.out.println(key.getAgreementNumber() + " " +key.getSubBrokerName1());
			if(key.getLastActivityNote()!=null) {
				System.out.println("getLastActivityNote "+key.getLastActivityNote().getUserActivityType());
			}
			if(key.getLastFollowupNote()!=null) {
				System.out.println("getLastFollowupNote "+key.getLastFollowupNote().getNote());
			}
		}
		System.out.println("SIZE : "+list.size());
	}
	
	//@Test
	@Transactional
	@Rollback
	public void testInsurancePolicyBrokerForAllMastersResultSet() {
		List<InsurancePolicyBrokerInfo> list = this.quoteBusinessProcess.getInsurancePolicyBrokerList(null, 31, ProvinceCodeEnum.QUEBEC, CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber(), ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION, true);
		for(InsurancePolicyBrokerInfo key : list){
			System.out.println(key.getAgreementNumber() + " " +key.getSubBrokerName1());
			if(key.getLastActivityNote()!=null) {
				System.out.println("getLastActivityNote "+key.getLastActivityNote().getUserActivityType());
			}
			if(key.getLastFollowupNote()!=null) {
				System.out.println("getLastFollowupNote "+key.getLastFollowupNote().getNote());
			}
		}
		System.out.println("SIZE : "+list.size());
	}
	
//	@Test
//	@Transactional
//	@Rollback
	public void testUpdateQuote() {

		PolicyVersion policyVersion = this.commandBusinessProcess.findLatestPolicyVersion("QA111133166");
		
		InsurancePolicy insurancePolicy = policyVersion.getInsurancePolicy();
		insurancePolicy.setAgreementFollowUpStatus(AgreementFollowUpStatusEnum.CONTACTED_FOLLOWUP_REQUIRED);
		addInsurancePolicyNote(insurancePolicy);
		
		this.quoteBusinessProcess.save(insurancePolicy);
		
		policyVersion = this.commandBusinessProcess.findLatestPolicyVersion("QA111133166");
		insurancePolicy = policyVersion.getInsurancePolicy();
		assertEquals(AgreementFollowUpStatusEnum.CONTACTED_FOLLOWUP_REQUIRED, insurancePolicy.getAgreementFollowUpStatus());
		assertEquals(1, insurancePolicy.getInsurancePolicyNotes().size());
		InsurancePolicyNote insurancePolicyNote = insurancePolicy.getInsurancePolicyNotes().iterator().next();
		assertNotNull(insurancePolicyNote.getId());
		assertEquals("LANOTE", insurancePolicyNote.getInsurancePolicyNote());
		assertEquals("AUTHOR", insurancePolicyNote.getAuthorUID());
	}
	
	private void addInsurancePolicyNote(InsurancePolicy insurancePolicy){
		InsurancePolicyNote insurancePolicyNote = new InsurancePolicyNote();
		insurancePolicy.addInsurancePolicyNote(insurancePolicyNote);
		insurancePolicyNote.setInsurancePolicyNote("LANOTE");
		insurancePolicyNote.setAuthorUID("AUTHOR");
	}
}
