package com.intact.brokeroffice.business.quote.impl;

import java.util.List;

import com.ing.canada.cif.domain.BrokerAssignationParameter;
import com.ing.canada.cif.domain.IBrokerLocators;
import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.domain.LanguageEnum;
import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.cif.domain.impl.SubBrokers;
import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.cif.service.exception.SubBrokerServiceException;

public class MockSubBrokerService implements ISubBrokersService {


	@Override
	public List<ISubBrokers> getWebSubBrokers(String companyNumber, String aProvinceCode,
			List<ApplicationIdEnum> applicationIds, List<LineOfBusinessEnum> linesOfBusiness) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<ISubBrokers> getWebSubBrokers(List<String> availableMasters, String companyNumber,
			List<ApplicationIdEnum> applicationIds, List<LineOfBusinessEnum> lineOfBusiness) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<ISubBrokers> getAllWebSubBrokers(String companyNumber, String aProvinceCode, String aBrokerCompanyNbr) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<ISubBrokers> getAllWebSubBrokers(String companyNumber, String aBrokerCompanyNbr) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ISubBrokers getSubBroker(String subBrokerNumber, String companyNumber) {
		System.out.println("[method=mockSubBrokerService.getSubBroker, subBrokerNumber=" + subBrokerNumber + ", companyNumber=" + companyNumber + "]");	
		SubBrokers broker = new SubBrokers();
		broker.setSubBrokerNumber("0060");
		broker.setSubBrokerId(8);
		
		return broker;
	}

	@Override
	public List<ISubBrokers> getWebSubBrokers(String companyNumber, String aBrokerCompanyNbr, String masterBrokerNbr,
			String aProvinceCode, boolean adminRole, List<ApplicationIdEnum> applicationIds,
			List<LineOfBusinessEnum> linesOfBusiness) {
		// TODO Auto-generated method stub
		return null;
	}
	
	@Override
	public List<ISubBrokers> getWebSubBrokers(String companyNumber, String aBrokerCompanyNbr, String masterBrokerNbr,
			boolean adminRole, List<ApplicationIdEnum> applicationIds,
			List<LineOfBusinessEnum> linesOfBusiness) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<ISubBrokers> getSubBrokers(String companyNumber) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ISubBrokers save(ISubBrokers aSubBrokers) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ISubBrokers update(ISubBrokers aSubBrokers) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public void delete(ISubBrokers aSubBrokers) {
		// TODO Auto-generated method stub

	}

	@Override
	public ISubBrokers getSubBrokerById(long subBrokerId) {
		System.out.println("[method=mockSubBrokerService.getSubBrokerById, subBrokerId=" + subBrokerId + "]");	
		SubBrokers broker = new SubBrokers();
		broker.setSubBrokerNumber("0060");
		broker.setSubBrokerId(8);
		
		return broker;
	}

	@Override
	public String getBrokerNumberFromPostalCode(String company, String postalCode, String externalSystemOriginCd) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getBrokerNumberFromPostalCode(String company, String postalCode, String masterBroker,
			String externalSystemOriginCd) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getBrokerNumberFromPostalCodeAndLang(String company, String postalCode, LanguageEnum language,
			String externalSystemOriginCd) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getBrokerNumberFromPostalCodeAndLang(String company, String postalCode, LanguageEnum language,
			String masterBroker, String externalSystemOriginCd) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<String> getRecordTypesForPostalCode(String postalCode) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ISubBrokers getSubBrokersForAgreements(Long agreementId, String company) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getDefaultSubBrokerNumber(String companyNumber) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getDefaultSubBrokerNumber(String companyNumber, String masterBrokerNumber,
			String externalSystemOriginCd) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<IBrokerLocators> getDefaultBrokerLocators(String companyNumber) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<IBrokerLocators> getBrokerLocatorsFromPostalCodeOrFsa(String companyNumber, String fsaOrPostalCode) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getDefaultSubBrokerNumber(String companyNumber, String language) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public String getDefaultSubBrokerNumber(String companyNumber, String masterBrokerNumber,
			String externalSystemOriginCd, String language) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public boolean verifyPosUnassignedToFsa(long subBrokerId, ApplicationIdEnum applicationId,
			LineOfBusinessEnum lineOfBusiness) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public boolean verifyPosUnassignedToQuote(String aSubbrokerNbr, String aCompanyNbr, String aProvince) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public boolean verifyPosUnassignedToQuote(String aSubbrokerNbr, String aCompanyNbr, String aProvince,
			String applicationMode) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public boolean verifyPosUnassignedToQuoteNationwide(long aSubbrokerId, ApplicationIdEnum applicationId,
			LineOfBusinessEnum lineOfBusiness) {
		// TODO Auto-generated method stub
		return false;
	}

	@Override
	public ISubBrokers findBroker(BrokerAssignationParameter parameter) throws SubBrokerServiceException {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<ISubBrokers> getAllSubBrokers(ISubBrokers master) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<String> getClosedSubBrokers(String aCompanyNumber) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public ISubBrokers getInactiveSubBroker(String subBrokerNbr, String aCompanyNumber,
			List<ApplicationIdEnum> apps, List<LineOfBusinessEnum> lobs) {
		// TODO Auto-generated method stub
		return null;
	}

	@Override
	public List<ISubBrokers> getInactiveSubBrokers(String aCompanyNumber,
			List<ApplicationIdEnum> apps, List<LineOfBusinessEnum> lobs) {
		// TODO Auto-generated method stub
		return null;
	}
	
	@Override
	public List<ISubBrokers> getInactiveSubBrokersForMaster(ISubBrokers master, 
			String aCompanyNumber, List<ApplicationIdEnum> apps, List<LineOfBusinessEnum> lobs) { 
		// TODO Auto-generated method stub
		return null;
	}
}
