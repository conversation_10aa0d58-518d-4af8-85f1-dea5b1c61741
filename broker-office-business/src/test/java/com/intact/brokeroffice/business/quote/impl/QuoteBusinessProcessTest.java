package com.intact.brokeroffice.business.quote.impl;

import static org.easymock.EasyMock.createStrictControl;
import static org.easymock.EasyMock.expect;
import static org.easymock.EasyMock.replay;
import static org.easymock.EasyMock.reset;
import static org.easymock.EasyMock.verify;
import static org.junit.jupiter.api.Assertions.*;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.lang.time.DateUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfInsuranceCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.subbrokerassignment.SubBrokerAssignment;
import com.ing.canada.plp.service.IInsurancePolicyService;
import com.intact.brokeroffice.business.domain.Quote;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.brokeroffice.service.impl.BrokerService;
import com.intact.brokeroffice.service.search.MultiThreadBrokerService;
import com.intact.business.service.broker.api.IBrokerService;
import com.intact.business.service.broker.common.Configuration;
import com.intact.business.service.broker.common.domain.UserContext;
import com.intact.business.service.broker.domain.IUserContext;

import junit.framework.JUnit4TestAdapter;

public class QuoteBusinessProcessTest {

  private com.intact.brokeroffice.service.IBrokerService quoteBusinessProcess;
  private IInsurancePolicyService insurancePolicyService;

  /**
   * Utility method need to run this test case in Maven 1.
   *
   * @return Test suite
   */
  public static junit.framework.Test suite() {
    return new JUnit4TestAdapter(QuoteBusinessProcessTest.class);
  }

	@BeforeEach
	void setup() {
    this.quoteBusinessProcess = new com.intact.brokeroffice.service.impl.BrokerService();
    this.insurancePolicyService = createStrictControl().createMock(IInsurancePolicyService.class);
    ReflectionTestUtils
        .setField(this.quoteBusinessProcess, "policyService", this.insurancePolicyService,
            IInsurancePolicyService.class);
  }

	@AfterEach
	void tearDown() throws Exception {
    this.quoteBusinessProcess = null;
    this.insurancePolicyService = null;
  }

	@Test
	void uploadQuote() {

    this.uploadQuote(this.buildService(""), null, null, null,
        BrokerServiceException.PARAM_CONTEXT_NULL);
    this.uploadQuote(this.buildService(""), this.buildContext(), null, null,
        BrokerServiceException.PARAM_QUOTE_NULL);
    this.uploadQuote(this.buildService(""), this.buildContext(), this.buildQuote(""), null,
        BrokerServiceException.PARAM_QUOTE_INVALID);
    this.uploadQuote(this.buildService(""), this.buildContext(), this.buildQuote("loi"), null,
        BrokerServiceException.PARAM_QUOTE_INVALID);
    this.uploadQuote(this.buildService(""), this.buildContext(), this.buildQuote("loi&lob"), null,
        BrokerServiceException.CONFIG_BROKER_SERVICE_NULL);
    this.uploadQuote(this.buildService("brks"), this.buildContext(), this.buildQuote("loi&lob"),
        null,
        BrokerServiceException.PARAM_SUB_BROKER_NULL);
    this.uploadQuote(this.buildService("brks"), this.buildContext(), this.buildQuote("loi&lob"),
        "0590",
        BrokerServiceException.CONFIG_UPLOAD_PROCESSORS_NULL);
    this.uploadQuote(this.buildService("proc&brks"), this.buildContext(),
        this.buildQuote("loi&lob"), "0590",
        BrokerServiceException.CONFIG_UPLOAD_PROCESSOR_NOT_FOUND);

    // TODO : Adapt test case with the new Configuration-oriented structure
//		this.uploadQuote(this.buildService("proc&brks&au&re&dao&adm"), this.buildContext(), this.buildQuote("loi&lob"), "0590",
//				null, "mockBrokerService.uploadQuote", "mockBrokerService.updateQuote");
//		this.uploadQuote(this.buildService("proc&brks&au&re&dao"), this.buildContext(), this.buildQuote("loi&lob"), "0590",
//				null, "mockBrokerService.uploadQuote", "mockBrokerService.updateQuote", "mockBrokerService.reassignQuote", "mockSubBrokerService.getSubBroker", "mockSubBrokerService.getSubBrokerById");
  }

	@Test
	void viewQuote() {
    this.viewQuote(this.buildService(""), null, null, BrokerServiceException.PARAM_CONTEXT_NULL);
    this.viewQuote(this.buildService(""), this.buildContext(), null,
        BrokerServiceException.PARAM_QUOTE_NULL);
    this.viewQuote(this.buildService(""), this.buildContext(), this.buildQuote(""),
        BrokerServiceException.PARAM_QUOTE_INVALID);
    this.viewQuote(this.buildService(""), this.buildContext(), this.buildQuote("loi"),
        BrokerServiceException.PARAM_QUOTE_INVALID);
    this.viewQuote(this.buildService(""), this.buildContext(), this.buildQuote("loi&lob"),
        BrokerServiceException.CONFIG_BROKER_SERVICE_NULL);
    this.viewQuote(this.buildService("brks"), this.buildContext(), this.buildQuote("loi&lob"),
        BrokerServiceException.PARAM_QUOTE_INVALID);
    this.viewQuote(this.buildService("brks"), this.buildContext(), this.buildQuote("loi&lob&view"),
        null, "mockBrokerService.updateQuote", "VIEW_QUO");
  }

  protected void uploadQuote(com.intact.brokeroffice.service.IBrokerService service,
      IUserContext context, Quote quote, String subBroker,
      String exceptionCode, String... successCodes) {

    ByteArrayOutputStream output = new ByteArrayOutputStream();
    Exception exception = null;

    try {
      System.setOut(new PrintStream(output));
      service.uploadQuote(context, quote, subBroker);
    } catch (BrokerServiceException ex) {
      exception = ex;
      System.err.println(ex.getMessage());
      if (exceptionCode != null) {
        assertEquals(exceptionCode, ex.getCode(), "This is not the right exception thrown : " + output.toString());
      } else {
        fail("Should not throw exception : " + output.toString());
      }
    }

    if (exception == null && exceptionCode != null) {
      fail("Should not throw exception :" + exceptionCode);
    }

    if (successCodes != null) {
      for (String successCode : successCodes) {
        assertTrue(output.toString().contains(successCode));
      }
    }

  }

  protected void viewQuote(com.intact.brokeroffice.service.IBrokerService service,
      IUserContext context, Quote quote, String exceptionCode, String... successCodes) {

    ByteArrayOutputStream output = new ByteArrayOutputStream();
    Exception exception = null;

    try {
      System.setOut(new PrintStream(output));
      service.viewQuote(context, quote);
    } catch (BrokerServiceException ex) {
      exception = ex;
      System.err.println(ex.getMessage());
      if (exceptionCode != null) {
        assertEquals(exceptionCode, ex.getCode(), "This is not the right exception thrown : " + output.toString());
      } else {
        ex.printStackTrace();
        fail("Should not throw exception" + output.toString());
      }
    }

    if (exception == null && exceptionCode != null) {
      fail("Should not throw exception :" + exceptionCode);
    }

    if (successCodes != null) {
      for (String successCode : successCodes) {
        assertTrue(output.toString().contains(successCode));
      }
    }

  }

  protected Quote buildQuote(String config) {
    Quote quote = new Quote();

    if (config.contains("loi")) {
      quote.setLineOfInsurance(LineOfInsuranceCodeEnum.RESIDENTIAL);
    }

    if (config.contains("lob")) {
      quote.setLineOfBusiness(LineOfBusinessCodeEnum.PERSONAL_LINES);
    }

    if (config.contains("view")) {
      quote.setViewNote("wow");
    }

    quote.setApplicationMode("QF");

    return quote;
  }

  private IUserContext buildContext() {
    UserContext context = new UserContext();
    context.setCompany("A");
    return (IUserContext) context;
  }

	@Test
	void emptyListReassign() {

    Map<String, InsurancePolicy> map = new HashMap<String, InsurancePolicy>();

    Long subBrokerId = 5L;

    reassign(map, subBrokerId);

    assertReassign(map, subBrokerId);
  }

  protected com.intact.brokeroffice.service.IBrokerService buildService(String context) {
    BrokerService service = new BrokerService();

    if (context.contains("proc")) {
      service.setUploadProcessors(new com.intact.brokeroffice.service.util.Configuration());
      service.getUploadProcessors().setConfigs(new HashMap<String, Object>());
    }

    if (context.contains("dao")) {
      MockSystemDAO systemDAO = new MockSystemDAO();
      systemDAO.setUploadUser("upl");
      systemDAO.setSourceUser("src");
      systemDAO.setLanguage("fr");
      service.setSystemDAO(systemDAO);

      if (context.contains("adm")) {
        systemDAO.setMaster(null);
      } else {
        systemDAO.setMaster("0060");
      }
    }

    if (context.contains("brks")) {
      service.setBrokerService(new MultiThreadBrokerService());
      service.setSubBrokerService(new MockSubBrokerService());
    }

    if (context.contains("au")) {
      AutoUploadProcessor auto = new AutoUploadProcessor();
      auto.setSystemDAO(service.getSystemDAO());
      auto.setBrokerService(new MultiThreadBrokerService());
      service.getUploadProcessors().getConfigs()
          .put(LineOfInsuranceCodeEnum.AUTOMOBILE.getCode(), auto);
    }

    if (context.contains("re")) {
      HomeUploadProcessor home = new HomeUploadProcessor();
      home.setSystemDAO(service.getSystemDAO());
      home.setBrokerService(new MultiThreadBrokerService());
      service.getUploadProcessors().getConfigs()
          .put(LineOfInsuranceCodeEnum.RESIDENTIAL.getCode(), home);
    }

    if (service.getBrokerService() != null) {
      Configuration<String, IBrokerService> services = new Configuration<String, IBrokerService>();
      Map<String, IBrokerService> configs = new HashMap<String, IBrokerService>();
      MockBrokerService mockBrokerService = new MockBrokerService();
      configs.put("webzone.P.RE.A", mockBrokerService);
      configs.put("webzone.QF", mockBrokerService);

      services.setConfigs(configs);
      service.getBrokerService().setServices(services);
    }

    return service;
  }

  private void reassign(Map<String, InsurancePolicy> map, Long subBrokerId) {
    reset(this.insurancePolicyService);
    for (Entry<String, InsurancePolicy> entry : map.entrySet()) {
      InsurancePolicy ip = entry.getValue();
      expect(this.insurancePolicyService.findByAgreementNumber(entry.getKey())).andReturn(ip);
      expect(this.insurancePolicyService.persist(ip)).andReturn(ip);
    }
    replay(this.insurancePolicyService);

    List<String> agreementNumberList = new ArrayList<String>();
    agreementNumberList.addAll(map.keySet());

    // this.quoteBusinessProcess.reassignQuotes(agreementNumberList, subBrokerId,
    // null);

    verify(this.insurancePolicyService);
  }

  /*
   * private void addSubBrokerAssignement(Long id, InsurancePolicy
   * insurancePolicy) { SubBrokerAssignment sba = new SubBrokerAssignment();
   * sba.setId(id); Date date = new Date(new Date().getTime()-4000);
   * sba.getAuditTrail().setCreateTimestamp(date); sba.setEffectiveDate(date);
   * insurancePolicy.addSubBrokerAssignment(sba); }
   *
   * private InsurancePolicy newInsurancePolicy(Long id) { InsurancePolicy
   * insurancePolicy; insurancePolicy = new InsurancePolicy();
   * insurancePolicy.setId(id); return insurancePolicy; }
   */

  private void assertReassign(Map<String, InsurancePolicy> map, Long subBrokerId) {

    Date today = new Date();
    for (Entry<String, InsurancePolicy> entry : map.entrySet()) {
      InsurancePolicy insurancePolicy = entry.getValue();
      SubBrokerAssignment latestSubBrokerAssignment = insurancePolicy
          .getLatestSubBrokerAssignment();
      assertEquals(latestSubBrokerAssignment.getCifSubBrokerId(), subBrokerId);
      assertTrue(DateUtils.isSameDay(latestSubBrokerAssignment.getEffectiveDate(), today));
      assertNull(latestSubBrokerAssignment.getExpiryDate());
      for (SubBrokerAssignment sba : insurancePolicy.getSubBrokerAssignments()) {
        if (sba != latestSubBrokerAssignment) {
          assertTrue(DateUtils.isSameDay(sba.getExpiryDate(), today));
        }
      }
    }
  }
}
