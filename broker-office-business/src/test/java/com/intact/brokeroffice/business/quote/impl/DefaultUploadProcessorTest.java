package com.intact.brokeroffice.business.quote.impl;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import com.ing.canada.plp.domain.enums.BusinessTransactionActivityCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfInsuranceCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.service.IInsurancePolicyService;
import com.ing.canada.plp.service.IPolicyVersionService;
import com.intact.brokeroffice.business.domain.Quote;
import com.intact.brokeroffice.dao.SystemDAO;
import com.intact.brokeroffice.service.search.DefaultBrokerService;
import com.intact.business.service.broker.common.domain.QuoteUpload;
import com.intact.business.service.broker.domain.IPolicy;
import org.mockito.MockitoAnnotations;

//import com.intact.plt.upload.service.client.ClientUploadApplicationService;

/**
 * Test for {@link DefaultUploadProcessor}
 * <AUTHOR>
 *
 */
class DefaultUploadProcessorTest {

	@InjectMocks
	private AutoUploadProcessor defaultUploadProcessor;

	private InsurancePolicy policy;
	
	@Mock
	private SystemDAO systemDAO;
	
	@Mock
	private IPolicy uploadInfo;
	
	@Mock
	private DefaultBrokerService brokerService;
	
	@Mock
	private IPolicyVersionService mockVersionService;
	
	@Mock
	private IInsurancePolicyService mockPolicyService;
	
	private static final String AGREEMENT_NUMBER = "1234-AGREEMENT";
	private static final String UPLOAD_USER = "JAY UNIT - UPLOAD";
	private static final String SOURCE_USER = "JAY UNIT - SOURCE";
	private static final String COMPANY = "UNIT TESTING CIE";
	private static final String LANGUAGE = "FRANCAIS";

	@BeforeEach
	void setUp() throws Exception {		
		// InsurancePolicy
		MockitoAnnotations.openMocks(this);
		policy = new InsurancePolicy();
	}

	@AfterEach
	void tearDown() throws Exception {
		defaultUploadProcessor = null;
		systemDAO = null;
		policy = null;
		uploadInfo = null;
	}

	@BeforeEach
	void setUpStaticMocks() {
	}

	@AfterEach
	void tearDownStaticMocks() {
	}

	/**
	 * Test for the method {@link DefaultUploadProcessor#execute(InsurancePolicy)}
	 * Validating the path and the returned UploadQuoteInformation.
	 */
	@Test
	void executeHappyPath() throws Exception {
		// Given
		policy.setAgreementNumber(AGREEMENT_NUMBER);
		PolicyVersion mockPolicyVersion = mock(PolicyVersion.class);
		when(mockVersionService.findLatestQuoteByTransactionActivityCodes(anyString(), 
				any(BusinessTransactionActivityCodeEnum.class), any(BusinessTransactionActivityCodeEnum.class)))
				.thenReturn(mockPolicyVersion);
		when(mockPolicyVersion.getInsurancePolicy()).thenReturn(mock(InsurancePolicy.class));
		
		when(systemDAO.getUploadUser()).thenReturn(UPLOAD_USER);
		when(systemDAO.getSourceUser()).thenReturn(SOURCE_USER);
		when(systemDAO.getCompany()).thenReturn(COMPANY);
		when(systemDAO.getLanguage()).thenReturn(LANGUAGE);
		
		when(brokerService.uploadQuote(anyString(), any(QuoteUpload.class))).thenReturn(uploadInfo);
		
		// When
		IPolicy info = defaultUploadProcessor.execute(new Quote(policy.getAgreementLegacyNumber(), 
				LineOfBusinessCodeEnum.PERSONAL_LINES, LineOfInsuranceCodeEnum.AUTOMOBILE, "QF"), "");

		// Then
		assertEquals(uploadInfo, info, "The UploadQuoteInformation should be the same");
	}
}
