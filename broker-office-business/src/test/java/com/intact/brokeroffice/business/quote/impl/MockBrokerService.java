package com.intact.brokeroffice.business.quote.impl;

import java.util.ArrayList;
import java.util.List;

import com.intact.business.service.broker.api.IBrokerService;
import com.intact.business.service.broker.common.domain.PDFDocument;
import com.intact.business.service.broker.common.domain.Policy;
import com.intact.business.service.broker.domain.IExtendedQuoteSearch;
import com.intact.business.service.broker.domain.IPDFDocument;
import com.intact.business.service.broker.domain.IPolicy;
import com.intact.business.service.broker.domain.IQuote;
import com.intact.business.service.broker.domain.IQuoteDetail;
import com.intact.business.service.broker.domain.IQuoteReassignment;
import com.intact.business.service.broker.domain.IQuoteSearch;
import com.intact.business.service.broker.domain.IQuoteUpload;
import com.intact.business.service.broker.domain.IUserContext;
import com.intact.business.service.exception.BrokerServiceException;

public class MockBrokerService implements IBrokerService {

	@Override
	public void reassignQuotes(String context, IQuoteReassignment reassign) throws BrokerServiceException {
		System.out.println("[method=mockBrokerService.reassignQuotes, context=" + context + ", quote=" + reassign + "]");	}

	@Override
	public void updateQuote(String context, IQuote quote) throws BrokerServiceException {
		System.out.println("[method=mockBrokerService.updateQuote, context=" + context + ", quote=" + quote + "]");	}

	@Override
	public IPolicy uploadQuote(String context, IQuoteUpload upload) throws BrokerServiceException {
		System.out.println("[method=mockBrokerService.uploadQuote, context=" + context + ", upload=" + upload + "]");
		return new Policy("xxx", "xxx");
	}

	@Override
	public List<IQuoteDetail> searchQuotes(String context, IUserContext user, IQuoteSearch search) throws BrokerServiceException {
		System.out.println("[method=mockBrokerService.searchQuotes, user=" + user + ", search=" + search + "]");	
		return new ArrayList<IQuoteDetail>();
	}

	@Override
	public List<IQuoteDetail> searchExtendedQuotes(String context, IUserContext user, IExtendedQuoteSearch search) throws BrokerServiceException {
		System.out.println("[method=mockBrokerService.searchQuotes, user=" + user + ", search=" + search + "]");	
		return new ArrayList<IQuoteDetail>();
	}

	@Override
	public List<IQuoteDetail> listQuotes(String context, IUserContext user) throws BrokerServiceException {
		System.out.println("[method=mockBrokerService.listQuotes, context=" + context + ", user=" + user + "]");	
		return new ArrayList<IQuoteDetail>();
	}

	@Override
	public IPDFDocument downloadDocument(String context, IQuote quote) throws BrokerServiceException {
		System.out.println("[method=mockBrokerService.downloadDocument, context=" + context + ", quote=" + quote + "]");
		return new PDFDocument("xxxx", new byte[0]);
	}
}
