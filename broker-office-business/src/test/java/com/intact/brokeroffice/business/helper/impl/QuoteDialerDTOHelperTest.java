package com.intact.brokeroffice.business.helper.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import org.apache.commons.lang.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.LocalDate;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfInsuranceCodeEnum;
import com.intact.brokeroffice.business.domain.dialer.PartyDTO;
import com.intact.brokeroffice.business.domain.dialer.PhoneDTO;
import com.intact.brokeroffice.business.domain.dialer.QuoteDialerDTO;
import com.intact.brokeroffice.business.domain.dialer.VehicleDTO;
import com.intact.brokeroffice.business.quote.QuotesBean;
import com.intact.business.service.broker.common.domain.UserContext;
import com.intact.brokeroffice.business.domain.dialer.QuoteDialerDTO.DatasourceOriginEnum;
import com.intact.brokeroffice.business.domain.dialer.QuoteDialerDTO.LineofBusinessEnum;
import com.intact.brokeroffice.business.domain.dialer.QuoteDialerDTO.QuoteAppEnumEnum;
import com.intact.brokeroffice.business.domain.dialer.QuoteDialerDTO.QuoteSourceEnum;
import com.intact.brokeroffice.business.domain.dialer.QuoteDialerDTO.SourceUnderwritingCompanyEnum;
import com.intact.plt.information.service.client.util.InformationPieceTO;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class QuoteDialerDTOHelperTest {

	QuoteDialerDTOHelper quoteDialerDTOHelper;

	@BeforeEach
	void setup() {
		this.quoteDialerDTOHelper = new QuoteDialerDTOHelper();
	}

	/*************************************************
	 *************************************************
	 *
	 * Build QuoteDialerDTO tests
	 *
	 *************************************************
	 *************************************************
	 */
	@Disabled
	@Test
	void buildQuoteDialerDTO_WithValidAutoInfo_ShouldReturnCorrespondingQuoteDialerDTO() {
		String pattern = "yyyy-MM-dd";
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
		Date testInceptionDate = new Date();
		String expectedInceptionDate = simpleDateFormat.format(testInceptionDate);
		Date testCreationDate = new Date();
		String expectedCreationDate = simpleDateFormat.format(testCreationDate);
		String testUserName = "my_user";
		String testQuoteNumber = "QA00052451256";
		String testApplicationMode = "QA";
		String testCompany = "A";
		String testBrokerNumber = "4218";
		String expectedAreaCode = "514";
		String expectedPhoneNumber = "8207725";
		String testFullPhone = "************";
		String testQuoteSource = "QB";
		QuotesBean testQuote = new QuotesBean();
		testQuote.setLineOfBusiness(LineOfBusinessCodeEnum.PERSONAL_LINES);
		testQuote.setLineOfInsurance(LineOfInsuranceCodeEnum.AUTOMOBILE);
		testQuote.setCreationDate(testCreationDate);
		testQuote.setInceptionDate(testInceptionDate);
		testQuote.setAgreementNumber(testQuoteNumber);
		testQuote.setQuoteSource(testQuoteSource);
		UserContext testUserContext = new UserContext();
		testUserContext.setCompany(testCompany);
		testUserContext.setUser(testUserName);
		InformationPieceTO generalInfoPiece = this.setupBasicPhoneInformationPiece(testFullPhone, "general_cellPhone");

		QuoteDialerDTO resultDialerQuote = this.quoteDialerDTOHelper.buildQuoteDialerDTO(
				testQuote,
				testUserContext,
				testBrokerNumber,
				testApplicationMode,
				generalInfoPiece
		);

		assertNotNull(resultDialerQuote, "Resulting QuoteDialerDTO should not have been null");
		assertEquals(expectedCreationDate, resultDialerQuote.getCreationTimeStamp());
		assertEquals(expectedInceptionDate, resultDialerQuote.getInceptionDate());
		assertEquals(testBrokerNumber, resultDialerQuote.getDistributorNumber());
		assertEquals(LineofBusinessEnum.PERSONAL, resultDialerQuote.getLineofBusiness());
		assertEquals(testQuoteNumber, resultDialerQuote.getQuoteNumber());
		assertEquals(QuoteSourceEnum.QB, resultDialerQuote.getQuoteSource());
		assertEquals(testUserName, resultDialerQuote.getUserName());
		assertNull(
				resultDialerQuote.getListRelatedQuotes(),
				"Resulting QuoteDialerDTO's related quote list should be null"
		);
		assertEquals(DatasourceOriginEnum.PLP, resultDialerQuote.getDatasourceOrigin());
		assertNotNull(resultDialerQuote.getPhone(), "Resulting QuoteDialerDTO's PhoneDTO should not be null");
		assertEquals(expectedAreaCode, resultDialerQuote.getPhone().getAreaCode());
		assertEquals(expectedPhoneNumber, resultDialerQuote.getPhone().getPhoneNumber());
		assertNull(
				resultDialerQuote.getPhone().getExtension(),
				"Resulting QuoteDialerDTO's PhoneDTO should have null extension"
		);
		assertEquals(QuoteAppEnumEnum.AUTOQUOTE, resultDialerQuote.getQuoteAppEnum());
		assertEquals(SourceUnderwritingCompanyEnum.QUEBEC, resultDialerQuote.getSourceUnderwritingCompany());
	}

	@Disabled
	@Test
	void buildQuoteDialerDTO_WithValidHomeInfo_ShouldReturnCorrespondingQuoteDialerDTO() {
		Date testCreationDate = new Date();
		String pattern = "yyyy-MM-dd";
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat(pattern);
		String expectedDate = simpleDateFormat.format(testCreationDate);
		String testUserName = "my_user";
		String testQuoteNumber = "QH00052451256";
		String testApplicationMode = "QH";
		String testCompany = "6";
		String testBrokerNumber = "4218";
		String expectedAreaCode = "514";
		String expectedPhoneNumber = "8207725";
		String testFullPhone = "************";
		String testQuoteSource = "QB";
		QuotesBean testQuote = new QuotesBean();
		testQuote.setLineOfBusiness(LineOfBusinessCodeEnum.PERSONAL_LINES);
		testQuote.setLineOfInsurance(LineOfInsuranceCodeEnum.RESIDENTIAL);
		testQuote.setCreationDate(testCreationDate);
		testQuote.setAgreementNumber(testQuoteNumber);
		testQuote.setQuoteSource(testQuoteSource);
		UserContext testUserContext = new UserContext();
		testUserContext.setCompany(testCompany);
		testUserContext.setUser(testUserName);
		InformationPieceTO generalInfoPiece = this.setupBasicPhoneInformationPiece(testFullPhone, "general_cellPhone");

		QuoteDialerDTO resultDialerQuote = this.quoteDialerDTOHelper.buildQuoteDialerDTO(
				testQuote,
				testUserContext,
				testBrokerNumber,
				testApplicationMode,
				generalInfoPiece
		);

		assertNotNull(resultDialerQuote, "Resulting QuoteDialerDTO should not have been null");
		assertEquals(expectedDate, resultDialerQuote.getCreationTimeStamp());
		assertNull(
				resultDialerQuote.getInceptionDate(),
				"Resulting QuoteDialerDTO's inception date should be null - Only auto quotes set the inception date"
		);
		assertEquals(testBrokerNumber, resultDialerQuote.getDistributorNumber());
		assertEquals(LineofBusinessEnum.PERSONAL, resultDialerQuote.getLineofBusiness());
		assertEquals(testQuoteNumber, resultDialerQuote.getQuoteNumber());
		assertEquals(QuoteSourceEnum.QB, resultDialerQuote.getQuoteSource());
		assertEquals(testUserName, resultDialerQuote.getUserName());
		assertNull(
				resultDialerQuote.getListRelatedQuotes(),
				"Resulting QuoteDialerDTO's related quote list should be null"
		);
		assertEquals(DatasourceOriginEnum.XPAS, resultDialerQuote.getDatasourceOrigin());
		assertNotNull(resultDialerQuote.getPhone(), "Resulting QuoteDialerDTO's PhoneDTO should not be null");
		assertEquals(expectedAreaCode, resultDialerQuote.getPhone().getAreaCode());
		assertEquals(expectedPhoneNumber, resultDialerQuote.getPhone().getPhoneNumber());
		assertNull(
				resultDialerQuote.getPhone().getExtension(),
				"Resulting QuoteDialerDTO's PhoneDTO should have null extension"
		);
		assertEquals(QuoteAppEnumEnum.HOME_QUICKQUOTE, resultDialerQuote.getQuoteAppEnum());
		assertEquals(SourceUnderwritingCompanyEnum.CENTRAL_ATLANTIC, resultDialerQuote.getSourceUnderwritingCompany());
	}

	@Test
	void buildQuoteDialerDTO_WithCommercialLoB_ShouldReturnDTOWithCorrespondingLoBEnumValue() {
		QuotesBean testQuote = new QuotesBean();
		testQuote.setLineOfBusiness(LineOfBusinessCodeEnum.COMMERCIAL_LINES);

		QuoteDialerDTO resultDialerQuote = this.quoteDialerDTOHelper.buildQuoteDialerDTO(
				testQuote,
				new UserContext(),
				"4218",
				"QA",
				new InformationPieceTO()
		);

		assertNotNull(resultDialerQuote, "Resulting QuoteDialerDTO should not have been null");
		assertEquals(LineofBusinessEnum.COMMERCIAL, resultDialerQuote.getLineofBusiness());
	}

	@Test
	void buildQuoteDialerDTO_WithNullEntryQuote_ShouldReturnNull() {
		QuoteDialerDTO resultDialerQuote = this.quoteDialerDTOHelper.buildQuoteDialerDTO(
				null,
				new UserContext(),
				"4218",
				"QA",
				new InformationPieceTO()
		);

		assertNull(resultDialerQuote, "Resulting QuoteDialerDTO should've been null - null entry quote");
	}

	@Test
	void buildQuoteDialerDTO_WithNullUserContext_ShouldReturnNull() {
		QuoteDialerDTO resultDialerQuote = this.quoteDialerDTOHelper.buildQuoteDialerDTO(
				new QuotesBean(),
				null,
				"4218",
				"QA",
				new InformationPieceTO()
		);

		assertNull(resultDialerQuote, "Resulting QuoteDialerDTO should've been null - null user context");
	}

	@Test
	void buildQuoteDialerDTO_WithNullGeneralInfoPiece_ShouldReturnNull() {
		QuoteDialerDTO resultDialerQuote = this.quoteDialerDTOHelper.buildQuoteDialerDTO(
				new QuotesBean(),
				new UserContext(),
				"4218",
				"QA",
				null
		);

		assertNull(resultDialerQuote, "Resulting QuoteDialerDTO should've been null - null general info piece");
	}

	@Test
	void buildQuoteDialerDTO_WithSingleChildQuote_ShouldPopulateRelatedQuotesListWithChildQuoteNumber() {
		QuotesBean testQuote = new QuotesBean();
		testQuote.setLineOfBusiness(LineOfBusinessCodeEnum.PERSONAL_LINES);
		QuotesBean childQuote1 = new QuotesBean();
		String expectedChildQuoteNumber1 = "QF5124545615";
		childQuote1.setAgreementNumber(expectedChildQuoteNumber1);
		testQuote.addChild(childQuote1);

		QuoteDialerDTO resultDialerQuote = this.quoteDialerDTOHelper.buildQuoteDialerDTO(
				testQuote,
				new UserContext(),
				"4218",
				"QA",
				new InformationPieceTO()
		);

		assertNotNull(resultDialerQuote, "Resulting QuoteDialerDTO should not have been null");
		assertNotNull(
				resultDialerQuote.getListRelatedQuotes(),
				"Resulting QuoteDialerDTO's related quotes list should not be null"
		);
		assertFalse(
				resultDialerQuote.getListRelatedQuotes().isEmpty(),
				"Resulting QuoteDialerDTO's related quotes list should not be empty"
		);
		assertEquals(expectedChildQuoteNumber1, resultDialerQuote.getListRelatedQuotes().get(0));
	}

	@Test
	void buildQuoteDialerDTO_WithMulitpleChildQuote_ShouldPopulateRelatedQuotesListWithAllChildQuotesNumber() {
		QuotesBean testQuote = new QuotesBean();
		testQuote.setLineOfBusiness(LineOfBusinessCodeEnum.PERSONAL_LINES);
		QuotesBean childQuote1 = new QuotesBean();
		String expectedChildQuoteNumber1 = "QF5124545615";
		childQuote1.setAgreementNumber(expectedChildQuoteNumber1);
		testQuote.addChild(childQuote1);
		QuotesBean childQuote2 = new QuotesBean();
		String expectedChildQuoteNumber2 = "QF5124549778";
		childQuote2.setAgreementNumber(expectedChildQuoteNumber2);
		testQuote.addChild(childQuote2);

		QuoteDialerDTO resultDialerQuote = this.quoteDialerDTOHelper.buildQuoteDialerDTO(
				testQuote,
				new UserContext(),
				"4218",
				"QA",
				new InformationPieceTO()
		);

		assertNotNull(resultDialerQuote, "Resulting QuoteDialerDTO should not have been null");
		assertNotNull(
				resultDialerQuote.getListRelatedQuotes(),
				"Resulting QuoteDialerDTO's related quotes list should not be null"
		);
		assertFalse(
				resultDialerQuote.getListRelatedQuotes().isEmpty(),
				"Resulting QuoteDialerDTO's related quotes list should not be empty"
		);
		assertEquals(expectedChildQuoteNumber1, resultDialerQuote.getListRelatedQuotes().get(0));
		assertEquals(expectedChildQuoteNumber2, resultDialerQuote.getListRelatedQuotes().get(1));
	}

	@Test
	void buildQuoteDialerDTO_WithNullQuoteSource_ShouldReturnDTOWithNullQuoteSource() {
		QuotesBean testQuote = new QuotesBean();
		testQuote.setQuoteSource(null);
		testQuote.setLineOfBusiness(LineOfBusinessCodeEnum.PERSONAL_LINES);

		QuoteDialerDTO resultDialerQuote = this.quoteDialerDTOHelper.buildQuoteDialerDTO(
				testQuote,
				new UserContext(),
				"4218",
				"QA",
				new InformationPieceTO()
		);

		assertNotNull(resultDialerQuote, "Resulting QuoteDialerDTO should not have been null");
		assertNull(resultDialerQuote.getQuoteSource(), "Resulting QuoteDialerDTO's quote source should've been null");
	}

	@Test
	void buildQuoteDialerDTO_WithHomeQuoteWithNullCreationDate_ShouldReturnDTOWithNullCreationAndInceptionDate() {
		QuotesBean testQuote = new QuotesBean();
		Date testInceptionDate = new Date();
		testQuote.setInceptionDate(testInceptionDate);
		testQuote.setLineOfBusiness(LineOfBusinessCodeEnum.PERSONAL_LINES);
		testQuote.setLineOfInsurance(LineOfInsuranceCodeEnum.RESIDENTIAL);

		QuoteDialerDTO resultDialerQuote = this.quoteDialerDTOHelper.buildQuoteDialerDTO(
				testQuote,
				new UserContext(),
				"4218",
				"QA",
				new InformationPieceTO()
		);

		assertNotNull(resultDialerQuote, "Resulting QuoteDialerDTO should not have been null");
		assertNull(resultDialerQuote.getInceptionDate(), "Resulting QuoteDialerDTO's inception date should've been null");
		assertNull(
				resultDialerQuote.getCreationTimeStamp(),
				"Resulting QuoteDialerDTO's inception date should've been null"
		);
	}

	/*************************************************
	 ************************************************

	 Build PartyDTO tests
	 ************************************************
	 ************************************************
	 */
	@Test
	void buildPartyDTO_WithPersonalQuoteValidInfo_ShouldReturnCorrespondingPartyDTO() {
		String testPolicyHolderName = "Bob Burgington";
		String testApplicationMode = "QA";
		String provinceFromCompany = "QC";
		QuotesBean testQuote = new QuotesBean();
		testQuote.setLineOfBusiness(LineOfBusinessCodeEnum.PERSONAL_LINES);

		PartyDTO resultParty = this.quoteDialerDTOHelper.buildDialerPartyDTO(
				null,
				null,
				testQuote,
				testPolicyHolderName,
				testApplicationMode,
				provinceFromCompany
		);

		assertNotNull(resultParty, "Resulting PartyDTO should not have been null");
		assertEquals(provinceFromCompany, resultParty.getProvince());
		assertEquals(testPolicyHolderName.toUpperCase(), resultParty.getFirstName());
		assertEquals("", resultParty.getLastName());
		assertEquals("", resultParty.getCompanyName());
	}

	@Test
	void buildPartyDTO_WithIRCAQuoteValidInfo_ShouldReturnCorrespondingPartyDTO() {
		String testDriverFirstName = "Bob";
		String testDriverLastName = "Burgington";
		String testApplicationMode = "IR";
		String provinceFromCompany = "QC";
		String testCompanyName = "Bob's burgers";
		QuotesBean testQuote = new QuotesBean();
		testQuote.setLineOfBusiness(LineOfBusinessCodeEnum.COMMERCIAL_LINES);
		testQuote.setUnstructuredName(testCompanyName);
		InformationPieceTO driverInfoPiece = this.setupBasicDriverInformationPiece(testDriverFirstName, testDriverLastName);

		PartyDTO resultParty = this.quoteDialerDTOHelper.buildDialerPartyDTO(
				driverInfoPiece,
				null,
				testQuote,
				null,
				testApplicationMode,
				provinceFromCompany
		);

		assertNotNull(resultParty, "Resulting PartyDTO should not have been null");
		assertEquals(provinceFromCompany, resultParty.getProvince());
		assertEquals(testDriverFirstName.toUpperCase(), resultParty.getFirstName());
		assertEquals(testDriverLastName.toUpperCase(), resultParty.getLastName());
		assertEquals(testCompanyName, resultParty.getCompanyName());
	}

	@Test
	void buildPartyDTO_WitPCQuoteValidInfo_ShouldReturnCorrespondingPartyDTO() {
		String testPrincipalOwnerName = "Bob Burgington";
		String testApplicationMode = "PC";
		String testProvince = "QC";
		String testCompanyName = "Bob's burgers";
		QuotesBean testQuote = new QuotesBean();
		testQuote.setLineOfBusiness(LineOfBusinessCodeEnum.COMMERCIAL_LINES);
		testQuote.setUnstructuredName(testCompanyName);
		InformationPieceTO generalInfoPiece = this.setupBasicPrincipalOwnerNameInformationPiece(testPrincipalOwnerName);
		InformationPieceTO addressInfoPiece = this.setupBasicAddressInformationPiece("1234 Test Street " + testProvince + " H0H0H0 CANADA");
		addressInfoPiece = addressInfoPiece.getChildren("general").getChildren("address_insured");
		when(generalInfoPiece.getChildren("general").getChildren("address_insured")).thenReturn(addressInfoPiece);

		PartyDTO resultParty = this.quoteDialerDTOHelper.buildDialerPartyDTO(
				null,
				generalInfoPiece,
				testQuote,
				null,
				testApplicationMode,
				null
		);

		assertNotNull(resultParty, "Resulting PartyDTO should not have been null");
		assertEquals(testProvince, resultParty.getProvince());
		assertEquals(testPrincipalOwnerName.toUpperCase(), resultParty.getFirstName());
		assertEquals("", resultParty.getLastName());
		assertEquals(testCompanyName, resultParty.getCompanyName());
	}

	@Test
	void buildPartyDTO_WitPCWithInvalidAddress_ShouldSetProvinceAsNull() {
		String testApplicationMode = "PC";
		QuotesBean testQuote = new QuotesBean();
		testQuote.setLineOfBusiness(LineOfBusinessCodeEnum.COMMERCIAL_LINES);
		testQuote.setUnstructuredName("Bob's burgers");
		InformationPieceTO generalInfoPiece = this.setupBasicPrincipalOwnerNameInformationPiece("Bob Burgington");
		InformationPieceTO addressInfoPiece = this.setupBasicAddressInformationPiece("Invalid");
		addressInfoPiece = addressInfoPiece.getChildren("general").getChildren("address_insured");
		when(generalInfoPiece.getChildren("general").getChildren("address_insured")).thenReturn(addressInfoPiece);

		PartyDTO resultParty = this.quoteDialerDTOHelper.buildDialerPartyDTO(
				null,
				generalInfoPiece,
				testQuote,
				null,
				testApplicationMode,
				null
		);

		assertNotNull(resultParty, "Resulting PartyDTO should not have been null");
		assertNull(resultParty.getProvince(), "Resulting PartyDTO's province should be null - invalid province string");
	}

	@Test
	void buildPartyDTO_WithNullQuoteObject_ShouldReturnNull() {
		PartyDTO resultParty = this.quoteDialerDTOHelper.buildDialerPartyDTO(
				new InformationPieceTO(),
				new InformationPieceTO(),
				null,
				"",
				"QA",
				"ON"
		);

		assertNull(resultParty, "Resulting PartyDTO should've been null - Null QuotesBean param.");
	}

	@Test
	void buildPartyDTO_WithNullDriverPieceGeneralPieceAndPolicyHolderName_ShouldReturnNull() {
		PartyDTO resultParty = this.quoteDialerDTOHelper.buildDialerPartyDTO(
				null,
				null,
				new QuotesBean(),
				null,
				"QA",
				"ON"
		);

		assertNull(resultParty, "Resulting PartyDTO should've been null - Null QuotesBean param.");
	}

	@Test
	void buildPartyDTO_WithCommercialQuoteWithoutUnstructuredName_ShouldSetCompanyToEmptyInCreatedPartyDTO() {
		String testApplicationMode = "IR";
		QuotesBean testQuote = new QuotesBean();
		testQuote.setLineOfBusiness(LineOfBusinessCodeEnum.COMMERCIAL_LINES);
		InformationPieceTO driverInfoPiece = this.setupBasicDriverInformationPiece("Bob", "Burgington");

		PartyDTO resultParty = this.quoteDialerDTOHelper.buildDialerPartyDTO(
				driverInfoPiece,
				null,
				testQuote,
				null,
				testApplicationMode,
				"ON"
		);

		assertNotNull(resultParty, "Resulting PartyDTO should not have been null");
		assertEquals(StringUtils.EMPTY, resultParty.getCompanyName());
	}

	/*************************************************
	 *************************************************
	 *
	 * Build VehicleDTO list tests
	 *
	 *************************************************
	 *************************************************
	 */
	@Test
	void buildVehicleDTOList_WithValidFrenchSingleVehicle_ShouldReturnVehicleInAList() {
		String testYear = "2019";
		String testMake = "Honda";
		String testModel = "Civic";
		String[] entryVehicles = {"Véhicule 1 - " + testMake + " " + testModel + " " + testYear + " (Code véhicule:55189856)"};
		InformationPieceTO vehicleInfoPiece = this.setupBasicVehicleInformationPiece(entryVehicles);

		List<VehicleDTO> resultVehicleList = this.quoteDialerDTOHelper.buildDialerVehicleDTOList(vehicleInfoPiece, "FR");

		assertNotNull(resultVehicleList, "Result vehicles list shouldn't be null");
		assertFalse(resultVehicleList.isEmpty(), "Result vehicles list shouldn't be empty.");
		assertEquals(1, resultVehicleList.size());
		assertEquals(testYear, resultVehicleList.get(0).getYear());
		assertEquals(testMake, resultVehicleList.get(0).getMake());
		assertEquals(testModel, resultVehicleList.get(0).getModel());
	}

	@Test
	void buildVehicleDTOList_WithValidEnglishSingleVehicle_ShouldReturnVehicleInAList() {
		String testYear = "2019";
		String testMake = "Honda";
		String testModel = "Civic";
		String[] entryVehicles = {"Vehicle 1 - " + testYear + " " + testMake + " " + testModel + " (Car code:55189856)"};
		InformationPieceTO vehicleInfoPiece = this.setupBasicVehicleInformationPiece(entryVehicles);

		List<VehicleDTO> resultVehicleList = this.quoteDialerDTOHelper.buildDialerVehicleDTOList(vehicleInfoPiece, "EN");

		assertNotNull(resultVehicleList, "Result vehicles list shouldn't be null");
		assertFalse(resultVehicleList.isEmpty(), "Result vehicles list shouldn't be empty.");
		assertEquals(1, resultVehicleList.size());
		assertEquals(testYear, resultVehicleList.get(0).getYear());
		assertEquals(testMake, resultVehicleList.get(0).getMake());
		assertEquals(testModel, resultVehicleList.get(0).getModel());
	}

	@Test
	void buildVehicleDTOList_WithValidFrenchTwoVehicles_ShouldReturnVehicleInAList() {
		String testYear1 = "2019";
		String testMake1 = "Honda";
		String testModel1 = "Civic";
		String testYear2 = "2020";
		String testMake2 = "Toyota";
		String testModel2 = "Corolla";
		String[] entryVehicles = {"Véhicule 1 - " + testMake1 + " " + testModel1 + " " + testYear1 + " (Code véhicule:55189856)",
				"Véhicule 2 - " + testMake2 + " " + testModel2 + " " + testYear2 + " (Code véhicule:44865351)"};
		InformationPieceTO vehicleInfoPiece = this.setupBasicVehicleInformationPiece(entryVehicles);

		List<VehicleDTO> resultVehicleList = this.quoteDialerDTOHelper.buildDialerVehicleDTOList(vehicleInfoPiece, "FR");

		assertNotNull(resultVehicleList, "Result vehicles list shouldn't be null");
		assertFalse(resultVehicleList.isEmpty(), "Result vehicles list shouldn't be empty.");
		assertEquals(2, resultVehicleList.size());
		assertEquals(testYear1, resultVehicleList.get(0).getYear());
		assertEquals(testMake1, resultVehicleList.get(0).getMake());
		assertEquals(testModel1, resultVehicleList.get(0).getModel());
		assertEquals(testYear2, resultVehicleList.get(1).getYear());
		assertEquals(testMake2, resultVehicleList.get(1).getMake());
		assertEquals(testModel2, resultVehicleList.get(1).getModel());
	}

	@Test
	void buildVehicleDTOList_WithValidEnglishTwoVehicles_ShouldReturnVehicleInAList() {
		String testYear1 = "2019";
		String testMake1 = "Honda";
		String testModel1 = "Civic";
		String testYear2 = "2020";
		String testMake2 = "Toyota";
		String testModel2 = "Corolla";
		String[] entryVehicles = {"Vehicle 1 - " + testYear1 + " " + testMake1 + " " + testModel1 + " (Car code:55189856)",
				"Vehicle 2 - " + testYear2 + " " + testMake2 + " " + testModel2 + " (Car code:44865351)"};
		InformationPieceTO vehicleInfoPiece = this.setupBasicVehicleInformationPiece(entryVehicles);

		List<VehicleDTO> resultVehicleList = this.quoteDialerDTOHelper.buildDialerVehicleDTOList(vehicleInfoPiece, "EN");

		assertNotNull(resultVehicleList, "Result vehicles list shouldn't be null");
		assertFalse(resultVehicleList.isEmpty(), "Result vehicles list shouldn't be empty.");
		assertEquals(2, resultVehicleList.size());
		assertEquals(testYear1, resultVehicleList.get(0).getYear());
		assertEquals(testMake1, resultVehicleList.get(0).getMake());
		assertEquals(testModel1, resultVehicleList.get(0).getModel());
		assertEquals(testYear2, resultVehicleList.get(1).getYear());
		assertEquals(testMake2, resultVehicleList.get(1).getMake());
		assertEquals(testModel2, resultVehicleList.get(1).getModel());
	}

	@Test
	void buildVehicleDTOList_WithValidFrenchThreeVehicles_ShouldReturnVehicleInAList() {
		String testYear1 = "2019";
		String testMake1 = "Honda";
		String testModel1 = "Civic";
		String testYear2 = "2020";
		String testMake2 = "Toyota";
		String testModel2 = "Corolla";
		String testYear3 = "2019";
		String testMake3 = "Acura";
		String testModel3 = "MDX SPORT HYBRID 4DR 4WD";
		String[] entryVehicles = {"Véhicule 1 - " + testMake1 + " " + testModel1 + " " + testYear1 + " (Code véhicule:55189856)",
				"Véhicule 2 - " + testMake2 + " " + testModel2 + " " + testYear2 + " (Code véhicule:44865351)",
				"Véhicule 3 - " + testMake3 + " " + testModel3 + " " + testYear3 + " (Code véhicule:34865367)"};
		InformationPieceTO vehicleInfoPiece = this.setupBasicVehicleInformationPiece(entryVehicles);

		List<VehicleDTO> resultVehicleList = this.quoteDialerDTOHelper.buildDialerVehicleDTOList(vehicleInfoPiece, "FR");

		assertNotNull(resultVehicleList, "Result vehicles list shouldn't be null");
		assertFalse(resultVehicleList.isEmpty(), "Result vehicles list shouldn't be empty.");
		assertEquals(3, resultVehicleList.size());
		assertEquals(testYear1, resultVehicleList.get(0).getYear());
		assertEquals(testMake1, resultVehicleList.get(0).getMake());
		assertEquals(testModel1, resultVehicleList.get(0).getModel());
		assertEquals(testYear2, resultVehicleList.get(1).getYear());
		assertEquals(testMake2, resultVehicleList.get(1).getMake());
		assertEquals(testModel2, resultVehicleList.get(1).getModel());
		assertEquals(testYear3, resultVehicleList.get(2).getYear());
		assertEquals(testMake3, resultVehicleList.get(2).getMake());
		assertEquals(testModel3, resultVehicleList.get(2).getModel());
	}

	@Test
	void buildVehicleDTOList_WithValidEnglishThreeVehicles_ShouldReturnVehicleInAList() {
		String testYear1 = "2019";
		String testMake1 = "Honda";
		String testModel1 = "Civic";
		String testYear2 = "2020";
		String testMake2 = "Toyota";
		String testModel2 = "Corolla";
		String testYear3 = "2019";
		String testMake3 = "Acura";
		String testModel3 = "MDX SPORT HYBRID 4DR 4WD";
		String[] entryVehicles = {"Vehicle 1 - " + testYear1 + " " + testMake1 + " " + testModel1 + " (Car code:55189856)",
				"Vehicle 2 - " + testYear2 + " " + testMake2 + " " + testModel2 + " (Car code:44865351)",
				"Vehicle 3 - " + testYear3 + " " + testMake3 + " " + testModel3 + " (Car code:34865367)"};
		InformationPieceTO vehicleInfoPiece = this.setupBasicVehicleInformationPiece(entryVehicles);

		List<VehicleDTO> resultVehicleList = this.quoteDialerDTOHelper.buildDialerVehicleDTOList(vehicleInfoPiece, "EN");

		assertNotNull(resultVehicleList, "Result vehicles list shouldn't be null");
		assertFalse(resultVehicleList.isEmpty(), "Result vehicles list shouldn't be empty.");
		assertEquals(3, resultVehicleList.size());
		assertEquals(testYear1, resultVehicleList.get(0).getYear());
		assertEquals(testMake1, resultVehicleList.get(0).getMake());
		assertEquals(testModel1, resultVehicleList.get(0).getModel());
		assertEquals(testYear2, resultVehicleList.get(1).getYear());
		assertEquals(testMake2, resultVehicleList.get(1).getMake());
		assertEquals(testModel2, resultVehicleList.get(1).getModel());
		assertEquals(testYear3, resultVehicleList.get(2).getYear());
		assertEquals(testMake3, resultVehicleList.get(2).getMake());
		assertEquals(testModel3, resultVehicleList.get(2).getModel());
	}

	@Test
	void buildVehicleDTOList_WithNonManagedLanguage_ShouldReturnEmptyList() {
		String[] entryVehicles = {"Véhicule 1 - Honda Civic 2019 (Car code:55189856)"};
		InformationPieceTO vehicleInfoPiece = this.setupBasicVehicleInformationPiece(entryVehicles);

		List<VehicleDTO> resultVehicleList = this.quoteDialerDTOHelper.buildDialerVehicleDTOList(vehicleInfoPiece, "JA");

		assertNotNull(resultVehicleList, "Result vehicles list shouldn't be null");
		assertTrue(resultVehicleList.isEmpty(), "Result vehicles list should be empty - Non managed language.");
	}

	@Test
	void buildVehicleDTOList_WithInvalidVehicleInfo_ShouldReturnEmptyList() {
		String[] entryVehicles = {"Invalid info"};
		InformationPieceTO vehicleInfoPiece = this.setupBasicVehicleInformationPiece(entryVehicles);

		List<VehicleDTO> resultVehicleList = this.quoteDialerDTOHelper.buildDialerVehicleDTOList(vehicleInfoPiece, "FR");

		assertNotNull(resultVehicleList, "Result vehicles list shouldn't be null");
		assertTrue(resultVehicleList.isEmpty(), "Result vehicles list should be empty - Invalid vehicle info.");
	}

	@Test
	void buildVehicleDTOList_WithNullVehicleInfoPiece_ShouldReturnNull() {
		List<VehicleDTO> resultVehicleList = this.quoteDialerDTOHelper.buildDialerVehicleDTOList(null, "FR");

		assertNull(
				resultVehicleList,
				"Resulting VehicleDTO list should've been null - Null entry vehicle InformationPieceTO"
		);
	}

	@Test
	void buildVehicleDTOList_WithNullLanguage_ShouldReturnNull() {
		String[] entryVehicles = {"Vehicle 1 - 2019 Honda Civic (Car code:55189856)"};
		InformationPieceTO vehicleInfoPiece = mock(InformationPieceTO.class);

		List<VehicleDTO> resultVehicleList = this.quoteDialerDTOHelper.buildDialerVehicleDTOList(vehicleInfoPiece, null);

		assertNull(
				resultVehicleList,
				"Resulting VehicleDTO list should've been null - Null entry vehicle InformationPieceTO"
		);
	}

	@Test
	void buildVehicleDTOList_WithEmptyLanguage_ShouldReturnNull() {
		String[] entryVehicles = {"Vehicle 1 - 2019 Honda Civic (Car code:55189856)"};
		InformationPieceTO vehicleInfoPiece = mock(InformationPieceTO.class);

		List<VehicleDTO> resultVehicleList = this.quoteDialerDTOHelper.buildDialerVehicleDTOList(vehicleInfoPiece, "");

		assertNull(resultVehicleList, "Resulting VehicleDTO list should've been null - Empty entry language");
	}

	@Test
	void buildVehicleDTOList_WithNoVehicles_ShouldReturnEmptyList() {
		String[] entryVehicles = {};
		InformationPieceTO vehicleInfoPiece = this.setupBasicVehicleInformationPiece(entryVehicles);

		List<VehicleDTO> resultVehicleList = this.quoteDialerDTOHelper.buildDialerVehicleDTOList(vehicleInfoPiece, "EN");

		assertNotNull(resultVehicleList, "Resulting VehicleDTO list should not be null - No vehicles.");
		assertTrue(resultVehicleList.isEmpty(), "Resulting VehicleDTO list should not be empty");
	}

	/*************************************************
	 *************************************************
	 *
	 * Convert vehicle info string tests
	 *
	 *************************************************
	 *************************************************
	 */
	@Test
	void convertVehicleInfoString_WithValidFrenchVehicleInfo_ShouldReturnCorrespondingVehicleDTO() {
		String testYear = "2020";
		String testMake = "Toyota";
		String testModel = "Corolla";
		// In French, order of infos is Make Model Year
		String testVehicleInfo = "Véhicule 1 - " + testMake + " " + testModel + " " + testYear + " (Code véhicule:54213548)";

		VehicleDTO resultVehicle = this.quoteDialerDTOHelper.convertVehicleInfoStringToVehicleDTO(testVehicleInfo, "FR");

		assertEquals(testYear, resultVehicle.getYear());
		assertEquals(testMake, resultVehicle.getMake());
		assertEquals(testModel, resultVehicle.getModel());
	}

	@Test
	void convertVehicleInfoString_WithMultipleInfoInFrenchModel_ShouldReturnCorrespondingVehicleDTO() {
		String testYear = "2019";
		String testMake = "Acura";
		String testModel = "MDX SPORT HYBRID 4DR 4WD";
		// In English, order of infos is Year Make Model
		String testVehicleInfo = "Véhicule 1 - " + testMake + " " + testModel + " " + testYear + " (Code véhicule:54213548)";

		VehicleDTO resultVehicle = this.quoteDialerDTOHelper.convertVehicleInfoStringToVehicleDTO(testVehicleInfo, "FR");

		assertEquals(testYear, resultVehicle.getYear());
		assertEquals(testMake, resultVehicle.getMake());
		assertEquals(testModel, resultVehicle.getModel());
	}

	@Test
	void convertVehicleInfoString_WithValidEnglishVehicleInfo_ShouldReturnCorrespondingVehicleDTO() {
		String testYear = "2020";
		String testMake = "Toyota";
		String testModel = "Corolla";
		// In English, order of infos is Year Make Model
		String testVehicleInfo = "Vehicle 1 - " + testYear + " " + testMake + " " + testModel + " (Car code:54213548)";

		VehicleDTO resultVehicle = this.quoteDialerDTOHelper.convertVehicleInfoStringToVehicleDTO(testVehicleInfo, "EN");

		assertEquals(testYear, resultVehicle.getYear());
		assertEquals(testMake, resultVehicle.getMake());
		assertEquals(testModel, resultVehicle.getModel());
	}

	@Test
	void convertVehicleInfoString_WithMultipleInfoInEnglishModel_ShouldReturnCorrespondingVehicleDTO() {
		String testYear = "2019";
		String testMake = "Acura";
		String testModel = "MDX SPORT HYBRID 4DR 4WD";
		// In English, order of infos is Year Make Model
		String testVehicleInfo = "Vehicle 1 - " + testYear + " " + testMake + " " + testModel + " (Car code:54213548)";

		VehicleDTO resultVehicle = this.quoteDialerDTOHelper.convertVehicleInfoStringToVehicleDTO(testVehicleInfo, "EN");

		assertEquals(testYear, resultVehicle.getYear());
		assertEquals(testMake, resultVehicle.getMake());
		assertEquals(testModel, resultVehicle.getModel());
	}

	@Test
	void convertVehicleInfoString_WithNullVehicleInfo_ShouldReturnNull() {
		VehicleDTO resultVehicle = this.quoteDialerDTOHelper.convertVehicleInfoStringToVehicleDTO(null, "EN");

		assertNull(resultVehicle, "Resulting vehicle should've been null - Null entry vehicle info");
	}

	@Test
	void convertVehicleInfoString_WithNullLanguage_ShouldReturnNull() {
		String testVehicleInfo = "Vehicle 1 - Toyota Corolla 2020 (Car code:54213548)";

		VehicleDTO resultVehicle = this.quoteDialerDTOHelper.convertVehicleInfoStringToVehicleDTO(testVehicleInfo, null);

		assertNull(resultVehicle, "Resulting vehicle should've been null - Null entry language");
	}

	@Test
	void convertVehicleInfoString_WithEmptyVehicleInfo_ShouldReturnNull() {
		VehicleDTO resultVehicle = this.quoteDialerDTOHelper.convertVehicleInfoStringToVehicleDTO("", "EN");

		assertNull(resultVehicle, "Resulting vehicle should've been null - Empty entry vehicle info");
	}

	@Test
	void convertVehicleInfoString_WithEmptyLanguage_ShouldReturnNull() {
		String testVehicleInfo = "Vehicle 1 - Toyota Corolla 2020 (Car code:54213548)";

		VehicleDTO resultVehicle = this.quoteDialerDTOHelper.convertVehicleInfoStringToVehicleDTO(testVehicleInfo, "");

		assertNull(resultVehicle, "Resulting vehicle should've been null - Empty entry language");
	}

	@Test
	void convertVehicleInfoString_WithNonManagedLanguage_ShouldReturnNull() {
		String testVehicleInfo = "Vehicle 1 - Toyota Corolla 2020 (Car code:54213548)";

		VehicleDTO resultVehicle = this.quoteDialerDTOHelper.convertVehicleInfoStringToVehicleDTO(testVehicleInfo, "JA");

		assertNull(resultVehicle, "Resulting vehicle should've been null - Non-managed entry language");
	}

	@Test
	void convertVehicleInfoString_WithTooShortVehicleInformation_ShouldReturnNull() {
		// In English, order of infos is Year Make Model
		String testVehicleInfo = "Vehicle 1 -  (Car code:54213548)";

		VehicleDTO resultVehicle = this.quoteDialerDTOHelper.convertVehicleInfoStringToVehicleDTO(testVehicleInfo, "EN");

		assertNull(resultVehicle, "Resulting vehicle should've been null - Too short vehicle information");
	}

	@Test
	void convertVehicleInfoString_WithInvalidFormatForBeginingOfString_ShouldReturnNull() {
		String testYear = "2020";
		String testMake = "Toyota";
		String testModel = "Corolla";
		// In English, order of infos is Year Make Model
		String testVehicleInfo = testYear + " " + testMake + " " + testModel + " (Car code:54213548)";

		VehicleDTO resultVehicle = this.quoteDialerDTOHelper.convertVehicleInfoStringToVehicleDTO(testVehicleInfo, "EN");

		assertNull(
				resultVehicle,
				"Resulting vehicle should've been null - Wrong format for beginning of String (should contain ' - ')"
		);
	}

	@Test
	void convertVehicleInfoString_WithInvalidFormatForEndOfString_ShouldReturnNull() {
		String testYear = "2020";
		String testMake = "Toyota";
		String testModel = "Corolla";
		// In English, order of infos is Year Make Model
		String testVehicleInfo = "Vehicle 1 - " + testYear + " " + testMake + " " + testModel;

		VehicleDTO resultVehicle = this.quoteDialerDTOHelper.convertVehicleInfoStringToVehicleDTO(testVehicleInfo, "EN");

		assertNull(
				resultVehicle,
				"Resulting vehicle should've been null - Wrong format for end of String (should contain ' ( ')"
		);
	}

	/*************************************************
	 *************************************************
	 *
	 * Obtain vehicle informations tests
	 *
	 *************************************************
	 *************************************************
	 */
	@Test
	void obtainingVehicleInfo_WithOneVehiclePiece_shouldReturnCorrespondingString() {
		String[] entryVehicles = {"2019 Honda Civic"};
		String expectedVehicleInfo = StringUtils.join(entryVehicles, " ");
		InformationPieceTO vehicleInfoPiece = this.setupBasicVehicleInformationPiece(entryVehicles);

		String resultVehicleInfo = this.quoteDialerDTOHelper.obtainVehiclesInformation(vehicleInfoPiece);

		assertEquals(expectedVehicleInfo, resultVehicleInfo);
	}

	@Test
	void obtainingVehicleInfo_WithTwoVehiclePieces_shouldReturnCorrespondingString() {
		String[] entryVehicles = {"2019 Honda Civic", "2020 Toyota Corolla"};
		String expectedVehicleInfo = StringUtils.join(entryVehicles, " ");
		InformationPieceTO vehicleInfoPiece = this.setupBasicVehicleInformationPiece(entryVehicles);

		String resultVehicleInfo = this.quoteDialerDTOHelper.obtainVehiclesInformation(vehicleInfoPiece);

		assertEquals(expectedVehicleInfo, resultVehicleInfo);
	}

	@Test
	void obtainingVehicleInfo_WithThreeVehiclePieces_shouldReturnCorrespondingString() {
		String[] entryVehicles = {"2019 Honda Civic", "2020 Toyota Corolla", "2014 Nissan Versa"};
		String expectedVehicleInfo = StringUtils.join(entryVehicles, " ");
		InformationPieceTO vehicleInfoPiece = this.setupBasicVehicleInformationPiece(entryVehicles);

		String resultVehicleInfo = this.quoteDialerDTOHelper.obtainVehiclesInformation(vehicleInfoPiece);

		assertEquals(expectedVehicleInfo, resultVehicleInfo);
	}

	@Test
	void obtainingVehicleInfo_WithEmptyVehiclePiece_shouldReturnEmptyString() {
		String[] entryVehicles = {""};
		String expectedVehicleInfo = StringUtils.join(entryVehicles, " ");
		InformationPieceTO vehicleInfoPiece = this.setupBasicVehicleInformationPiece(entryVehicles);

		String resultVehicleInfo = this.quoteDialerDTOHelper.obtainVehiclesInformation(vehicleInfoPiece);

		assertEquals(expectedVehicleInfo, resultVehicleInfo);
	}

	@Test
	void obtainingVehicleInfo_WithNoVehicles_shouldReturnEmptyString() {
		String[] entryVehicles = {};
		InformationPieceTO vehicleInfoPiece = this.setupBasicVehicleInformationPiece(entryVehicles);

		String resultVehicleInfo = this.quoteDialerDTOHelper.obtainVehiclesInformation(vehicleInfoPiece);

		assertNull(resultVehicleInfo, "Resulting vehicle info should've been null - No vehicles");
	}

	@Test
	void obtainingVehicleInfo_WithNullParentPiece_shouldReturnNull() {
		String resultVehicleInfo = this.quoteDialerDTOHelper.obtainVehiclesInformation(null);

		assertNull(resultVehicleInfo, "Resulting vehicle info should've been null - Null parent InformationPieceTO");
	}

	@Test
	void obtainingVehicleInfo_WithNoChildrenInParentInfoPiece_shouldReturnNull() {
		String resultVehicleInfo = this.quoteDialerDTOHelper.obtainVehiclesInformation(new InformationPieceTO());

		assertNull(resultVehicleInfo, "Null parent InformationPieceTO");
	}

	/*************************************************
	 *************************************************
	 *
	 * Populate party name tests
	 *
	 *************************************************
	 *************************************************
	 */
	@Test
	void populateName_ForPersonalLinesWithValidName_shouldSetUpperCasePolicyHolderNameAsFirstName() {
		PartyDTO testParty = new PartyDTO();
		String policyHolderName = "Bob Burgington";

		this.quoteDialerDTOHelper.populateDialerPartyName(
				testParty,
				null,
				null,
				LineOfBusinessCodeEnum.PERSONAL_LINES,
				policyHolderName,
				null
		);

		assertEquals(policyHolderName.toUpperCase(), testParty.getFirstName());
		assertEquals(StringUtils.EMPTY, testParty.getLastName());
	}

	@Test
	void populateName_WithNullPartyDTO_shouldNotCreatePartyDTO() {
		PartyDTO testParty = null;

		this.quoteDialerDTOHelper.populateDialerPartyName(
				testParty,
				new InformationPieceTO(),
				new InformationPieceTO(),
				LineOfBusinessCodeEnum.PERSONAL_LINES,
				"",
				null
		);

		assertNull(testParty, "PartyDTO objtect should not have been instanciated.");
	}

	@Test
	void populateName_ForPersonalLinesWithNullPolicyHolderName_shouldSetEmptyFirstAndLastNames() {
		PartyDTO testParty = new PartyDTO();
		String policyHolderName = null;

		this.quoteDialerDTOHelper.populateDialerPartyName(
				testParty,
				null,
				null,
				LineOfBusinessCodeEnum.PERSONAL_LINES,
				policyHolderName,
				null
		);

		assertEquals(StringUtils.EMPTY, testParty.getFirstName());
		assertEquals(StringUtils.EMPTY, testParty.getLastName());
	}

	@Test
	void populateName_ForPersonalLinesWithPolicyHolderNameAsNull_shouldSetEmptyFirstAndLastNames() {
		PartyDTO testParty = new PartyDTO();
		String policyHolderName = "null";

		this.quoteDialerDTOHelper.populateDialerPartyName(
				testParty,
				null,
				null,
				LineOfBusinessCodeEnum.PERSONAL_LINES,
				policyHolderName,
				null
		);

		assertEquals(StringUtils.EMPTY, testParty.getFirstName());
		assertEquals(StringUtils.EMPTY, testParty.getLastName());
	}

	@Test
	void populateName_ForIRCAQuotesWithFirstDriverInfos_shouldSetUppercaseDriverInfoForFirstAndLastNames() {
		PartyDTO testParty = new PartyDTO();
		String testFirstName = "Bob";
		String testLastName = "Burgington";
		InformationPieceTO vehicleInfoPiece = this.setupBasicDriverInformationPiece(testFirstName, testLastName);

		this.quoteDialerDTOHelper.populateDialerPartyName(
				testParty,
				vehicleInfoPiece,
				null,
				LineOfBusinessCodeEnum.COMMERCIAL_LINES,
				null,
				"IR"
		);

		assertEquals(testFirstName.toUpperCase(), testParty.getFirstName());
		assertEquals(testLastName.toUpperCase(), testParty.getLastName());
	}

	@Test
	void populateName_ForIRCAQuotesWithOnlyDriverFirstName_shouldSetUppercaseDriverInfoForFirstName() {
		PartyDTO testParty = new PartyDTO();
		String testFirstName = "Bob";
		String testLastName = null;
		InformationPieceTO vehicleInfoPiece = this.setupBasicDriverInformationPiece(testFirstName, testLastName);

		this.quoteDialerDTOHelper.populateDialerPartyName(
				testParty,
				vehicleInfoPiece,
				null,
				LineOfBusinessCodeEnum.COMMERCIAL_LINES,
				null,
				"IR"
		);

		assertEquals(testFirstName.toUpperCase(), testParty.getFirstName());
		assertEquals(StringUtils.EMPTY, testParty.getLastName());
	}

	@Test
	void populateName_ForIRCAQuotesWithOnlyDriverLastName_shouldSetUppercaseDriverInfoForLastName() {
		PartyDTO testParty = new PartyDTO();
		String testFirstName = null;
		String testLastName = "Burgington";
		InformationPieceTO vehicleInfoPiece = this.setupBasicDriverInformationPiece(testFirstName, testLastName);

		this.quoteDialerDTOHelper.populateDialerPartyName(
				testParty,
				vehicleInfoPiece,
				null,
				LineOfBusinessCodeEnum.COMMERCIAL_LINES,
				null,
				"IR"
		);

		assertEquals(StringUtils.EMPTY, testParty.getFirstName());
		assertEquals(testLastName.toUpperCase(), testParty.getLastName());
	}

	@Test
	void populateName_ForIRCAQuotesWithNullFirstAndLastName_shouldSetUppercaseDriverInfoForLastName() {
		PartyDTO testParty = new PartyDTO();
		String testFirstName = null;
		String testLastName = null;
		InformationPieceTO vehicleInfoPiece = this.setupBasicDriverInformationPiece(testFirstName, testLastName);

		this.quoteDialerDTOHelper.populateDialerPartyName(
				testParty,
				vehicleInfoPiece,
				null,
				LineOfBusinessCodeEnum.COMMERCIAL_LINES,
				null,
				"IR"
		);

		assertEquals(StringUtils.EMPTY, testParty.getFirstName());
		assertEquals(StringUtils.EMPTY, testParty.getLastName());
	}

	@Test
	void populateName_ForIRCAQuotesWithNullParentInfoPiece_ShouldSetEmptyFirstAndLastNames() {
		PartyDTO testParty = new PartyDTO();
		this.quoteDialerDTOHelper.populateDialerPartyName(
				testParty,
				null,
				null,
				LineOfBusinessCodeEnum.COMMERCIAL_LINES,
				null,
				"IR"
		);

		assertEquals(StringUtils.EMPTY, testParty.getFirstName());
		assertEquals(StringUtils.EMPTY, testParty.getLastName());
	}

	@Test
	void populateName_ForIRCAQuotesWithNullFirstName_ShouldSetEmptyFirstAndLastNames() {
		PartyDTO testParty = new PartyDTO();
		this.quoteDialerDTOHelper.populateDialerPartyName(
				testParty,
				null,
				null,
				LineOfBusinessCodeEnum.COMMERCIAL_LINES,
				null,
				"IR"
		);

		assertEquals(StringUtils.EMPTY, testParty.getFirstName());
		assertEquals(StringUtils.EMPTY, testParty.getLastName());
	}

	@Test
	void populateName_ForPCWithValidPrincipalOwnerName_ShouldSetUpperCasePrincipalOwnerNameInFirstName() {
		PartyDTO testParty = new PartyDTO();
		String testPrincipalOwnerName = "Bob Burgington";
		InformationPieceTO parentInfoPiece = this.setupBasicPrincipalOwnerNameInformationPiece(testPrincipalOwnerName);

		this.quoteDialerDTOHelper.populateDialerPartyName(
				testParty,
				null,
				parentInfoPiece,
				LineOfBusinessCodeEnum.COMMERCIAL_LINES,
				null,
				"PC"
		);

		assertEquals(testPrincipalOwnerName.toUpperCase(), testParty.getFirstName());
		assertEquals(StringUtils.EMPTY, testParty.getLastName());
	}

	@Test
	void populateName_ForPCWithNullGeneralInfoPiece_ShouldSetEmptyFirstAndLastNames() {
		PartyDTO testParty = new PartyDTO();

		this.quoteDialerDTOHelper.populateDialerPartyName(
				testParty,
				null,
				null,
				LineOfBusinessCodeEnum.COMMERCIAL_LINES,
				null,
				"PC"
		);

		assertEquals(StringUtils.EMPTY, testParty.getFirstName());
		assertEquals(StringUtils.EMPTY, testParty.getLastName());
	}

	@Test
	void populateName_ForPCWithNullPrincipalOwnerName_ShouldSetEmptyFirstAndLastNames() {
		PartyDTO testParty = new PartyDTO();
		String testPrincipalOwnerName = null;
		InformationPieceTO parentInfoPiece = this.setupBasicPrincipalOwnerNameInformationPiece(testPrincipalOwnerName);

		this.quoteDialerDTOHelper.populateDialerPartyName(
				testParty,
				null,
				parentInfoPiece,
				LineOfBusinessCodeEnum.COMMERCIAL_LINES,
				null,
				"PC"
		);

		assertEquals(StringUtils.EMPTY, testParty.getFirstName());
		assertEquals(StringUtils.EMPTY, testParty.getLastName());
	}

	/*************************************************
	 *************************************************
	 *
	 * Obtain province tests
	 *
	 *************************************************
	 *************************************************
	 */
	@Test
	void obtainingProvince_WithAddressInfoPieceWithCommas_ShouldReturnProvinceFromAddress() {
		String expectedProvince = "SK";
		InformationPieceTO parentInfoPiece = this.setupBasicAddressInformationPiece("1234 Test Street, SK, H0H0H0, CANADA");

		String resultProvince = this.quoteDialerDTOHelper.obtainProvinceFromAddressInsuredField(parentInfoPiece);

		assertEquals(expectedProvince, resultProvince);
	}

	@Test
	void obtainingProvince_WithAddressInfoPieceWithoutCommas_ShouldReturnProvinceFromAddress() {
		String expectedProvince = "SK";
		InformationPieceTO parentInfoPiece = this.setupBasicAddressInformationPiece("1234 Test Street SK H0H0H0 CANADA");

		String resultProvince = this.quoteDialerDTOHelper.obtainProvinceFromAddressInsuredField(parentInfoPiece);

		assertEquals(expectedProvince, resultProvince);
	}

	@Test
	void obtainingProvince_WithNullParentInfoPiece_ShouldReturnNull() {
		String resultProvince = this.quoteDialerDTOHelper.obtainProvinceFromAddressInsuredField(null);

		assertNull(resultProvince, "Resulting province should've been null - Null parent info piece");
	}

	@Test
	void obtainingProvince_WithBlankAddress_ShouldReturnNull() {
		InformationPieceTO parentInfoPiece = this.setupBasicAddressInformationPiece(" ");

		String resultProvince = this.quoteDialerDTOHelper.obtainProvinceFromAddressInsuredField(parentInfoPiece);

		assertNull(resultProvince, "Resulting province should've been null - Blank address");
	}

	@Test
	void obtainingProvince_WithNullAddress_ShouldReturnNull() {
		InformationPieceTO parentInfoPiece = this.setupBasicAddressInformationPiece(null);

		String resultProvince = this.quoteDialerDTOHelper.obtainProvinceFromAddressInsuredField(parentInfoPiece);

		assertNull(resultProvince, "Resulting province should've been null - Null address");
	}

	@Test
	void obtainingProvince_WithTooSmallAddress_ShouldReturnNull() {
		InformationPieceTO parentInfoPiece = this.setupBasicAddressInformationPiece("1234AlloStreet ,SK");

		String resultProvince = this.quoteDialerDTOHelper.obtainProvinceFromAddressInsuredField(parentInfoPiece);

		assertNull(resultProvince, "Resulting province should've been null - Too small address");
	}


	/*************************************************
	 *************************************************
	 *
	 * Obtain client phone test
	 *
	 *************************************************
	 *************************************************
	 */

	@Test
	void obtainingClientPhone_WithValidHomePhoneNumber_ShouldReturnPhoneDTOWithPhoneInfos() {
		String expectedAreaCode = "514";
		String expectedPhoneNumber = "8207725";
		String testFullPhone = "************";
		InformationPieceTO parentInfoPiece = this.setupBasicPhoneInformationPiece(testFullPhone, "general_homePhone");

		PhoneDTO resultPhone = this.quoteDialerDTOHelper.obtainClientPhoneNumber(parentInfoPiece);

		assertEquals(expectedAreaCode, resultPhone.getAreaCode());
		assertEquals(expectedPhoneNumber, resultPhone.getPhoneNumber());
		assertNull(resultPhone.getExtension(), "Extension field of the result PhoneDTO should be null.");
	}

	@Disabled
	@Test
	void obtainingClientPhone_WithValidCellPhoneNumber_ShouldReturnPhoneDTOWithPhoneInfos() {
		String expectedAreaCode = "514";
		String expectedPhoneNumber = "8207725";
		String testFullPhone = "************";
		InformationPieceTO parentInfoPiece = this.setupBasicPhoneInformationPiece(testFullPhone, "general_cellPhone");
		
		PhoneDTO resultPhone = this.quoteDialerDTOHelper.obtainClientPhoneNumber(parentInfoPiece);

		assertEquals(expectedAreaCode, resultPhone.getAreaCode());
		assertEquals(expectedPhoneNumber, resultPhone.getPhoneNumber());
		assertNull(resultPhone.getExtension(), "Extension field of the result PhoneDTO should be null.");
	}

	@Test
	void obtainingClientPhone_WithValidHomeAndCellPhoneNumber_ShouldReturnPhoneDTOWithHomePhoneInfos() {
		String expectedAreaCode = "514";
		String expectedPhoneNumber = "8207725";
		String testFullHomePhone = "************";
		InformationPieceTO parentInfoPiece = this.setupBasicPhoneInformationPiece(testFullHomePhone, "general_homePhone");

		PhoneDTO resultPhone = this.quoteDialerDTOHelper.obtainClientPhoneNumber(parentInfoPiece);

		assertEquals(expectedAreaCode, resultPhone.getAreaCode());
		assertEquals(expectedPhoneNumber, resultPhone.getPhoneNumber());
		assertNull(resultPhone.getExtension(), "Extension field of the result PhoneDTO should be null.");
	}

	@Test
	void obtainingClientPhone_WithNullParentInfoPiece_ShouldReturnNull() {
		PhoneDTO resultPhone = this.quoteDialerDTOHelper.obtainClientPhoneNumber(null);

		assertNull(resultPhone, "Resulting PhoneDTO should've been null - Null parent info piece");
	}

	@Test
	void obtainingClientPhone_WithBlankPhoneNumber_ShouldReturnNull() {
		InformationPieceTO parentInfoPiece = this.setupBasicPhoneInformationPiece(" ", "general_homePhone");

		PhoneDTO resultPhone = this.quoteDialerDTOHelper.obtainClientPhoneNumber(parentInfoPiece);

		assertNull(resultPhone, "Resulting PhoneDTO should've been null - Blank phone");
	}

	@Test
	void obtainingClientPhone_WithNullPhone_ShouldReturnNull() {
		InformationPieceTO parentInfoPiece = this.setupBasicPhoneInformationPiece(null, "general_homePhone");

		PhoneDTO resultPhone = this.quoteDialerDTOHelper.obtainClientPhoneNumber(parentInfoPiece);

		assertNull(resultPhone, "Resulting PhoneDTO should've been null - Null phone");
	}

	@Test
	void obtainingClientPhone_WithTooSmallPhoneNumber_ShouldReturnNull() {
		InformationPieceTO parentInfoPiece = this.setupBasicPhoneInformationPiece("45", "general_homePhone");

		PhoneDTO resultPhone = this.quoteDialerDTOHelper.obtainClientPhoneNumber(parentInfoPiece);

		assertNull(resultPhone, "Resulting PhoneDTO should've been null - Too small phone");
	}


	/*************************************************
	 *************************************************
	 *
	 * Tests for child InformationPieceTO validation
	 *
	 *************************************************
	 *************************************************
	 */

	@Test
	void validateInfoPiece_WithValidInfoPiece_ShouldReturnTrue() {
		String childName = "testChild";
		InformationPieceTO testInfoPiece = this.setupBasicValidTestInformationPiece(childName, "");

		boolean isValid = this.quoteDialerDTOHelper.validateInformationPieceChildPieces(testInfoPiece, childName);

		assertTrue(isValid, "Validation should've returned true - valid info piece");
	}

	@Test
	void validateInfoPiece_WithNullInfoPiece_ShouldReturnFalse() {
		boolean isValid = this.quoteDialerDTOHelper.validateInformationPieceChildPieces(null, "");

		assertFalse(isValid, "Valdiation should've returned false - Null info piece");
	}

	@Test
	void validateInfoPiece_WithNullChildName_ShouldReturnFalse() {
		InformationPieceTO testInfoPiece = mock(InformationPieceTO.class);

		when(testInfoPiece.getChildren(null)).thenReturn(null);
		boolean isValid = this.quoteDialerDTOHelper.validateInformationPieceChildPieces(testInfoPiece, null);

		assertFalse(isValid, "Valdiation should've returned false - Null child name");
	}

	@Test
	void validateInfoPiece_WithNullChildInfoPiece_ShouldReturnFalse() {
		String childName = "testChild";
		InformationPieceTO testInfoPiece = mock(InformationPieceTO.class);
		when(testInfoPiece.getChildren(childName)).thenReturn(null);

		boolean isValid = this.quoteDialerDTOHelper.validateInformationPieceChildPieces(testInfoPiece, childName);

		assertFalse(isValid, "Valdiation should've returned false - Null child InformationPieceTO");
	}

	@Test
	void validateInfoPiece_WithNullChildPiecesList_ShouldReturnFalse() {
		String childName = "testChild";
		InformationPieceTO testInfoPiece = this.setupBasicValidTestInformationPiece(childName, "testValue");
		when(testInfoPiece.getChildren(childName).getPieces()).thenReturn(null);

		boolean isValid = this.quoteDialerDTOHelper.validateInformationPieceChildPieces(testInfoPiece, childName);

		assertFalse(isValid, "Valdiation should've returned false - Null child pieces list");
	}

	@Test
	void validateInfoPiece_WithEmptyPhonePiecesList_ShouldReturnFalse() {
		String childName = "testChild";
		InformationPieceTO testInfoPiece = this.setupBasicValidTestInformationPiece(childName, "testValue");
		when(testInfoPiece.getChildren(childName).getPieces()).thenReturn(new ArrayList<InformationPieceTO>());

		boolean isValid = this.quoteDialerDTOHelper.validateInformationPieceChildPieces(testInfoPiece, childName);

		assertFalse(isValid, "Valdiation should've returned false - Empty child pieces list");
	}

	@Test
	void validateInfoPiece_WithOnlyOnePhonePiece_ShouldReturnFalse() {
		String childName = "testChild";
		String testValue = "test value";
		InformationPieceTO testInfoPiece = this.setupBasicValidTestInformationPiece(childName, "testValue");
		List<InformationPieceTO> testPiecesList = new ArrayList<InformationPieceTO>();
		InformationPieceTO testPiece = new InformationPieceTO();
		testPiece.setValue(testValue);
		testPiecesList.add(testPiece);
		when(testInfoPiece.getChildren(childName).getPieces()).thenReturn(testPiecesList);

		boolean isValid = this.quoteDialerDTOHelper.validateInformationPieceChildPieces(testInfoPiece, childName);

		assertFalse(isValid, "Valdiation should've returned false - Only one piece instead of the minimum 2 (key + value)");
	}

	@Test
	void validateInfoPiece_WithNullPieceValue_ShouldReturnFalse() {
		String childName = "testChild";
		InformationPieceTO testInfoPiece = this.setupBasicValidTestInformationPiece(childName, "testValue");
		when(testInfoPiece.getChildren(childName).getPieces().get(1).getValue()).thenReturn(null);

		boolean isValid = this.quoteDialerDTOHelper.validateInformationPieceChildPieces(testInfoPiece, childName);

		assertFalse(isValid, "Valdiation should've returned false - Only one piece instead of the minimum 2 (key + value)");
	}

	/*************************************************
	 *************************************************
	 *
	 * Tests for converter methods
	 *
	 *************************************************
	 *************************************************
	 */
	@Test
	void convertCompanyToEnum_WithExistingCompany_shouldReturnCorrespondingEnumValue() {
		String companyQuebec = "A";
		String companyCentral = "6";
		String companyWestern = "3";

		SourceUnderwritingCompanyEnum resultQuebec = this.quoteDialerDTOHelper.convertCompanyToSourceUnderwritingCompanyEnum(
				companyQuebec);
		SourceUnderwritingCompanyEnum resultCentral = this.quoteDialerDTOHelper.convertCompanyToSourceUnderwritingCompanyEnum(
				companyCentral);
		SourceUnderwritingCompanyEnum resultWestern = this.quoteDialerDTOHelper.convertCompanyToSourceUnderwritingCompanyEnum(
				companyWestern);

		assertEquals(SourceUnderwritingCompanyEnum.QUEBEC, resultQuebec);
		assertEquals(SourceUnderwritingCompanyEnum.CENTRAL_ATLANTIC, resultCentral);
		assertEquals(SourceUnderwritingCompanyEnum.WESTERN, resultWestern);
	}

	@Test
	void convertCompanyToEnum_WithNullEntryCompany_shouldReturnNull() {
		SourceUnderwritingCompanyEnum resultCompanyEnum = this.quoteDialerDTOHelper.convertCompanyToSourceUnderwritingCompanyEnum(
				null);

		assertNull(resultCompanyEnum, "Resulting SourceUnderwritingCompanyEnum should've been null - Null entry company");
	}

	@Test
	void convertCompanyToEnum_WithInvalidEntryCompany_shouldReturnNull() {
		String entryCompany = "This doesn't exist";

		SourceUnderwritingCompanyEnum resultCompanyEnum = this.quoteDialerDTOHelper.convertCompanyToSourceUnderwritingCompanyEnum(
				entryCompany);

		assertNull(
				resultCompanyEnum,
				"Resulting SourceUnderwritingCompanyEnum should've been null - Invalid entry company"
		);
	}


	@Test
	void convertApplicationModeToEnum_WithExistingApplicationMode_shouldReturnCorrespondingEnumValue() {
		String autoQQAppMode = "QF";
		String autoFQAppMode = "QA";
		String homeQQAppMode = "QH";
		String condoQQAppMode = "QC";
		String tenantQQAppMode = "QT";
		String ircaQQAppMode = "IR";
		String CCQQAppMode = "PC";

		QuoteAppEnumEnum resultAutoQQ = this.quoteDialerDTOHelper.convertApplicationModeToQuoteAppEnum(autoQQAppMode);
		QuoteAppEnumEnum resultAutoFQ = this.quoteDialerDTOHelper.convertApplicationModeToQuoteAppEnum(autoFQAppMode);
		QuoteAppEnumEnum resultHomeQQ = this.quoteDialerDTOHelper.convertApplicationModeToQuoteAppEnum(homeQQAppMode);
		QuoteAppEnumEnum resultCondoQQ = this.quoteDialerDTOHelper.convertApplicationModeToQuoteAppEnum(condoQQAppMode);
		QuoteAppEnumEnum resultTenantQQ = this.quoteDialerDTOHelper.convertApplicationModeToQuoteAppEnum(tenantQQAppMode);
		QuoteAppEnumEnum resultIrcaQQ = this.quoteDialerDTOHelper.convertApplicationModeToQuoteAppEnum(ircaQQAppMode);
		QuoteAppEnumEnum resultCCQQ = this.quoteDialerDTOHelper.convertApplicationModeToQuoteAppEnum(CCQQAppMode);

		assertEquals(QuoteAppEnumEnum.AUTO_QUICKQUOTE, resultAutoQQ);
		assertEquals(QuoteAppEnumEnum.AUTOQUOTE, resultAutoFQ);
		assertEquals(QuoteAppEnumEnum.HOME_QUICKQUOTE, resultHomeQQ);
		assertEquals(QuoteAppEnumEnum.HOME_QUICKQUOTE, resultCondoQQ);
		assertEquals(QuoteAppEnumEnum.HOME_QUICKQUOTE, resultTenantQQ);
		assertEquals(QuoteAppEnumEnum.IRCA_QUICKQUOTE, resultIrcaQQ);
		assertEquals(QuoteAppEnumEnum.COMMERCIAL_QUICKQUOTE, resultCCQQ);
	}

	@Test
	void convertApplicationModeToEnum_WithNullApplicationMode_shouldReturnNull() {
		QuoteAppEnumEnum resultQuoteApp = this.quoteDialerDTOHelper.convertApplicationModeToQuoteAppEnum(null);

		assertNull(resultQuoteApp, "Resulting QuoteAppEnumEnum should've been null - Null entry application mode");
	}

	@Test
	void convertApplicationModeToEnum_WithInvalidApplicationMode_shouldReturnNull() {
		String entryAppMode = "This doesn't exist";

		QuoteAppEnumEnum resultQuoteApp = this.quoteDialerDTOHelper.convertApplicationModeToQuoteAppEnum(entryAppMode);

		assertNull(resultQuoteApp, "Resulting QuoteAppEnumEnum should've been null - Invalid entry application mode");
	}


	/*************************************************
	 *************************************************
	 *
	 * Private functions
	 *
	 *************************************************
	 *************************************************
	 */

	/**
	 * Function to create a basic valid InformationPieceTO containing a child with received name which has a value piece set to the value received.
	 *
	 * @param childName  Name of the child InformationPieceTO to set in the structure
	 * @param pieceValue The value to set in the value piece of the child InformationPieceTO
	 * @return Basic test InformationPieceTO structure
	 */
	private InformationPieceTO setupBasicValidTestInformationPiece(String childName, String pieceValue) {
		InformationPieceTO parentInfoPiece = mock(InformationPieceTO.class);
		InformationPieceTO childInfoPiece = mock(InformationPieceTO.class);
		when(parentInfoPiece.getChildren(childName)).thenReturn(childInfoPiece);

		InformationPieceTO testKey = new InformationPieceTO();
		InformationPieceTO testValuePiece = new InformationPieceTO();
		testValuePiece.setValue(pieceValue);

		List<InformationPieceTO> testPieces = new ArrayList<InformationPieceTO>();
		testPieces.add(testKey);
		testPieces.add(testValuePiece);
		when(childInfoPiece.getPieces()).thenReturn(testPieces);

		return parentInfoPiece;
	}

	/**
	 * Private function used to setup a basic InformationPieceTO for a given array of vehicles
	 *
	 * @param vehicles Array of vehicle Strings to add to the information piece
	 * @return InformationPieceTO containing the different vehicle infos
	 */
	private InformationPieceTO setupBasicVehicleInformationPiece(String[] vehicles) {
		InformationPieceTO vehicleInfoPiece = mock(InformationPieceTO.class);
		List<InformationPieceTO> vehicleInfoChildren = new ArrayList<InformationPieceTO>();

		for (int i = 0; i < vehicles.length; i++) {
			InformationPieceTO currentVehiclePiece = mock(InformationPieceTO.class);
			vehicleInfoChildren.add(currentVehiclePiece);
			when(vehicleInfoPiece.getChildrens()).thenReturn(vehicleInfoChildren);

			InformationPieceTO currentVehicleInfo = new InformationPieceTO();
			currentVehicleInfo.setValue(vehicles[i]);
			List<InformationPieceTO> firstVehiclePieces = new ArrayList<InformationPieceTO>();
			firstVehiclePieces.add(currentVehicleInfo);
			when(currentVehiclePiece.getPieces()).thenReturn(firstVehiclePieces);
		}

		return vehicleInfoPiece;
	}

	/**
	 * Private function that creates the basic InformationPieceTO structure containing the driver first and last names and return it.
	 *
	 * @param firstName First name String to set in the info piece
	 * @param lastName  Last name String to set in the info piece
	 */
	private InformationPieceTO setupBasicDriverInformationPiece(String firstName, String lastName) {
		InformationPieceTO parentInfoPiece = mock(InformationPieceTO.class);
		InformationPieceTO firstDriverInfoPiece = mock(InformationPieceTO.class);
		when(parentInfoPiece.getChildren("firstDriver")).thenReturn(firstDriverInfoPiece);

		// Driver first name info pieces
		InformationPieceTO driverFirstNameInfoPiece = mock(InformationPieceTO.class);
		when(firstDriverInfoPiece.getChildren("firstDriver_firstName")).thenReturn(driverFirstNameInfoPiece);

		InformationPieceTO driverFirstNameKey = new InformationPieceTO();
		InformationPieceTO driverFirstNamePiece = new InformationPieceTO();
		driverFirstNamePiece.setValue(firstName);

		List<InformationPieceTO> driverFirstNamePieces = new ArrayList<InformationPieceTO>();
		driverFirstNamePieces.add(driverFirstNameKey);
		driverFirstNamePieces.add(driverFirstNamePiece);
		when(driverFirstNameInfoPiece.getPieces()).thenReturn(driverFirstNamePieces);

		// Driver last name info pieces
		InformationPieceTO driverLastNameInfoPiece = mock(InformationPieceTO.class);
		when(firstDriverInfoPiece.getChildren("firstDriver_lastName")).thenReturn(driverLastNameInfoPiece);

		InformationPieceTO driverLastNameKey = new InformationPieceTO();
		InformationPieceTO driverLastNamePiece = new InformationPieceTO();
		driverLastNamePiece.setValue(lastName);

		List<InformationPieceTO> driverLastNamePieces = new ArrayList<InformationPieceTO>();
		driverLastNamePieces.add(driverLastNameKey);
		driverLastNamePieces.add(driverLastNamePiece);
		when(driverLastNameInfoPiece.getPieces()).thenReturn(driverLastNamePieces);

		return parentInfoPiece;
	}

	/**
	 * Private function that creates the basic InformationPieceTO structure containing the  and return it.
	 *
	 * @param testAddress Address to set in the info piece
	 */
	private InformationPieceTO setupBasicPrincipalOwnerNameInformationPiece(String testPrincipalOwnerName) {
		InformationPieceTO parentInfoPiece = mock(InformationPieceTO.class);
		InformationPieceTO generalInfoPiece = mock(InformationPieceTO.class);
		when(parentInfoPiece.getChildren("general")).thenReturn(generalInfoPiece);

		InformationPieceTO principalOwnerNameInfoPiece = mock(InformationPieceTO.class);
		when(generalInfoPiece.getChildren("general_principalOwnerName")).thenReturn(principalOwnerNameInfoPiece);

		InformationPieceTO principalOwnerNameKey = new InformationPieceTO();
		InformationPieceTO principalOwnerNamePiece = new InformationPieceTO();
		principalOwnerNamePiece.setValue(testPrincipalOwnerName);

		List<InformationPieceTO> principalOwnerNamePieces = new ArrayList<InformationPieceTO>();
		principalOwnerNamePieces.add(principalOwnerNameKey);
		principalOwnerNamePieces.add(principalOwnerNamePiece);
		when(principalOwnerNameInfoPiece.getPieces()).thenReturn(principalOwnerNamePieces);

		return parentInfoPiece;
	}

	/**
	 * Private function that creates the basic InformationPieceTO structure containing the address insured and return it.
	 *
	 * @param testAddress Address to set in the info piece
	 */
	private InformationPieceTO setupBasicAddressInformationPiece(String testAddress) {
		InformationPieceTO parentInfoPiece = mock(InformationPieceTO.class);
		InformationPieceTO generalInfoPiece = mock(InformationPieceTO.class);
		when(parentInfoPiece.getChildren("general")).thenReturn(generalInfoPiece);

		InformationPieceTO addressInfoPiece = mock(InformationPieceTO.class);
		when(generalInfoPiece.getChildren("address_insured")).thenReturn(addressInfoPiece);

		InformationPieceTO addressKey = new InformationPieceTO();
		InformationPieceTO addressPiece = new InformationPieceTO();
		addressPiece.setValue(testAddress);

		List<InformationPieceTO> addressPieces = new ArrayList<InformationPieceTO>();
		addressPieces.add(addressKey);
		addressPieces.add(addressPiece);
		when(addressInfoPiece.getPieces()).thenReturn(addressPieces);

		return parentInfoPiece;
	}

	/**
	 * Private function that creates the basic InformationPieceTO structure containing either the home or cell phone and return it.
	 *
	 * @param testFullPhone Phone number to set in the info piece
	 * @param phoneType     Either general_homePhone or general_cellPhone to insert in the info piece
	 */
	private InformationPieceTO setupBasicPhoneInformationPiece(String testFullPhone, String phoneType) {
		InformationPieceTO parentInfoPiece = mock(InformationPieceTO.class);
		InformationPieceTO generalInfoPiece = mock(InformationPieceTO.class);
		when(parentInfoPiece.getChildren("general")).thenReturn(generalInfoPiece);

		InformationPieceTO phoneInfoPiece = mock(InformationPieceTO.class);
		when(generalInfoPiece.getChildren(phoneType)).thenReturn(phoneInfoPiece);

		InformationPieceTO phoneKey = new InformationPieceTO();
		InformationPieceTO phonePiece = new InformationPieceTO();
		phonePiece.setValue(testFullPhone);

		List<InformationPieceTO> phonePieces = new ArrayList<InformationPieceTO>();
		phonePieces.add(phoneKey);
		phonePieces.add(phonePiece);
		when(phoneInfoPiece.getPieces()).thenReturn(phonePieces);

		return parentInfoPiece;
	}
}
