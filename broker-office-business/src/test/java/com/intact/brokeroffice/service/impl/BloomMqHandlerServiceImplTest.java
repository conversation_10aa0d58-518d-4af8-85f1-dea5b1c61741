package com.intact.brokeroffice.service.impl;

import com.intact.brokeroffice.service.bloommqhandler.BloomMqHandlerServiceImpl;
import com.intact.brokeroffice.service.bloommqhandler.IBloomMqHandlerService;
import com.intact.tools.logging.exception.LoggingServiceException;
import com.intact.tools.logging.service.LoggingApplicationService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BloomMqHandlerServiceImplTest {

    @InjectMocks
    private BloomMqHandlerServiceImpl bloomMqHandlerService;

    @Mock
    private LoggingApplicationService mockLogService;

    @Mock
    private RestTemplate mockRestTemplate;

    private String bloomMqServiceUrl = "http://localhost:8080";

	@BeforeEach
	void setUp() {

        ReflectionTestUtils.setField(bloomMqHandlerService, "bloomMqServiceUrl", bloomMqServiceUrl);

    }

	@Test
	void send_message_with_null_parameter_should_do_nothing() throws LoggingServiceException {

        bloomMqHandlerService.sendMessage(null, null);

        verify(this.mockLogService, times(0)).logError("error", "An error occured while sending quote 'QF00002123458' to the dialer.");
    }

	@Test
	void send_message_with_invalid_data_should_throw_http_status_code_exception() throws LoggingServiceException {

        HttpStatusCodeException httpStatusCodeException = getHttpStatusCodeException();

        when(this.mockRestTemplate.postForEntity(anyString(), any(HttpEntity.class), eq(String.class)))
                .thenThrow(httpStatusCodeException);

        bloomMqHandlerService.sendMessage("Q_BLOOM_AUTO", "123456");

        verify(this.mockLogService, times(1)).logError("error", "An Exception occured in bloom mq handler service with statusCode = 417 and response = {\"errors\" :  [ {\"code\" :\"417\",\"message\" : An error occured.}]}");
    }

    private HttpStatusCodeException getHttpStatusCodeException() {

        return new HttpClientErrorException(HttpStatus.EXPECTATION_FAILED, "417",
                getHttpStatusCodeExceptionBodyAsString().getBytes(), null );
    }

    private String getHttpStatusCodeExceptionBodyAsString() {
        return "{"	+ "\"errors\" : " + " [ {"
                + "\"code\" :" + "\"417\"" + ","
                + "\"message\" :" + " An error occured." + ""
                + "}]"
                + "}";
    }
}
