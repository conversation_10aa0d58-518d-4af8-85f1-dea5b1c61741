package com.intact.brokeroffice.service.impl;


import static org.easymock.EasyMock.anyObject;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import com.intact.brokeroffice.business.domain.ResponseDTO;
import com.intact.brokeroffice.mock.MockResponseDTO;
import com.intact.brokeroffice.service.IQuoteService;
import com.intact.tools.logging.service.LoggingApplicationService;

@ExtendWith(MockitoExtension.class)
class QuoteServiceTest {

	@InjectMocks
	private QuoteService quoteService;


	private String homeUploadRestURL = "https://quoters-intg-webquote-transfer-api.ocp-np-a.iad.ca.inet/transferToPendingQuote";

	private String homeUploadRestAPIKey = "1234556";

	@Mock
	private LoggingApplicationService logService;

	@Mock
	private RestTemplate template;


	private ResponseEntity<ResponseDTO> responseEntity;

	private ResponseDTO responseDTO;


	@BeforeEach
	void setUp() {

		responseDTO = MockResponseDTO.createResponseDTO();
		responseEntity = new ResponseEntity<ResponseDTO>(responseDTO, HttpStatus.OK);

		ReflectionTestUtils.setField(quoteService, "homeUploadRestURL", homeUploadRestURL);
		ReflectionTestUtils.setField(quoteService, "homeUploadRestAPIKey", homeUploadRestAPIKey);

	}

	@Test
	void upload_to_contact_success() throws Exception {

		when(template.postForEntity(anyString(), any(), eq(ResponseDTO.class))).thenReturn(responseEntity);

		String referenceNumer = quoteService.uploadToContact("4e9c2da5-3b96-35a1-ac53-2ee9e8f2fc02", "WZ_ADMN");

		assertEquals("123456", referenceNumer);

	}

	@Test
	void upload_to_contact_throw_http_status_code_exception() throws Exception {
		HttpStatusCodeException httpStatusCodeException = getHttpStatusCodeException();

		when(template.postForEntity(anyString(), any(), eq(ResponseDTO.class))).thenThrow(httpStatusCodeException);

		try {
			quoteService.uploadToContact("4e9c2da5-3b96-35a1-ac53-2ee9e8f2fc02", "WZ_ADMN");
			fail("Should throw exception");

		}catch (RuntimeException rex) {
			verify(logService, times(2)).logError(anyString(), anyString());
		}

	}

	private HttpStatusCodeException getHttpStatusCodeException() {

		return new HttpClientErrorException(HttpStatus.EXPECTATION_FAILED, null,
				getHttpStatusCodeExceptionBodyAsString().getBytes(), null );

	}

	private String getHttpStatusCodeExceptionBodyAsString() {
		return "{"	+ "\"errors\" : " + " [ {"
						+ "\"code\" :" + "\"417\"" + ","
						+ "\"message\" :" + "\"error on call retrieveProfileByUserId with userId = wz_admn.\"" + ","
						+ "\"stackTrace\" :" + "\"com.intact.api.transfer.webquote.exception.ProfileNotFoundException\""
					+ "}]"
				+ "}";

	}
}
