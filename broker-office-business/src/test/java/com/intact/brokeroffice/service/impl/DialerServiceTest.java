package com.intact.brokeroffice.service.impl;


import com.intact.brokeroffice.business.domain.dialer.QuoteDialerDTO;
import com.intact.brokeroffice.business.exception.DialerServiceException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.springframework.web.client.HttpClientErrorException;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;

import static org.easymock.EasyMock.eq;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class DialerServiceTest {
	
	@InjectMocks
	private DialerService dialerService;
	
	private String dialerRestURL = "http://swarm-int-s1-app1.dc2.intactlab.ca:3115/v1/dialer/send";
	
	private String dialerRestAPIKey = "1234556";
	
	@Mock
	private RestTemplate mockTemplate;


	@BeforeEach
	void setUp() {
		ReflectionTestUtils.setField(dialerService, "dialerRestURL", dialerRestURL);
		ReflectionTestUtils.setField(dialerService, "dialerRestAPIKey", dialerRestAPIKey);
	}


	@Test
	void sentToDialer_WithValidData_ShouldReturnConfirmationNumber() throws Exception {
		QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
		String testQuoteNumber = "QF00002123458";
		testQuoteDialerDTO.setQuoteNumber(testQuoteNumber);
		String expectedConfirmationNumber = "1234567890123456";

		when(this.mockTemplate.postForEntity(anyString(), any(HttpEntity.class), any(Class.class)))
				.thenReturn(new ResponseEntity<>(expectedConfirmationNumber, HttpStatus.OK));

		String resultConfirmationNumber = this.dialerService.sendToDialer(testQuoteDialerDTO);

		assertEquals(expectedConfirmationNumber, resultConfirmationNumber);
	}

	@Test
	void sentToDialer_WithNullQuoteDialerDTO_ShouldThrowException() {
		assertThrows(RuntimeException.class, () -> {
			this.dialerService.sendToDialer(null);
		});
	}

	@Test
	void sentToDialer_WithDialerCallThrowingHttpStatusCodeException_ShouldThrowDialerServiceException() {
		String exceptionAsString = "[{"
					+ "\"code\" :" + "\"broker\"" + ","
					+ "\"description\" : \"Test error\""
					+ "}]";
		HttpStatusCodeException httpStatusCodeException = new HttpClientErrorException(HttpStatus.EXPECTATION_FAILED, null, exceptionAsString.getBytes(), null);
		QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
		String testQuoteNumber = "QF00002123458";
		testQuoteDialerDTO.setQuoteNumber(testQuoteNumber);
		when(this.mockTemplate.postForEntity(anyString(), any(HttpEntity.class), any(Class.class)))
					.thenThrow(httpStatusCodeException);
		assertThrows(DialerServiceException.class, () ->

			this.dialerService.sendToDialer(testQuoteDialerDTO));
	}

	@Test
	void sentToDialer_WithDialerCallThrowingHttpStatusCodeExceptionWithErrorCreatingCustomException_ShouldThrowRuntimeException() {
		String exceptionAsString = "Invalid";
		HttpStatusCodeException httpStatusCodeException = new HttpClientErrorException(HttpStatus.EXPECTATION_FAILED, null, exceptionAsString.getBytes(), null);
		QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
		String testQuoteNumber = "QF00002123458";
		testQuoteDialerDTO.setQuoteNumber(testQuoteNumber);
		when(this.mockTemplate.postForEntity(anyString(), any(HttpEntity.class), any(Class.class)))
				.thenThrow(httpStatusCodeException);

		assertThrows(RuntimeException.class, () -> {
			this.dialerService.sendToDialer(testQuoteDialerDTO);
		});
	}

	@Test
	void sentToDialer_WithDialerCallThrowingException_ShouldThrowRuntimeException() {
		QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
		String testQuoteNumber = "QF00002123458";
		testQuoteDialerDTO.setQuoteNumber(testQuoteNumber);
		when(this.mockTemplate.postForEntity(anyString(), any(HttpEntity.class), any(Class.class)))
					.thenThrow(new NumberFormatException());
		assertThrows(RuntimeException.class, () ->

			this.dialerService.sendToDialer(testQuoteDialerDTO));
	}


	@Test
	void logAndThrowCustomError_WithValidExceptionFormat_ShouldThrowDialerServiceException() {
		String exceptionAsString = "[{"
				+ "\"code\" :" + "\"broker\"" + ","
				+ "\"description\" : \"Test error\""
				+ "}]";
		HttpStatusCodeException httpStatusCodeException = new HttpClientErrorException(HttpStatus.EXPECTATION_FAILED, null, exceptionAsString.getBytes(), null);

		assertThrows(DialerServiceException.class, () ->
				this.dialerService.logAndThrowCustomError(httpStatusCodeException, "QF00002123458"));
	}

	@Test
	void logAndThrowCustomError_WithJSONMappingExceptionThrown_ShouldOnlyLogErrors() throws Exception {
		String exceptionAsString = "{}";
		HttpStatusCodeException httpStatusCodeException = new HttpClientErrorException(HttpStatus.EXPECTATION_FAILED, null, exceptionAsString.getBytes(), null);

		this.dialerService.logAndThrowCustomError(httpStatusCodeException, "QF00002123458");
	}

	@Test
	void logAndThrowCustomError_WithJSONParseExceptionThrown_ShouldOnlyLogErrors() throws Exception {
		String exceptionAsString = "Invalid";
		HttpStatusCodeException httpStatusCodeException = new HttpClientErrorException(HttpStatus.EXPECTATION_FAILED, null, exceptionAsString.getBytes(), null);

		this.dialerService.logAndThrowCustomError(httpStatusCodeException, "QF00002123458");	}
}
