package com.intact.brokeroffice.mock;

import java.util.HashSet;

import com.intact.brokeroffice.business.domain.ErrorDTO;
import com.intact.brokeroffice.business.domain.ResponseDTO;
import com.intact.brokeroffice.business.domain.TransferToPendingQuoteResponseDTO;

public class MockResponseDTO {

	
	public static ResponseDTO createResponseDTOWithError() {
		ResponseDTO responseDTO = new ResponseDTO();
		responseDTO.setErrors(new HashSet<ErrorDTO>());
		
		return responseDTO;
		
	}
	
	public static ResponseDTO createResponseDTO() {
		ResponseDTO responseDTO = new ResponseDTO();
		responseDTO.setData(createTransferToPendingQuoteResponseDTO());
		
		return responseDTO;
		
	}
	
	public static ErrorDTO setErrorDTO() {
		
		ErrorDTO errorDTO = new ErrorDTO();
		errorDTO.setCode("417");
		errorDTO.setMessage("profile not found");
		errorDTO.setStackTrace("error when calling upload service");
		
		return errorDTO;
	}
	
	public static TransferToPendingQuoteResponseDTO createTransferToPendingQuoteResponseDTO() {
		
		TransferToPendingQuoteResponseDTO transferToPendingQuoteResponseDTO = new TransferToPendingQuoteResponseDTO();
		transferToPendingQuoteResponseDTO.setContractUUID("");
		transferToPendingQuoteResponseDTO.setContractReferenceNumber("123456");
		
		return transferToPendingQuoteResponseDTO;
	}
	
}
