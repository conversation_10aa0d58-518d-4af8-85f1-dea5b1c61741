package com.intact.brokeroffice.service.bloommqhandler;

import com.intact.brokeroffice.business.domain.Quote;
import com.intact.brokeroffice.business.quote.QuotesBean;

public interface IBloomMqHandlerService {
    /*
        Send MqMessage object to bloom MQ handler service
     */
    void sendMessage(String queueName, String quoteId);
    
    void sendQuoteToBloom(Quote quote);
    
    void sendQuoteToBloom(QuotesBean quoteBean);
}
