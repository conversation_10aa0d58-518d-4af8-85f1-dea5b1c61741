package com.intact.brokeroffice.service.impl;

import com.fasterxml.jackson.core.JsonParseException;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.intact.brokeroffice.business.domain.DialerErrorDTO;
import com.intact.brokeroffice.business.domain.dialer.ListDialerErrorDTO;
import com.intact.brokeroffice.business.domain.dialer.QuoteDialerDTO;
import com.intact.brokeroffice.business.exception.DialerServiceException;
import com.intact.brokeroffice.service.IDialerService;
import com.intact.tools.logging.exception.LoggingServiceException;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

@Named
public class DialerService implements IDialerService {

	private static final Log log = LogFactory.getLog(DialerService.class);

	@Inject
	@Named("dialerRestURL")
	private String dialerRestURL;

	@Inject
	@Named("dialerRestAPIKey")
	private String dialerRestAPIKey;

	@Autowired
	private RestTemplate template;

	@Override
	public String sendToDialer(QuoteDialerDTO quoteDialerInfo) throws Exception {

		if (quoteDialerInfo == null) {
			log.error("Could not send quote to broker dialer service - Null QuoteDialerDTO object.");
			throw new RuntimeException("Could not send quote to broker dialer service - Null QuoteDialerDTO object.");
		}

		ResponseEntity<String> response;
		UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(dialerRestURL);

		HttpHeaders headers = new HttpHeaders();
		headers.setContentType(MediaType.APPLICATION_JSON);
		headers.add("dialerApiKey", this.dialerRestAPIKey);
		HttpEntity<QuoteDialerDTO> request = new HttpEntity<>(quoteDialerInfo, headers);

		try {
			response = template.postForEntity(builder.toUriString(),request,String.class);

			log.info("Succesfully sent quote '%s' to the dialer.".formatted(quoteDialerInfo.getQuoteNumber()));

		} catch (HttpStatusCodeException ex) {
			logAndThrowCustomError(ex, quoteDialerInfo.getQuoteNumber());
			throw new RuntimeException(ex);

		} catch (Exception e) {

			log.error("An error occured while sending quote '%s' to the dialer : %s.".formatted(
					quoteDialerInfo.getQuoteNumber(), e.getMessage()), e);
			throw new RuntimeException(e.getMessage());
		}

		return response.getBody();
	}

	/**
	 * Function used to log HttpStatusCodeException thrown by the broker dialer service and build a custom
	 * DialerServiceException from the exception body.
	 *
	 * @param ex          HttpStatusCodeException thrown by the broker dialer service
	 * @param quoteNumber The quote's number, for logging purposes
	 * @throws LoggingServiceException, DialerServiceException
	 */
	protected void logAndThrowCustomError(HttpStatusCodeException ex, String quoteNumber) throws LoggingServiceException {
		StringBuffer fullMessage = new StringBuffer();
		List<String> errorCodes = new ArrayList<>();
		List<String> errorMessages = new ArrayList<>();

		try {
			log.error("An error occured while sending quote '%s' to the dialer.".formatted(quoteNumber));

			ObjectMapper objectMapper = new ObjectMapper();
			ListDialerErrorDTO errorList = objectMapper.readValue(ex.getResponseBodyAsString(),
																  ListDialerErrorDTO.class);

			for (DialerErrorDTO errorDto : errorList) {
				log.error(errorDto.toString());
				fullMessage.append(errorDto.getDescription()).append(";");
				errorCodes.add(errorDto.getCode());
				errorMessages.add(errorDto.getDescription());
			}

			throw new DialerServiceException(errorCodes, errorMessages, fullMessage.toString(), ex);
		} catch (JsonMappingException e) {
			log.error("An error occurred while mapping the received HttpStatusCodeException's body to a ListErrorDTO",
					  e);
		} catch (JsonParseException e) {
			log.error(
					"A JSON parsing exception occurred while mapping the received HttpStatusCodeException's body to a ListErrorDTO",
					e);
		} catch (IOException e) {
			log.error(
					"An IO Exception occurred while mapping the received HttpStatusCodeException's body to a ListErrorDTO",
					e);
		}
	}

}
