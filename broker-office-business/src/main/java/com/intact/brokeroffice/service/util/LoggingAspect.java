package com.intact.brokeroffice.service.util;

import java.util.HashMap;
import java.util.Map;

import jakarta.inject.Inject;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;

import com.intact.tools.logging.service.LoggingApplicationService;
import com.intact.tools.template.service.TemplateApplicationService;


public class LoggingAspect {

	@Inject
	private TemplateApplicationService templateService = null;

	@Inject
	private LoggingApplicationService loggingService = null;

	public LoggingAspect() {
	}

	public TemplateApplicationService getTemplateService() {
		return this.templateService;
	}

	public void setTemplateService(TemplateApplicationService templateService) {
		this.templateService = templateService;
	}

	public LoggingApplicationService getLoggingService() {
		return this.loggingService;
	}

	public void setLoggingService(LoggingApplicationService loggingService) {
		this.loggingService = loggingService;
	}

	public void logApplication(JoinPoint joinPoint) {

		String loggerKey = joinPoint.getTarget().getClass().getSimpleName() + "." + joinPoint.getSignature().getName();
		this.info("application", loggerKey, joinPoint.getArgs());
	}

	public Object logError(ProceedingJoinPoint joinPoint) throws Throwable {

		Object returnedValue = null;

		try {
			returnedValue = joinPoint.proceed();
		} catch (Exception ex) {
			this.getLoggingService().logError("error", ex.getMessage(), ex);
			throw ex;
		}

		return returnedValue;
	}

	public Object logTime(ProceedingJoinPoint joinPoint) throws Throwable {
		Object returnedValue = null;

		long beforeCall = System.currentTimeMillis();
		returnedValue = joinPoint.proceed();
		long afterCall = System.currentTimeMillis();

		String loggerKey = joinPoint.getTarget().getClass().getSimpleName() + "." + joinPoint.getSignature().getName();

		this.info("time", "RESPONSE_TIME", new Object[] { loggerKey, afterCall - beforeCall });

		return returnedValue;
	}

	protected void info(String loggerName, String templateKey, Object[] arguments) {

		try {
			this.getLoggingService().logInfo(loggerName,
					this.getTemplateService().format(templateKey, this.buildContext(arguments)));
		} catch (Exception ignored) {
			System.err.println("NO LOGGER FOR " + templateKey + " BUT THIS CLASS IS WRAPPED FOR LOGGING");
		}
	}

	protected Map<String, Object> buildContext(Object[] arguments) throws Exception {

		Map<String, Object> context = new HashMap<String, Object>();

		int i = 1;

		if (arguments != null) {
			for (Object argument : arguments) {
				context.put("param" + i++, argument);
			}
		}

		return context;
	}

	public String toString() {
		return "Logging Aspect";
	}

}
