package com.intact.brokeroffice.service.domain.context;

public enum UserType {

	ADMIN("ADM"), <PERSON><PERSON><PERSON>("BRK"), PROGRAM_ADMIN("PRG");
	
	private String code = null;
	
	private UserType(String code) {
		this.setCode(code);
	}

	public String getCode() {
		return this.code;
	}

	public void setCode(String code) {
		this.code = code;
	}
	
	public String toString() {
		return this.getCode();
	}
}
