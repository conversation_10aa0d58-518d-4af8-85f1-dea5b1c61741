package com.intact.brokeroffice.service.search;

import java.util.Comparator;

import com.intact.brokeroffice.business.quote.QuotesBean;

public class QuoteBeanComparator implements Comparator<QuotesBean> {

	public QuoteBeanComparator() {
	}

	@Override
	public int compare(QuotesBean quote1, QuotesBean quote2) {

		int value = 0;

		if (quote1 == null || quote1.getLastUpdate() == null) {
			value = 1;
		} else if (quote2 == null || quote2.getLastUpdate() == null) {
			return -1;
		} else {
			value = quote2.getLastUpdate().compareTo(quote1.getLastUpdate());
		}

		return value;
	}
}
