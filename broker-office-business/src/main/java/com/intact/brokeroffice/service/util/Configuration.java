package com.intact.brokeroffice.service.util;

import java.util.Map;

public class Configuration<T, U> {

	private Map<T, U> configs = null;

	public Configuration() {
		super();
	}

	public Map<T, U> getConfigs() {
		return this.configs;
	}

	public void setConfigs(Map<T, U> configs) {
		this.configs = configs;
	}

	public U getValue(T key) {
		return this.getConfigs().get(key);
	}
	
	public String toString() {
		return "[configs=" + this.getConfigs() + "]";
	}

}
