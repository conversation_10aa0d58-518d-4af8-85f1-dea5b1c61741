/**
 * 
 */
package com.intact.brokeroffice.service.search.builder;

import java.util.HashSet;
import java.util.Set;

import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.plp.report.insurancePolicy.criteria.QuoteSearchCriteria;
import com.intact.brokeroffice.business.domain.BrokerQuoteSearchCriteria;
import com.intact.business.service.broker.common.domain.ExtendedQuoteSearch;
import com.intact.business.service.broker.common.domain.QuoteSearch;
import com.intact.business.service.broker.domain.IQuoteSearch;
import com.intact.business.service.broker.domain.IUserContext;
import com.intact.business.service.broker.domain.LineOfBusiness;
import com.intact.business.service.broker.domain.LineOfInsurance;
import com.intact.business.service.broker.domain.QuoteFollowUpStatus;
import com.intact.business.service.broker.domain.QuoteOrigin;
import com.intact.business.service.broker.domain.QuoteStatus;
import com.intact.business.service.broker.domain.UserType;

/**
 * Builder class to create the search criteria object to be used for the quote search.
 * 
 * <AUTHOR>
 *
 */
public class QuoteSearchBuilder {

	public static IQuoteSearch build(IUserContext userContext, BrokerQuoteSearchCriteria criteria) {
		  
		QuoteSearch newSearch = UserType.ADMIN.equals(userContext.getType()) ?  new ExtendedQuoteSearch() : new QuoteSearch();		
		
		// Add the common criteria for the search
		newSearch.setOwner(criteria.getSelectedMaster());
		newSearch.setPointOfSale(criteria.getSelectedPointOfSale()); 
		
		newSearch.setLinesOfBusiness(QuoteSearchBuilder.buildLinesOfBusiness(criteria.getSelectedLinesOfBusiness()));
		newSearch.setLinesOfInsurance(QuoteSearchBuilder.buildLinesOfInsurance(criteria.getSelectedLinesOfInsurance()));
		
		newSearch.setQuoteReference(criteria.getAgreementNumber());
		
		String value = criteria.getPhoneAreaCode() + criteria.getPhoneNumberPrefix() + criteria.getPhoneNumberSuffix();
		newSearch.setPhoneNumber(value.length() > 0 ? value : null);
		
		newSearch.setPhoneAreaCode(criteria.getPhoneAreaCode());
		newSearch.setPhoneNumberPrefix(criteria.getPhoneNumberPrefix());
		newSearch.setPhoneNumberSuffix(criteria.getPhoneNumberSuffix());
		
		newSearch.setBusinessName(criteria.getBusinessName());
		newSearch.setFirstName(criteria.getFirstName());
		newSearch.setLastName(criteria.getLastName());
		
		newSearch.setEmailAddress(criteria.getEmail());
		newSearch.setPostalCode(criteria.getPostalCode());
		
		// Add the extended criteria for admins only
		if (UserType.ADMIN.equals(userContext.getType())) {
			((ExtendedQuoteSearch)newSearch).setLastUpdateDateFrom(criteria.getFrom());
			((ExtendedQuoteSearch)newSearch).setLastUpdateDateTo(criteria.getTo());
			((ExtendedQuoteSearch)newSearch).setCreationDateFrom(criteria.getCreationDateFrom());
			((ExtendedQuoteSearch)newSearch).setCreationDateTo(criteria.getCreationDateTo());
			((ExtendedQuoteSearch)newSearch).setOrigin(QuoteOrigin.fromCode(criteria.getSelectedQuoteSource()));
			((ExtendedQuoteSearch)newSearch).setStatus(QuoteStatus.fromCode(criteria.getSelectedQuoteStatus()));
			((ExtendedQuoteSearch)newSearch).setClosedBrokers(criteria.isClosedBrokerQuotes());
			
			if (criteria.getSelectedClientFollowUp() != null  && !QuoteFollowUpStatus.BLANK.getCode().equals(criteria.getSelectedClientFollowUp())) {
				((ExtendedQuoteSearch)newSearch).setFollowUp(QuoteFollowUpStatus.fromValue(criteria.getSelectedClientFollowUp()));
			}
			else {
				((ExtendedQuoteSearch)newSearch).setFollowUp(null);
			}
		}
	
		return newSearch;
	}
	
	
	/**
	 * Convert an array string representation of lines of business codes to the equivalent {@link LineOfBusiness} instance 
	 * @param linesOfBusiness  The array of codes to be converted to enum.
	 * @return Converted set of equivalent {@link LineOfBusiness} based on the provided array string 
	 */
	protected static Set<LineOfBusiness> buildLinesOfBusiness(String[] linesOfBusiness) {
		Set<LineOfBusiness> result = new HashSet<LineOfBusiness>();

		if(linesOfBusiness != null) {

			for(String val : linesOfBusiness) {
				LineOfBusiness value = LineOfBusiness.fromCode(val);
				if(value != null) {
					result.add(value);
				}
			}
		}

		return result;
	}
	
	protected static Set<LineOfInsurance> buildLinesOfInsurance(String[] linesOfInsurance) {
		Set<LineOfInsurance> result = new HashSet<LineOfInsurance>();
		
		if(linesOfInsurance != null) {
				
			for(String val : linesOfInsurance) {
				LineOfInsurance value = LineOfInsurance.fromCode(val);
				if(value != null) {
					result.add(value);
				}
			}
		}
		
		return result;
	}
	
	/**
	 * Convert an array string representation of lines of insurance codes to the equivalent {@link LineOfInsurance} instance 
	 * @param linesOfInsurance  The array of codes to be converted to enum.
	 * @return Converted set of equivalent {@link LineOfInsurance} based on the provided array string 
	 */
	protected static Set<LineOfInsurance> toLinesOfInsurance(String[] linesOfInsurance, QuoteSearchCriteria criteria) {
		Set<LineOfInsurance> result = new HashSet<LineOfInsurance>();

		if(linesOfInsurance != null) {

			result = new HashSet<LineOfInsurance>(linesOfInsurance.length);

			for(String val : linesOfInsurance) {
				LineOfInsurance value = LineOfInsurance.fromCode(val);
				if(value != null && (criteria.getBusinessName() == null || value.equals(LineOfInsurance.AUTOMOBILE))) {
					result.add(value);
				}
			}
		}

		return result;
	}
	
	/**
	 * Convert the {@link LineOfBusinessEnum} to a set of equivalent {@link LineOfInsurance}
	 * @param set Set of {@link LineOfBusinessEnum} to be converted
	 * @return Converted set of equivalent {@link LineOfInsurance} based on the provided set of {@link LineOfBusinessEnum}
	 */
	protected static Set<LineOfInsurance> toLinesOfInsurance(Set<LineOfBusinessEnum> set) {
		Set<LineOfInsurance> result = new HashSet<LineOfInsurance>();

		if(set != null) {
			result = new HashSet<LineOfInsurance>(set.size());

			if(set.contains(LineOfBusinessEnum.PERSONAL_LINE)) {
				result.add(LineOfInsurance.AUTOMOBILE);
				result.add(LineOfInsurance.RESIDENTIAL);
			}

			if(set.contains(LineOfBusinessEnum.COMMERCIAL_LINE)) {
				result.add(LineOfInsurance.AUTOMOBILE);
			}
		}

		return result;
	}
}
