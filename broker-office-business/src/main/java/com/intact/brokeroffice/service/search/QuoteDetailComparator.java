package com.intact.brokeroffice.service.search;

import java.util.Comparator;

import com.intact.business.service.broker.domain.IQuoteDetail;

public class QuoteDetailComparator implements Comparator<IQuoteDetail> {

	public QuoteDetailComparator() {
	}

	@Override
	public int compare(IQuoteDetail quote1, IQuoteDetail quote2) {

		int value = 0;

		if (quote1 == null || quote1.getLastUpdateDate() == null) {
			value = 1;
		} else if (quote2 == null || quote2.getLastUpdateDate() == null) {
			return -1;
		} else {
			value = quote2.getLastUpdateDate().compareTo(quote1.getLastUpdateDate());
		}

		return value;
	}
}
