package com.intact.brokeroffice.service.search;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;

import jakarta.inject.Inject;

import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.intact.business.service.broker.api.IBrokerService;
import com.intact.business.service.broker.common.Configuration;
import com.intact.business.service.broker.common.domain.Quote;
import com.intact.business.service.broker.common.domain.QuoteUpload;
import com.intact.business.service.broker.domain.IExtendedQuoteSearch;
import com.intact.business.service.broker.domain.IPDFDocument;
import com.intact.business.service.broker.domain.IPolicy;
import com.intact.business.service.broker.domain.IQuoteReassignment;
import com.intact.business.service.broker.domain.IQuoteSearch;
import com.intact.business.service.broker.domain.IUserContext;
import com.intact.business.service.broker.domain.LineOfBusiness;
import com.intact.business.service.broker.domain.LineOfInsurance;
import com.intact.business.service.broker.plt.PLTBrokerService;
import com.intact.business.service.exception.BrokerServiceException;
import com.intact.tools.logging.service.LoggingApplicationService;

public class DefaultBrokerService {

  @Inject
  private LoggingApplicationService logService = null;

  private Configuration<String, Map<String, IBrokerService>> configurations = null;

  private Configuration<String, IBrokerService> services = null;

  public DefaultBrokerService() {
    super();
    this.setConfigurations(new Configuration<String, Map<String, IBrokerService>>());
    this.getConfigurations()
        .setConfigs(new ConcurrentHashMap<String, Map<String, IBrokerService>>());
  }

  public Configuration<String, IBrokerService> getServices() {
    return this.services;
  }

  public void setServices(Configuration<String, IBrokerService> services) {
    this.services = services;
  }

  public Configuration<String, Map<String, IBrokerService>> getConfigurations() {
    return this.configurations;
  }

  public void setConfigurations(Configuration<String, Map<String, IBrokerService>> configurations) {
    this.configurations = configurations;
  }

  public LoggingApplicationService getLogService() {
    return logService;
  }

  public void setLogService(LoggingApplicationService logService) {
    this.logService = logService;
  }

  protected void validate(String context) throws BrokerServiceException {

    if (context == null) {
      throw new BrokerServiceException(BrokerServiceException.PARAM_CONTEXT_NULL);
    }

    if (this.getServices() == null) {
      throw new BrokerServiceException(BrokerServiceException.CONFIG_SERVICES_NULL, this);
    }
  }

  protected Map<String, IBrokerService> getServices(IQuoteSearch search, IUserContext user) {

    Map<String, IBrokerService> services = null;
    IBrokerService service = null;
    String newKey = null;
    String key =
        search.getLinesOfBusiness() + "-" + search.getLinesOfInsurance() + "-" + user.getCompany();

    services = this.getConfigurations().getValue(key);

    if (services == null || services.isEmpty()) {

      // hack for multithread call
      PLTBrokerService.initEnpoint();

      services = new HashMap<String, IBrokerService>();
      this.getConfigurations().getConfigs().put(key, services);

      for (LineOfBusiness lob : search.getLinesOfBusiness()) {

        for (LineOfInsurance loi : search.getLinesOfInsurance()) {
          newKey = "webzone." + lob.getCode() + "." + loi.getCode() + "." + user.getCompany();
          service = this.getServices().getValue(newKey);

          if (service != null) {
            services.put(newKey, service);
          }

          // check if legacy call is needed
          newKey = "webzone." + lob.getCode() + "." + loi.getCode() + "." + user.getCompany()
              + ".LEGACY";
          service = this.getServices().getValue(newKey);

          if (service != null) {
            services.put(newKey, service);
          }

        }
      }
    }
    // Cleanup auto services (for when both PL and CL are present)
    this.cleanupAutoServices(services, ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION.getCode());
    this.cleanupAutoServices(services,
        ManufacturerCompanyCodeEnum.ING_CENTRAL_ATLANTIC_REGION.getCode());
    this.cleanupAutoServices(services, ManufacturerCompanyCodeEnum.ING_WESTERN_REGION.getCode());
    return this.getConfigurations().getValue(key);
  }

  protected Map<String, IBrokerService> getServices(IUserContext user) {

    Map<String, IBrokerService> services = null;
    IBrokerService service = null;
    String newKey = null;
    String key = user.getLinesOfBusiness() + "-null-" + user.getCompany();

    services = this.getConfigurations().getValue(key);

    if (services == null || services.isEmpty()) {

      // hack for multithread call
      PLTBrokerService.initEnpoint();

      services = new HashMap<String, IBrokerService>();
      this.getConfigurations().getConfigs().put(key, services);

      for (LineOfBusiness lob : user.getLinesOfBusiness()) {

        for (LineOfInsurance loi : LineOfInsurance.values()) {
          newKey = "webzone." + lob.getCode() + "." + loi.getCode() + "." + user.getCompany();
          service = this.getServices().getValue(newKey);

          if (service != null) {
            services.put(newKey, service);
          }

          // check if legacy call is needed
          newKey = "webzone." + lob.getCode() + "." + loi.getCode() + "." + user.getCompany()
              + ".LEGACY";
          service = this.getServices().getValue(newKey);

          if (service != null) {
            services.put(newKey, service);
          }
        }
      }

      // Cleanup auto services (for when both PL and CL are present)
      this.cleanupAutoServices(services,
          ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION.getCode());
      this.cleanupAutoServices(services,
          ManufacturerCompanyCodeEnum.ING_CENTRAL_ATLANTIC_REGION.getCode());
      this.cleanupAutoServices(services, ManufacturerCompanyCodeEnum.ING_WESTERN_REGION.getCode());
    }

    return this.getConfigurations().getValue(key);
  }

  /**
   * Function to remove one of the Auto services for a given company if both PL and CL are present.
   *
   * @param services : the map of services
   * @param company  : the company for which to clean the services
   */
  protected void cleanupAutoServices(Map<String, IBrokerService> services, String company) {
    // Stored proc manage all types of quotes (CL and PL) so remove one from the services to use.
    IBrokerService service1 = services.get("webzone.C.AU." + company);
    IBrokerService service2 = services.get("webzone.P.AU." + company);

    if (service1 != null && service2 != null) {
      services.remove("webzone.C.AU." + company);
    }
  }

  public QuoteData searchExtendedQuotes(IUserContext userContext, IExtendedQuoteSearch search)
      throws Exception {

    QuoteData data = new QuoteData();

    Map<String, IBrokerService> services = this.getServices(search, userContext);

    for (Entry<String, IBrokerService> entry : services.entrySet()) {
      try {
        data.getQuotes()
            .addAll(entry.getValue().searchExtendedQuotes(entry.getKey(), userContext, search));
      } catch (Exception ex) {
        this.getLogService().logError("error", ex.getMessage(), ex);
      }
    }

    return data;
  }

  public QuoteData listQuotes(IUserContext userContext) throws Exception {

    QuoteData data = new QuoteData();

    Map<String, IBrokerService> services = this.getServices(userContext);

    for (Entry<String, IBrokerService> entry : services.entrySet()) {
      try {
        data.getQuotes().addAll(entry.getValue().listQuotes(entry.getKey(), userContext));
      } catch (Exception ex) {
        this.getLogService().logError("error", ex.getMessage(), ex);
        data.getExceptions().add(entry.getKey());
      }
    }

    return data;
  }

  public QuoteData searchQuotes(IUserContext userContext, IQuoteSearch search) throws Exception {
    QuoteData data = new QuoteData();

    Map<String, IBrokerService> services = this.getServices(search, userContext);

    for (Entry<String, IBrokerService> entry : services.entrySet()) {
      try {
        data.getQuotes().addAll(entry.getValue().searchQuotes(entry.getKey(), userContext, search));

      } catch (Exception ex) {
        this.getLogService().logError("error", ex.getMessage(), ex);
        data.getExceptions().add(entry.getKey());
      }
    }

    return data;
  }

  public void updateQuote(String context, Quote quote, String applicationMode)
      throws BrokerServiceException {
    //this.getServices().getConfigs().get(context).updateQuote(context, quote);
    this.getServices().getValue("webzone." + applicationMode).updateQuote(context, quote);
  }

  public void reassignQuotes(String context, IQuoteReassignment reassignment, String applicationMode)
      throws BrokerServiceException {
    //this.getServices().getConfigs().get(context).reassignQuotes(context, reassignment);
    this.getServices().getValue("webzone." + applicationMode).reassignQuotes(context, reassignment);
  }

  public IPolicy uploadQuote(String context, QuoteUpload uploadInfo) throws BrokerServiceException {
    return this.getServices().getConfigs().get(context).uploadQuote(context, uploadInfo);
  }

  public IPDFDocument downloadDocument(String context, Quote quote, String applicationMode) throws BrokerServiceException {
    //return this.getServices().getConfigs().get(context).downloadDocument(context, quote);
    return this.getServices().getValue("webzone." + applicationMode).downloadDocument(context, quote);
  }

  public String toString() {
    return "Web Zone Broker Service";
  }
}
