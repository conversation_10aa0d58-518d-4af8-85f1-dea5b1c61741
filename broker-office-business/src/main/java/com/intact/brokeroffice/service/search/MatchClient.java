package com.intact.brokeroffice.service.search;

import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.intact.brokeroffice.business.quote.QuotesBean;
import com.intact.business.service.broker.domain.LineOfBusiness;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang.StringUtils;

public class MatchClient {

  /**
   * Matches quotes if one or many of them have the same common denominator
   *
   * @param quotesList The list for which to find matching quotes
   * @return A list of {@link QuotesBean} including the parent/child hierarchy based on the common
   * denominator among them. The returned list should be used by the caller instead of the original
   * one. Original list remains unchanged.
   */
  public static List<QuotesBean> match(List<QuotesBean> quotes) {
    List<QuotesBean> result = new ArrayList<QuotesBean>();

    if (quotes != null) {

      Map<String, QuotesBean> quotesMap = new HashMap<String, QuotesBean>();

      for (QuotesBean quote : quotes) {

        String matchKey = null;

        if (quote != null) {
          if (LineOfBusiness.PERSONAL_LINES.getCode().equals(quote.getLineOfBusiness().getCode())) {
            matchKey = createPersonalLineKey(quote);
          } else if (LineOfBusiness.COMMERCIAL_LINES.getCode()
              .equals(quote.getLineOfBusiness().getCode())) {
            matchKey = createCommercialLineKey(quote);
          }

          QuotesBean parentQuote = quotesMap.get(matchKey);

          if (parentQuote == null) {
            quotesMap.put(matchKey, quote);
            result.add(quote);
          } else {
            // Add the quote as a child of his parent
            // If not the same id ... (duplicates)
            if (!StringUtils.equals(parentQuote.getId(), quote.getId())) {
              parentQuote.addChild(quote);
            }
          }
        }
      }

      // End of the match method
      quotesMap = null;
    }

    return result;
  }

  protected static String createCommercialLineKey(QuotesBean quote) {
    return "%s-%s-%s".formatted(LineOfBusinessCodeEnum.COMMERCIAL_LINES,
				quote.getUnstructuredName().toUpperCase().trim(), quote.getPostalCode());
  }

  protected static String createPersonalLineKey(QuotesBean quote) {
    return "%s-%s-%s-%s-%s".formatted(LineOfBusinessCodeEnum.PERSONAL_LINES,
				quote.getFirstName().toUpperCase().trim(), quote.getLastName().toUpperCase().trim(),
				quote.getBirthDate().getTime(), quote.getPostalCode());
  }
}
