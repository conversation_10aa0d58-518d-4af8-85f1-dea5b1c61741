package com.intact.brokeroffice.service.bloommqhandler;

import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfInsuranceCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.brokeroffice.business.common.impl.CommandBusinessProcess;
import com.intact.brokeroffice.business.domain.Quote;
import com.intact.brokeroffice.business.domain.bloommq.BloomQNameEnum;
import com.intact.brokeroffice.business.domain.bloommq.MQMessage;
import com.intact.brokeroffice.business.domain.dialer.QuoteDialerDTO.QuoteAppEnumEnum;
import com.intact.brokeroffice.business.quote.QuotesBean;
import com.intact.tools.logging.exception.LoggingServiceException;
import com.intact.tools.logging.service.LoggingApplicationService;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import jakarta.inject.Inject;
import jakarta.inject.Named;

/**
 * Class handle method to call bloom mq handler service and manage errors
 * This is temporary to synchronize webzone with bloom prject when we uplaod quote or send it to the dialer.
 */
@Component
public class BloomMqHandlerServiceImpl implements IBloomMqHandlerService{

    @Inject
    @Named("bloomMqServiceUrl")
    private String bloomMqServiceUrl;

    @Inject
    private RestTemplate restTemplate;

    @Inject
    private LoggingApplicationService logService;
    
    @Inject
	private CommandBusinessProcess commandBusinessProcess;

    @Override
    public void sendMessage(String queueName, String quoteId) {

        if (StringUtils.isBlank(queueName) || StringUtils.isBlank(quoteId)) {
            log("error", "mq message object can't be null");
        }

        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(bloomMqServiceUrl);

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);

        HttpEntity<MQMessage> request = new HttpEntity<MQMessage>(getMqMessage(queueName, quoteId), headers);
       log("info", "Start sending mq message to bloom mq handler");

        try {

        	restTemplate.postForEntity(builder.toUriString(), request, String.class);

            log("info", "Succesfully call bloom mq handler service with uuid = %s.".formatted(quoteId));

        } catch (HttpStatusCodeException statusCodeException) {

            log("error",
								"An Exception occured in bloom mq handler service with statusCode = %s and response = %s".formatted(
										statusCodeException.getStatusText(), statusCodeException.getResponseBodyAsString()));

        }catch (Exception e) {

            log("error","An Exception occured when trying to call bloom mq handler service with uuid = %s. exception = %s".formatted(quoteId, ExceptionUtils.getStackTrace(e)));
        }
    }

    /**
     *
     * @param queueName : name of queue determine by LineOfInsuranceCodeEnum
     * @param quoteId : id of quote
     * @return MQMessage
     */
    private MQMessage getMqMessage(String queueName, String quoteId) {
        MQMessage mqMessage = new MQMessage();
        mqMessage.setUuid(quoteId);
        mqMessage.setQueueName(queueName);
        mqMessage.setMessageType("MSG_BLOOM");

        return mqMessage;
    }

    private void log(String logType, String message) {

        try {

            if(StringUtils.equalsIgnoreCase("info", logType)) {
                this.logService.logInfo("info", message);
            } else if(StringUtils.equalsIgnoreCase("error", logType)) {
                this.logService.logError("error", message);
            }
        }catch (LoggingServiceException loggingEx) {
            loggingEx.printStackTrace();
        }
    }

	@Override
	public void sendQuoteToBloom(Quote quote) {
		String uuid = quote.getId();
		
		// we don't have uuid in Quote object for auto quote so we have to retrieve the policyversion first
		// the value id in quoteBean correspond to agreementNumber for auto quotes
		if (LineOfInsuranceCodeEnum.AUTOMOBILE == quote.getLineOfInsurance()) {
			uuid = findPlpUuid(quote.getId());
		}
		
		sendQuoteToBloom(uuid, quote.getApplicationMode());
	}
	
	@Override
	public void sendQuoteToBloom(QuotesBean quote) {
		String uuid = quote.getId();
		
		// we don't have uuid in QuotesBean object for auto quote so we have to retrieve the policyversion first
		// the value id in quoteBean correspond to agreementNumber for auto quotes
		if (StringUtils.equalsIgnoreCase("QA", quote.getApplicationMode())
        || StringUtils.equalsIgnoreCase("QF", quote.getApplicationMode())
        || StringUtils.equalsIgnoreCase("IR", quote.getApplicationMode())) {
			uuid = findPlpUuid(quote.getId());
		}
		
		sendQuoteToBloom(uuid, quote.getApplicationMode());
		
	}
	
	private void sendQuoteToBloom(String uuid, String applicationMode) {
		if(StringUtils.isBlank(uuid) || StringUtils.isBlank(applicationMode)) {
			return;
		}
		sendMessage(this.getBloomQueue(applicationMode).name(), uuid);
	}

	private BloomQNameEnum getBloomQueue (String applicationMode) {
    if (StringUtils.equalsIgnoreCase("QF", applicationMode)) {
      return BloomQNameEnum.Q_BLOOM_AUTO;
    } else if (StringUtils.equalsIgnoreCase("QA", applicationMode)) {
      return BloomQNameEnum.Q_BLOOM_AUTO;
    } else if (StringUtils.equalsIgnoreCase("IR", applicationMode)) {
      return BloomQNameEnum.Q_BLOOM_CL;
    } else if (StringUtils.equalsIgnoreCase("QH", applicationMode)) {
      return BloomQNameEnum.Q_BLOOM_HOME;
    } else if (StringUtils.equalsIgnoreCase("QC", applicationMode)) {
      return BloomQNameEnum.Q_BLOOM_HOME;
    } else if (StringUtils.equalsIgnoreCase("QT", applicationMode)) {
      return BloomQNameEnum.Q_BLOOM_HOME;
    } else if (StringUtils.equalsIgnoreCase("PC", applicationMode)) {
      return BloomQNameEnum.Q_BLOOM_CL;
    } else if (StringUtils.equalsIgnoreCase("BA", applicationMode)) {
      return BloomQNameEnum.Q_BLOOM_HOME;
    } else if (StringUtils.equalsIgnoreCase("BH", applicationMode)) {
      return BloomQNameEnum.Q_BLOOM_HOME;
    } else if (StringUtils.equalsIgnoreCase("BC", applicationMode)) {
      return BloomQNameEnum.Q_BLOOM_HOME;
    } else if (StringUtils.equalsIgnoreCase("BT", applicationMode)) {
      return BloomQNameEnum.Q_BLOOM_HOME;
    }
    return null;
  }
	
	private String findPlpUuid(String quoteNumber) {
		PolicyVersion policyVersion = commandBusinessProcess.findLatestPolicyVersion(quoteNumber);
		return ( policyVersion != null && policyVersion.getInsurancePolicy() != null ) ? policyVersion.getInsurancePolicy().getUuId() : null;
	}
	
}
