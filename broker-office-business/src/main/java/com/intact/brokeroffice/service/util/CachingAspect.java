package com.intact.brokeroffice.service.util;

import jakarta.inject.Inject;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;

import com.intact.tools.cache.exception.CacheServiceException;
import com.intact.tools.cache.service.CacheApplicationService;
import com.intact.tools.logging.service.LoggingApplicationService;

public class CachingAspect {

	@Inject
	private CacheApplicationService cacheService = null;

	@Inject
	private LoggingApplicationService logService = null;

	public CachingAspect() {
	}

	public CacheApplicationService getCacheService() {
		return cacheService;
	}

	public void setCacheService(CacheApplicationService cacheService) {
		this.cacheService = cacheService;
	}

	public LoggingApplicationService getLogService() {
		return this.logService;
	}

	public void setLogService(LoggingApplicationService logService) {
		this.logService = logService;
	}

	public Object cacheDomain(ProceedingJoinPoint joinPoint) throws Throwable {
		return this.cacheObject(joinPoint, "webzone");
	}

	public Object cacheObject(ProceedingJoinPoint joinPoint, String cacheService) throws Throwable {

		Object value = null;
		this.validate(joinPoint);

		try {
			String cacheName = joinPoint.getTarget().getClass().getSimpleName() + "."
					+ joinPoint.getSignature().getName();
			String cacheKey = this.buildKey(joinPoint.getArgs());

			try {
				value = this.getCacheService().get(cacheService, cacheName, cacheKey);
			} catch (Exception ex) {
				this.getLogService().logError("error", ex.getMessage(), ex);
			}

			if (value == null) {
				value = joinPoint.proceed();

				try {
					this.addToCache(cacheService, cacheName, cacheKey, value);
				} catch (Exception ex) {
					this.getLogService().logError("error", ex.getMessage(), ex);
				}
			}
		} catch (Exception ex) {
			throw ex;
		}

		return value;
	}

	protected void addToCache(String cacheService, String cacheName, String cacheKey, Object value)
			throws CacheServiceException {
		this.getCacheService().put(cacheService, cacheName, cacheKey, value);

	}

	protected String buildKey(Object[] arguments) {

		StringBuilder builder = new StringBuilder(" ");

		if (arguments != null) {
			for (Object argument : arguments) {
				builder.append(argument != null ? argument.toString() : "null");
				builder.append("-");
			}

			builder.deleteCharAt(builder.length() - 1);
		}

		return builder.toString();
	}

	protected void validate(JoinPoint joinPoint) {
		// TODO Auto-generated method stub

	}

}
