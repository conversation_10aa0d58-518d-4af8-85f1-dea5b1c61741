package com.intact.brokeroffice.service.domain.context;

import java.util.Set;

import jakarta.xml.bind.annotation.XmlRootElement;

@XmlRootElement
public class UserContext {

	private String user = null;
	private String language = null;
	private String province = null;
	private String company = null;
	private UserType type = null;
	private Set<String> companies = null;
	private Set<String> provinces = null;
	private GroupType group = null;

	public UserContext() {
		super();
	}

	public String getUser() {
		return this.user;
	}

	public void setUser(String user) {
		this.user = user;
	}

	public UserType getType() {
		return this.type;
	}

	public void setType(UserType type) {
		this.type = type;
	}

	public GroupType getGroupType() {
		return this.group;
	}

	public void setGroupType(GroupType group) {
		this.group = group;
	}

	public Set<String> getCompanies() {
		return this.companies;
	}

	public void setCompanies(Set<String> companies) {
		this.companies = companies;
	}

	public Set<String> getProvinces() {
		return this.provinces;
	}

	public void setProvinces(Set<String> provinces) {
		this.provinces = provinces;
	}

	public String getLanguage() {
		return language;
	}

	public void setLanguage(String language) {
		this.language = language;
	}

	public String getProvince() {
		return this.province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

	public String getCompany() {
		return this.company;
	}

	public void setCompany(String company) {
		this.company = company;
	}

	@Override
	public String toString() {
		return "user=" + this.getUser() + ", type=" + this.getType() + "group=" + this.getGroupType() +", companies="
				+ this.getCompanies() + ", provinces=" + this.getProvinces() + ", language=" + this.getLanguage()
				+ ", province=" + this.getProvince() + ", company=" + this.getCompany() + "]";
	}

}
