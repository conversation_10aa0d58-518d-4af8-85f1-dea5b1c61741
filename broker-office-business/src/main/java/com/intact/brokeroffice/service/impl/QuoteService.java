package com.intact.brokeroffice.service.impl;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.HttpStatusCodeException;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.intact.brokeroffice.business.domain.ErrorDTO;
import com.intact.brokeroffice.business.domain.ResponseDTO;
import com.intact.brokeroffice.business.domain.TransferToPendingQuoteRequestDTO;
import com.intact.brokeroffice.service.IQuoteService;
import com.intact.tools.logging.exception.LoggingServiceException;
import com.intact.tools.logging.service.LoggingApplicationService;

@Named
public class QuoteService implements IQuoteService {
	

	@Inject
	@Named("homeUploadRestURL")
	private String homeUploadRestURL;
	
	@Inject
	@Named("homeUploadRestAPIKey")
	private String homeUploadRestAPIKey;
	
	@Inject
	private LoggingApplicationService logService;
	
	@Autowired
	private RestTemplate template;
	
	
	
	@Override
	public String uploadToContact(String uuid, String userId) throws Exception {
		ResponseEntity<ResponseDTO> response = null;		

        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(homeUploadRestURL)
                .queryParam("apiKey", homeUploadRestAPIKey);        

        HttpEntity<TransferToPendingQuoteRequestDTO> request = getRequest(uuid, userId);
        
        try {
        	

            response = template.postForEntity(
                    builder.toUriString(),
                    request,
                    ResponseDTO.class);
            
            this.logService.logInfo("info", "succefull upload a Home quote to Contact with userID = " + userId + " and uuid = " + uuid );
            
            // If no data or any errors are returned, consider it as an error
			if (response != null && response.getBody() != null && !CollectionUtils.isEmpty(response.getBody().getErrors())) {
				this.logService.logError("error", "errors return by upload service  " + response.getBody().getErrors().toString());
				throw new RuntimeException("Service returned errors. ");
			} else if (response != null && response.getBody().getData() == null) {
				this.logService.logError("error", response.getBody().getErrors().toString());
            	throw new RuntimeException("Null data object returned after calling the RQQ upload service.");
            }          
			
        } catch (HttpStatusCodeException ex) {

        	logError(ex, userId, uuid);
         	throw new RuntimeException(ex.getResponseBodyAsString(), ex);
         	
     	  }catch (Exception e) {
        		 
			this.logService.logError("error", "An error occured while uploading a Home quote to Contact with userID " + userId + " and uuid " + uuid + " : e = " + e);	 
			e.printStackTrace();			
			throw new RuntimeException(e.getMessage());
        }
        
		return response.getBody().getData().getContractReferenceNumber();
    }
	
	
	private void logError(HttpStatusCodeException ex, String userId, String uuid) throws LoggingServiceException {
		
		try {
			
			ObjectMapper objectMapper = new ObjectMapper();
	   	    ResponseDTO responseDTO = objectMapper.readValue(ex.getResponseBodyAsString(), ResponseDTO.class);
	   	    
	   	    this.logService.logError("error" , "An error occured while uploading a Home quote to Contact with userID = " + userId + " and uuid = " + uuid + "\n");
	   	    
		    if (responseDTO != null && !CollectionUtils.isEmpty(responseDTO.getErrors())) {
		    	for(ErrorDTO errorDto : responseDTO.getErrors()) {
			      	this.logService.logError("error" , errorDto.toString());
		    	}
		   	 }
		}catch(Exception e) {
			this.logService.logError("error" , "An error occur when transform upload service response to responseDTO");
		}
	}
	
	private HttpEntity<TransferToPendingQuoteRequestDTO> getRequest(String uuid, String userId) {
		HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        
		TransferToPendingQuoteRequestDTO transferToPendingQuoteRequestDTO = new TransferToPendingQuoteRequestDTO();		
		transferToPendingQuoteRequestDTO.setUserId(userId);
		transferToPendingQuoteRequestDTO.setContractUUID(uuid);
		
		return new HttpEntity<TransferToPendingQuoteRequestDTO>(convertToTransferToPendingQuoteRequestDTO(uuid, userId), headers);
		
	}
	
	private TransferToPendingQuoteRequestDTO convertToTransferToPendingQuoteRequestDTO(String uuid, String userId) {
		
		TransferToPendingQuoteRequestDTO transferToPendingQuoteRequestDTO = new TransferToPendingQuoteRequestDTO();		
		transferToPendingQuoteRequestDTO.setContractUUID(uuid);
		transferToPendingQuoteRequestDTO.setUserId(userId);
		
		return transferToPendingQuoteRequestDTO;
	}
	
}
