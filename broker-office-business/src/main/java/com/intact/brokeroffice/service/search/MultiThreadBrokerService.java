package com.intact.brokeroffice.service.search;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import jakarta.inject.Inject;

import com.intact.business.service.broker.api.IBrokerService;
import com.intact.business.service.broker.domain.IExtendedQuoteSearch;
import com.intact.business.service.broker.domain.IQuoteDetail;
import com.intact.business.service.broker.domain.IQuoteSearch;
import com.intact.business.service.broker.domain.IUserContext;
import com.intact.tools.multithreading.service.MultiThreadingApplicationService;
import com.intact.tools.multithreading.util.Task;

public class MultiThreadBrokerService extends DefaultBrokerService {

	@Inject
	private MultiThreadingApplicationService threadingService = null;
	
	private Integer threadTimeout = null;
	
	public Integer getThreadTimeout() {
		return threadTimeout;
	}

	public void setThreadTimeout(Integer threadTimeout) {
		this.threadTimeout = threadTimeout;
	}
	
	public MultiThreadBrokerService() {
		super();
	}

	public MultiThreadingApplicationService getThreadingService() {
		return this.threadingService;
	}

	public void setThreadingService(MultiThreadingApplicationService threadingService) {
		this.threadingService = threadingService;
	}

	protected Task buildTask(IBrokerService service, String method, String key, Object... parameters) {
		
		List<Object> newParameters = new ArrayList<Object>();
		Task task = new Task();
		task.setTarget(service);
		task.setMethod(method);
		
		newParameters.add(key);
		
		for (Object param : parameters) {
			newParameters.add(param);
		}
		
		task.setParameters(newParameters.toArray());

		return task;
	}

	@Override
	public QuoteData searchExtendedQuotes(IUserContext userContext, IExtendedQuoteSearch search) throws Exception {
		Map<String, IBrokerService> services = this.getServices(search, userContext);
		return this.makeConcurrentCalls("searchExtendedQuotes", services, userContext, search);
	}
	
	@Override
	public QuoteData searchQuotes(IUserContext userContext, IQuoteSearch search) throws Exception {
		Map<String, IBrokerService> services = this.getServices(search, userContext);
		return this.makeConcurrentCalls("searchQuotes", services, userContext, search);
	}

	@Override
	public QuoteData listQuotes(IUserContext userContext) throws Exception {
		Map<String, IBrokerService> services = this.getServices(userContext);
		return this.makeConcurrentCalls("listQuotes", services, userContext);
	}

	protected QuoteData makeConcurrentCalls(String action, Map<String, IBrokerService> services, Object... parameters) throws Exception {
		
		QuoteData data = new QuoteData();
		Map<String, Future<Object>> futures = new HashMap<String, Future<Object>>();

		for (Entry<String, IBrokerService> entry : services.entrySet()) {
			try {
				futures.put(entry.getKey(), this.getThreadingService().execute("search",
						this.buildTask(entry.getValue(), action, entry.getKey(), parameters)));
			} catch (Exception ex) {
				this.getLogService().logError("error", ex.getMessage(), ex);
				data.getExceptions().add(entry.getKey());
			}
		}

		for (Entry<String, Future<Object>> future : futures.entrySet()) {
			try {
				if (future.getValue() != null) {
					List<IQuoteDetail> list = (List<IQuoteDetail>)future.getValue().get(this.threadTimeout, TimeUnit.SECONDS);
					if (list != null) {
						data.getQuotes().addAll(list);
					}
				}
			} catch (Exception ex) {
				this.getLogService().logError("error", ex.getMessage(), ex);
				data.getExceptions().add(future.getKey());
			}
		}

		return data;
	}

	public String toString() {
		return "Multi Thread Broker Service";
	}
}
