package com.intact.brokeroffice.service;

import java.util.List;

import com.intact.brokeroffice.business.domain.BrokerQuoteSearchCriteria;
import com.intact.brokeroffice.business.domain.Quote;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.brokeroffice.business.quote.QuotesDTO;
import com.intact.brokeroffice.business.quote.util.PDFDocumentInfo;
import com.intact.brokeroffice.business.quote.util.PolicyInfo;
import com.intact.business.service.broker.domain.IUserContext;
import com.intact.tools.logging.exception.LoggingServiceException;

public interface IBrokerService {

	public QuotesDTO searchQuotes(IUserContext context, BrokerQuoteSearchCriteria quoteSearch)
			throws BrokerServiceException;

	public QuotesDTO listQuotes(IUserContext context) throws BrokerServiceException;

	public void viewQuote(IUserContext context, Quote quote) throws BrokerServiceException;

	public void updateQuote(IUserContext context, Quote quote) throws BrokerServiceException;

	public PolicyInfo uploadQuote(IUserContext context, Quote quote, String subBroker) throws BrokerServiceException;

	public void reassignQuotes(IUserContext context, List<Quote> quotes, String broker) throws BrokerServiceException;
	
    public PDFDocumentInfo downloadDocument(IUserContext context, Quote quote) throws BrokerServiceException;

    public void reassignQuote(IUserContext context, Quote quote) throws BrokerServiceException, LoggingServiceException;
    
    public void addUploadNoteHome(IUserContext context, Quote quote) throws BrokerServiceException;
}
