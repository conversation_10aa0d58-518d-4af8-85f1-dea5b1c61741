package com.intact.brokeroffice.business.quote.util;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Locale;
import java.util.ResourceBundle;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.plp.domain.enums.BusinessTransactionActivityCodeEnum;
import com.intact.brokeroffice.business.quote.AutoQuotesBean;
import com.intact.brokeroffice.business.quote.QuotesBean;
import com.intact.brokeroffice.service.util.Configuration;
import com.intact.business.service.broker.domain.IQuoteDetail;
import com.intact.business.service.broker.domain.IUserContext;
import com.intact.business.service.broker.domain.Language;
import com.intact.business.service.broker.plp.domain.VehicleQuoteDetail;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
public class AutoQuotesBeanBuilder extends QuotesBeanBuilder {

	@Autowired
	@Qualifier("autoRoadBlocksMap")
	private Configuration<String, String> roadBlocks = null;
	
	/** Constants **/
	private static final String PREFIX_TYPE_OF_RESIDENCE = "driver.type.residence.";

	private static final String CUSTOMER_VALUE_INDEX_BAND = "cvi.band.";

	private static final List<String> VCA = Arrays.asList("01", "02", "03", "07");
	private static final List<String> VCB = Arrays.asList("08", "09", "10", "11", "12", "13", "18", "19");

	private final String VALUE_4 = "4.0";
	private final String DEFAULT = "DEFAULT";
	private final String CVI_CV1 = "CV1";
	private final String CVI_CV2 = "CV2";
	private final String CVI_CV3 = "CV3";
	private final String CVI_CV4 = "CV4";
	private final String CVI_CV5 = "CV5";
	private final String CVI_RC = "RC";
	private final String CVI_VC = "VC";
	
	@Override
	public QuotesBean build(IQuoteDetail detail, IUserContext context, QuotesBean newBean) {
		AutoQuotesBean bean = (AutoQuotesBean)super.build(detail, context, new AutoQuotesBean());
		
		if (detail instanceof VehicleQuoteDetail vehicleDetail) {
			
			bean.setCustomerValueIndex1(vehicleDetail.getCustomerValueIndex1());
			bean.setCustomerValueIndex2(vehicleDetail.getCustomerValueIndex2());
			bean.setVehicleClass1(vehicleDetail.getVehicleClass1());
			bean.setVehicleClass2(vehicleDetail.getVehicleClass2());
			bean.setIntactRank(vehicleDetail.getIntactRank());
			bean.setAutoplusAvailable(vehicleDetail.isAutoplusAvailable());
			
			// Roadblocks
			Locale locale = vehicleDetail.getLanguage() == Language.FRENCH ? Locale.CANADA_FRENCH : Locale.CANADA;
			loadRoadBlock(bean, vehicleDetail.getRoadblock().getCode(), locale);

			// Home insured
			bean.setTypeOfResidence(vehicleDetail.getTypeOfResidence() != null ? PREFIX_TYPE_OF_RESIDENCE + vehicleDetail.getTypeOfResidence() : "");
			bean.setNoHomeInsured(vehicleDetail.isNoHomeInsured());
			bean.setAlreadyHomeInsured(vehicleDetail.isAlreadyHomeInsured());
			
			// CVI Band
			loadCustomerValueBandKey(bean, 0, context.getCompany());
			loadCustomerValueBandKey(bean, 1, context.getCompany());
			
			// Quote status synchronized
			bean.setSync(vehicleDetail.getSync());
			
			// Quote state
			if (vehicleDetail.getBTrxActivityCode() != null && vehicleDetail.getBTrxActivityCode().equals(BusinessTransactionActivityCodeEnum.INITIAL_QUOTE_OR_POLICY_CREATION.getCode())) {
				Date creationDate = vehicleDetail.getLastUpdateDate();
				this.setQuoteState(bean, creationDate,  context.getCompany());
			}
		}
		
		return bean;
	}
	
	/**
	 * Function to load the roadblock information for the current quote
	 * @param quotesBean : The current quote bean
	 * @param roadblockCode : The code of the roadblock associated with the quote (null if there are none)
	 * @param locale : The Locale for which to get the roadblock
	 */
	protected void loadRoadBlock(QuotesBean quotesBean, String roadblockCode, Locale locale) {

		String rbMsg = getRoadBlockMessage(roadblockCode, locale);

		if (rbMsg == null) {
			rbMsg = getRoadBlockMessage(roadblockCode + ".content", locale);
			rbMsg = rbMsg == null ? roadblockCode : rbMsg;
		}

		quotesBean.setRoadblockCode(roadblockCode);
		quotesBean.setRoadblockMessage(rbMsg);

		String image = roadBlocks.getValue(roadblockCode);

		if (image == null) {
			image = roadBlocks.getValue("default");
		}

		// roadblock message image
		quotesBean.setRoadBlockImage(image);

	}

	/**
	 * Get the roadblock message for the given roadblock code
	 * @param roadblockCode : The roadblock code for which to get the message
	 * @param locale : The Locale for which to get the roadblock message
	 * @return the roadblock message
	 */
	protected static String getRoadBlockMessage(String roadblockCode, Locale locale) {
		String value = null;

		try {
			value = ResourceBundle.getBundle("autoquote_roadblock", locale).getString(roadblockCode);
		} catch (Exception ignored) {
		}

		return value;
	}
	
	/**
	 * 
	 * load the customer value index band
	 * 
	 * @param aQuotesBean
	 * @param index
	 *            ( 0 : FIRST vehicle , 1 : second vehicle)
	 */
	protected void loadCustomerValueBandKey(QuotesBean aQuotesBean, int index, String company) {
		String aCustomerValueIndex = index == 0 ? aQuotesBean.getCustomerValueIndex1()
				: aQuotesBean.getCustomerValueIndex2();
		String aKey = CUSTOMER_VALUE_INDEX_BAND + DEFAULT;

		if (CifCompanyEnum.INTACT_HALIFAX.getSubBrokerCompanyNumber().equals(company) && aCustomerValueIndex != null) {
			aKey = loadCVIOntario(aQuotesBean, index, aCustomerValueIndex);
		} else {
			aKey = loadCVI(aQuotesBean, index, aCustomerValueIndex);
		}

		if (index == 0) {
			aQuotesBean.setCustomerValueIndexBand1(aKey);
		} else {
			aQuotesBean.setCustomerValueIndexBand2(aKey);
		}
	}

	/**
	 * Function to load the CVI information for a given vehicle on a quote  for Ontario
	 * @param aQuotesBean : The current quote bean
	 * @param index : ( 0 : FIRST vehicle , 1 : second vehicle)
	 * @param aCustomerValueIndex : The CVI index for the quote for the current vehicle
	 * @return The CVI band for the vehicle
	 */
	protected String loadCVIOntario(QuotesBean aQuotesBean, int index, String aCustomerValueIndex) {
		String aKey = CUSTOMER_VALUE_INDEX_BAND + DEFAULT;

		int aCustomerValueInt = Integer.valueOf(aCustomerValueIndex);
		String vehicleClass = index == 0 ? aQuotesBean.getVehicleClass1() : aQuotesBean.getVehicleClass2();
		String rank = aQuotesBean.getIntactRank();
		BigDecimal rankValue = getRank(rank);
		BigDecimal four = new BigDecimal(VALUE_4);
		boolean isRankLessThanFour = rankValue.compareTo(four) < 0;

		if (VCA.contains(vehicleClass)) {
			if (-999 < aCustomerValueInt && aCustomerValueInt < -14) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + (isRankLessThanFour ? DEFAULT : CVI_VC);
			} else if (-13 < aCustomerValueInt && aCustomerValueInt < 19) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + (isRankLessThanFour ? DEFAULT : CVI_VC);
			} else if (20 < aCustomerValueInt && aCustomerValueInt < 999) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + CVI_RC;
			}
		} else if (VCB.contains(vehicleClass)) {
			if (-999 < aCustomerValueInt && aCustomerValueInt < -44) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + (isRankLessThanFour ? DEFAULT : CVI_VC);
			} else if (-43 < aCustomerValueInt && aCustomerValueInt < 6) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + (isRankLessThanFour ? DEFAULT : CVI_VC);
			} else if (7 < aCustomerValueInt && aCustomerValueInt < 999) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + CVI_RC;
			}
		}

		return aKey;
	}

	/**
	 * Function to load the CVI information for a given vehicle on a quote for other provinces (only Alberta currently)
	 * @param aQuotesBean : The current quote bean
	 * @param index : ( 0 : FIRST vehicle , 1 : second vehicle)
	 * @param aCustomerValueIndex : The CVI index for the quote for the current vehicle
	 * @return The CVI band for the vehicle
	 */
	protected String loadCVI(QuotesBean aQuotesBean, int index, String aCustomerValueIndex) {
		String aKey = CUSTOMER_VALUE_INDEX_BAND + DEFAULT;

		if (aCustomerValueIndex != null) {

			int aCustomerValueInt = Integer.valueOf(aCustomerValueIndex);

			if (-999 < aCustomerValueInt && aCustomerValueInt < -30) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + CVI_CV1;
			} else if (-29 < aCustomerValueInt && aCustomerValueInt < -15) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + CVI_CV2;
			} else if (-14 < aCustomerValueInt && aCustomerValueInt < 8) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + CVI_CV3;
			} else if (9 < aCustomerValueInt && aCustomerValueInt < 28) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + CVI_CV4;
			} else if (29 < aCustomerValueInt && aCustomerValueInt < 999) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + CVI_CV5;
			}
		}

		return aKey;
	}

	/**
	 * Function to convert a rank (String) to a BigDecimal. Returns 0 if String is null.
	 * @param rank : the String to convert
	 * @return The converted rank
	 */
	protected BigDecimal getRank(String rank) {
		if (rank == null)
			return new BigDecimal(0);
		BigDecimal response = new BigDecimal(0);
		try {
			response = new BigDecimal(rank);
		} catch (Exception e) {
		}
		return response;
	}
}
