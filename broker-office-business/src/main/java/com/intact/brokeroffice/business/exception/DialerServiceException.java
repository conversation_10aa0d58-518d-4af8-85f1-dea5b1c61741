package com.intact.brokeroffice.business.exception;

import java.io.Serial;
import java.util.List;

public class DialerServiceException extends RuntimeException {

	@Serial
	private static final long serialVersionUID = -8033111591582708270L;
	public static final String UNMATCH_BROKER_CODE = "broker";
	
	private List<String> codes;
	private List<String> messages;
	private String fullMessage;
	
	public DialerServiceException(List<String> codes, List<String> messages, String fullMessage, Throwable exception) {
		super(fullMessage, exception);
		
		this.codes = codes;
		this.messages = messages;
		this.fullMessage = fullMessage;
	}

	public List<String> getCodes() {
		return codes;
	}

	public void setCodes(List<String> codes) {
		this.codes = codes;
	}

	public List<String> getMessages() {
		return messages;
	}

	public void setMessages(List<String> messages) {
		this.messages = messages;
	}

	public String getFullMessage() {
		return fullMessage;
	}

	public void setFullMessage(String fullMessage) {
		this.fullMessage = fullMessage;
	}
}
