package com.intact.brokeroffice.business.helper;

import java.util.List;

import com.intact.brokeroffice.business.domain.dialer.PartyDTO;
import com.intact.brokeroffice.business.domain.dialer.QuoteDialerDTO;
import com.intact.brokeroffice.business.domain.dialer.VehicleDTO;
import com.intact.brokeroffice.business.quote.QuotesBean;
import com.intact.business.service.broker.domain.IUserContext;
import com.intact.plt.information.service.client.util.InformationPieceTO;

public interface IQuoteDialerDTOHelper {

	public QuoteDialerDTO buildQuoteDialerDTO(QuotesBean quote, IUserContext userContext, String brokerNumber, String applicationMode, InformationPieceTO generalInfoPiece);
	
	public PartyDTO buildDialerPartyDTO(InformationPieceTO driverInfoPiece, InformationPieceTO generalInfoPiece, QuotesBean quote, String policyHolderName,
			String applicationMode, String provinceFromCompany);
	
	public List<VehicleDTO> buildDialerVehicleDTOList(InformationPieceTO vehicleInfoPiece, String language);
}
