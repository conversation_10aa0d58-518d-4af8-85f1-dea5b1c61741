package com.intact.brokeroffice.business.common;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;

import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;

public class LineOfBusinessUtils {

	private static final String JOIN_PARAM = "ß";
	
	/**
	 * Join a collection of {@link LineOfBusinessCodeEnum} with the default join param ["ß"].  
	 * (Mostly used to prepare the lineOfBusiness in performing search on stored procedures). 
	 * @param set A collection of {@link LineOfBusinessCodeEnum}
	 * @return A string 
	 */
	public static String join(Set<LineOfBusinessEnum> set) {
		if(CollectionUtils.isEmpty(set)) {
			return null;
		}

		Set<String> result = new HashSet<>(set.size());
		for(LineOfBusinessEnum entry : set) {
			if(entry != null) {
				result.add(entry.getCode());
			}
		}

		return StringUtils.join(result, JOIN_PARAM);
	}
	
	/**
	 * Array representation of the equivalent code values of the provided collection of {@link LineOfBusinessEnum} 
	 * @param values The collection of {@link LineOfBusinessEnum} from which to derive the code values
	 * @return A String array of the code values of the provided collection of {@link LineOfBusinessEnum}
	 */
	public static String[] toStringArray(Set<LineOfBusinessEnum> values) {
		if(CollectionUtils.isEmpty(values)) {
			return null;
		}

		List<String> list = new ArrayList<String>(values.size());
		for(LineOfBusinessEnum val : values) {
			if(val != null) {
				list.add(val.getCode());
			}
		}

		return list.toArray(new String[list.size()]);
	}
	
	/**
	 * Convert an array string representation of lines of business codes to the equivalent {@link LineOfBusinessEnum} instance 
	 * @param linesOfBusiness  The array of codes to be converted to enum.
	 * @return Converted set of equivalent {@link LineOfBusinessEnum} based on the provided array string 
	 */
	public static Set<LineOfBusinessEnum> toLinesOfBusiness(String[] linesOfBusiness) {
		Set<LineOfBusinessEnum> result = null;

		if(linesOfBusiness != null) {

			result = new HashSet<LineOfBusinessEnum>(linesOfBusiness.length);

			for(String val : linesOfBusiness) {
				LineOfBusinessEnum value = LineOfBusinessEnum.valueOfCode(val);
				if(value != null) {
					result.add(value);
				}
			}
		}

		return CollectionUtils.isEmpty(result) ? Collections.<LineOfBusinessEnum>emptySet() : result;
	}

	/**
	 * {@link LineOfBusinessUtils#toStringValue(LineOfBusinessCodeEnum)}
	 */
	public static String toStringValue(String lineOfBusiness) {
		return toStringValue(LineOfBusinessEnum.valueOfCode(lineOfBusiness));
	}

	/**
	 * Convert to a string value the line of business passed as a parameter
	 * @param lineOfBusiness Line of business from which to derive the string value
	 * @return <ul>
	 * 				<li>{@link Converter#PERSONAL.code} for personal</li>
	 * 				<li>{@link Converter#COMMERCIAL.code} for commercial</li>
	 *  	   </ul>
	 */
	public static String toStringValue(LineOfBusinessEnum lineOfBusiness) {
		return lineOfBusiness == null ? null : Converter.getBy(lineOfBusiness).getCode();
	}
}

/**
 * Enum matching a {@link LineOfBusinessEnum} from cif to its equivalent string representation
 */
enum Converter {
	/** Personal [PL] */
	PERSONAL(LineOfBusinessEnum.PERSONAL_LINE, "PL"),

	/** Commercial [CL] */
	COMMERCIAL(LineOfBusinessEnum.COMMERCIAL_LINE, "CL");
	
	private LineOfBusinessCodeEnum lineOfBusinessCodeEnum;
	private LineOfBusinessEnum lineOfBusinessEnum;
	private String code;

	private Converter(LineOfBusinessEnum lineOfBusinessEnum, String code) {
		this.lineOfBusinessEnum = lineOfBusinessEnum;
		this.code = code; 
	}
	
	public LineOfBusinessCodeEnum getLineOfBusinessCodeEnum() {
		return lineOfBusinessCodeEnum;
	}
	
	public LineOfBusinessEnum getLineOfBusinessEnum() {
		return lineOfBusinessEnum;
	}

	public String getCode() {
		return code;
	}

	public static Converter getBy(LineOfBusinessEnum lineOfBusinessEnum) {
		for(Converter c : Converter.values()) {
			if(c.getLineOfBusinessEnum() == lineOfBusinessEnum) {
				return c;
			}
		}

		throw new IllegalArgumentException("Provided line of business [" + lineOfBusinessEnum + "] not supported");
	}
	
}

