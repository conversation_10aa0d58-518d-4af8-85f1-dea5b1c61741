package com.intact.brokeroffice.business.quote.util;

import java.util.Date;
import java.util.Locale;
import java.util.ResourceBundle;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import com.ing.canada.plp.domain.enums.AgreementFollowUpStatusEnum;
import com.ing.canada.plp.domain.enums.LanguageCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfInsuranceCodeEnum;
import com.ing.canada.plp.domain.enums.UserActivityTypeCodeEnum;
import com.ing.canada.plp.report.enums.QuoteStatusCodeEnum;
import com.intact.brokeroffice.business.quote.NoteBean;
import com.intact.brokeroffice.business.quote.QuotesBean;
import com.intact.brokeroffice.service.util.Configuration;
import com.intact.business.service.broker.domain.ActivityNote;
import com.intact.business.service.broker.domain.Broker;
import com.intact.business.service.broker.domain.IQuoteDetail;
import com.intact.business.service.broker.domain.IUserContext;
import com.intact.business.service.broker.domain.Language;
import com.intact.business.service.broker.domain.LineOfBusiness;
import com.intact.business.service.broker.domain.LineOfInsurance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

@Component
public class QuotesBeanBuilder {
	
	@Autowired
	@Qualifier("time.flag.config")
	private Configuration<String, Long> timeFlags = null;

	@Autowired
	@Qualifier("homeRoadBlocksMap")
	private Configuration<String, String> homeRoadBlocks = null;
	
	public QuotesBeanBuilder() {
	}

	public QuotesBean build(IQuoteDetail detail, IUserContext context, QuotesBean newQuote) {
		QuotesBean bean = newQuote;

		bean.setId(detail.getId());
		bean.setAgreementNumber(detail.getQuoteNumber());
		bean.setAgreementLegacyNumber(detail.getPolicyNumber());
		bean.setLineOfInsurance(LineOfInsuranceCodeEnum.valueOfCode(detail.getLineOfInsurance().getCode()));
		bean.setLineOfBusiness(LineOfBusinessCodeEnum.valueOfCode(detail.getLineOfBusiness().getCode()));
		bean.setLastUpdate(detail.getLastUpdateDate());
		bean.setCreationDate(detail.getCreationDate());
		bean.setInceptionDate(detail.getInceptionDate());
		bean.setConsent(detail.isConsent());

		if (detail.getFollowUpStatus() != null) {
			bean.setFollowUp(AgreementFollowUpStatusEnum.valueOfCode(detail.getFollowUpStatus().getCode()));
		}

		bean.setQuoteSource(detail.getOrigin().getCode());

    if (detail.getStatus() != null) {
      bean.setStatus(QuoteStatusCodeEnum.valueOfCode(detail.getStatus().getCode()));
    }

		bean.setFirstName(detail.getFirstName());
		bean.setLastName(detail.getLastName());
		bean.setUnstructuredName(detail.getBusinessName());
		bean.setBirthDate(detail.getBirthDate());
		bean.setPostalCode(detail.getPostalCode());
		bean.setProvince(detail.getProvince());
		bean.setLanguageCommunication(LanguageCodeEnum.valueOfCodeISO(detail.getLanguage().getCode()));

		bean.setSelected(Boolean.FALSE);

		// Activity Note
		if (detail.getLastActivityNote() != null && detail.getLastActivityNote().getType() != null) {
			NoteBean note = new NoteBean();
			ActivityNote activityNote = detail.getLastActivityNote();
			note.setAccount(activityNote.getUser());
			note.setActivity(UserActivityTypeCodeEnum.valueOfCode(activityNote.getType().getValue()));
			note.setDate(activityNote.getTime());
			note.setNote(activityNote.getText());
			bean.setLastActivityNote(note);
		}

		// Broker info
		Broker broker = detail.getBroker();
		String brokerCity = broker.getCity() != null ? ", " + broker.getCity() : "";
		bean.setBrokerName(broker.getName() + brokerCity);
		bean.setBrokerNumber(broker.getNumber());

		// Load roadblocks for P&C (temporary)
		// TODO : Review solution to use DUS like for RQQ instead
		if (LineOfInsurance.RESIDENTIAL.equals(detail.getLineOfInsurance()) && detail.getRoadblock() != null) {
			Locale locale = detail.getLanguage() == Language.FRENCH ? Locale.CANADA_FRENCH : Locale.CANADA;		
			String rbMsg = detail.getRoadblock().getMessage();
			String roadblockCode = detail.getRoadblock().getCode();
			
			if (LineOfBusiness.COMMERCIAL_LINES.equals(detail.getLineOfBusiness())) {
				try {
					rbMsg = ResourceBundle.getBundle("ccqq_roadblock", locale).getString("ccqq.roadblock." + roadblockCode);
				} catch (Exception ignored) {
				}

				rbMsg = rbMsg == null ? roadblockCode : rbMsg;
			} 

			bean.setRoadblockCode(roadblockCode);
			bean.setRoadblockMessage(rbMsg);

			String image = homeRoadBlocks.getValue(roadblockCode);

			// roadblock message image
			if (image == null) {
				image = homeRoadBlocks.getValue("default");
			}
			bean.setRoadBlockImage(image);
		}
		
		return bean;
	};

	/**
	 * Set the quote state for the current quote based on it's creation date and
	 * company
	 * 
	 * @param bean
	 *            : The current quote bean
	 * @param creationDate
	 *            : The creation date of the quote
	 * @param company
	 *            : The company of the quote
	 */
	protected void setQuoteState(QuotesBean bean, Date creationDate, String company) {

		Date now = new Date();

		bean.setOpenedQuoteStatus(null);

		long gap = Math.abs(now.getTime() - creationDate.getTime());

		/**
		 * The quote has not been read in the last SMALL time in minutes (ex 300000 ms)
		 * - Green
		 **/
		if (gap < (Long) this.getTimeFlags().getValue(company + "-" + "SMALL")) {
			bean.setOpenedQuoteStatus("green");
		}

		/**
		 * The quote has not been read in the last MEDIUM time in minutes (ex 600000 ms)
		 * - Yellow
		 **/
		else if (gap < (Long) this.getTimeFlags().getValue(company + "-" + "MEDIUM")) {
			bean.setOpenedQuoteStatus("yellow");
		}

		/** The quote has not been read - Red **/
		else {
			bean.setOpenedQuoteStatus("red");
		}
	}
	
	
	public Configuration<String, Long> getTimeFlags() {
		return this.timeFlags;
	}

	public void setTimeFlags(Configuration<String, Long> timeFlags) {
		this.timeFlags = timeFlags;
	}
}
