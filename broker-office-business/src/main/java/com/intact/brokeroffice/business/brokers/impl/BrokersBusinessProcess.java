package com.intact.brokeroffice.business.brokers.impl;

import java.util.List;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;
import com.ing.canada.cif.domain.IBrokersInfos;
import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.cif.service.IBrokerService;
import com.intact.brokeroffice.business.brokers.IBrokersBusinessProcess;

/**
 * Business class to cover the sub broker page of the application
 */
@Component
public class BrokersBusinessProcess implements IBrokersBusinessProcess {

	@Autowired
	@Qualifier("cifBrokersService")
	private IBrokerService brokersService;

	/**
	 * @see com.intact.brokeroffice.business.brokers.IBrokersBusinessProcess#getAssignedMasterBrokers(com.ing.canada.cif.domain.enums.CifCompanyEnum, java.lang.String)
	 */
	public Map<String, String> getAssignedMasterBrokers(CifCompanyEnum aCifCompanyEnum, List<ApplicationIdEnum> apps, List<LineOfBusinessEnum> lobs) {		
		return this.brokersService.getAssignedMasterBrokers(aCifCompanyEnum, apps, lobs);
	}

	/** 
	 * @see com.intact.brokeroffice.business.brokers.IBrokersBusinessProcess#getAssignedMasterBrokers(java.util.List, boolean, com.ing.canada.cif.domain.enums.CifCompanyEnum, java.lang.String)
	 */
	public Map<String, String> getAssignedMasterBrokers(
			List<String> masterBrokerNbrList, boolean adminRole, CifCompanyEnum aCifCompanyEnum, String aManufacturerCompanyCode) {		
		return this.brokersService.getAssignedMasterBrokers(masterBrokerNbrList, adminRole, aCifCompanyEnum,  aManufacturerCompanyCode );
	}

	/**
	 * @see com.intact.brokeroffice.business.brokers.IBrokersBusinessProcess#getBrokerInfos(java.lang.String)
	 */
	public IBrokersInfos getBrokerInfos(String subBrokerNo) {		
		return null;
	}

	/** 
	 * @see com.intact.brokeroffice.business.brokers.IBrokersBusinessProcess#getBrokersInfos(java.lang.String)
	 */
	public List<IBrokersInfos> getBrokersInfos(String companyNumber) {	
		return null;
	}


}
