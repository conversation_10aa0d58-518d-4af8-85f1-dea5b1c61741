package com.intact.brokeroffice.business.domain;

import com.ing.canada.plp.domain.enums.AgreementFollowUpStatusEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfInsuranceCodeEnum;

public class Quote {

  private LineOfBusinessCodeEnum lineOfBusiness = null;
  private LineOfInsuranceCodeEnum lineOfInsurance = null;
  private String quoteId = null;
  private String statusNote = null;
  private String activityNote = null;
  private String viewNote = null;
  private String uploadNote = null;
  private String reassignNote = null;
  private AgreementFollowUpStatusEnum status = null;
  private String language = null;
  private String applicationMode = null;

  // dialer information
  private String riskType = null;
  private String quoteDate = null;
  private String termDate = null;
  private String clientName = null;
  private String phoneNumber = null;
  private String province = null;
  private String broker = null;

  public Quote() {
  }

  public Quote(String quote, LineOfBusinessCodeEnum lineOfBusiness,
      LineOfInsuranceCodeEnum lineOfInsurance, String applicationMode) {
    this.setId(quote);
    this.setLineOfBusiness(lineOfBusiness);
    this.setLineOfInsurance(lineOfInsurance);
    this.setApplicationMode(applicationMode);
  }

  public String getId() {
    return this.quoteId;
  }

  public void setId(String quoteId) {
    this.quoteId = quoteId;
  }

  public LineOfBusinessCodeEnum getLineOfBusiness() {
    return this.lineOfBusiness;
  }

  public void setLineOfBusiness(LineOfBusinessCodeEnum lineOfBusiness) {
    this.lineOfBusiness = lineOfBusiness;
  }

  public LineOfInsuranceCodeEnum getLineOfInsurance() {
    return this.lineOfInsurance;
  }

  public void setLineOfInsurance(LineOfInsuranceCodeEnum lineOfInsurance) {
    this.lineOfInsurance = lineOfInsurance;
  }

  public AgreementFollowUpStatusEnum getStatus() {
    return this.status;
  }

  public void setStatus(AgreementFollowUpStatusEnum status) {
    this.status = status;
  }

  public String getStatusNote() {
    return this.statusNote;
  }

  public void setStatusNote(String statusNote) {
    this.statusNote = statusNote;

  }

  public String getActivityNote() {
    return this.activityNote;
  }

  public void setActivityNote(String activityNote) {
    this.activityNote = activityNote;
  }

  public String getViewNote() {
    return viewNote;
  }

  public void setViewNote(String viewNote) {
    this.viewNote = viewNote;
  }

  public String getUploadNote() {
    return uploadNote;
  }

  public void setUploadNote(String uploadNote) {
    this.uploadNote = uploadNote;
  }

  public String getReassignNote() {
    return reassignNote;
  }

  public void setReassignNote(String reassignNote) {
    this.reassignNote = reassignNote;
  }

  public String getLanguage() {
    return language;
  }

  public void setLanguage(String language) {
    this.language = language;
  }

  public String getRiskType() {
    return riskType;
  }

  public void setRiskType(String riskType) {
    this.riskType = riskType;
  }

  public String getQuoteDate() {
    return quoteDate;
  }

  public void setQuoteDate(String quoteDate) {
    this.quoteDate = quoteDate;
  }

  public String getTermDate() {
    return termDate;
  }

  public void setTermDate(String termDate) {
    this.termDate = termDate;
  }

  public String getClientName() {
    return clientName;
  }

  public void setClientName(String clientName) {
    this.clientName = clientName;
  }

  public String getPhoneNumber() {
    return phoneNumber;
  }

  public void setPhoneNumber(String phoneNumber) {
    this.phoneNumber = phoneNumber;
  }

  public String getProvince() {
    return province;
  }

  public void setProvince(String province) {
    this.province = province;
  }

  public String getBroker() {
    return broker;
  }

  public void setBroker(String broker) {
    this.broker = broker;
  }

  public String getApplicationMode() {
    return applicationMode;
  }

  public void setApplicationMode(String applicationMode) {
    this.applicationMode = applicationMode;
  }

  @Override
  public String toString() {
    return "identifier=" + this.getId() + ", line of business=" + this.getLineOfBusiness()
        + ", line of insurance="
        + this.getLineOfInsurance() + ", status=" + this.getStatus() + ", status node=" + this
        .getStatusNote()
        + ", activity note=" + this.getActivityNote() + ", view note=" + this.getViewNote()
        + ", upload note="
        + this.getUploadNote() + ", reassign note=" + getReassignNote() + "]";
  }
}
