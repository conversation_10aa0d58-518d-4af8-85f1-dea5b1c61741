package com.intact.brokeroffice.business.common;


import com.ing.canada.plp.domain.policyversion.PolicyVersion;

/**
 * Everything common that is useful on every page
 */
public interface ICommandBusinessProcess {
	
	/**
	 * Find latest policy version.
	 * 
	 * @param agreementNumber the agreement number
	 * 
	 * @return the policy version
	 */
	PolicyVersion findLatestPolicyVersion(String agreementNumber);
}
