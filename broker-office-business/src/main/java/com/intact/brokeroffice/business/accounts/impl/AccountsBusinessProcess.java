/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.business.accounts.impl;

import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.cif.service.IBrokerService;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.canada.brm.domain.broker.BrokerWebOfficeAccess;
import com.intact.canada.brm.domain.profile.AccessProfile;
import com.intact.canada.brm.domain.profile.AccessProfileEnum;
import com.intact.canada.brm.domain.user.UserAccount;
import com.intact.canada.brm.exception.BRMServiceException;
import com.intact.canada.brm.service.IAccessProfileService;
import com.intact.canada.brm.service.IUserAccountService;
import com.intact.tools.ldap.util.LdapTemplate;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.ldap.core.AttributesMapper;
import org.springframework.ldap.filter.AndFilter;
import org.springframework.ldap.filter.EqualsFilter;
import org.springframework.transaction.annotation.Transactional;

import javax.naming.NamingException;
import javax.naming.directory.Attribute;
import javax.naming.directory.Attributes;
import java.util.*;

@Transactional
public class AccountsBusinessProcess implements IAccountsBusinessProcess {
	/** The ldap template. */
	private LdapTemplate ldapTemplate;

//	@Inject
//	@Named("brmUserAccountService")
	@Autowired
	@Qualifier("brmUserAccountService")
	private IUserAccountService userAccountService;
	
	@Autowired
	@Qualifier("brmAccessProfileService")
	private IAccessProfileService accessProfileService;

	@Autowired
	@Qualifier("cifBrokersService")
	private IBrokerService brokerService;

	@Value("${ldap-group-broker}")
	private String ldapGroupBroker;

	@Value("${ldap-group-broker-reassign}")
	private String ldapGroupBrokerReassign;

	@Value("${LDAP_BASE_DN}")
	private String ldapBaseDN;

	/**
	 * Spring injection.
	 * 
	 * @param template the template
	 */
	public void setLdapTemplate(LdapTemplate template) {
		this.ldapTemplate = template;
	}

	/**
	 * @throws BRMServiceException
	 * @see com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess#findByUId(java.lang.String)
	 */
	public UserAccount findByUId(String uId) throws BrokerServiceException {

		UserAccount user = null;
		
		try {
			user = this.userAccountService.findByUId(uId);
		} catch (BRMServiceException bse) {
			throw new BrokerServiceException(this.toString(), bse);
		}
		
		return user;
	}


/**
	 * @see com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess#getAssignedMasterBrokers(com.ing.canada.cif.domain.enums.CifCompanyEnum,
	 *      java.lang.String)
	 */
    @Transactional(readOnly = true)
	public Map<String, String> getAssignedMasterBrokers(CifCompanyEnum aCifCompanyEnum, List<ApplicationIdEnum> apps, List<LineOfBusinessEnum> lobs) {
		return this.brokerService.getAssignedMasterBrokers(aCifCompanyEnum, apps, lobs);
	}

	/**
	 * @see com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess#update(com.intact.canada.brm.domain.user.UserAccount)
	 */
	public void update(UserAccount aUserAccount) throws BrokerServiceException {
		try {
			this.userAccountService.saveUser(aUserAccount);
		} catch (BRMServiceException bse) {
			throw new BrokerServiceException(this.toString(), bse);
		}
	}

	/**
	 * (non-Javadoc)
	 * 
	 * @throws BrokerServiceException
	 * 
	 * @see com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess#getUsers(java.lang.String) BR5828 - Items
	 *      Displayed per Regional Access : List of Users
	 * 
	 */
	public List<UserAccount> getUsers(String aCurrentUid, ProvinceCodeEnum aProvinceCode) throws BrokerServiceException {
		List<Enumeration<String>> ldapMemberList = getMembersByCN(this.ldapGroupBroker + "-" + aProvinceCode.getCode());
		List<Enumeration<String>> ldapReassignMemberList = null;

		// this group might not have been created for all provinces
		try {
			ldapReassignMemberList = getMembersByCN(this.ldapGroupBrokerReassign + "-" + aProvinceCode.getCode());
		} catch (Exception e) {
		}

		if (ldapReassignMemberList != null && !ldapReassignMemberList.isEmpty()) {
			ldapMemberList.addAll(ldapReassignMemberList);
		}

		List<UserAccount> userList = new ArrayList<UserAccount>();
		List<UserAccount> validUserList = new ArrayList<UserAccount>();

		if (ldapMemberList.size() > 0) {
			String memberAttribute = null;
			int index = -1;
			UserAccount userAccount = null;
			for (Enumeration<String> usernames : ldapMemberList) {
				while (usernames.hasMoreElements()) {
					memberAttribute = usernames.nextElement();
					index = memberAttribute.indexOf("uid=");
					if (index > -1) {
						int endOfName = memberAttribute.indexOf(",", index);
						String userName;

						if (endOfName > -1) {
							userName = memberAttribute.substring(index + 4, endOfName);
						} else {
							userName = memberAttribute.substring(index + 4);
						}

						try {
							userAccount = this.findByUId(userName);
							// BR5940 - Synchronize BRM with TAM accounts :
							// Whenever a user with an "Intact Super Admin" profile enters WebZone,
							// the system will parse the existing brokers accounts in the BRM and
							// compare them with the user accounts in TAM. Those that do not exist
							// in TAM will be removed from the BRM. Conversely it will parse the TAM
							// accounts with a WebZone "Broker" type profile and compare them against
							// the BRM accounts. If they do not exist, they will be added.

							// user does not exist, user will be added
							if (userAccount == null) {
								userAccount = new UserAccount();
								userAccount.setUid(userName);
								userAccount.setName(this.getUserName(userName));
								userAccount.setLastAdminInterventionUid(aCurrentUid);
								userAccount.setLastAdminInterventionDateTime(new Date());
								this.userAccountService.saveUser(userAccount);
							} else if (userAccount.getName() == null) {
								userAccount.setName(this.getUserName(userName));
								this.userAccountService.saveUser(userAccount);
							}
						} catch (BRMServiceException bse) {
							throw new BrokerServiceException(this.toString(), bse);
						}

						userList.add(userAccount);
						validUserList.add(userAccount);
					}
				}
			}
		}
		return userList;
	}

	/**
	 * We update current process to allow that when all WebZone access has been removed for an account in TAM, in
	 * addition to removing the list of users the associated tables in BRM will be updated to remove user access. BR5940
	 * - Synchronize BRM with TAM accounts : User that does not exist in TAM will be removed from the BRM
	 * 
	 * @param aCurrentUid
	 * 
	 * @param usersWithAccess
	 * @throws BrokerServiceException 
	 */
	public void checkForUsersToRemove(List<UserAccount> usersWithAccess) throws BrokerServiceException {

		HashMap<String, Object> userAccessMap = new HashMap<String, Object>();

		for (UserAccount anAccount : usersWithAccess) {
			if (!userAccessMap.containsKey(anAccount.getUid())) {
				userAccessMap.put(anAccount.getUid(), null);
			}
		}

		Object[] brokerWebOfficeAccesses = null;

		try {
			for (UserAccount anAccount : this.userAccountService.findWithBrokerWebOfficeAccess()) {
				if (!userAccessMap.containsKey(anAccount.getUid())) {
					brokerWebOfficeAccesses = anAccount.getBrokerWebOfficeAccesses().toArray();
					for (Object brokerWebOfficeAccess : brokerWebOfficeAccesses) {
						anAccount.removeBrokerWebOfficeAccess((BrokerWebOfficeAccess) brokerWebOfficeAccess);
					}
				}
			}
		} catch (BRMServiceException bse) {
			throw new BrokerServiceException(this.toString(), bse);
		}

		userAccessMap.clear();
	}

	/**
	 * get all the members of a group.
	 * 
	 * @param CN the cN
	 * 
	 * @return the list
	 */
	@SuppressWarnings("unchecked")
	private List<Enumeration<String>> getMembersByCN(String CN) {
		return this.ldapTemplate.searchUser(this.ldapBaseDN, CN, new MemberAttributeMapper());
	}

	/**
	 * get the user common name of a group.
	 * 
	 * @param userId the user id
	 * @return
	 */
	@SuppressWarnings("unchecked")
	private Enumeration<String> getUserNamesByCN(String userId) {
		AndFilter filter = new AndFilter();
		filter.and(new EqualsFilter("uid", userId));

		List<Enumeration<String>> result = this.ldapTemplate.search(this.ldapBaseDN, filter.encode(),
				new UserNameAttributeMapper());
		if (result.size() > 0) {
			// return the first one.
			return result.get(0);
		}

		return null;
	}

	/**
	 * get all the members of a group.
	 * 
	 * @param userId the user id
	 * 
	 * @return the list
	 */
	@SuppressWarnings("unchecked")
	private Enumeration<String> getProvincesByUid(String userId) {

		AndFilter filter = new AndFilter();
		filter.and(new EqualsFilter("uid", userId));

		List<Enumeration<String>> result = this.ldapTemplate.search(this.ldapBaseDN, filter.encode(),
				new UserAttributeMapper());

		if (result.size() > 0) {
			// return the first one.
			return result.get(0);
		}
		return null;
	}

	/**
	 * @param anUid
	 * @return
	 */
	public String getUserName(String anUid) {
		Enumeration<String> usernames = getUserNamesByCN(anUid);
		if (usernames != null) {
			while (usernames.hasMoreElements()) {
				return usernames.nextElement();
			}
		}
		return null;
	}

	/*
	 * (non-Javadoc) BR5787 - Use st Attribute in TAM as Default Region
	 * 
	 * @see com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess#getDefaultProvince(java.lang.String)
	 */
	public String getDefaultProvince(String anUid) {
		Enumeration<String> usernames = getProvincesByUid(anUid);
		if (usernames != null) {
			while (usernames.hasMoreElements()) {
				return usernames.nextElement();
			}
		}
		return null;
	}

	/**
	 * This class is used to map the ldap attribute value to Enumeration.
	 */
	private class MemberAttributeMapper implements AttributesMapper {

		/**
		 * Map from attributes.
		 * 
		 * @param attributes the attributes
		 * 
		 * @return the enumeration<?>
		 * 
		 * @throws NamingException the naming exception
		 * 
		 * @see org.springframework.ldap.core.AttributesMapper#mapFromAttributes(javax.naming.directory.Attributes)
		 */
		public Enumeration<?> mapFromAttributes(Attributes attributes) throws NamingException {
			Attribute attr = attributes.get("member");
			if (attr != null && attr.size() > 0) {
				return attr.getAll();
			}

			return null;
		}
	}

	/**
	 * This class is used to map the ldap attribute value to Enumeration. BR5787 - Use st Attribute in TAM as Default
	 * Region
	 */
	public class UserAttributeMapper implements AttributesMapper {
		/**
		 * @see org.springframework.ldap.core.AttributesMapper#mapFromAttributes(javax.naming.directory.Attributes)
		 */
		public Enumeration<?> mapFromAttributes(Attributes attributes) throws NamingException {
			Attribute attr = attributes.get("st");
			if (attr != null && attr.size() > 0) {
				return attr.getAll();
			}

			return null;
		}
	}

	/**
	 * This class is used to map the ldap attribute value to Enumeration.
	 */
	public class UserNameAttributeMapper implements AttributesMapper {
		/**
		 * @see org.springframework.ldap.core.AttributesMapper#mapFromAttributes(javax.naming.directory.Attributes)
		 */
		public Enumeration<?> mapFromAttributes(Attributes attributes) throws NamingException {
			Attribute attr = attributes.get("cn");
			if (attr != null && attr.size() > 0) {
				return attr.getAll();
			}

			return null;
		}
	}

	/**
	 * Find a list of user by owner code
	 * 
	 * @param anOwnerCode the owner code
	 * 
	 * @return the list of user account
	 */
	public List<UserAccount> findByOwner(String anOwnerCode) throws BrokerServiceException {

		List<UserAccount> users = null;

		try {
			users = this.userAccountService.findByOwner(anOwnerCode);
		} catch (BRMServiceException bse) {
			throw new BrokerServiceException(this.toString(), bse);
		}

		return users;

	}

	/**
	 * (non-Javadoc)
	 * 
	 * @see com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess#getMasterOwnerName(java.lang.String)
	 */
	public String findMasterOwnerName(String ownerCode) {
		return this.brokerService.getMasterOwnerName(ownerCode);
	}
	
	public List<AccessProfile> findAccessProfilesByName(AccessProfileEnum... names) {
		return this.accessProfileService.findByName(names);
	}

	public String toString() {
		return "Account Business Process";
	}
}
