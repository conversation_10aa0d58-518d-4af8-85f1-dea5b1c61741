package com.intact.brokeroffice.business.quote;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import jakarta.inject.Named;

import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.report.insurancePolicy.InsurancePolicyNoteInfo;

@Named
public class AutoQuotesBean extends QuotesBean {

	private static final String PREFIX_TYPE_OF_RESIDENCE = "driver.type.residence.";

	private static final String CUSTOMER_VALUE_INDEX_BAND = "cvi.band.";

	private static final List<String> VCA = Arrays.asList("01", "02", "03", "07");
	private static final List<String> VCB = Arrays.asList("08", "09", "10", "11", "12", "13", "18", "19");

	private final String VALUE_4 = "4.0";
	private final String DEFAULT = "DEFAULT";
	private final String CVI_CV1 = "CV1";
	private final String CVI_CV2 = "CV2";
	private final String CVI_CV3 = "CV3";
	private final String CVI_CV4 = "CV4";
	private final String CVI_CV5 = "CV5";
	private final String CVI_RC = "RC";
	private final String CVI_VC = "VC";

	/** configured time interval by province **/

	public AutoQuotesBean() {
		super();
	}

	/**
	 * Creates the note bean.
	 * 
	 * @param noteInfo
	 *            the note info
	 * 
	 * @return the note bean
	 */
	private NoteBean createNoteBean(InsurancePolicyNoteInfo noteInfo) {
		NoteBean note = new NoteBean();
		note.setAccount(noteInfo.getAuthorUID());
		note.setActivity(noteInfo.getUserActivityType());
		note.setDate(noteInfo.getAuditTrail().getCreateTimestamp());
		note.setNote(noteInfo.getNote());
		return note;
	}

	/**
	 * 
	 * validate that the note event occurs between 0 min and the betweenInterval
	 * parameter
	 * 
	 * @param noteCreateTs
	 * @param betweenInterval
	 * @return
	 */
	private boolean insideIntervalMins(Date noteCreateTs, int betweenInterval) {
		Date now = Calendar.getInstance().getTime();
		long millis = Math.abs(now.getTime() - noteCreateTs.getTime());

		// 1000ms * 60s * 15m
		if (millis <= (1000 * 60 * betweenInterval)) {
			return true;
		}

		return false;
	}

	/**
	 * Compare that insurance policy note info 1 is more recent than insurance
	 * policy note info 2
	 * 
	 * @param noteInfo1
	 * @param noteInfo2
	 * @return true if more recent else false
	 */
	private boolean compareIpNoteInfoCreationTs(InsurancePolicyNoteInfo noteInfo1, InsurancePolicyNoteInfo noteInfo2) {
		if (noteInfo2 == null) {
			return true;
		}
		long millis = noteInfo1.getAuditTrail().getCreateTimestamp().getTime()
				- noteInfo2.getAuditTrail().getCreateTimestamp().getTime();
		return (millis > 0) ? true : false;
	}

	/**
	 * Gets the driver license type key from code enum
	 * 
	 * @param typeResidence
	 * @see driver_en_CA.properties (driver_fr_CA.properties)
	 * @return String
	 */
	private String getTypeOfResidenceTypeKey(String typeResidence) {
		if (typeResidence != null) {
			return PREFIX_TYPE_OF_RESIDENCE + typeResidence;
		}
		return "";
	}

	/**
	 * 
	 * load the customer value index band
	 * 
	 * @param aQuotesBean
	 * @param index
	 *            ( 0 : FIRST vehicle , 1 : second vehicle)
	 */
	private void loadCustomerValueBandKey(QuotesBean aQuotesBean, int index, ProvinceCodeEnum provinceCode) {
		String aCustomerValueIndex = index == 0 ? aQuotesBean.getCustomerValueIndex1()
				: aQuotesBean.getCustomerValueIndex2();
		String aKey = CUSTOMER_VALUE_INDEX_BAND + DEFAULT;

		if (ProvinceCodeEnum.ONTARIO.equals(provinceCode) && aCustomerValueIndex != null) {
			aKey = loadCVIOntario(aQuotesBean, index, aCustomerValueIndex);
		} else {
			aKey = loadCVI(aQuotesBean, index, aCustomerValueIndex);
		}

		if (index == 0) {
			aQuotesBean.setCustomerValueIndexBand1(aKey);
		} else {
			aQuotesBean.setCustomerValueIndexBand2(aKey);
		}
	}

	private String loadCVIOntario(QuotesBean aQuotesBean, int index, String aCustomerValueIndex) {
		String aKey = CUSTOMER_VALUE_INDEX_BAND + DEFAULT;

		int aCustomerValueInt = Integer.valueOf(aCustomerValueIndex);
		String vehicleClass = index == 0 ? aQuotesBean.getVehicleClass1() : aQuotesBean.getVehicleClass2();
		String rank = aQuotesBean.getIntactRank();
		BigDecimal rankValue = getRank(rank);
		BigDecimal four = new BigDecimal(VALUE_4);
		boolean isRankLessThanFour = rankValue.compareTo(four) < 0;

		if (VCA.contains(vehicleClass)) {
			if (-999 < aCustomerValueInt && aCustomerValueInt < -14) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + (isRankLessThanFour ? DEFAULT : CVI_VC);
			} else if (-13 < aCustomerValueInt && aCustomerValueInt < 19) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + (isRankLessThanFour ? DEFAULT : CVI_VC);
			} else if (20 < aCustomerValueInt && aCustomerValueInt < 999) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + CVI_RC;
			}
		} else if (VCB.contains(vehicleClass)) {
			if (-999 < aCustomerValueInt && aCustomerValueInt < -44) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + (isRankLessThanFour ? DEFAULT : CVI_VC);
			} else if (-43 < aCustomerValueInt && aCustomerValueInt < 6) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + (isRankLessThanFour ? DEFAULT : CVI_VC);
			} else if (7 < aCustomerValueInt && aCustomerValueInt < 999) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + CVI_RC;
			}
		}

		return aKey;
	}

	private String loadCVI(QuotesBean aQuotesBean, int index, String aCustomerValueIndex) {
		String aKey = CUSTOMER_VALUE_INDEX_BAND + DEFAULT;

		if (aCustomerValueIndex != null) {

			int aCustomerValueInt = Integer.valueOf(aCustomerValueIndex);

			if (-999 < aCustomerValueInt && aCustomerValueInt < -30) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + CVI_CV1;
			} else if (-29 < aCustomerValueInt && aCustomerValueInt < -15) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + CVI_CV2;
			} else if (-14 < aCustomerValueInt && aCustomerValueInt < 8) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + CVI_CV3;
			} else if (9 < aCustomerValueInt && aCustomerValueInt < 28) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + CVI_CV4;
			} else if (29 < aCustomerValueInt && aCustomerValueInt < 999) {
				aKey = CUSTOMER_VALUE_INDEX_BAND + CVI_CV5;
			}
		}

		return aKey;
	}

	protected BigDecimal getRank(String rank) {
		if (rank == null)
			return new BigDecimal(0);
		BigDecimal response = new BigDecimal(0);
		try {
			response = new BigDecimal(rank);
		} catch (Exception e) {
		}
		return response;
	}
}
