/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.business.accounts;

import java.util.List;
import java.util.Map;

import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.canada.brm.domain.profile.AccessProfile;
import com.intact.canada.brm.domain.profile.AccessProfileEnum;
import com.intact.canada.brm.domain.user.UserAccount;

public interface IAccountsBusinessProcess {
	
	/**
	 * Gets the list of users from LDAP
	 * 
	 * BR5828 - Items Displayed per Regional Access : 
 	 * The items displayed in the various lists and search results (i.e.: List of Quotes, 
 	 * List of Users, Point of Sale List, etc.) will be limited to the items available 
 	 * for the current region of the user.
	 * 
	 * @return the users
	 * @throws BrokerServiceException 
	 */
	List<UserAccount> getUsers(String aCurrentUid, ProvinceCodeEnum aProvinceCode) throws BrokerServiceException;
	
	/**
	 * Finds a specific user in BRM with his user name
	 * 
	 * @param uId the u id
	 * 
	 * @return the user account
	 * @throws BrokerServiceException 
	 */
	UserAccount findByUId(String uId) throws BrokerServiceException;
	
	/**
	 * Gets the list of assignable master broker
	 * 
	 * BR5828 - Items Displayed per Regional Access : 
 	 * The items displayed in the various lists and search results (i.e.: List of Quotes, 
 	 * List of Users, Point of Sale List, etc.) will be limited to the items available 
 	 * for the current region of the user. 
	 * 
	 * @param aCifCompanyEnum the a cif company enum
	 * @param apps the list of application ids
	 * @param lobs the list of lines of business
	 * 
	 * @return the assigned master brokers
	 */
	Map<String,String> getAssignedMasterBrokers(CifCompanyEnum aCifCompanyEnum, List<ApplicationIdEnum> apps, List<LineOfBusinessEnum> lobs);
	
	/**
	 * Update a account in BRM
	 * 
	 * @param aUserAccount the a user account
	 * @throws BrokerServiceException 
	 */
	void update(UserAccount aUserAccount) throws BrokerServiceException;
	
	/**
	 * Find a list of user by owner code
	 * 
	 * @param anOwnerCode the owner code
	 * 
	 * @return the list of user account
	 * @throws BrokerServiceException 
	 */
	List<UserAccount> findByOwner(String anOwnerCode) throws BrokerServiceException;
	
	/**
	 * Find the master owner name
	 * @param ownerCode
	 * @return the master owner name
	 */
	String findMasterOwnerName(String ownerCode);
	
	
	/**
	 * Gets the default province (st attribute) based on
	 * 
	 * BR5787  - Use st Attribute in TAM as Default Region:
	 * Use the "st" attribute in TAM to determine the default region 
	 * for the user when entering Web Zone for the first time. Thereafter, 
	 * if the user changes their region within Web Zone, the latest selected
	 *  region will become the default region for subsequent Web Zone sessions.
	 * 
	 * @param anUid
	 * @return
	 */
	String getDefaultProvince(String anUid);
	
	/**
	 * Gets the user name value based on uid)
	 * @param anUid
	 * @return
	 */
	String getUserName(String anUid);
	
	/**
	 * Returns the access profiles based on their names.
	 * @param names
	 * @return
	 */
	public List<AccessProfile> findAccessProfilesByName(AccessProfileEnum... names);
	
	
}
