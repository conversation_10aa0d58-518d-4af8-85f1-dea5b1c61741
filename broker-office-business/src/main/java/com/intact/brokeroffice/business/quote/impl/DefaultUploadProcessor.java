package com.intact.brokeroffice.business.quote.impl;

import com.intact.brokeroffice.business.domain.Quote;
import com.intact.brokeroffice.business.quote.UploadProcessor;
import com.intact.brokeroffice.dao.SystemDAO;
import com.intact.brokeroffice.service.search.DefaultBrokerService;
import com.intact.business.service.broker.common.domain.QuoteUpload;
import com.intact.business.service.broker.domain.IPolicy;
import com.intact.business.service.broker.domain.IUserContext;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import jakarta.inject.Inject;

public abstract class DefaultUploadProcessor implements UploadProcessor {

	private final Log log = LogFactory.getLog(DefaultUploadProcessor.class);

	@Autowired
	@Qualifier("webSystemDAO")
	private SystemDAO systemDAO = null;

	@Inject
	private DefaultBrokerService brokerService = null;

	public DefaultUploadProcessor() {
	}

	public SystemDAO getSystemDAO() {
		return this.systemDAO;
	}

	public void setSystemDAO(SystemDAO systemDAO) {
		this.systemDAO = systemDAO;
	}

	public DefaultBrokerService getBrokerService() {
		return this.brokerService;
	}

	public void setBrokerService(DefaultBrokerService brokerService) {
		this.brokerService = brokerService;
	}

	public abstract boolean isQuickQuote(IUserContext context, Quote quote) throws Exception;

	@Override
	public IPolicy execute(Quote quote, String subBrokerNumber) throws Exception {

		IPolicy policy = null;

		// CALL THE UPLOAD

		if (log.isDebugEnabled()) {
			log.debug("Uploading quote: [" + quote.toString() + ", subBrokerNumber=" + subBrokerNumber);
		}

		QuoteUpload uploadInfo = new QuoteUpload();
		uploadInfo.setQuoteId(quote.getId());
		uploadInfo.setUser(this.getSystemDAO().getSourceUser());
		uploadInfo.setUploadUser(this.getSystemDAO().getUploadUser());
		uploadInfo.setBroker(subBrokerNumber);
		uploadInfo.setLanguage(this.getSystemDAO().getLanguage());

		policy = this.getBrokerService().uploadQuote(
				"upload." + quote.getLineOfBusiness().getCode() + "." + quote.getLineOfInsurance().getCode(),
				uploadInfo);

		return policy;
	}

	public String toString() {
		return "Default Upload Processor";
	}

}
