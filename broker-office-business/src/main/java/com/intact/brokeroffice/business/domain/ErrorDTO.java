package com.intact.brokeroffice.business.domain;

public class ErrorDTO {
    private String code;
    private String message;
    private String stackTrace;

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getStackTrace() {
        return stackTrace;
    }

    public void setStackTrace(String stackTrace) {
        this.stackTrace = stackTrace;
    }
    
    public String toString() {
    	StringBuilder stB = new StringBuilder();
    	
    	return stB.append("ErrorDTO :")
    		.append("code = " + code)
    		.append("message = " + message)
    		.append("stacktrace : " + stackTrace)
    		.append("\n")
    		.toString();
    }
    
}
