package com.intact.brokeroffice.business.quote;

import com.intact.brokeroffice.business.domain.Quote;
import com.intact.business.service.broker.domain.IPolicy;
import com.intact.business.service.broker.domain.IUserContext;

public interface UploadProcessor {
 
	public IPolicy execute(Quote quote, String subBrokerNumber) throws Exception;
	
	public boolean isQuickQuote(IUserContext context, Quote quote) throws Exception;

}
