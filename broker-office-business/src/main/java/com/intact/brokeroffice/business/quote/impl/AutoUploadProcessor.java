package com.intact.brokeroffice.business.quote.impl;

import jakarta.inject.Inject;
import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;

import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.BusinessTransactionActivityCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.service.IInsurancePolicyService;
import com.ing.canada.plp.service.IPolicyVersionService;
import com.intact.brokeroffice.business.domain.Quote;
import com.intact.brokeroffice.business.exception.BusinessException;
import com.intact.business.service.broker.domain.IPolicy;
import com.intact.business.service.broker.domain.IUserContext;

public class AutoUploadProcessor extends DefaultUploadProcessor {
	
	private static final Logger LOG = ESAPI.getLogger(AutoUploadProcessor.class);
	
	@Inject
	private IPolicyVersionService versionService = null;
	
	@Inject
	private IInsurancePolicyService policyService = null;
	
	
	public AutoUploadProcessor() {
		super();
	}
	
	public IPolicyVersionService getVersionService() {
		return this.versionService;
	}

	public void setVersionService(IPolicyVersionService versionService) {
		this.versionService = versionService;
	}

	public IInsurancePolicyService getPolicyService() {
		return this.policyService;
	}

	public void setPolicyService(IInsurancePolicyService policyService) {
		this.policyService = policyService;
	}

	@Override
	public boolean isQuickQuote(IUserContext context, Quote quote) throws BusinessException {
		
		InsurancePolicy policy = this.getPolicyService().findByAgreementNumber(quote.getId());
		
		return (ApplicationModeEnum.QUICK_QUOTE.equals(policy.getApplicationMode()) && policy
				.getManufacturerCompany().equals(ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION));
	}
	

	@Override
	public IPolicy execute(Quote quote, String subBrokerNumber) throws Exception {
		
		// Clear the legacy number to upload multiple times
		// Get the current policy
		PolicyVersion policyVersion = this.getVersionService().findLatestQuoteByTransactionActivityCodes(quote.getId(),
				BusinessTransactionActivityCodeEnum.INITIAL_QUOTE_OR_POLICY_CREATION,
				BusinessTransactionActivityCodeEnum.VERSION_RETRIEVED);
		if (policyVersion != null) {
			InsurancePolicy insurancePolicy = policyVersion.getInsurancePolicy();
			
			if (insurancePolicy != null) {
				// Set it's agreement number to null to allow multiple uploads
				insurancePolicy.setAgreementLegacyNumber(null);
				this.getPolicyService().persist(insurancePolicy);
				LOG.info(Logger.EVENT_SUCCESS, "Legacy number cleared to allow multiple uploads for quote with ID [%s]".formatted(quote.getId()));
			} else {
				LOG.error(Logger.EVENT_FAILURE, "Legacy number not cleared because no insurance policy returned for quote with ID [%s]".formatted(quote.getId()));
			}
		} else {
			LOG.error(Logger.EVENT_FAILURE, "Legacy number not cleared because no policy version was returned for quote with ID [%s]".formatted(quote.getId()));
		}

		return super.execute(quote, subBrokerNumber);
	}
	
	public String toString() {
		return "Auto Upload Processor";
	}

}
