package com.intact.brokeroffice.business.domain.dialer;

import org.apache.commons.lang3.StringUtils;

public class VehicleDTO {
    private String make = null;
    private String model = null;
    private String year = null;

    public VehicleDTO() {
    }

    public VehicleDTO make(String make) {
        this.make = make;
        return this;
    }

    public String getMake() {
        return this.make;
    }

    public void setMake(String make) {
        this.make = make;
    }

    public VehicleDTO model(String model) {
        this.model = model;
        return this;
    }

    public String getModel() {
        return this.model;
    }

    public void setModel(String model) {
        this.model = model;
    }

    public VehicleDTO year(String year) {
        this.year = year;
        return this;
    }

    public String getYear() {
        return this.year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (o != null && this.getClass() == o.getClass()) {
            VehicleDTO vehicleDTO = (VehicleDTO)o;
            return StringUtils.equals(this.make, vehicleDTO.make) && StringUtils.equals(this.model, vehicleDTO.model) 
            		&& StringUtils.equals(this.year, vehicleDTO.year);
        } else {
            return false;
        }
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class VehicleDTO {\n");
        sb.append("    make: ").append(this.toIndentedString(this.make)).append("\n");
        sb.append("    model: ").append(this.toIndentedString(this.model)).append("\n");
        sb.append("    year: ").append(this.toIndentedString(this.year)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    private String toIndentedString(Object o) {
        return o == null ? "null" : o.toString().replace("\n", "\n    ");
    }
}

