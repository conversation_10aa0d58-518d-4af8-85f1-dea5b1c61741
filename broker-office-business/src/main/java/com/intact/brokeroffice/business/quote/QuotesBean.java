package com.intact.brokeroffice.business.quote;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Set;
import java.util.TreeSet;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.apache.commons.lang3.StringUtils;

import com.ing.canada.plp.domain.enums.AgreementFollowUpStatusEnum;
import com.ing.canada.plp.domain.enums.LanguageCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfInsuranceCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.report.enums.QuoteStatusCodeEnum;
import com.intact.brokeroffice.service.search.QuoteBeanComparator;
import com.intact.brokeroffice.service.util.Configuration;
import com.intact.business.service.broker.common.domain.QuoteSearch;

/**
 * The Class QuotesBean.
 */
public class QuotesBean {

	/** The selected indicator. */
	private Boolean selected;

	/** DE234 Quote Reference Number */
	private String agreementNumber;

	private String agreementLegacyNumber;

	/** The sync indicator. */
	private Boolean sync;

	/** The broker name. */
	private String brokerName;

	/** The broker number. */
	private String brokerNumber;

	/** The follow up. */
	private AgreementFollowUpStatusEnum followUp;

	/** The last followup note. */
	private NoteBean lastFollowupNote = null;

	/** The last activity note. */
	private NoteBean lastActivityNote = null;

	/** The first name. */
	private String firstName;

	/** The last name. */
	private String lastName;

	/** The creation date. */
	private Date creationDate;

	/** The last update. */
	private Date lastUpdate;

	/** The quote status. */
	private QuoteStatusCodeEnum status;

	/** The inception date. */
	private Date inceptionDate;

	private String quoteSource;

	private LanguageCodeEnum languageCommunication;

	private Boolean consent;

	private String roadblockCode;

	private String roadblockMessage;

	private String roadBlockImage;

	private String customerValueIndex1;

	private String customerValueIndex2;

	private String typeOfResidence;

	private boolean alreadyHomeInsured;

	private boolean noHomeInsured;

	private String customerValueIndexBand1;

	private String customerValueIndexBand2;

	private String vehicleClass1;

	private String vehicleClass2;

	private Date birthDate = null;

	private String postalCode = null;
	
	private String province = null;

	private Set<QuotesBean> childrens = null;

	private List<QuotesBean> children = null;

	private boolean expanded = false;

	private String openedQuoteStatus = null;

	private String intactRank = null;

	private boolean autoplusAvailable = false;

	private LineOfBusinessCodeEnum lineOfBusiness = null;

	private String unstructuredName;

	private LineOfInsuranceCodeEnum lineOfInsurance = null;

	private String quoteId = null;
	
	private String phoneNumber = null;

	public QuotesBean() {
		this.setChildrens(new TreeSet<QuotesBean>(new QuoteBeanComparator()));
	}

	public QuotesBean(String quoteId, LineOfBusinessCodeEnum lineOfBusiness, LineOfInsuranceCodeEnum lineOfInsurance) {
		this();
		this.setId(quoteId);
		this.setLineOfBusiness(lineOfBusiness);
		this.setLineOfInsurance(lineOfInsurance);
	}

	public QuotesBean(String quoteId, String lineOfBusiness, String lineOfInsurance) {
		this(quoteId, LineOfBusinessCodeEnum.valueOfCode(lineOfBusiness),
				LineOfInsuranceCodeEnum.valueOfCode(lineOfInsurance));
	}

	public String getId() {
		return this.quoteId;
	}

	public void setId(String quoteId) {
		this.quoteId = quoteId;
	}

	public void setChildrens(Set<QuotesBean> childrens) {
		this.childrens = childrens;
	}

	public String getApplicationMode() {
		if ((this.agreementNumber == null || this.agreementNumber.length() < 2)) {
			if (this.quoteId != null && this.quoteId.length() > 1) {
				return this.quoteId.substring(0, 2);
			}
			return "";
		}
					
		return this.getAgreementNumber().substring(0, 2);
	}

	public boolean isParent() {
		return this.getChildrens() != null && this.getChildrens().size() > 0;
	}

	public String getCustomerValueIndex1() {
		return customerValueIndex1;
	}

	public void setCustomerValueIndex1(String customerValueIndex) {
		this.customerValueIndex1 = customerValueIndex;
	}

	public String getCustomerValueIndex2() {
		return customerValueIndex2;
	}

	public void setCustomerValueIndex2(String customerValueIndex2) {
		this.customerValueIndex2 = customerValueIndex2;
	}

	public boolean isNoHomeInsured() {
		return this.noHomeInsured;
	}

	public void setNoHomeInsured(boolean noHomeInsured) {
		this.noHomeInsured = noHomeInsured;
	}

	public String getTypeOfResidence() {
		return this.typeOfResidence;
	}

	public void setTypeOfResidence(String typeOfResidence) {
		this.typeOfResidence = typeOfResidence;
	}

	public boolean getAlreadyHomeInsured() {
		return alreadyHomeInsured;
	}

	public void setAlreadyHomeInsured(boolean aAlreadyHomeInsured) {
		this.alreadyHomeInsured = aAlreadyHomeInsured;
	}

	/**
	 * Gets the agreement number.
	 * 
	 * BR5110 Displays Quote/ Bind Reference number assigned by On-line Auto Quote
	 * application.
	 * 
	 * @return the agreement number
	 */
	public String getAgreementNumber() {
		return this.agreementNumber;
	}

	/**
	 * Sets the agreement number.
	 * 
	 * @param anAgreementNumber
	 *            the new agreement number
	 */
	public void setAgreementNumber(String anAgreementNumber) {
		this.agreementNumber = anAgreementNumber;
	}

	/**
	 * Gets the broker name. BR5116 Displays the Name and City of the point of sale
	 * assigned during the quote
	 * 
	 * @return the broker name
	 */
	public String getBrokerName() {
		return this.brokerName;
	}

	/**
	 * Sets the broker name.
	 * 
	 * @param aBroker
	 *            the new broker name
	 */
	public void setBrokerName(String aBrokerName) {
		this.brokerName = aBrokerName;
	}

	/**
	 * Gets the broker number.
	 * 
	 * @return the broker number
	 */
	public String getBrokerNumber() {
		return this.brokerNumber;
	}

	/**
	 * Sets the broker number.
	 * 
	 * @param aBroker
	 *            the new broker number
	 */
	public void setBrokerNumber(String aBrokerNumber) {
		this.brokerNumber = aBrokerNumber;
	}

	/**
	 * Gets the first name.
	 * 
	 * BR5123 Displays clients first name as entered in the On-line Auto Quote
	 * application.
	 * 
	 * 
	 * @return the first name
	 */
	public String getFirstName() {
		return this.firstName;
	}

	/**
	 * Sets the first name.
	 * 
	 * @param aFirstName
	 *            the new first name
	 */
	public void setFirstName(String aFirstName) {
		this.firstName = aFirstName;
	}

	/**
	 * Gets the follow up.
	 * 
	 * BR5118 White checkmark is displayed if further follow-up activity is required
	 * on the quote. see quotes.xhtml
	 * rendered="#{qb.followUp=='CONTACTED_FOLLOWUP_REQUIRED'}" BR5119 Red checkmark
	 * is displayed if further follow-up activity is not required on the quote.: see
	 * quotes.xhtml rendered="#{qb.followUp=='CONTACTED_NO_FOLLOWUP_REQUIRED'}"
	 * BR5121 Field is blank if no follow-up activity has been initiated.
	 * 
	 * @return the follow up
	 */
	public AgreementFollowUpStatusEnum getFollowUp() {
		return this.followUp;
	}

	/**
	 * Sets the follow up.
	 * 
	 * @param aFollowUp
	 *            the new follow up
	 */
	public void setFollowUp(AgreementFollowUpStatusEnum aFollowUp) {
		this.followUp = aFollowUp;
	}

	/**
	 * Gets the inception date. BR5136 Displays the inception date as requested by
	 * the client in the On-line Auto Quote application
	 * 
	 * @return the inception date
	 */
	public Date getInceptionDate() {
		return this.inceptionDate;
	}

	/**
	 * Sets the inception date.
	 * 
	 * @param anInceptionDate
	 *            the new inception date
	 */
	public void setInceptionDate(Date anInceptionDate) {
		this.inceptionDate = anInceptionDate;
	}

	/**
	 * Gets the last name. BR5128 Displays clients last name as entered in the
	 * On-line Auto Quote application.:
	 * 
	 * @return the last name
	 */
	public String getLastName() {
		return this.lastName;
	}

	/**
	 * Sets the last name.
	 * 
	 * @param aLastName
	 *            the new last name
	 */
	public void setLastName(String aLastName) {
		this.lastName = aLastName;
	}

	public final Date getBirthDate() {
		return this.birthDate;
	}

	public final void setBirthDate(Date birthDate) {
		this.birthDate = birthDate;
	}

	public final String getPostalCode() {
		return this.postalCode;
	}

	public final void setPostalCode(String postalCode) {
		this.postalCode = postalCode;
	}

	/**
	 * Gets the last update. BR5132 Displays the most recent date and time of any
	 * changes to the original submission. If no changes have been made, the Quote
	 * Date and time value is displayed
	 * 
	 * @return the last update
	 */
	public Date getLastUpdate() {
		return this.lastUpdate;
	}

	/**
	 * Sets the last update.
	 * 
	 * @param aLastUpdate
	 *            the new last update
	 */
	public void setLastUpdate(Date aLastUpdate) {
		this.lastUpdate = aLastUpdate;
	}

	/**
	 * Gets the status.
	 * 
	 * @return the status
	 */
	public QuoteStatusCodeEnum getStatus() {
		return this.status;
	}

	/**
	 * Sets the status.
	 * 
	 * @param aStatus
	 *            the new status
	 */
	public void setStatus(QuoteStatusCodeEnum aStatus) {
		this.status = aStatus;
	}

	/**
	 * Gets the selected.
	 * 
	 * BR5105 The check box will be checked to indicate quotes to be reassigned.
	 * 
	 * @return the selected
	 */
	public Boolean getSelected() {
		return this.selected;
	}

	/**
	 * Sets the selected.
	 * 
	 * @param aSelected
	 *            the new selected
	 */
	public void setSelected(Boolean aSelected) {
		this.selected = aSelected;
	}

	/**
	 * BR5115 Displays the image if the client has an existing Intact homeowner
	 * policy or has indicated they are interested in the Synchro discount,
	 * otherwise the field is blank.:
	 * 
	 * BR5365 Displays the image if the client has an existing Intact homeowner
	 * policy or has indicated they are interested in the My Home & Auto discount,
	 * otherwise the field is blank.:
	 * 
	 * @return the sync
	 */
	public Boolean getSync() {
		return this.sync;
	}

	/**
	 * Sets the sync.
	 * 
	 * @param aSync
	 *            the new sync
	 */
	public void setSync(Boolean aSync) {
		this.sync = aSync;
	}

	/**
	 * Gets the creation date. BR5129 Displays the date the Quote was originally
	 * submitted in the On-line Auto Quote application.
	 * 
	 * @return the creation date
	 */
	public Date getCreationDate() {
		return this.creationDate;
	}

	/**
	 * Sets the creation date.
	 * 
	 * @param aCreationDate
	 *            the new creation date
	 */
	public void setCreationDate(Date aCreationDate) {
		this.creationDate = aCreationDate;
	}

	/**
	 * Gets the last activity note.
	 * 
	 * 
	 * BR5109 On mouse-over the image, a pop-up is displayed showing date/timestamp,
	 * most recent activity and the user name that performed the activity, in the
	 * format "[YYYY-MM-DD HH:MM] by " will be one of the f:
	 * 
	 * @return the last activity note
	 */
	public NoteBean getLastActivityNote() {
		return this.lastActivityNote;
	}

	/**
	 * Sets the last activity note.
	 * 
	 * @param aLastActivityNote
	 *            the new last activity note
	 */
	public void setLastActivityNote(NoteBean aLastActivityNote) {
		this.lastActivityNote = aLastActivityNote;
	}

	/**
	 * Gets the last followup note.
	 * 
	 * @return the last followup note
	 */
	public NoteBean getLastFollowupNote() {
		return this.lastFollowupNote;
	}

	/**
	 * Sets the last followup note.
	 * 
	 * @param aLastFollowupNote
	 *            the new last followup note
	 */
	public void setLastFollowupNote(NoteBean aLastFollowupNote) {
		this.lastFollowupNote = aLastFollowupNote;
	}

	/**
	 * Gets the agreement legacy number
	 * 
	 * BR5110 Displays Quote/ Bind Reference number assigned by On-line Auto Quote
	 * application.: BR5111 On Search Results page, the Policy number will be
	 * displayed under the Reference number when the quote is uploaded to goBRIO.
	 * 
	 * @return the agreement legacy number
	 */
	public String getAgreementLegacyNumber() {
		return this.agreementLegacyNumber;
	}

	/**
	 * Sets the agreement legacy number
	 * 
	 * @param agreementLegacyNumber
	 *            the agreemenent legacy number to set
	 */
	public void setAgreementLegacyNumber(String agreementLegacyNumber) {
		this.agreementLegacyNumber = agreementLegacyNumber;
	}

	/**
	 * Checks if last name is too long compare to maximum length
	 * 
	 * @return
	 */
	public boolean isLastNameLong() {
		return StringUtils.length(this.getLastName()) > 12;
	}

	/**
	 * Checks if first name is too long compare to maximum length
	 * 
	 * @return
	 */
	public boolean isFirstNameLong() {
		return StringUtils.length(this.getFirstName()) > 12;
	}

	/**
	 * Checks if unstructured name is too long compare to maximum length
	 * 
	 * @return
	 */
	public boolean isUnstructuredNameLong() {
		return StringUtils.length(this.getUnstructuredName()) > 12;
	}

	/**
	 * Gets the quote source BR5385 Displays the source of the quote. Will indicate
	 * I if the quote originated from the Intact Insurance website or B (C in
	 * French) if the quote originated from a participating broker website
	 * 
	 * @return the quote source
	 */
	public String getQuoteSource() {
		return this.quoteSource;
	}

	/**
	 * Sets the quote source
	 * 
	 * @param aQuoteSource
	 *            the quote source to set
	 */
	public void setQuoteSource(String aQuoteSource) {
		this.quoteSource = aQuoteSource;
	}

	/**
	 * Gets the language communication
	 * 
	 * @return the language communication
	 */
	public LanguageCodeEnum getLanguageCommunication() {
		return this.languageCommunication;
	}

	/**
	 * Sets the language communication
	 * 
	 * @param aLanguageCommunication
	 *            the language communication to set
	 */
	public void setLanguageCommunication(LanguageCodeEnum aLanguageCommunication) {
		this.languageCommunication = aLanguageCommunication;
	}

	/**
	 * Gets the consent
	 * 
	 * @return the consent
	 */
	public Boolean getConsent() {
		return this.consent;
	}

	/**
	 * Sets the consent
	 * 
	 * @param aConsent
	 *            the consent to set
	 */
	public void setConsent(Boolean aConsent) {
		this.consent = aConsent;
	}

	/**
	 * Gets the roadblock code
	 * 
	 * @return the roadblock code
	 */
	public String getRoadblockCode() {
		return roadblockCode;
	}

	/**
	 * Sets the roadblock code
	 * 
	 * @param roadblockCode
	 *            the roadblock code to set
	 */
	public void setRoadblockCode(String roadblockCode) {
		this.roadblockCode = roadblockCode;
	}

	/**
	 * Gets the roadblock message
	 * 
	 * @return the roadblock message
	 */
	public String getRoadblockMessage() {
		return this.roadblockMessage;
	}

	/**
	 * Sets the roadblock message
	 * 
	 * @param aRoadblockMessage
	 *            the roadblock message to set
	 */
	public void setRoadblockMessage(String aRoadblockMessage) {
		this.roadblockMessage = aRoadblockMessage;
	}

	public String getCustomerValueIndexBand1() {
		return customerValueIndexBand1;
	}

	public void setCustomerValueIndexBand1(String customerValueIndexBand1) {
		this.customerValueIndexBand1 = customerValueIndexBand1;
	}

	public String getCustomerValueIndexBand2() {
		return customerValueIndexBand2;
	}

	public void setCustomerValueIndexBand2(String customerValueIndexBand2) {
		this.customerValueIndexBand2 = customerValueIndexBand2;
	}

	/**
	 * Add a child to the current parent object
	 * 
	 * @param child
	 *            The child instance
	 */
	public void addChild(QuotesBean child) {
		this.childrens.add(child);
	}

	/**
	 * Return an ordered list of children
	 * 
	 * @return Return an ordered list of children
	 */
	public List<QuotesBean> getChildrens() {
		if (children == null && this.childrens != null) {
			children = new ArrayList<QuotesBean>(this.childrens);
		}

		return this.children;
	}

	public boolean isExpanded() {
		return this.expanded;
	}

	public void setExpanded(boolean expanded) {
		this.expanded = expanded;
	}

	public String getRoadBlockImage() {
		return roadBlockImage;
	}

	public void setRoadBlockImage(String roadBlockImage) {
		this.roadBlockImage = roadBlockImage;
	}

	public String toString() {
		return "[agreement number" + this.getAgreementNumber() + "]";
	}

	public String getOpenedQuoteStatus() {
		return openedQuoteStatus;
	}

	public void setOpenedQuoteStatus(String openedQuoteStatus) {
		this.openedQuoteStatus = openedQuoteStatus;
	}

	public String getVehicleClass1() {
		return vehicleClass1;
	}

	public void setVehicleClass1(String vehicleClass1) {
		this.vehicleClass1 = vehicleClass1;
	}

	public String getVehicleClass2() {
		return vehicleClass2;
	}

	public void setVehicleClass2(String vehicleClass2) {
		this.vehicleClass2 = vehicleClass2;
	}

	public String getIntactRank() {
		return intactRank;
	}

	public void setIntactRank(String intactRank) {
		this.intactRank = intactRank;
	}

	public boolean isAutoplusAvailable() {
		return autoplusAvailable;
	}

	public void setAutoplusAvailable(boolean autoplusAvailable) {
		this.autoplusAvailable = autoplusAvailable;
	}

	public LineOfBusinessCodeEnum getLineOfBusiness() {
		return lineOfBusiness;
	}

	public void setLineOfBusiness(LineOfBusinessCodeEnum lineOfBusiness) {
		this.lineOfBusiness = lineOfBusiness;
	}

	public String getUnstructuredName() {
		return unstructuredName;
	}

	public void setUnstructuredName(String unstructuredName) {
		this.unstructuredName = unstructuredName;
	}

	public LineOfInsuranceCodeEnum getLineOfInsurance() {
		return this.lineOfInsurance;
	}

	public void setLineOfInsurance(LineOfInsuranceCodeEnum lineOfInsurance) {
		this.lineOfInsurance = lineOfInsurance;
	}

	public QuotesBean clone(Object quote, QuoteSearch params) throws Exception {
		return new QuotesBean();
	}
	public String getPhoneNumber() {
		return phoneNumber;
	}

	public void setPhoneNumber(String phoneNumber) {
		this.phoneNumber = phoneNumber;
	}
	
	public String getProvince() {
		return province;
	}

	public void setProvince(String province) {
		this.province = province;
	}

}
