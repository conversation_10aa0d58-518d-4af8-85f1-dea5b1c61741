package com.intact.brokeroffice.business.exception;

import java.io.Serial;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

public class BrokerServiceException extends Exception {

	@Serial
	private static final long serialVersionUID = -1782637979561840013L;

	public static final String EXEC_DEFAULT_ERROR = "exec.broker.default.error";

	public static final String EXEC_SEARCH_QUOTES_ERROR = "exec.broker.search.quotes.error";

	public static final String PARAM_CONTEXT_NULL = "param.broker.context.null";
	
	public static final String PARAM_QUOTE_NULL = "param.broker.quote.null";
	
	public static final String PARAM_SUB_BROKER_NULL = "param.broker.sub.broker.null";

	public static final String EXEC_UPLOAD_QUOTE_ERROR = "exec.broker.upload.quote.error";

	public static final String PARAM_QUOTE_INVALID = "param.broker.quote.invalid";

	public static final String CONFIG_UPLOAD_PROCESSORS_NULL = "config.broker.upload.processors.null";

	public static final String CONFIG_UPLOAD_PROCESSOR_NOT_FOUND = "config.broker.upload.processor.not.found";

	public static final String CONFIG_BROKER_SERVICE_NULL = "config.broker.broker.service.null";

	public static final String EXEC_VIEW_QUOTE_ERROR = "exec.broker.view.quote.error";

	public static final String EXEC_LIST_QUOTES_ERROR = "exec.broker.list.quotes.error";

	private static Map<String, String> messages = null;

	private String code = null;
	
	public BrokerServiceException(String message, Throwable exception) {
		super(message, exception);
	}

	public BrokerServiceException(String exceptionCode, Object... parameters) {
		this(exceptionCode, null, parameters);
	}

	public BrokerServiceException(String exceptionCode, Throwable cause, Object... parameters) {
		this(BrokerServiceException.getMessage(exceptionCode, cause, parameters), cause);
		this.setCode(exceptionCode);
	}

	public String getCode() {
		return code;
	}

	public void setCode(final String code) {
		this.code = code;
	}

	public static Map<String, String> getMessages() {
		return BrokerServiceException.messages;
	}

	public static void setMessages(final Map<String, String> messages) {
		BrokerServiceException.messages = messages;
	}

	protected static String getMessage(String exceptionCode, Throwable cause, Object... parameters) {
		
		String message = null;
		
		if (BrokerServiceException.getMessages() == null) {
			BrokerServiceException.initMessages();
		}

		String messageFormat = BrokerServiceException.getMessages().get(exceptionCode);

		if (messageFormat == null) {
			messageFormat = BrokerServiceException.getMessages().get(BrokerServiceException.EXEC_DEFAULT_ERROR);
		}
		
		
		if (parameters != null) {
			message = MessageFormat.format(messageFormat, parameters);
		} else {
			message = MessageFormat.format(messageFormat, cause);
		}

		return message;
	}

	protected static synchronized void initMessages() {
		Map<String, String> messages = new HashMap<String, String>();

		messages.put(BrokerServiceException.EXEC_DEFAULT_ERROR,
					"An unknown error occurred while using the Quote Service. This exception is not documented at this time.  The cause is {0}");
		messages.put(BrokerServiceException.EXEC_SEARCH_QUOTES_ERROR, "An error occured while searching quotes using the {1} with the user context {2} and the search {3}.  The cause is {0}");
		messages.put(BrokerServiceException.EXEC_LIST_QUOTES_ERROR, "An error occured while listing quotes using the {1} with the user context {2}.  The cause is {0}");
		messages.put(BrokerServiceException.EXEC_UPLOAD_QUOTE_ERROR, "An error occured while uploading quote using the {1} with the user context {2} and the quote {3} and the broker number {4}.  The cause is {0}");
		messages.put(BrokerServiceException.EXEC_VIEW_QUOTE_ERROR, "An error occured while viewing quote using the {1} with the user context {2} and the quote {3}.  The cause is {0}");
		messages.put(BrokerServiceException.PARAM_CONTEXT_NULL, "The context passed as parameter is null.  Please call the method using a non-null UserContext object.");
		messages.put(BrokerServiceException.PARAM_QUOTE_NULL, "The quote passed as parameter is null.  Please call the method using a non-null Quote object.");
		messages.put(BrokerServiceException.PARAM_SUB_BROKER_NULL, "The sub broker number passed as parameter is null.  Please call the method using a non-null String object.");
		messages.put(BrokerServiceException.PARAM_QUOTE_INVALID, "The quote passed as parameter is not valid.  Please call the method using a valid Quote object.");
		messages.put(BrokerServiceException.CONFIG_UPLOAD_PROCESSORS_NULL, "The map of processors configured in the service {0} is null.  Please inject the upload processors before using the service.");
		messages.put(BrokerServiceException.CONFIG_BROKER_SERVICE_NULL, "The broker service configured in the service {0} is null.  Please inject the broker service before using the service.");
		messages.put(BrokerServiceException.CONFIG_UPLOAD_PROCESSOR_NOT_FOUND, "The processor with key {1} cannot be found in the map of processors configured in the service {0}.  Please inject the upload processor with this key before using the service.");
		

		BrokerServiceException.setMessages(messages);
	}

}
