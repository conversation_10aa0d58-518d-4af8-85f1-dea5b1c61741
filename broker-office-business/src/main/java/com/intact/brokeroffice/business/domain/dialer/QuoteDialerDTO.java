package com.intact.brokeroffice.business.domain.dialer;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * QuoteDialerDTO
 */

public class QuoteDialerDTO   {
  private String id = null;
  
  /**
   *******************IMPORTANT***************
   * Note on the dates : 
   * Even though dates in the QuoteDialerDTO object in the Broker Dialer Service are of type "DateTime" or "LocalDate", they must be String in WebZone.
   * This is a legacy technology limitation, because sending a DateTime or a LocalDate causes a JSON format exception in the service.
   * As such, the QuoteDialerDTO in WebZone and the Broker Dialer Service are not exactly the same and must stay that way.
   */
  private String creationTimeStamp = null;
  private String inceptionDate = null;
  
  private String distributorNumber = null;

  /**
   * Gets or Sets lineofBusiness
   */
  public enum LineofBusinessEnum {
    PERSONAL("PERSONAL"),
    
    COMMERCIAL("COMMERCIAL");

    private String value;

    LineofBusinessEnum(String value) {
      this.value = value;
    }

    @Override
    @JsonValue
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static LineofBusinessEnum fromValue(String text) {
      for (LineofBusinessEnum b : LineofBusinessEnum.values()) {
        if (String.valueOf(b.value).equals(text)) {
          return b;
        }
      }
      return null;
    }
  }

  private LineofBusinessEnum lineofBusiness = null;
  private String quoteNumber = null;

  /**
   * Gets or Sets quoteSource
   */
  public enum QuoteSourceEnum {
    QI("QI"),
    
    QB("QB"),
    
    QIB("QIB");

    private String value;

    QuoteSourceEnum(String value) {
      this.value = value;
    }

    @Override
    @JsonValue
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static QuoteSourceEnum fromValue(String text) {
      for (QuoteSourceEnum b : QuoteSourceEnum.values()) {
        if (String.valueOf(b.value).equals(text)) {
          return b;
        }
      }
      return null;
    }
  }

  private QuoteSourceEnum quoteSource = null;
  private String userName = null;
  private List<String> listRelatedQuotes = null;

  /**
   * Gets or Sets datasourceOrigin
   */
  public enum DatasourceOriginEnum {
    PLP("PLP"),
    
    XPAS("XPAS"),
    
    XPAS_CL("XPAS_CL"),
    
    IPAS("IPAS");

    private String value;

    DatasourceOriginEnum(String value) {
      this.value = value;
    }

    @Override
    @JsonValue
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static DatasourceOriginEnum fromValue(String text) {
      for (DatasourceOriginEnum b : DatasourceOriginEnum.values()) {
        if (String.valueOf(b.value).equals(text)) {
          return b;
        }
      }
      return null;
    }
  }

  private DatasourceOriginEnum datasourceOrigin = null;

  /**
   * Gets or Sets sourceUnderwritingCompany
   */
  public enum SourceUnderwritingCompanyEnum {
    CENTRAL_ATLANTIC("INTACT_CENTRAL_ATLANTIC"),
    
    QUEBEC("INTACT_QUEBEC"),
    
    WESTERN("INTACT_WESTERN");

    private String value;

    SourceUnderwritingCompanyEnum(String value) {
      this.value = value;
    }

    @Override
    @JsonValue
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static SourceUnderwritingCompanyEnum fromValue(String text) {
      for (SourceUnderwritingCompanyEnum b : SourceUnderwritingCompanyEnum.values()) {
        if (String.valueOf(b.value).equals(text)) {
          return b;
        }
      }
      return null;
    }
  }

  private SourceUnderwritingCompanyEnum sourceUnderwritingCompany = null;
  private PhoneDTO phone = null;
  private List<VehicleDTO> listVehiclesDTO = null;
  private PartyDTO party = null;

  /**
   * Gets or Sets quoteAppEnum
   */
  public enum QuoteAppEnumEnum {
    AUTOQUOTE("AUTOQUOTE"),
    
    AUTO_QUICKQUOTE("AUTO_QUICKQUOTE"),
    
    HOME_QUICKQUOTE("HOME_QUICKQUOTE"),
    
    IRCA_QUICKQUOTE("IRCA_QUICKQUOTE"),
    
    BUNDLE_QUOTE("BUNDLE_QUOTE"),
    
    COMMERCIAL_QUICKQUOTE("COMMERCIAL_QUICKQUOTE");

    private String value;

    QuoteAppEnumEnum(String value) {
      this.value = value;
    }

    @Override
    @JsonValue
    public String toString() {
      return String.valueOf(value);
    }

    @JsonCreator
    public static QuoteAppEnumEnum fromValue(String text) {
      for (QuoteAppEnumEnum b : QuoteAppEnumEnum.values()) {
        if (String.valueOf(b.value).equals(text)) {
          return b;
        }
      }
      return null;
    }
  }

  private QuoteAppEnumEnum quoteAppEnum = null;

  public QuoteDialerDTO id(String id) {
    this.id = id;
    return this;
  }

  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public QuoteDialerDTO creationTimeStamp(String creationTimeStamp) {
    this.creationTimeStamp = creationTimeStamp;
    return this;
  }

  public String getCreationTimeStamp() {
    return creationTimeStamp;
  }

  public void setCreationTimeStamp(String creationTimeStamp) {
    this.creationTimeStamp = creationTimeStamp;
  }

  public QuoteDialerDTO inceptionDate(String inceptionDate) {
    this.inceptionDate = inceptionDate;
    return this;
  }

  public String getInceptionDate() {
    return inceptionDate;
  }

  public void setInceptionDate(String inceptionDate) {
    this.inceptionDate = inceptionDate;
  }

  public QuoteDialerDTO distributorNumber(String distributorNumber) {
    this.distributorNumber = distributorNumber;
    return this;
  }

  public String getDistributorNumber() {
    return distributorNumber;
  }

  public void setDistributorNumber(String distributorNumber) {
    this.distributorNumber = distributorNumber;
  }

  public QuoteDialerDTO lineofBusiness(LineofBusinessEnum lineofBusiness) {
    this.lineofBusiness = lineofBusiness;
    return this;
  }

  public LineofBusinessEnum getLineofBusiness() {
    return lineofBusiness;
  }

  public void setLineofBusiness(LineofBusinessEnum lineofBusiness) {
    this.lineofBusiness = lineofBusiness;
  }

  public QuoteDialerDTO quoteNumber(String quoteNumber) {
    this.quoteNumber = quoteNumber;
    return this;
  }

  public String getQuoteNumber() {
    return quoteNumber;
  }

  public void setQuoteNumber(String quoteNumber) {
    this.quoteNumber = quoteNumber;
  }

  public QuoteDialerDTO quoteSource(QuoteSourceEnum quoteSource) {
    this.quoteSource = quoteSource;
    return this;
  }
  
  public QuoteSourceEnum getQuoteSource() {
    return quoteSource;
  }

  public void setQuoteSource(QuoteSourceEnum quoteSource) {
    this.quoteSource = quoteSource;
  }

  public QuoteDialerDTO userName(String userName) {
    this.userName = userName;
    return this;
  }

  public String getUserName() {
    return userName;
  }

  public void setUserName(String userName) {
    this.userName = userName;
  }

  public QuoteDialerDTO listRelatedQuotes(List<String> listRelatedQuotes) {
    this.listRelatedQuotes = listRelatedQuotes;
    return this;
  }

  public QuoteDialerDTO addListRelatedQuotesItem(String listRelatedQuotesItem) {
    if (this.listRelatedQuotes == null) {
      this.listRelatedQuotes = new ArrayList<String>();
    }
    this.listRelatedQuotes.add(listRelatedQuotesItem);
    return this;
  }

  public List<String> getListRelatedQuotes() {
    return listRelatedQuotes;
  }

  public void setListRelatedQuotes(List<String> listRelatedQuotes) {
    this.listRelatedQuotes = listRelatedQuotes;
  }

  public QuoteDialerDTO datasourceOrigin(DatasourceOriginEnum datasourceOrigin) {
    this.datasourceOrigin = datasourceOrigin;
    return this;
  }

  public DatasourceOriginEnum getDatasourceOrigin() {
    return datasourceOrigin;
  }

  public void setDatasourceOrigin(DatasourceOriginEnum datasourceOrigin) {
    this.datasourceOrigin = datasourceOrigin;
  }

  public QuoteDialerDTO sourceUnderwritingCompany(SourceUnderwritingCompanyEnum sourceUnderwritingCompany) {
    this.sourceUnderwritingCompany = sourceUnderwritingCompany;
    return this;
  }

  public SourceUnderwritingCompanyEnum getSourceUnderwritingCompany() {
    return sourceUnderwritingCompany;
  }

  public void setSourceUnderwritingCompany(SourceUnderwritingCompanyEnum sourceUnderwritingCompany) {
    this.sourceUnderwritingCompany = sourceUnderwritingCompany;
  }

  public QuoteDialerDTO phone(PhoneDTO phone) {
    this.phone = phone;
    return this;
  }

  public PhoneDTO getPhone() {
    return phone;
  }

  public void setPhone(PhoneDTO phone) {
    this.phone = phone;
  }

  public QuoteDialerDTO listVehiclesDTO(List<VehicleDTO> listVehiclesDTO) {
    this.listVehiclesDTO = listVehiclesDTO;
    return this;
  }

  public QuoteDialerDTO addListVehiclesDTOItem(VehicleDTO listVehiclesDTOItem) {
    if (this.listVehiclesDTO == null) {
      this.listVehiclesDTO = new ArrayList<VehicleDTO>();
    }
    this.listVehiclesDTO.add(listVehiclesDTOItem);
    return this;
  }

  public List<VehicleDTO> getListVehiclesDTO() {
    return listVehiclesDTO;
  }

  public void setListVehiclesDTO(List<VehicleDTO> listVehiclesDTO) {
    this.listVehiclesDTO = listVehiclesDTO;
  }

  public QuoteDialerDTO party(PartyDTO party) {
    this.party = party;
    return this;
  }
  
  public PartyDTO getParty() {
    return party;
  }

  public void setParty(PartyDTO party) {
    this.party = party;
  }

  public QuoteDialerDTO quoteAppEnum(QuoteAppEnumEnum quoteAppEnum) {
    this.quoteAppEnum = quoteAppEnum;
    return this;
  }
  
  public QuoteAppEnumEnum getQuoteAppEnum() {
    return quoteAppEnum;
  }

  public void setQuoteAppEnum(QuoteAppEnumEnum quoteAppEnum) {
    this.quoteAppEnum = quoteAppEnum;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    QuoteDialerDTO quoteDialerDTO = (QuoteDialerDTO) o;
    return StringUtils.equals(this.id, quoteDialerDTO.id) &&
        this.creationTimeStamp.equals(quoteDialerDTO.creationTimeStamp) &&
        this.inceptionDate.equals(quoteDialerDTO.inceptionDate) &&
        StringUtils.equals(this.distributorNumber, quoteDialerDTO.distributorNumber) &&
        this.lineofBusiness.equals(quoteDialerDTO.lineofBusiness) &&
        StringUtils.equals(this.quoteNumber, quoteDialerDTO.quoteNumber) &&
        this.quoteSource.equals(quoteDialerDTO.quoteSource) &&
        StringUtils.equals(this.userName, quoteDialerDTO.userName) &&
        this.listRelatedQuotes.equals(quoteDialerDTO.listRelatedQuotes) &&
        this.datasourceOrigin.equals(quoteDialerDTO.datasourceOrigin) &&
        this.sourceUnderwritingCompany.equals(quoteDialerDTO.sourceUnderwritingCompany) &&
        this.phone.equals(quoteDialerDTO.phone) &&
        this.listVehiclesDTO.equals(quoteDialerDTO.listVehiclesDTO) &&
        this.party.equals(quoteDialerDTO.party) &&
        this.quoteAppEnum.equals(quoteDialerDTO.quoteAppEnum);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QuoteDialerDTO {\n");
    
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    creationTimeStamp: ").append(toIndentedString(creationTimeStamp)).append("\n");
    sb.append("    inceptionDate: ").append(toIndentedString(inceptionDate)).append("\n");
    sb.append("    distributorNumber: ").append(toIndentedString(distributorNumber)).append("\n");
    sb.append("    lineofBusiness: ").append(toIndentedString(lineofBusiness)).append("\n");
    sb.append("    quoteNumber: ").append(toIndentedString(quoteNumber)).append("\n");
    sb.append("    quoteSource: ").append(toIndentedString(quoteSource)).append("\n");
    sb.append("    userName: ").append(toIndentedString(userName)).append("\n");
    sb.append("    listRelatedQuotes: ").append(toIndentedString(listRelatedQuotes)).append("\n");
    sb.append("    datasourceOrigin: ").append(toIndentedString(datasourceOrigin)).append("\n");
    sb.append("    sourceUnderwritingCompany: ").append(toIndentedString(sourceUnderwritingCompany)).append("\n");
    sb.append("    phone: ").append(toIndentedString(phone)).append("\n");
    sb.append("    listVehiclesDTO: ").append(toIndentedString(listVehiclesDTO)).append("\n");
    sb.append("    party: ").append(toIndentedString(party)).append("\n");
    sb.append("    quoteAppEnum: ").append(toIndentedString(quoteAppEnum)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}