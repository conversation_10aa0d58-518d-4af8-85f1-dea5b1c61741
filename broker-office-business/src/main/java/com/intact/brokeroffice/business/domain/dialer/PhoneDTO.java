package com.intact.brokeroffice.business.domain.dialer;

import org.apache.commons.lang3.StringUtils;

public class PhoneDTO {
    private String areaCode = null;
    private String extension = null;
    private String phoneNumber = null;

    public PhoneDTO() {
    }

    public PhoneDTO areaCode(String areaCode) {
        this.areaCode = areaCode;
        return this;
    }

    public String getAreaCode() {
        return this.areaCode;
    }

    public void setAreaCode(String areaCode) {
        this.areaCode = areaCode;
    }

    public PhoneDTO extension(String extension) {
        this.extension = extension;
        return this;
    }

    public String getExtension() {
        return this.extension;
    }

    public void setExtension(String extension) {
        this.extension = extension;
    }

    public PhoneDTO phoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
        return this;
    }

    public String getPhoneNumber() {
        return this.phoneNumber;
    }

    public void setPhoneNumber(String phoneNumber) {
        this.phoneNumber = phoneNumber;
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else if (o != null && this.getClass() == o.getClass()) {
            PhoneDTO phoneDTO = (PhoneDTO)o;
            return StringUtils.equals(this.areaCode, phoneDTO.areaCode) && StringUtils.equals(this.extension, phoneDTO.extension) 
            		&& StringUtils.equals(this.phoneNumber, phoneDTO.phoneNumber);
        } else {
            return false;
        }
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class PhoneDTO {\n");
        sb.append("    areaCode: ").append(this.toIndentedString(this.areaCode)).append("\n");
        sb.append("    extension: ").append(this.toIndentedString(this.extension)).append("\n");
        sb.append("    phoneNumber: ").append(this.toIndentedString(this.phoneNumber)).append("\n");
        sb.append("}");
        return sb.toString();
    }

    private String toIndentedString(Object o) {
        return o == null ? "null" : o.toString().replace("\n", "\n    ");
    }
}

