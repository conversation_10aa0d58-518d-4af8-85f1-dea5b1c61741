package com.intact.brokeroffice.business.domain.dialer;

import org.apache.commons.lang3.StringUtils;

/**
 * PartyDTO
 */
public class PartyDTO   {
  private String firstName = null;
  private String lastName = null;
  private String companyName = null;
  private String province = null;

  public PartyDTO firstName(String firstName) {
    this.firstName = firstName;
    return this;
  }

  public String getFirstName() {
    return firstName;
  }

  public void setFirstName(String firstName) {
    this.firstName = firstName;
  }

  public PartyDTO lastName(String lastName) {
    this.lastName = lastName;
    return this;
  }

  public String getLastName() {
    return lastName;
  }

  public void setLastName(String lastName) {
    this.lastName = lastName;
  }

  public PartyDTO companyName(String companyName) {
    this.companyName = companyName;
    return this;
  }

  public String getCompanyName() {
    return companyName;
  }

  public void setCompanyName(String companyName) {
    this.companyName = companyName;
  }

  public PartyDTO province(String province) {
    this.province = province;
    return this;
  }

  public String getProvince() {
    return province;
  }

  public void setProvince(String province) {
    this.province = province;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    PartyDTO partyDTO = (PartyDTO) o;
    return StringUtils.equals(this.firstName, partyDTO.firstName) &&
    		StringUtils.equals(this.lastName, partyDTO.lastName) &&
    		StringUtils.equals(this.companyName, partyDTO.companyName) &&
    		StringUtils.equals(this.province, partyDTO.province);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class PartyDTO {\n");
    
    sb.append("    firstName: ").append(toIndentedString(firstName)).append("\n");
    sb.append("    lastName: ").append(toIndentedString(lastName)).append("\n");
    sb.append("    companyName: ").append(toIndentedString(companyName)).append("\n");
    sb.append("    province: ").append(toIndentedString(province)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

