package com.intact.brokeroffice.business.quote.impl;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.intact.brokeroffice.business.domain.Quote;
import com.intact.brokeroffice.business.exception.BusinessException;
import com.intact.business.service.broker.domain.IPolicy;
import com.intact.business.service.broker.domain.IUserContext;

public class HomeUploadProcessor extends DefaultUploadProcessor {

	@Override
	public IPolicy execute(Quote quote, String subBrokerNumber) throws Exception {
		return super.execute(quote, subBrokerNumber);
	}

	@Override
	public boolean isQuickQuote(IUserContext context, Quote quote) throws BusinessException {
		return CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber().equals(this.getSystemDAO().getCompany());
	}

}
