package com.intact.brokeroffice.business.quote;

import java.util.Date;

import com.ing.canada.plp.domain.enums.UserActivityTypeCodeEnum;

/**
 * The Class NoteBean.
 */
public class NoteBean {
	
	/** The account. */
	private String account;
	
	/** The note. */
	private String note;
	
	/** The date. */
	private Date date;
	
	/** The activity. */
	private UserActivityTypeCodeEnum activity;


	/**
	 * Instantiates a new note bean.
	 */
	public NoteBean(){
		//default constructor
	}

	/**
	 * Instantiates a new note bean.
	 * 
	 * @param aNote the note
	 * @param aDate the date
	 */
	public NoteBean(String aNote, Date aDate){
		this.note = aNote;
		this.date = aDate;
	}

	/**
	 * Gets the date.
	 * 
	 * @return the date
	 */
	public Date getDate() {
		return this.date;
	}
	
	/**
	 * Sets the date.
	 * 
	 * @param aDate the new date
	 */
	public void setDate(Date aDate) {
		this.date = aDate;
	}

	/**
	 * Sets the note.
	 * 
	 * @param aNote the new note
	 */
	public void setNote(String aNote) {
		this.note = aNote;
	}

	/**
	 * Gets the note.
	 * 
	 * @return the note
	 */
	public String getNote() {
		return this.note;
	}
	
	/**
	 * Gets the account.
	 * 
	 * @return the account
	 */
	public String getAccount() {
		return this.account;
	}

	/**
	 * Sets the account.
	 * 
	 * @param anAccount the new account
	 */
	public void setAccount(String anAccount) {
		this.account = anAccount;
	}

	/**
	 * Gets the activity.
	 * 
	 * @return the activity
	 */
	public UserActivityTypeCodeEnum getActivity() {
		return this.activity;
	}

	/**
	 * Sets the activity.
	 * 
	 * @param anActivity the new activity
	 */
	public void setActivity(UserActivityTypeCodeEnum anActivity) {
		this.activity = anActivity;		
	}	
}
