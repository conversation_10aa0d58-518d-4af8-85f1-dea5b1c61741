package com.intact.brokeroffice.business.domain;

import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;

/**
 * ErrorDTO
 */
public class DialerErrorDTO   {
  private String code = null;
  
  private String description = null;

  public DialerErrorDTO code(String code) {
    this.code = code;
    return this;
  }

  public String getCode() {
    return code;
  }

  public void setCode(String code) {
    this.code = code;
  }

  public DialerErrorDTO description(String description) {
    this.description = description;
    return this;
  }

  public String getDescription() {
    return description;
  }

  public void setDescription(String description) {
    this.description = description;
  }


  @Override
  public boolean equals(java.lang.Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DialerErrorDTO errorDTO = (DialerErrorDTO) o;
    return StringUtils.equals(this.code, errorDTO.code) &&
		StringUtils.equals(this.description, errorDTO.description);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class ErrorDTO {\n");
    
    sb.append("    code: ").append(toIndentedString(code)).append("\n");
    sb.append("    description: ").append(toIndentedString(description)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(java.lang.Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

