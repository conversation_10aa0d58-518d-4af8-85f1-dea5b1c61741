package com.intact.brokeroffice.business.domain.dialer;

import java.util.ArrayList;

import com.intact.brokeroffice.business.domain.DialerErrorDTO;

public class ListDialerErrorDTO extends ArrayList<DialerErrorDTO> {
    public ListDialerErrorDTO() {
    }

    public boolean equals(Object o) {
        if (this == o) {
            return true;
        } else {
            return o != null && this.getClass() == o.getClass() ? super.equals(o) : false;
        }
    }

    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("class ListErrorDTO {\n");
        sb.append("    ").append(this.toIndentedString(super.toString())).append("\n");
        sb.append("}");
        return sb.toString();
    }

    private String toIndentedString(Object o) {
        return o == null ? "null" : o.toString().replace("\n", "\n    ");
    }
}
