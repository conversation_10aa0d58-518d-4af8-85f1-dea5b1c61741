package com.intact.brokeroffice.business.common;

import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;



import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.plp.domain.enums.LineOfInsuranceCodeEnum;
import com.ing.canada.plp.report.insurancePolicy.criteria.QuoteSearchCriteria;
import org.apache.commons.collections4.CollectionUtils;

public class LineOfInsuranceUtils {


	/**
	 * Convert the {@link LineOfBusinessEnum} to a set of equivalent {@link LineOfInsuranceCodeEnum}
	 * @param set Set of {@link LineOfBusinessEnum} to be converted
	 * @return Converted set of equivalent {@link LineOfInsuranceCodeEnum} based on the provided set of {@link LineOfBusinessEnum}
	 */
	public static Set<LineOfInsuranceCodeEnum> toLinesOfInsurance(Set<LineOfBusinessEnum> set) {
		Set<LineOfInsuranceCodeEnum> result = null;

		if(CollectionUtils.isNotEmpty(set)) {
			result = new HashSet<>(set.size());

			if(set.contains(LineOfBusinessEnum.PERSONAL_LINE)) {
				result.add(LineOfInsuranceCodeEnum.AUTOMOBILE);
				result.add(LineOfInsuranceCodeEnum.RESIDENTIAL);
			}

			if(set.contains(LineOfBusinessEnum.COMMERCIAL_LINE)) {
				result.add(LineOfInsuranceCodeEnum.AUTOMOBILE);
			}
		}

		return result == null ? Collections.<LineOfInsuranceCodeEnum>emptySet() : result;
	}
	
	/**
	 * Convert an array string representation of lines of insurance codes to the equivalent {@link LineOfInsuranceCodeEnum} instance 
	 * @param linesOfInsurance  The array of codes to be converted to enum.
	 * @return Converted set of equivalent {@link LineOfInsuranceCodeEnum} based on the provided array string 
	 */
	public static Set<LineOfInsuranceCodeEnum> toLinesOfInsurance(String[] linesOfInsurance, QuoteSearchCriteria criteria) {
		Set<LineOfInsuranceCodeEnum> result = null;

		if(linesOfInsurance != null) {

			result = new HashSet<LineOfInsuranceCodeEnum>(linesOfInsurance.length);

			for(String val : linesOfInsurance) {
				LineOfInsuranceCodeEnum value = LineOfInsuranceCodeEnum.valueOfCode(val);
				if(value != null && (criteria.getBusinessName() == null || value.equals(LineOfInsuranceCodeEnum.AUTOMOBILE))) {
					result.add(value);
				}
			}
		}

		return CollectionUtils.isEmpty(result) ? Collections.<LineOfInsuranceCodeEnum>emptySet() : result;
	}
	
	/**
	 * Array representation of the equivalent code values of the provided collection of {@link LineOfInsuranceCodeEnum} 
	 * @param values The collection of {@link LineOfInsuranceCodeEnum} from which to derive the code values
	 * @return A String array of the code values of the provided collection of {@link LineOfInsuranceCodeEnum}
	 */
	public static String[] toStringArray(Set<LineOfInsuranceCodeEnum> values) {
		if(CollectionUtils.isEmpty(values)) {
			return null;
		}

		List<String> list = new ArrayList<>(values.size());
		for(LineOfInsuranceCodeEnum val : values) {
			if(val != null) {
				list.add(val.getCode());
			}
		}

		return list.toArray(new String[list.size()]);
	}
}

