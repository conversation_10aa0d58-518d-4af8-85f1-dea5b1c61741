<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
  ~ without the written permission of Intact Insurance
  ~
  ~ Copyright (c) 2013 Intact Insurance, All rights reserved.
  -->
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:jee="http://www.springframework.org/schema/jee"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
						http://www.springframework.org/schema/context
						http://www.springframework.org/schema/context/spring-context-3.0.xsd
						http://www.springframework.org/schema/jee
						http://www.springframework.org/schema/jee/spring-jee-3.0.xsd">

	<import resource="classpath:business-service-broker.xml" />
	<import resource="classpath:tools-multithreading-service.xml" />
	<import resource="classpath:broker-office-aop.xml" />
	<import resource="classpath:tools-template-service.xml" />
	<import resource="classpath:tools-logging-service.xml" />
	<import resource="classpath:tools-cache-service.xml" />

	<context:annotation-config />
	<context:component-scan base-package="com.intact.brokeroffice.business" />
	<context:component-scan base-package="com.intact.brokeroffice.service" />
	<context:component-scan base-package="com.intact.uploadtosavers" />

	<!-- Resolve LDAP properties -->
	<bean id="ldapPlaceholderConfig" class="org.springframework.context.support.PropertySourcesPlaceholderConfigurer">
		<property name="location" value="classpath:ldap.properties" />
		<property name="placeholderPrefix" value="?[" />
		<property name="placeholderSuffix" value="]" />
	</bean>

	<!-- Configure LDAP context -->
	<bean id="contextSource" class="org.springframework.ldap.core.support.LdapContextSource">
		<property name="url" value="?[LDAP_URL]:?[LDAP_PORT]" />
		<property name="userDn" value="?[LDAP_USER_DN]" />
		<property name="password" value="?[LDAP_PASSWORD]" />
	</bean>

	<bean id="ldapTemplate" class="com.intact.tools.ldap.util.LdapTemplate">
		<constructor-arg ref="contextSource" />
	</bean>

	<bean id="ldap-base-dn" class="java.lang.String">
		<constructor-arg value="?[LDAP_BASE_DN]" />
	</bean>

	<bean id="accountsBusinessProcess" class="com.intact.brokeroffice.business.accounts.impl.AccountsBusinessProcess">
		<property name="ldapTemplate" ref="ldapTemplate" />
	</bean>

	<bean id="quote.builders" class="com.intact.brokeroffice.service.util.Configuration">
	    <property name="configs">
		        <map key-type="java.lang.String" value-type="com.intact.brokeroffice.business.quote.util.QuotesBeanBuilder">
		            <entry key="AU">
		            	<bean class="com.intact.brokeroffice.business.quote.util.AutoQuotesBeanBuilder" />
		          	</entry>
		            <entry key="RE">
		            	<bean class="com.intact.brokeroffice.business.quote.util.QuotesBeanBuilder" />
		          	</entry>
		          	<entry key="CP">
		            	<bean class="com.intact.brokeroffice.business.quote.util.QuotesBeanBuilder" />
		          	</entry>
		        </map>
		</property>
	</bean>


	<bean id="uploadProcessors" class="com.intact.brokeroffice.service.util.Configuration">
	    <property name="configs">
		        <map key-type="java.lang.String" value-type="com.intact.brokeroffice.business.quote.UploadProcessor">
		            <entry key="AU">
		            	<bean class="com.intact.brokeroffice.business.quote.impl.AutoUploadProcessor" />
		          	</entry>
		            <entry key="CP">
		            	<bean class="com.intact.brokeroffice.business.quote.impl.AutoUploadProcessor" />
		          	</entry>
		            <entry key="RE">
		            	<bean class="com.intact.brokeroffice.business.quote.impl.HomeUploadProcessor" />
		          	</entry>
		        </map>
		</property>
	</bean>

	<bean id="exception.service.business" class="com.intact.brokeroffice.business.exception.BusinessException" lazy-init="false">
		<property name="errors">
			<map>
				<entry key="exception.broker.office.business.exec.execute" value="An error occured while trying to upload the quote to mainframe using the upload info object [{1}]. The cause is {0}." />
			</map>
		</property>
	</bean>

	<bean id="restTemplate" class="org.springframework.web.client.RestTemplate"/>

</beans>
