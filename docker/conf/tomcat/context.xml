<Context>
	<!-- Define datasources (should match JNDI name for lookup) -->
	<Resource
			name="jdbc/plpolicy"
			auth="Container"
			type="javax.sql.DataSource"
			maxTotal="50"
			maxIdle="0"
			maxWaitMillis="180000"
			username="${DB_PLP_USERNAME}"
			password="${DB_PLP_PASSWORD}"
			driverClassName="oracle.jdbc.OracleDriver"
			url="${DB_PLP_URL}"/>

	<Resource
			name="jdbc/brm"
			auth="Container"
			type="javax.sql.DataSource"
			maxTotal="25"
			maxIdle="0"
			maxWaitMillis="180000"
			username="${DB_BRM_USERNAME}"
			password="${DB_BRM_PASSWORD}"
			driverClassName="oracle.jdbc.OracleDriver"
			url="${DB_BRM_URL}"/>

	<Resource
			name="jdbc/CIF"
			auth="Container"
			type="javax.sql.DataSource"
			maxTotal="25"
			maxIdle="0"
			maxWaitMillis="180000"
			username="${DB_CIF_USERNAME}"
			password="${DB_CIF_PASSWORD}"
			driverClassName="oracle.jdbc.OracleDriver"
			url="${DB_CIF_URL}"/>

	<Resource
			name="jdbc/SAD"
			auth="Container"
			type="javax.sql.DataSource"
			maxTotal="10"
			maxIdle="1"
			maxWaitMillis="180000"
			username="${DB_SAD_USERNAME}"
			password="${DB_SAD_PASSWORD}"
			driverClassName="oracle.jdbc.OracleDriver"
			url="${DB_SAD_URL}"/>

	<Resource
			name="jdbc/dw"
			auth="Container"
			type="javax.sql.DataSource"
			maxTotal="25"
			maxIdle="0"
			maxWaitMillis="180000"
			username="${DB_DW_USERNAME}"
			password="${DB_DW_PASSWORD}"
			driverClassName="oracle.jdbc.OracleDriver"
			url="${DB_DW_URL}"/>

	<!-- Add SameSite to all cookies sent by the server -->
	<CookieProcessor sameSiteCookies="strict"/>
</Context>
