# Actuator Endpoints Troubleshooting Guide

## Issue: ClassNotFoundException: org.springframework.core.annotation.AnnotatedMethod

### Problem
When starting Tomcat, you get `java.lang.ClassNotFoundException: org.springframework.core.annotation.AnnotatedMethod`.

### Root Cause
This error occurs due to Spring Framework version compatibility issues when using Spring MVC annotations with Spring 6.0.23.

### Solution Applied: Servlet-Based Approach

Instead of using Spring MVC controllers, we've implemented a servlet-based approach that's more compatible with the existing JSF application.

#### 1. Replaced Spring MVC with Direct Servlets
**File**: `broker-office-web/src/main/webapp/WEB-INF/web.xml`

```xml
<!-- Actuator Health Servlet -->
<servlet>
    <servlet-name>actuatorHealthServlet</servlet-name>
    <servlet-class>com.intact.brokeroffice.actuator.servlet.ActuatorHealthServlet</servlet-class>
    <load-on-startup>2</load-on-startup>
</servlet>

<!-- Actuator Info Servlet -->
<servlet>
    <servlet-name>actuatorInfoServlet</servlet-name>
    <servlet-class>com.intact.brokeroffice.actuator.servlet.ActuatorInfoServlet</servlet-class>
    <load-on-startup>2</load-on-startup>
</servlet>

<!-- Actuator Metrics Servlet -->
<servlet>
    <servlet-name>actuatorMetricsServlet</servlet-name>
    <servlet-class>com.intact.brokeroffice.actuator.servlet.ActuatorMetricsServlet</servlet-class>
    <load-on-startup>2</load-on-startup>
</servlet>

<!-- Servlet mappings -->
<servlet-mapping>
    <servlet-name>actuatorHealthServlet</servlet-name>
    <url-pattern>/actuator/health</url-pattern>
</servlet-mapping>
<servlet-mapping>
    <servlet-name>actuatorInfoServlet</servlet-name>
    <url-pattern>/actuator/info</url-pattern>
</servlet-mapping>
<servlet-mapping>
    <servlet-name>actuatorMetricsServlet</servlet-name>
    <url-pattern>/actuator/metrics</url-pattern>
</servlet-mapping>
```

#### 2. Created Spring MVC Configuration
**File**: `broker-office-web/src/main/webapp/WEB-INF/actuator-servlet.xml`

```xml
<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:mvc="http://www.springframework.org/schema/mvc"
       xmlns:context="http://www.springframework.org/schema/context">
    
    <!-- Enable Spring MVC annotations -->
    <mvc:annotation-driven />
    
    <!-- Component scan for actuator controllers -->
    <context:component-scan base-package="com.intact.brokeroffice.actuator" />
    
    <!-- JSON message converters -->
    <mvc:annotation-driven>
        <mvc:message-converters>
            <bean class="org.springframework.http.converter.json.MappingJackson2HttpMessageConverter"/>
        </mvc:message-converters>
    </mvc:annotation-driven>
</beans>
```

#### 3. Updated Controllers to Use @RestController
Changed all actuator controllers from `@Controller` to `@RestController` and removed `@ResponseBody` annotations.

**Before:**
```java
@Controller
@RequestMapping("/actuator")
public class HealthController {
    @GetMapping("/health")
    @ResponseBody
    public ResponseEntity<Map<String, Object>> health() {
        // ...
    }
}
```

**After:**
```java
@RestController
@RequestMapping("/actuator")
public class HealthController {
    @GetMapping("/health")
    public ResponseEntity<Map<String, Object>> health() {
        // ...
    }
}
```

## Testing the Fix

### 1. Test Basic Connectivity
```bash
curl http://localhost:8080/webzone/actuator/test
```
**Expected Response:**
```json
{
  "status": "OK",
  "message": "Actuator endpoints are working!",
  "timestamp": "2025-01-21T15:30:00Z"
}
```

### 2. Test Health Endpoint
```bash
curl http://localhost:8080/webzone/actuator/health
```

### 3. Test Info Endpoint
```bash
curl http://localhost:8080/webzone/actuator/info
```

## Common Issues and Solutions

### Issue: JSON Not Returned Properly
**Symptom**: Endpoints return HTML error pages instead of JSON
**Solution**: Ensure `MappingJackson2HttpMessageConverter` is configured in `actuator-servlet.xml`

### Issue: 500 Internal Server Error
**Symptom**: Endpoints return 500 errors
**Possible Causes**:
1. Missing Jackson dependencies
2. DataSource injection issues
3. Missing Spring MVC dependencies

**Check**:
1. Verify Jackson is in classpath
2. Check application logs for specific errors
3. Ensure all required Spring dependencies are available

### Issue: Security Filter Blocking Requests
**Symptom**: 403 Forbidden errors
**Solution**: Check `ActuatorSecurityFilter` configuration and ensure it's properly mapped

### Issue: Context Loading Problems
**Symptom**: Application fails to start
**Possible Causes**:
1. XML configuration errors
2. Component scan conflicts
3. Bean definition issues

**Debug Steps**:
1. Check application startup logs
2. Verify XML syntax in `actuator-servlet.xml`
3. Ensure no duplicate bean definitions

## Architecture Overview

```
Request: /webzone/actuator/health
    ↓
Tomcat receives request
    ↓
web.xml routes /actuator/* to DispatcherServlet
    ↓
DispatcherServlet loads actuator-servlet.xml
    ↓
Spring MVC finds @RestController in actuator package
    ↓
HealthController.health() method executed
    ↓
JSON response returned via Jackson converter
```

## Verification Checklist

- [ ] DispatcherServlet configured in web.xml
- [ ] actuator-servlet.xml exists and is valid
- [ ] Controllers use @RestController annotation
- [ ] Jackson dependencies available
- [ ] Component scanning includes actuator package
- [ ] No conflicting URL mappings
- [ ] Application starts without errors
- [ ] Test endpoint returns JSON response

## Next Steps

Once the basic setup is working:

1. **Enable Security**: Configure proper authentication for production
2. **Add Monitoring**: Integrate with monitoring tools like Prometheus
3. **Custom Metrics**: Add application-specific metrics
4. **Health Checks**: Add more comprehensive health checks
5. **Documentation**: Update API documentation with endpoint details
