
create PACKAGE BODY          SEARCH_QUOTE IS
-- ===============================================================================================
  --  Procedure     : search_quote_broker
  --  Created by    : <PERSON><PERSON><PERSON>
  --  Creation Date : 2010-11-01
  --  Desc.         : Dynamic SQL search for valid quotations.
  --  -----------+-----------+------------+---------------------------------------------------------
  --  Date Modif | Reference | Who        | Comment
  --  -----------+-----------+------------+---------------------------------------------------------
  --  2011-01-12 |           | <PERSON><PERSON> | Initial Creation
  --  2011-07-08 |           | E.Gauthier | Added the consent indicator to make sure we only have
  --             |           |            | people that gave their consent to be contacted
  --  2011-07-18 |  PT01     | P.Tremblay | Added a new parameter to identify the external system
  --             |           |            | making the research
  --  2011-08-26 |  RL01     | R.Lapointe | Added a condition on the type 'PR' on the table CONSENT
  --  2012-01-05 |  PT02     | P.Tremblay | Added a new parameter T_USER to identify the user making the query.
  --             |           |            | If this parameter is not NULL, the query must apply some security
  --             |           |            | condition in order to return only data that the user has access to
  --  2012-01-17 |  PT03     | P.Tremblay | Added new parameters T_PROVINCE, T_MANUF_COMPANY and T_SUB_COMPANY_NBR
  --  2012-04-16 |  PT04     | P.Tremblay | It fix the WHERE condition that fail when phone number criteria is selected
  --  2012-04-18 |  RL02     | R.Lapointe | RANB-13 - Webzone Improvement
  --  2012-04-17 |  PT05     | P.Tremblay | Modified the condition for searching quotation with status of :  
  --             |           |            | Puchase Completed and Upload Accepted  
  --  2012-04-26 |  PT06     | P.Tremblay | Added two new parameters T_CREATION_DATE_FROM and T_CREATION_DATE_TO   
  --             |           |            | 
  --  2012-04-26 |  PT07     | P.Tremblay | Modification for Sierra Release.   
  --             |           |            | 
  --  2012-05-31 |  PT08     | P.Tremblay | Modification for Blue Mountain. Change the ORDER BY clause    
  --             |           |            | 
  --  2012-07-19 |  PT09     | P.Tremblay | Correction dans la condition liée au statut UPL      
  --             |           |            | 
  --  2012-07-23 |  PT10     | P.Tremblay | Modification de la limitte de rangées retournées par la recherche (Victor Angheluta)   
  --             |           |            |
  --  2013-04-10 |  HS01     | H.Savard   | Projet AB Migration (ajout du retour du champ cvi_pure_with_credit_qty pour le 
  --             |           |            | véhicule 1 et 2.  Aussi ajout du lien avec le manufacturier avec les tables
  --             |           |            | sub_brokers et broker_infos.
  --  2013-05-29 |  HS02     | H.Savard   | Projet Migration11g changement du nom du dblink pour brmadmin   
  --  2013-07-16 |  KH01     | K.Hounnou  | Modification to fix (name apostrophe) bug in search_quote 
  --  2014-01-08 |  HS03     | H.Savard   | Optimisation de la requête suite au PM11236
  --  2014-09-11 |  HS04     | H.Savard   | Inconsistance entre l'interrogation de status et l'affichage PM13858
  --             |           |            | Utilisation du nouveau champ search indexer au lieu de last_name et first_name
  --  2015-01-28 |  HS05     | H.Savard   | Ajout des champs postal code et birth_date en retour dans le cursor pour le
  --             |           |            | Quick Quote/WebZone dans Queen
  --  2015-04-17 |  HS06     | H.Savard   | Amélioration de la performance dans le WITH avec la note de police et
  --             |           |            | tenir compte des valeurs 'NONE' dans la colonne quick_quote_web_access_type +
  --             |           |            | nouveaux requis pour avoir le dernier activity code de la quote (bta.bsns_trx_activity)
  --  2015-06-08 |  HS07     | H.Savard   | Modif pour sorir jusaqu'à 500 rows quand c'est pour la cie 'A'' et pour l'affichage des 
  --             |           |            | Quick Quote
  --  2016-04-07 |  HS08     | H.Savard   | Ajout de la classe de Vehicule dans le select.
  --  2016-11-28 |  HS09     | H.Savard   | Optimization et ajout paramètre pour le projet sharp
  --  2017-03-03 |  HS10     | H.Savard   | Ajout dans le select du nouveau champ fsa_broker_locators.intact_rank dans le cadre de 
  --             |           |            | Evolution.
  --  2017-03-03 |  HS11     | H.Savard   | Ajout d'un indicateur de présence de document driver licence
  --  2017-06-02 |  JG12     | J.Giroux   | Ajout de l'attribut plpadmin.party.unstructured_name_txt dans le resultset (out cursor)
  --  2017-06-02 |  JG13     | J.Giroux   | Ajout de la table plpadmin.insurance_policy dans la liste des tables de la requete principale
  --  2017-06-02 |  JG14     | J.Giroux   | Ajout de l'attribut I'm gonna wait for GH Actionplpadmin.insurance_policy.line_of_business_cd dans le resultset (out cursor)
  --  2017-06-29 |  JG15     | J.Giroux   | Ajout des parametres t_unstructured_name et t_line_of_business_cd
  --  2017-12-21 |  VM05     | V.McNicoll | Ajout du parametre t_inactive_pos_only 
  --  2018-02-12 |  VM09     | V.McNicoll | Correction Task 176497 SEARCH_QUOTE : follow-up status parameter not working as expected
  --  2018-03-29 |  KH02     | K.Hounnou  | RTC181199-Decommissionnement de certains champs de cifadmin.sub_brokers. 
  --  2018-05-17 |  KH03     | K.Hounnou  | RTC202184-probleme de lenteur dans WZ suite à la modification du pkg PLPADMIN.SEARCH_QUOTE lors de la Decom.
  --             |           |            | Rajouter la nouvelle table contextuel_sub_broker_gn_infos dans la table de travail WRK_BROKER_WEB_ACCESS afin d'eviter de l'appeler dans les requêtes internes  
  --  2018-08-01 |  KH04     | K.Hounnou  | RTC213676- restreindre la query du selected_point_of_sale a point_of_sale not null and quote is null et enlever la condition de t_province = 'QC' - Proc returns too many quotes when searching with owner and point of sale.
  --  -----------+-----------+------------+---------------------------------------------------------
  --  NB : RTC202184 - La modification du package SEARCH_QUOTE est tres sensible pour la performance de l'application Webzone, il est recommandé 
  --                   de ne pas rajouter de futures tables si possible dans ce package. Une refonte optimale est nécessaire. 
  --                   Tenir compte de la job Control-M PDPSWPURT01 /apps/dev_distribue/prod/DB_DPS/generic/purge_tables/ksh/purge_tables_main.ksh 
  --                   il est possible de diminuer la période de purge de la table PLPADMIN.SEARCH_WZ (actuellement à 6 mois).   
  -- ===============================================================================================================================

-- VM05 Définition de SP avec 26 PARAMETRES. 
PROCEDURE search_quote_broker
(
     t_search_return             OUT NOCOPY SYS_REFCURSOR
    ,t_selected_master            IN VARCHAR2 
    ,t_selected_point_of_sale     IN VARCHAR2 
    ,t_quote_ref_number           IN VARCHAR2 
    ,t_quote_ref_legacy_number    IN VARCHAR2 
    ,t_postal_code                IN VARCHAR2
    ,t_telephone                  IN VARCHAR2 
    ,t_last_name                  IN VARCHAR2 
    ,t_first_name                 IN VARCHAR2 
    ,t_last_update_from           IN VARCHAR2
    ,t_last_update_to             IN VARCHAR2 
    ,t_email                      IN VARCHAR2 
    ,t_selected_client_followUp   IN VARCHAR2 
    ,t_selected_quote_status      IN VARCHAR2 
    ,t_external_system_origin     IN VARCHAR2 
    ,t_user                       IN VARCHAR2 
    ,t_province                   IN VARCHAR2 
    ,t_manuf_company              IN VARCHAR2 
    ,t_sub_company_nbr            IN VARCHAR2 
    ,t_creation_date_from         IN VARCHAR2 
    ,t_creation_date_to           IN VARCHAR2 
    ,t_cancelled_broker           IN VARCHAR2 
    ,t_token_ind                  IN VARCHAR2 
    ,t_unstructured_name          IN VARCHAR2 
    ,t_line_of_business_cd        IN VARCHAR2 
)
IS
   l_inactive_pos_only            VARCHAR2(1) := NULL;  --VM05
begin

-- Appel procedure avec 27 PARAMETRES   
   search_quote_broker
   (
    t_search_return,
    t_selected_master,
    t_selected_point_of_sale,
    t_quote_ref_number,
    t_quote_ref_legacy_number,
    t_postal_code,
    t_telephone,
    t_last_name,
    t_first_name,
    t_last_update_from,
    t_last_update_to,
    t_email,
    t_selected_client_followUp,
    t_selected_quote_status,
    t_external_system_origin,
    t_user,
    t_province,
    t_manuf_company,
    t_sub_company_nbr,
    t_creation_date_from,
    t_creation_date_to,
    t_cancelled_broker,
    t_token_ind,
    t_unstructured_name,
    t_line_of_business_cd,
    l_inactive_pos_only  --VM05
   );

end;

-- VM05 2018-01-12
-- Cette signature contient tous les paramètres permisent.
-- 27 PARAMETRES
PROCEDURE search_quote_broker
(
     t_search_return             OUT NOCOPY SYS_REFCURSOR
    ,t_selected_master            IN VARCHAR2 
    ,t_selected_point_of_sale     IN VARCHAR2 
    ,t_quote_ref_number           IN VARCHAR2 
    ,t_quote_ref_legacy_number    IN VARCHAR2 
    ,t_postal_code                IN VARCHAR2
    ,t_telephone                  IN VARCHAR2 
    ,t_last_name                  IN VARCHAR2 
    ,t_first_name                 IN VARCHAR2 
    ,t_last_update_from           IN VARCHAR2
    ,t_last_update_to             IN VARCHAR2 
    ,t_email                      IN VARCHAR2 
    ,t_selected_client_followUp   IN VARCHAR2 
    ,t_selected_quote_status      IN VARCHAR2 
    ,t_external_system_origin     IN VARCHAR2 
    ,t_user                       IN VARCHAR2 
    ,t_province                   IN VARCHAR2 
    ,t_manuf_company              IN VARCHAR2 
    ,t_sub_company_nbr            IN VARCHAR2 
    ,t_creation_date_from         IN VARCHAR2 
    ,t_creation_date_to           IN VARCHAR2 
    ,t_cancelled_broker           IN VARCHAR2 
    ,t_token_ind                  IN VARCHAR2 
    ,t_unstructured_name          IN VARCHAR2 
    ,t_line_of_business_cd        IN VARCHAR2 
    ,t_inactive_pos_only          IN VARCHAR2  --VM05
)
IS
PRAGMA AUTONOMOUS_TRANSACTION;
  
  vProcess   utiladmin.util_process_errors.process_name%TYPE := 'search_quote_broker' ;

  vFirstName                    VARCHAR2(200);
  vLastName                     VARCHAR2(200);
  vSearchName                   VARCHAR2(200);
  vUnstructuredNname            VARCHAR2(200);
  vLastUpdateFrom               VARCHAR2(100);
  vLastUpdateTo                 VARCHAR2(100);
  vValidationDates              VARCHAR2(300);
  vCreationDateFrom             VARCHAR2(100); -- PT06 
  vCreationDateTo               VARCHAR2(100); -- PT06 
  vCreationDates                VARCHAR2(300); -- PT06 
  vAreaPhone                    VARCHAR2(9);
  vPhoneNbr                     VARCHAR2(9);
  vSelectedQuoteStatus          VARCHAR2(2000);
  vRequest                      VARCHAR2(32100);
  vSelectStatement              VARCHAR2(25000);
  vFromStatement                VARCHAR2(25000);
  vWhereClause                  VARCHAR2(32100); --HS01
  vExternalSystemOrigin         VARCHAR2(250); -- PT01 
  vUserWebAccessSecurity        VARCHAR2(25000); -- PT02 
  vcancelled_broker             VARCHAR2(100); -- HS05
  vTokenInd                     VARCHAR2(200); -- HS09

  v_quote_ref_number            VARCHAR2(200);
  v_quote_ref_legacy_number     VARCHAR2(200);
  v_selected_client_followUp    VARCHAR2(200); 
  v_line_of_business_cd         VARCHAR2(200); 
  v_followUp_list               VARCHAR2(200);
  v_inception_date_from         VARCHAR2(200);
  v_inception_date_to           VARCHAR2(200);
  v_email                       VARCHAR2(200);
  v_postal_code                 VARCHAR2(100);
  v_telephone                   VARCHAR2(200);
  v_selected_point_of_sale      VARCHAR2(400);
  v_synchro_discount            VARCHAR2(400);
  v_manufacturing_context_id    NUMBER(12);     --HS03
  v_quick_quote_web_access      NUMBER(4);
  vExcetionErrorCode            PLS_INTEGER := -20000 ;

  vLeft                         VARCHAR2(32200);

  v_inactive_pos_only           VARCHAR2(400); --VM05
  v_table_inactive_pos_only     VARCHAR2(100); --VM05
  
  v_parenthese_value            NUMBER:= 0;    --KH03

-- *******************************************************************
-- PRIVATE procedures
--********************************************************************

  BEGIN

    -- First thing first !!  Log
    utiladmin.util_procedure_audit_sql.g_init;
    utiladmin.util_procedure_audit_sql.g_procedure_name := $$plsql_unit;

    begin
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_selected_master')          :=  t_selected_master;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_selected_point_of_sale')   :=  t_selected_point_of_sale;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_quote_ref_number')         :=  t_quote_ref_number;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_quote_ref_legacy_number')  :=  t_quote_ref_legacy_number;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_postal_code')              :=  t_postal_code;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_telephone')                :=  t_telephone;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_last_name')                :=  t_last_name;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_first_name')               :=  t_first_name;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_last_update_from')         :=  t_last_update_from;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_last_update_to')           :=  t_last_update_to;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_email')                    :=  t_email;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_selected_client_followUp') :=  t_selected_client_followUp;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_selected_quote_status')    :=  t_selected_quote_status;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_external_system_origin')   :=  t_external_system_origin;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_user')                     :=  t_user;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_province')                 :=  t_province;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_manuf_company')            :=  t_manuf_company;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_sub_company_nbr')          :=  t_sub_company_nbr;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_creation_date_from')       :=  t_creation_date_from;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_creation_date_to')         :=  t_creation_date_to;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_cancelled_broker')         :=  t_cancelled_broker;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_token_ind')                :=  t_token_ind;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_unstructured_name')        :=  t_unstructured_name;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_line_of_business_cd')      :=  t_line_of_business_cd;
      utiladmin.util_procedure_audit_sql.g_sp_parameter('t_inactive_pos_only')        :=  upper(t_inactive_pos_only);  --VM05
	  
	  
    end;

  ---------------------------------------------
  -- Validate mandatory element t_selected_master
  ---------------------------------------------
  -- IF (t_selected_master) IS NULL THEN                                                              -- PT02
  --   raise_application_error(vExcetionErrorCode,'Mandatory Selected master owner is missing');      -- PT02
  -- END IF;                                                                                          -- PT02
  ---------------------------------------------

  -- HS03
  declare
     l_sqltext   varchar2(4000);
  begin
     l_sqltext := 'SELECT manufacturing_context_id FROM plpadmin.manufacturing_context WHERE province_cd = :b1 AND manufacturer_company_cd = :b2';

      execute immediate l_sqltext
         into v_manufacturing_context_id
        using t_province, t_manuf_company
     ;
  exception
    when others then
       v_manufacturing_context_id := 0;
       utiladmin.util_procedure_audit_sql.g_sql_bind_variable('b1') := t_province;
       utiladmin.util_procedure_audit_sql.g_sql_bind_variable('b2') := t_manuf_company;
       utiladmin.util_procedure_audit_sql.g_sql_text                := l_sqltext;
       utiladmin.util_procedure_audit_sql.g_sql_message             := sqlerrm;
       utiladmin.util_procedure_audit_sql.save_error ($$plsql_line);
  end;

  -- RL02 -------------------------------------------------
  vSelectStatement :=
  '
  WITH 
  INSURANCE_POLICY_NOTES_ AS 
  (
    select  insurance_policy_id,insurance_policy_note_id,user_activity_type as activity_type
      from (
              select insurance_policy_id, 
                     insurance_policy_note_id, 
                     user_activity_type,
                     row_number() over (partition by insurance_policy_id, user_activity_type order by insurance_policy_note_id desc) as last_user_activity_type_rn 
                from (
                     select  /*+ opt_param(''optimizer_index_cost_adj'',30) */ 
                             ipn.insurance_policy_id, 
                             ipn.Insurance_policy_note_id, 
                             sw.manufacturing_context_id,
                             case when ipn.user_activity_type_cd in (''VIEW_QUO'', ''UPLOAD_QUO'', ''CHG_FLW_ST'') then ''OTHER'' 
                                  when ipn.user_activity_type_cd in (''ADD_NOTE'')                             then ''NOTE'' 
                             end as user_activity_type
                        from plpadmin.ins_pol_not_mv  ipn, 
                             plpadmin.search_wz       sw 
                       where 1=1
                         and sw.insurance_policy_id      =  ipn.insurance_policy_id 
                         and ipn.user_activity_type_cd   IN (''VIEW_QUO'', ''UPLOAD_QUO'', ''CHG_FLW_ST'',''ADD_NOTE'') 
                         and sw.manufacturer_company_cd  =  ''' || t_manuf_company || '''
                         and sw.manufacturing_context_id =  ' || v_manufacturing_context_id || '
                         and sw.ip_system_create_ts between :b3 and :b4  
                     )
           )  
     where last_user_activity_type_rn = 1
  ) 
  ';


  vSelectStatement := vSelectStatement || 
  q'{
  SELECT ROWNUM RNUM, 
         a.insurance_policy_id,
         a.party_id,
         a.externalSystemOriginCd,
         a.actDte,
         a.actAuthor,
         a.actTypeCde,
         a.ipAgreementNb,
         a.ipAgreementLegacyNbr,
         a.pvSynchro,
         a.sNameLine1,
         a.sbNameLine2,
         a.followUpStatusCd,
         a.followupdte,
         a.followupauthor,
         a.followupcontactnote,
         a.paFname,
         a.paLname,
         a.paBirthdt,
         a.adrPostalCd,
         a.ipQuoteDate,
         a.btLastUpdateTs,
         a.btTransStatusCd,
         a.pvInceptionDt,
         a.ville_sub_broker,
         a.sub_broker_nbr,
         a.sbNameLine3,
         a.langCommCd,
         CASE
            WHEN (SELECT COUNT(cst.party_id)
                    FROM plpadmin.consent cst
                   WHERE cst.party_id= a.party_id
                     AND cst.consent_type_cd = 'QF'
                     AND cst.consent_ind = 'N') >= 1 THEN 'N'
            ELSE 'Y'
         END                                                                         AS consent,
         (SELECT btsaci.attribute_value_txt
            FROM            plpadmin.business_trx_sub_activity ibtsa
                 INNER JOIN plpadmin.bus_trx_sub_actv_comp_inf btsaci  ON ibtsa.business_trx_sub_activity_id = btsaci.business_trx_sub_activity_id
           WHERE ibtsa.business_trx_sub_activity_id = (SELECT MAX(ibtsa2.business_trx_sub_activity_id)
                                                         FROM plpadmin.business_trx_sub_activity ibtsa2
                                                        WHERE ibtsa2.business_trx_activity_id = a.business_trx_activity_id
                                                      )
             AND ibtsa.business_trx_sub_activity_cd = 'ROAD_BLOCK'
             AND ibtsa.business_trx_activity_id     = a.business_trx_activity_id
         )                                                                           AS roadblockMsg,
         (SELECT iro.cvi_pure_with_credit_qty  
            FROM plpadmin.insurance_risk       ir, 
                 plpadmin.insurance_risk_offer iro                                 
           WHERE a.policy_version_id = ir.policy_version_id 
             AND iro.insurance_risk_id               = ir.insurance_risk_id 
             AND iro.insurance_risk_offer_id         = ir.selected_ins_risk_offer_id 
             AND iro.selected_for_pol_offer_rat_ind  = 'Y'
             AND ir.insurance_risk_seq               = 1     -- VEHICLE 1
          )                                                                          AS cviwithcrt_1, 
          (SELECT iro.cvi_pure_with_credit_qty
             FROM plpadmin.insurance_risk       ir, 
                  plpadmin.insurance_risk_offer iro                                 
            WHERE a.policy_version_id                = ir.policy_version_id 
              AND iro.insurance_risk_id               = ir.insurance_risk_id 
              AND iro.insurance_risk_offer_id         = ir.selected_ins_risk_offer_id 
              AND iro.selected_for_pol_offer_rat_ind  = 'Y'                                
              AND ir.insurance_risk_seq               = 2  -- VEHICLE 2
          )                                                                          AS cviwithcrt_2,
          (SELECT ir.vehicle_class_cd 
             FROM plpadmin.insurance_risk ir, 
                  plpadmin.insurance_risk_offer iro 
            WHERE a.policy_version_id  = ir.policy_version_id 
              AND iro.insurance_risk_id = ir.insurance_risk_id 
              AND iro.insurance_risk_offer_id = ir.selected_ins_risk_offer_id 
              AND iro.selected_for_pol_offer_rat_ind  = 'Y' 
              AND ir.insurance_risk_seq = 1 
          )                                                                          AS vehicleclass_1,   -- HS08
          (SELECT ir.vehicle_class_cd 
             FROM plpadmin.insurance_risk ir,
                  plpadmin.insurance_risk_offer iro 
            WHERE a.policy_version_id = ir.policy_version_id 
              AND iro.insurance_risk_id = ir.insurance_risk_id 
              AND iro.insurance_risk_offer_id = ir.selected_ins_risk_offer_id 
              AND iro.selected_for_pol_offer_rat_ind  = 'Y' 
              AND ir.insurance_risk_seq = 2 
          )                                                                          AS vehicleclass_2,   

          trxactcd                                                                   AS TRXACTCD,

          -- HS10 un FSA et BROKER peut avoir 2 ligne car il y a la langue aussi
          -- mais ça sera le meme rank.
          (
           SELECT intact_rank
             FROM cifadmin.fsa_broker_locators   fsa
            WHERE fsa.sub_broker = A.sub_broker
              AND SUBSTR(A.adrPostalCd,1,3) = fsa.fsa
              AND ROWNUM = 1
          )                                                                          AS INTACT_RANK, 

          -- HS11 
          CASE  
             WHEN (SELECT COUNT(1)
                     FROM glbadmin.document                doc,
                          glbadmin.task_document_request   tdr,
                          plpadmin.driver_complement_info  dci
                    WHERE doc.task_document_request_id = tdr.task_document_request_id
                      AND tdr.req_driving_license_nbr  = dci.license_nbr
                      AND doc.mime_type_cd  = 'XML'
                      AND tdr.type_cd       = 'ATPL'
                      AND tdr.req_status_cd = '4'
                      AND tdr.uw_company_cd = 'A'   -- HS10 SOA cie code always A
                      AND A.party_id        = dci.party_id
                  ) >= 1   THEN 'Y'
                           ELSE 'N'
          END                                                                   AS ATPL,
          a.unstructuredNameTxt,
          a.lineofbusinesscd
    FROM 
          (
           SELECT /*+ opt_param('optimizer_index_cost_adj',30) */
                  sw.insurance_policy_id,
                  sw.external_system_origin_cd                                  AS externalSystemOriginCd,
                  ipn.system_create_ts                                          AS actDte,
                  ipn.author_u_id                                               AS actAuthor,
                  ipn.user_activity_type_cd                                     AS actTypeCde,
                  sw.agreement_nbr                                              AS ipAgreementNb,
                  sw.agreement_legacy_nbr                                       AS ipAgreementLegacyNbr,
                  CASE
                     WHEN sw.combined_policy_cd = 'C' 
                          THEN 'Y'
                     WHEN sw.combined_policy_cd = 'M' AND sw.combined_policy_scenario_cd ='C'
                          THEN 'Y'
                     ELSE 'N'
                  END                                                           AS pvSynchro,
                  sb.name_line1                                                 AS sNameLine1,
                  sb.name_line2                                                 AS sbNameLine2,
                  NVL(sw.agreement_follow_up_status_cd,follow.followupstatuscd) AS followUpStatusCd,    -- HS03
                  follow.followupdte,                                                                   -- HS03
                  follow.followupauthor,                                                                -- HS03
                  follow.followupcontactnote,                                                           -- HS03                                                    
                  pa.first_name_txt                                             AS paFname,
                  pa.last_name_txt                                              AS paLname,
                  pa.birth_dt                                                   AS paBirthdt,           -- HS05
                  adr.postal_cd                                                 AS adrPostalCd,         -- HS05
                  sw.ip_system_create_ts                                        AS ipQuoteDate,
                  sw.party_id,
                  sw.policy_version_id,
                  bta.business_trx_activity_id,
                  bt.last_update_ts AS btLastUpdateTs,

                  CASE                                                                          -- HS04
                    WHEN sw.quotation_validity_expiry_dt < TRUNC(SYSDATE)  AND bt.transaction_status_cd IN ('S','J','X','I','Y') AND sw.agreement_legacy_nbr IS NULL 
                         THEN 'EXP'
                    WHEN sw.quotation_validity_expiry_dt >= TRUNC(SYSDATE) AND bt.transaction_status_cd IN ('S','I') AND sw.agreement_legacy_nbr IS NULL 
                         THEN 'QIN'
                    WHEN sw.quotation_validity_expiry_dt >= TRUNC(SYSDATE) AND bt.transaction_status_cd IN ('X','J') AND sw.agreement_legacy_nbr IS NULL 
                         THEN 'QCO'
                    WHEN sw.quotation_validity_expiry_dt >= TRUNC(SYSDATE) AND bt.transaction_status_cd = 'Y' AND sw.agreement_legacy_nbr IS NULL 
                         THEN 'PIN'
                    WHEN bt.transaction_status_cd = 'W' AND sw.agreement_legacy_nbr IS NULL 
                         THEN 'PCO'
                    WHEN bt.transaction_status_cd IN ('A', 'V') 
                         THEN 'ACC'
                    WHEN bt.transaction_status_cd IN ('D', 'R', 'Z') AND sw.agreement_legacy_nbr IS NOT NULL 
                         THEN 'REF'
                    WHEN bt.transaction_status_cd NOT IN ('A','V','D','R','Z') AND sw.agreement_legacy_nbr IS NOT NULL 
                         THEN 'UPL'
                  END                                AS btTransStatusCd,

                  sw.policy_inception_dt             AS pvInceptionDt,
                  sb.city                            AS ville_sub_broker,
                  sb.sub_broker                      AS SUB_BROKER,
                  sb.sub_broker_number               AS sub_broker_nbr,
                  sb.attending_broker_name_line1     AS sbNameLine3,
                  sw.language_of_communication_cd    AS langCommCd,
                  bta.bsns_trx_activity_cd           AS trxactcd,
                  pa.unstructured_name_txt           as unstructuredNameTxt,
                  ip.line_of_business_cd             as lineOfBusinessCd
  }';

-- HS05 ajout de la table address pour pouvoir retourner le postal code
  vFromStatement := 
  q'{    
             FROM 
                  (
                   SELECT 'ADD_NOTE'                AS followUpStatusCd,
                          system_create_ts          AS followupdte, 
                          author_u_id               AS followupauthor, 
                          insurance_policy_note     AS followupcontactnote,
                          i.insurance_policy_id     AS insurance_policy_id
                     FROM plpadmin.ins_pol_not_mv    sipn,  
                          INSURANCE_POLICY_NOTES_    i
                    WHERE i.activity_type = 'NOTE' 
                      AND sipn.insurance_policy_note_id = i.insurance_policy_note_id
                   ) 
                   follow,
                   INSURANCE_POLICY_NOTES_                 ipnv,
                   :v_table_inactive_pos_only                       --VM05
                   --cifadmin.contextual_sub_broker_gn_infos csb,        --KH02
                   cifadmin.sub_brokers                    sb,
                   plpadmin.business_trx_activity          bta,
                   plpadmin.search_wz                      sw,
                   plpadmin.ins_pol_not_mv                 ipn,
                   plpadmin.party                          pa,
                   plpadmin.business_transaction           bt,
                   plpadmin.address                        adr, 
                   plpadmin.insurance_policy               ip      
   }';

  --VM05 debut ajout
  ----------------------------------------------------------------------
  -- t_inactive_pos_only pour trouver point de vente inactive seulement.
  -- Les valeures possibles sont 'Y' = inactice seulement 
  -- 		             		 ''  = Les deux, inactive et active. 
  ----------------------------------------------------------------------
  IF UPPER(t_inactive_pos_only) = 'Y' THEN
     vFromStatement := replace (vFromStatement, ':v_table_inactive_pos_only', 'cifadmin.contextual_sub_broker_gn_infos csb, ' );  
  else
     vFromStatement := replace (vFromStatement, ':v_table_inactive_pos_only', '' );  
  end if;
--VM05 fin ajout   
   
-- FIN HS03

 -- RL02 ----- END --------------------------------------------

  /*--------------------------------------------------------
       *** CHANGE FROM STATEMENT AND WHERE CLAUSE ****
   --------------------------------------------------------*/
  ------------------------------------------
  -- t_telephone
  -- HS04 ajout d'un index sur le telephone
  ------------------------------------------
  IF (t_telephone) IS NOT NULL THEN
    --extract AREA_CODE_NBR 3 premiers chiffres et PHONE_NBR 7 derniers chiffres
    vAreaPhone  := SUBSTR(t_telephone, 1,3);
    vPhoneNbr   := SUBSTR(t_telephone, 4);
    vFromStatement := vFromStatement ||  ', plpadmin.phone phn ';
  END IF;

 
  /*--------------------------------------------------------
          ****  CHANGE WHERE CLAUSE STATEMENT ****
  --------------------------------------------------------*/
  -----------------------------------------------------
  -- t_last_update_from and t_last_update_to
  -----------------------------------------------------
  -- If the date is null return an error
  -- E.Gauthier : 2011-01-12
  -- Dates are defaulted in the JAVA before being passed to the stored proc.

   IF (t_last_update_from) IS NOT NULL AND
      (t_last_update_to) IS NOT NULL THEN
       vLastUpdateFrom := 'TO_TIMESTAMP(  '''|| t_last_update_from ||''' ,''YYYY-MM-DD'')';
       vLastUpdateTo   := 'TO_TIMESTAMP('''||t_last_update_to||''',''YYYY-MM-DD'')';
       vValidationDates  := ' AND TRUNC(bt.last_update_ts) BETWEEN '|| vLastUpdateFrom || ' AND '|| vLastUpdateTo;
   else
       vValidationDates  := '';
   end if;


  -----------------------------------------------------
  -- t_creation_date_from and t_creation_date_to
  -----------------------------------------------------
/*
   IF (t_creation_date_from) IS NOT NULL AND                                                                            -- PT06  
      (t_creation_date_to) IS NOT NULL 
   THEN                                                                             -- PT06 
       vCreationDateFrom := 'TO_TIMESTAMP(  '''|| t_creation_date_from ||''' ,''YYYY-MM-DD'')';                         -- PT06 
       vCreationDateTo   := 'TO_TIMESTAMP('''||t_creation_date_to||''',''YYYY-MM-DD'')';                                -- PT06 
       vCreationDates  := '     AND TRUNC(sw.ip_system_create_ts) BETWEEN '|| vCreationDateFrom || ' AND '|| vCreationDateTo;  
   ELSE                                                                                                                 -- PT06 
       vCreationDates  := '';                                                                                           -- PT06 
   END IF;                                                                                                              -- PT06 
*/
   vCreationDates  := q'{     AND sw.ip_system_create_ts between :b5 and :b6  }';  

  -----------------------------------------------------
  -- t_synchro_discount
  -----------------------------------------------------
  /*
  IF (t_synchro_discount) IS NOT NULL  THEN
    --validate if t_syncho_discount = Y or N otherwise raise exception
    IF (t_synchro_discount <> 'Y' AND t_synchro_discount <> 'N') THEN
      raise_application_error(vExcetionErrorCode, 't_synchro_discount shoud be equal to null, Y or N. Value was = ' || t_synchro_discount);
    END IF;
  END IF;
  */
-----------------------------------------------------
  -- t_selected_client_followUp
  -- Valid values for this field:  null, 'NOT_CONT', 'CONT_BUT_FLW', 'CONT_BUT_NO_FLW', 'NOT_CONT_NO_FLW'
  -- otherwise: raise exception
  -----------------------------------------------------
 -- IF (t_selected_client_followUp) IS NOT NULL  THEN               
 --   IF (t_selected_client_followUp <> 'NOT_CONT'                  
 --   AND t_selected_client_followUp <> 'CONT_BUT_FLW'              
 --   AND t_selected_client_followUp <> 'CONT_BUT_NO_FLW'           
 --   AND t_selected_client_followUp <> 'NOT_CONT_NO_FLW') THEN          -- PT07 - Release - Sierra  
 --     RAISE_APPLICATION_ERROR(vExcetionErrorCode, 't_selected_client_followUp shoud be equal to null, NOT_CONT, CONT_BUT_FLW, CONT_BUT_NO_FLW or NOT_CONT_NO_FLW. Value was = ' ||t_selected_client_followUp );
 --   END IF;
 -- END IF;

  -----------------------------------------------------
  -- t_quote_ref_number
  -----------------------------------------------------
/*
  v_quote_ref_number := NULL;
  IF t_quote_ref_number IS NOT NULL THEN
    v_quote_ref_number := '                AND sw.agreement_nbr like ''' || t_quote_ref_number||''' || ''%'' ';
  END IF;
*/
  v_quote_ref_number := q'{   AND sw.agreement_nbr like nvl(:b7, sw.agreement_nbr) }';

  -----------------------------------------------------
  -- t_quote_ref_legacy_number
  -----------------------------------------------------
  v_quote_ref_legacy_number := NULL;
  IF t_quote_ref_legacy_number IS NOT NULL THEN
    v_quote_ref_legacy_number := '                AND sw.agreement_legacy_nbr like ''' || t_quote_ref_legacy_number||''' || ''%'' ';
  END IF;

  -----------------------------------------------------
  -- t_selected_client_followUp
  -----------------------------------------------------

  v_selected_client_followUp := NULL;
  IF t_selected_client_followUp IS NOT NULL THEN
     v_followUp_list := replace(t_selected_client_followUp, cif_class.c_separator, ''','''); 

     IF INSTR(v_followUp_list, ',', 1) > 0 THEN       -- VM09
        v_selected_client_followUp := q'{              AND (sw.agreement_follow_up_status_cd in (':selected_client_followUp') OR sw.agreement_follow_up_status_cd IS NULL) }';
     ELSE   
 	    v_selected_client_followUp := q'{              AND sw.agreement_follow_up_status_cd in (':selected_client_followUp') }';    --VM09                                            
     END IF;
     v_selected_client_followUp := replace (v_selected_client_followUp, ':selected_client_followUp', v_followUp_list);
  END IF;  
  
  
  -----------------------------------------------------
  -- t_email
  -----------------------------------------------------
  v_email := null;
  IF t_email IS NOT NULL THEN
    v_email := UPPER(t_email);
    v_email := '                AND pa.EMAIL_ADDRESS_TXT LIKE '''|| v_email ||''' || ''%'' ';
  END IF;

  -----------------------------------------------------
  -- t_postal_code
  -----------------------------------------------------
  v_postal_code := null;
  IF t_postal_code IS NOT NULL THEN
    v_postal_code := '                AND adr.postal_cd = '''||t_postal_code||''' ';
  END IF;

  -----------------------------------------------------
  -- t_telephone
  -----------------------------------------------------
  v_telephone := null;
  IF t_telephone IS NOT NULL THEN
    v_telephone := ' AND sw.party_id = phn.party_id ' ||
                   ' AND phn.area_code_nbr = ''' || vAreaPhone || '''' || -- PT04
                   ' AND phn.phone_nbr = ''' || vPhoneNbr ||'''';         -- PT04
  END IF;

  -----------------------------------------------------
  -- t_first_name
  -- HS04
  -----------------------------------------------------
  vFirstName := NULL;
  IF t_first_name is not null then   --- KH01  fix (name apostrophe) bug in search_quote 
    vFirstName := plp_utility.generate_search_string(t_first_name);
  END IF;

  -----------------------------------------------------
  -- t_last_name
  -- HS04
  -----------------------------------------------------
  vLastName := NULL;
  if t_last_name IS NOT NULL THEN    -- KH01 fix (name apostrophe) bug in search_quote 
    vLastName := plp_utility.generate_search_string(t_last_name);
  END IF;
  
  ------------------------------------------------------------------
  -- HS04  Utilisation du nouveau champ search_name_txt au lieu du 
  -- last et first name de cette maniere, on a plus besoin d'utiliser
  -- le plp_utility.generate_search_string car il est déjà appliquer
  -- sur le search_name_txt
  ------------------------------------------------------------------
  IF t_first_name IS NOT NULL OR t_last_name IS NOT NULL THEN
     vSearchName := '     AND pa.search_name_txt LIKE '''|| vLastName ||''' || ''%'' || '''|| vFirstName ||''' || ''%'' ';
  END IF; 
  
  ------------------------------------------------------------------
  -- JG15 Utilisation du parametre t_unstructured_name
  ------------------------------------------------------------------
  IF t_unstructured_name IS NOT NULL THEN
     vUnstructuredNname := '     AND pa.unstructured_name_txt LIKE '|| chr(39) || '%' || t_unstructured_name || '%' || chr(39);
  END IF; 
  
  -----------------------------------------------------
  -- t_selected_point_of_sale
  -----------------------------------------------------
  -- HS01
  v_selected_point_of_sale := NULL;
  v_quick_quote_web_access := 0; 

  /*IF t_selected_point_of_sale IS NOT NULL 
  THEN
    -- KH04
    IF t_quote_ref_number IS NULL  THEN
        --vSearchName IS NOT NULL OR t_telephone IS NOT NULL OR t_postal_code IS NOT NULL OR t_quote_ref_legacy_number IS NOT NULL OR t_quote_ref_number IS NOT NULL)
        --AND t_province = 'QC'
       v_selected_point_of_sale := ' AND  sb.sub_broker_number = '''||t_selected_point_of_sale||''' ';
    END IF;
  ELSE
    IF t_selected_master IS NOT NULL THEN 
       --IF     (vSearchName is not null OR t_telephone IS NOT NULL OR t_postal_code is not null OR t_quote_ref_legacy_number IS NOT NULL OR t_quote_ref_number IS NOT NULL) 
          --AND t_province = 'QC' 
       --THEN
	       IF t_quote_ref_number is null or length(t_quote_ref_number) <10 THEN --VM09
                v_selected_point_of_sale :=   ' AND (sb.sub_broker_number, sb.company_number) IN (SELECT subroker_nbr, bi.intact_companie_nbr FROM cifadmin.broker_infos bi WHERE bi.master_owner_nbr = ''' ||t_selected_master||''') '; -- HS01
           END IF;
       END IF;
    END IF;
  END IF;*/
  
   -- KH04
  IF t_selected_point_of_sale IS NOT NULL AND t_quote_ref_number IS NULL  THEN
       v_selected_point_of_sale := ' AND  sb.sub_broker_number = '''||t_selected_point_of_sale||''' ';
  ELSE
       IF t_selected_master IS NOT NULL AND (t_quote_ref_number IS NULL OR length(t_quote_ref_number) <10) THEN 
            v_selected_point_of_sale :=   ' AND (sb.sub_broker_number, sb.company_number) IN (SELECT subroker_nbr, bi.intact_companie_nbr FROM cifadmin.broker_infos bi WHERE bi.master_owner_nbr = ''' ||t_selected_master||''') '; -- HS01
       END IF;
  END IF;


  /*-----------------------------------------------------
  -- Changes were added on Jan 14th 2011 due to new specs from David Coderre
  -- t_selected_quote_status
  Valid values
  from Java
  EXP         Expired                INSURANCE_POLICY.QUOTATION_VALIDITY_EXPIRY_DT (this condition in th select statement has to be checked first)
  QIN         Quote Imcomplete       BUSINESS_TRANSACTION.TRANSACTION_STATUS_CD in ('S','J') and ip.agreement_legacy_nbr is not null
  QCO         Quote Complete         BUSINESS_TRANSACTION.TRANSACTION_STATUS_CD in ('X','I') and ip.agreement_legacy_nbr is not null
  PIN         Purchase Incomplete    BUSINESS_TRANSACTION.TRANSACTION_STATUS_CD = 'Y' and ip.agreement_legacy_nbr is not null
  PCO         Purchase Complete      BUSINESS_TRANSACTION.TRANSACTION_STATUS_CD = 'W' and ip.agreement_legacy_nbr is not null
  ACC         Accepted               BUSINESS_TRANSACTION.TRANSACTION_STATUS_CD in ('A', 'V') and ip.agreement_legacy_nbr is not null
  REF         Refused                BUSINESS_TRANSACTION.TRANSACTION_STATUS_CD in ('D', 'R', 'Z') and ip.agreement_legacy_nbr is not null
  UPL         Uploaded               INSURANCE_POLICY.AGREEMENT_LEGACY_NBR is not null and bt.transaction_status_cd not in ('A','V','D','R','Z')

  otherwise: raise exception
  ----------------------------------------------------- */
  vSelectedQuoteStatus := NULL;
  IF (t_selected_quote_status) IS NOT NULL  
  THEN

    vSelectedQuoteStatus:= CASE t_selected_quote_status
                                WHEN 'QIN' THEN q'{ AND sw.quotation_validity_expiry_dt >= TRUNC(SYSDATE) AND bt.transaction_status_cd IN ('S','I') AND sw.agreement_legacy_nbr IS NULL }'
                                WHEN 'QCO' THEN q'{ AND sw.quotation_validity_expiry_dt >= TRUNC(SYSDATE) AND bt.transaction_status_cd IN ('X','J') AND sw.agreement_legacy_nbr IS NULL }'
                                WHEN 'PIN' THEN q'{ AND sw.quotation_validity_expiry_dt >= TRUNC(SYSDATE) AND bt.transaction_status_cd = 'Y' AND sw.agreement_legacy_nbr IS NULL }'
                                WHEN 'PCO' THEN q'{ AND bt.transaction_status_cd = 'W' AND sw.agreement_legacy_nbr IS NULL }'
                                WHEN 'ACC' THEN q'{ AND bt.transaction_status_cd IN ('A','V')  }'
                                WHEN 'REF' THEN q'{ AND bt.transaction_status_cd IN ('D','R', 'Z') AND sw.agreement_legacy_nbr IS NOT NULL }'
                                WHEN 'EXP' THEN q'{ AND sw.quotation_validity_expiry_dt < TRUNC(SYSDATE) AND bt.transaction_status_cd IN ('S','J','X','I','Y') AND sw.agreement_legacy_nbr IS NULL }'
                                WHEN 'UPL' THEN q'{ AND sw.agreement_legacy_nbr IS NOT NULL and bt.transaction_status_cd not in ('A','V','D','R','Z')  }' -- PT09 
                             ELSE 'ERROR'
                           END;

    if vSelectedQuoteStatus = 'ERROR' 
    then
       --raise_application_error(vExcetionErrorCode, 't_selected_quote_status should be equal to null, QIN,QCO,PIN,PCO,ACC,REF,EXP,UPL. Value was = ' || t_selected_quote_status);

       utiladmin.util_procedure_audit_sql.g_sql_message := 't_selected_quote_status should be equal to null, QIN,QCO,PIN,PCO,ACC,REF,EXP,UPL. Value was = ' || t_selected_quote_status;
       utiladmin.util_procedure_audit_sql.save_error ($$plsql_line);

    end if;
  END IF;

  -----------------------------------------------------
  -- t_external_system_origin
  -----------------------------------------------------
  --  PT01 - begin
/* VM05
  vExternalSystemOrigin := NULL;
  IF t_external_system_origin IS NULL 
  THEN
     IF l_external_system_orig_qq IS NOT NULL 
     THEN
        IF l_external_system_orig_qq = 'INT' 
        THEN
           vExternalSystemOrigin := ' AND ((sw.external_system_origin_cd is null) OR (sw.external_system_origin_cd = ''WEBBK'' AND sw.application_mode_cd IN (''R'',NULL))) ';
        else
           vExternalSystemOrigin := ' AND ((sw.external_system_origin_cd = '''||t_external_system_origin||''') or (sw.external_system_origin_cd is null and sw.application_mode_cd in (''R'',null))) ';
        end if;
     else 
        vExternalSystemOrigin := null;
     end if;
  end if;
  
  if t_external_system_origin = 'INT' 
  then
     if l_external_system_orig_qq is not null 
     then
        if l_external_system_orig_qq = 'INT' 
        then
           vExternalSystemOrigin := ' AND sw.external_system_origin_cd is null ';
        else
            vExternalSystemOrigin := ' AND ((sw.external_system_origin_cd is null and sw.application_mode_cd in (''R'',null)) or (sw.external_system_origin_cd = ''WEBBK'' and sw.application_mode_cd = ''Q'')) ';
        end if;
     else 
        vExternalSystemOrigin := ' AND ((sw.external_system_origin_cd is null) or (sw.external_system_origin_cd = ''WEBBK'' and sw.application_mode_cd = ''Q''))  ';
     end if;
  end if;
  
  if t_external_system_origin = 'WEBBK' 
  then
     if l_external_system_orig_qq is not null 
     then
        if l_external_system_orig_qq = 'INT' 
        then  
            vExternalSystemOrigin := ' AND ((sw.external_system_origin_cd = ''WEBBK'' and sw.application_mode_cd in (''R'',null)) or (sw.external_system_origin_cd is null and sw.application_mode_cd = ''Q'')) ';
        else
            vExternalSystemOrigin := ' AND sw.external_system_origin_cd  = ''WEBBK'' ';
        end if;
     else 
        vExternalSystemOrigin := ' AND ((sw.external_system_origin_cd = ''WEBBK'') or ( sw.external_system_origin_cd is null and sw.application_mode_cd = ''Q''))  ';
     end if;
  end if;
-- VM05 */  

  -- DEBUT VM05

  vExternalSystemOrigin := NULL;
  IF t_external_system_origin IS NOT NULL 
  THEN
    IF t_external_system_origin = 'QI' 
    THEN
           vExternalSystemOrigin := ' AND ip.external_system_origin_cd is null '; 
    ELSIF t_external_system_origin = 'QB' THEN
		   vExternalSystemOrigin := ' AND ip.external_system_origin_cd = ''WEBBK'' ';
   	END IF;
  END IF;
  
  -- FIN   VM05
  
  --  PT01 - end


  -----------------------------------------------------
  -- t_user
  -- HS01 Ajout du lien avec la compagnie
  -----------------------------------------------------
  --  PT02 - begin
  vUserWebAccessSecurity := null;

  if t_user is not null 
  then
  -- HS03
     EXECUTE IMMEDIATE 'TRUNCATE TABLE PLPADMIN.WRK_BROKER_WEB_ACCESS';

     /*INSERT 
       INTO PLPADMIN.WRK_BROKER_WEB_ACCESS (master_owner_cd, company_cd, application_id, web_access_type)
     select bwa.master_owner_cd, bwa.company_cd 
       from broker_web_office_access@brmadmin bwa,
            user_account@brmadmin             ua  
      where bwa.user_account_id = ua.user_account_id 
        and UPPER(ua.u_id) = UPPER(t_user)
     ;*/

     INSERT -- KH03
       INTO PLPADMIN.WRK_BROKER_WEB_ACCESS (master_owner_cd, company_cd,application_id,web_access_type)
     SELECT DISTINCT bwa.master_owner_cd, bwa.company_cd, csb.application_id, csb.web_access_type
       FROM broker_web_office_access@brmadmin bwa,
            user_account@brmadmin ua ,
            cifadmin.sub_brokers sb,
            cifadmin.contextual_sub_broker_gn_infos csb          
      WHERE bwa.user_account_id = ua.user_account_id
        AND sb.sub_broker_number = bwa.master_owner_cd
        AND sb.company_number = bwa.company_cd
        AND sb.sub_broker = csb.sub_broker
        AND csb.application_id IN ('AQQK','AQRG')
        AND csb.web_access_type in ('QI','QB','QIB','NONE')
        AND sysdate between csb.start_date and nvl(csb.end_date, sysdate+1) 
        AND UPPER(ua.u_id) =  UPPER(t_user)
        AND csb.line_of_business_code = 'P'          
UNION
SELECT DISTINCT sb.sub_broker_number, sb.company_number, csb.application_id, csb.web_access_type
       FROM broker_web_office_access@brmadmin bwa,
            user_account@brmadmin ua,
            cifadmin.sub_brokers sb,
			cifadmin.broker_infos bi,
            cifadmin.contextual_sub_broker_gn_infos csb
      WHERE bwa.user_account_id = ua.user_account_id
        AND bi.master_owner_nbr = bwa.master_owner_cd
		AND bi.subroker_nbr = sb.sub_broker_number
		AND bi.intact_companie_nbr = sb.company_number
        AND csb.sub_broker = sb.sub_broker
        AND csb.application_id IN ('AQQK','AQRG')
        AND csb.web_access_type in ('QI','QB','QIB','NONE')
        AND sysdate BETWEEN csb.start_date AND nvl(csb.end_date, sysdate+1) 
        AND upper(ua.u_id) =  UPPER(t_user)
        AND csb.line_of_business_code = 'P'
        ;
        
     /*select distinct bwa.master_owner_cd, bwa.company_cd, csb.application_id, csb.web_access_type
       from broker_web_office_access@brmadmin bwa,
            user_account@brmadmin ua ,
            cifadmin.sub_brokers sb,
            cifadmin.contextual_sub_broker_gn_infos csb
      where bwa.user_account_id = ua.user_account_id
        and sb.sub_broker_number = bwa.master_owner_cd
        and sb.company_number = bwa.company_cd
        and sb.sub_broker = csb.sub_broker
        and csb.application_id IN ('AQQK','AQRG')
        and csb.web_access_type in ('QI','QB','QIB','NONE')
        and sysdate between csb.start_date and nvl(csb.end_date, sysdate+1) 
        and UPPER(ua.u_id) = UPPER(t_user)
        and csb.line_of_business_code = 'P'; */ 
        --NB RTC202184 - Actuellement les BR des requêtes search_quote ne sont pas optmiale pour les business_code = 'C'
        --   le code doit être revu par les BO pour integrer les business_code = 'C'
        --  pour palier les problemes de performance, on force le code 'P' en attendant l'analyse de la business.   
       
    
     commit;

 --HS07    
      v_quick_quote_web_access := 0; 

      if     (vSearchName is not null or t_telephone is not null or t_postal_code is not null or t_quote_ref_legacy_number is not null or t_quote_ref_number is not null) 
--VM05         and (nvl(l_external_system_orig_qq,'INT') != 'WEBBK' or nvl(t_external_system_origin,'INT') != 'WEBBK') 
         and t_province = 'QC' 
      then

         /*select count(1)
           into v_quick_quote_web_access
           from cifadmin.sub_brokers            sb2,
                PLPADMIN.WRK_BROKER_WEB_ACCESS  bwa2
          where sb2.sub_broker_number            = bwa2.master_owner_cd
            and sb2.company_number               = bwa2.company_cd 
            and sb2.quick_quote_web_access_type in ('QI','QIB')*/
         
         --HK RTC181199
         select count(1)
           into v_quick_quote_web_access
           from cifadmin.sub_brokers            sb2,
                PLPADMIN.WRK_BROKER_WEB_ACCESS  bwa2
          where sb2.sub_broker_number            = bwa2.master_owner_cd
            and sb2.company_number               = bwa2.company_cd
            and bwa2.application_id              = 'AQQK'
            and bwa2.web_access_type             in ('QI','QIB') -- KH03
         ;
                                         
         if v_quick_quote_web_access >= 1 
         then
             v_parenthese_value := 1;  -- KH03
             /*vUserWebAccessSecurity := ' AND ((sw.application_mode_cd = ''Q'' and sw.external_system_origin_cd is null) ' ||                               
                                       '  OR  ((sb.sub_broker_number,sb.company_number) in '||
                                                          '(select distinct bwa.master_owner_cd, bwa.company_cd '||
                                                             'from PLPADMIN.WRK_BROKER_WEB_ACCESS bwa)) ';          --KH03*/
             vUserWebAccessSecurity := 
                 ' 
                 AND ((sw.application_mode_cd = ''Q'' and sw.external_system_origin_cd is null)   
                       OR                                                                                      
                     ((sb.sub_broker_number,sb.company_number) in (select bwa.master_owner_cd, bwa.company_cd  
                                                                     from PLPADMIN.WRK_BROKER_WEB_ACCESS bwa)' ; --KH03
                 
             /*vUserWebAccessSecurity := ' AND ((sw.application_mode_cd = ''Q'' and sw.external_system_origin_cd is null) ' ||                                
                                       '  OR  ((sb.sub_broker_number,sb.company_number) in( select distinct bi.subroker_nbr, bi.intact_companie_nbr '|| --HS01
                                                                                            'from cifadmin.broker_infos bi '||
                                                                                            'where (bi.master_owner_nbr, bi.intact_companie_nbr) '||    --HS01 
                                                                                             'in (select bwa.master_owner_cd, bwa.company_cd '||           --HS01
                                                                                                  'from PLPADMIN.WRK_BROKER_WEB_ACCESS bwa)))) ';  */        --HS03
                                       /*' AND ((sb.quick_quote_web_access_type in (''QI'',''QB'',''QIB'') and nvl(sb.sub_broker_web_access_type,''NONE'') not in (''QB'',''QI'',''QIB'') and sw.application_mode_cd = ''Q'') OR'|| -- HS05
                                       ' (sb.quick_quote_web_access_type in (''QI'',''QB'',''QIB'') and sb.sub_broker_web_access_type in (''QB'',''QI'',''QIB'')) OR'||  --HS05
                                       ' (nvl(sb.quick_quote_web_access_type,''NONE'') = ''NONE'' and sw.application_mode_cd != ''Q'')))) ';*/
         else
             vUserWebAccessSecurity := 
                 ' 
                 AND ((sb.sub_broker_number,sb.company_number) in (select bwa.master_owner_cd, bwa.company_cd  
                                                                     from PLPADMIN.WRK_BROKER_WEB_ACCESS bwa) ';           --KH03
             /*vUserWebAccessSecurity := ' AND (((sb.sub_broker_number,sb.company_number) in( select distinct bi.subroker_nbr, bi.intact_companie_nbr '|| --HS01
                                                                                          'from cifadmin.broker_infos bi '||
                                                                                          'where (bi.master_owner_nbr, bi.intact_companie_nbr) '||    --HS01 
                                                                                          'in (select bwa.master_owner_cd, bwa.company_cd '||           --HS01
                                                                                               'from PLPADMIN.WRK_BROKER_WEB_ACCESS bwa))) '; */          --HS03 
                                       /*' AND ((sb.quick_quote_web_access_type in (''QI'',''QB'',''QIB'') and nvl(sb.sub_broker_web_access_type,''NONE'') not in (''QB'',''QI'',''QIB'') and sw.application_mode_cd = ''Q'') OR'|| -- HS05
                                       ' (sb.quick_quote_web_access_type in (''QI'',''QB'',''QIB'') and sb.sub_broker_web_access_type in (''QB'',''QI'',''QIB'')) OR'||  --HS05
                                       ' (nvl(sb.quick_quote_web_access_type,''NONE'') = ''NONE'' and sw.application_mode_cd != ''Q'')) '; --HS05 HS06*/
         end if;     
      else  
             vUserWebAccessSecurity := 
                 ' 
                 AND ((sb.sub_broker_number,sb.company_number) in (select bwa.master_owner_cd, bwa.company_cd  
                                                                     from PLPADMIN.WRK_BROKER_WEB_ACCESS bwa) ';           --KH03
            /*vUserWebAccessSecurity := ' AND (sb.sub_broker_number,sb.company_number) in( select distinct bi.subroker_nbr, bi.intact_companie_nbr '|| --HS01  
                                                                                         'from cifadmin.broker_infos bi '||
                                                                                         'where (bi.master_owner_nbr, bi.intact_companie_nbr) '||    --HS01 
                                                                                                 'in (select bwa.master_owner_cd, bwa.company_cd '||           --HS01
                                                                                                     'from PLPADMIN.WRK_BROKER_WEB_ACCESS bwa)) ';       */    --HS03  
                                       /*' AND ((sb.quick_quote_web_access_type in (''QI'',''QB'',''QIB'') and nvl(sb.sub_broker_web_access_type,''NONE'') not in (''QB'',''QI'',''QIB'') and sw.application_mode_cd = ''Q'') OR'|| -- HS05
                                       ' (sb.quick_quote_web_access_type in (''QI'',''QB'',''QIB'') and sb.sub_broker_web_access_type in (''QB'',''QI'',''QIB'')) OR'||  --HS05
                                       ' (nvl(sb.quick_quote_web_access_type,''NONE'') = ''NONE'' and sw.application_mode_cd != ''Q'')) '; --HS05 HS06*/
                                       
      end if;  
             --KH02 
             vUserWebAccessSecurity := vUserWebAccessSecurity || 
                                       /*' AND (((csb.application_id = ''AQQK'' and csb.web_access_type in (''QI'',''QB'',''QIB'') and sysdate between csb.start_date and nvl(csb.end_date, sysdate+1) ) '|| 
                                              ' and not exists (select 1 from cifadmin.contextual_sub_broker_gn_infos where sub_broker = csb.sub_broker and  application_id = ''AQRG'' and nvl(web_access_type,''NONE'') in (''QB'',''QI'',''QIB'')  and sysdate between start_date and nvl(end_date, sysdate+1))'||
                                              ' and sw.application_mode_cd = ''Q'') '||
                                       '   OR ((csb.application_id = ''AQQK'' and csb.web_access_type in (''QI'',''QB'',''QIB'') and sysdate between csb.start_date and nvl(csb.end_date, sysdate+1) ) '||
                                              ' and exists (select 1 from cifadmin.contextual_sub_broker_gn_infos where sub_broker = csb.sub_broker and  application_id = ''AQRG'' and nvl(web_access_type,''NONE'') in (''QB'',''QI'',''QIB'') and sysdate between start_date and nvl(end_date, sysdate+1)  ))'||
                                       '   OR (csb.application_id = ''AQQK'' and nvl(csb.web_access_type,''NONE'') = ''NONE'' and sw.application_mode_cd != ''Q'' and sysdate between csb.start_date and nvl(csb.end_date, sysdate+1) )) '; --KH02 */
                              
                                       --KH03
                                       ' 
                 AND ((sb.sub_broker_number,sb.company_number) in (select bwa.master_owner_cd, bwa.company_cd 
                                                                     from PLPADMIN.WRK_BROKER_WEB_ACCESS bwa   
                                                                    where application_id = ''AQQK''   
                                                                      and nvl(web_access_type,''NONE'') in (''QB'',''QI'',''QIB''))  
                         and not exists (select 1 from PLPADMIN.WRK_BROKER_WEB_ACCESS  
                                          where master_owner_cd = sb.sub_broker_number 
                                            and  application_id = ''AQRG'' 
                                            and nvl(web_access_type,''NONE'') in (''QB'',''QI'',''QIB'') )  
                         and sw.application_mode_cd = ''Q'')                                        
                  OR ((sb.sub_broker_number,sb.company_number) in (select bwa.master_owner_cd, bwa.company_cd
                                                                     from PLPADMIN.WRK_BROKER_WEB_ACCESS bwa  
                                                                    where application_id = ''AQQK'' 
                                                                      and nvl(web_access_type,''NONE'') in (''QB'',''QI'',''QIB''))
                         and exists    (select 1 from PLPADMIN.WRK_BROKER_WEB_ACCESS   
                                         where master_owner_cd = sb.sub_broker_number  
                                           and  application_id = ''AQRG'' 
                                           and nvl(web_access_type,''NONE'') in (''QB'',''QI'',''QIB'') )) 
                  OR ((sb.sub_broker_number,sb.company_number) in (select bwa.master_owner_cd, bwa.company_cd 
                                                                     from PLPADMIN.WRK_BROKER_WEB_ACCESS bwa  
                                                                    where application_id = ''AQQK'' 
                                                                      and nvl(web_access_type,''NONE'') = ''NONE'') 
                         and sw.application_mode_cd != ''Q'') ';   
                                       
                                       /*--KH03
                                       ' AND (((sb.sub_broker_number,sb.company_number) in (select distinct bi.subroker_nbr, bi.intact_companie_nbr                                                                       '||
                                       '                                                      from cifadmin.broker_infos bi                                                                                               '||
                                       '                                                     where (bi.master_owner_nbr, bi.intact_companie_nbr) in (select bwa.master_owner_cd, bwa.company_cd                           '||
                                       '                                                                                                               from PLPADMIN.WRK_BROKER_WEB_ACCESS bwa                            '||
                                       '                                                                                                              where application_id = ''AQQK''                                     '||  
                                       '                                                                                                                and nvl(web_access_type,''NONE'') in (''QB'',''QI'',''QIB'')))    '||  
                                       '        and not exists (select 1 from PLPADMIN.WRK_BROKER_WEB_ACCESS                                                                                                              '||
                                       '                         where master_owner_cd = sb.sub_broker_number                                                                                                             '||
                                       '                           and  application_id = ''AQRG''                                                                                                                         '||
                                       '                           and nvl(web_access_type,''NONE'') in (''QB'',''QI'',''QIB'') )                                                                                         '|| 
                                       '        and sw.application_mode_cd = ''Q'')                                                                                                                                       '||                                       
                                       '  OR ((sb.sub_broker_number,sb.company_number) in(select distinct bi.subroker_nbr, bi.intact_companie_nbr                                                                         '||
                                       '                                                    from cifadmin.broker_infos bi                                                                                                 '||
                                       '                                                   where (bi.master_owner_nbr, bi.intact_companie_nbr) in (select bwa.master_owner_cd, bwa.company_cd                             '||
                                       '                                                                                                             from PLPADMIN.WRK_BROKER_WEB_ACCESS bwa                              '||
                                       '                                                                                                            where application_id = ''AQQK''                                       '||
                                       '                                                                                                              and nvl(web_access_type,''NONE'') in (''QB'',''QI'',''QIB'')))      '||
                                       '        and exists    (select 1 from PLPADMIN.WRK_BROKER_WEB_ACCESS                                                                                                               '||
                                       '                        where master_owner_cd = sb.sub_broker_number                                                                                                              '||
                                       '                          and  application_id = ''AQRG''                                                                                                                          '||
                                       '                          and nvl(web_access_type,''NONE'') in (''QB'',''QI'',''QIB'') ))                                                                                         '||
                                       '  OR ((sb.sub_broker_number,sb.company_number) in( select distinct bi.subroker_nbr, bi.intact_companie_nbr                                                                        '||
                                       '                              from cifadmin.broker_infos bi                                                                                                                       '|| 
                                       '                             where (bi.master_owner_nbr, bi.intact_companie_nbr) in (select bwa.master_owner_cd, bwa.company_cd                                                   '||
                                       '                                                                                       from PLPADMIN.WRK_BROKER_WEB_ACCESS bwa                                                    '||
                                       '                                                                                      where application_id = ''AQQK''                                                             '||
                                       '                                                                                        and nvl(web_access_type,''NONE'') = ''NONE''))                                            '||
                                       '        and sw.application_mode_cd != ''Q''))) ';   */  
      
      if v_parenthese_value = 1 then        --KH03
             vUserWebAccessSecurity := vUserWebAccessSecurity || '))';
      else          
             vUserWebAccessSecurity := vUserWebAccessSecurity || ')';
      end if;                           
  else   
     vUserWebAccessSecurity := '';
  end if;
  --  PT02 - end
  
   -----------------------------------------------------
   -- t_cancelled_broker
   -- HS05 Ajout pour faire une liste des soumissions
   -- des courtiers qui sont cancellés
   -----------------------------------------------------
  vcancelled_broker := NULL;
  IF t_cancelled_broker IS NOT NULL THEN
     vcancelled_broker := ' AND sb.end_date IS NOT NULL AND sw.ip_system_create_ts >= sysdate - 100 ';
  else
     vcancelled_broker := '';
  end if;

  -----------------------------------------------------
  -- t_token_ind    HS09
  -----------------------------------------------------
  IF t_token_ind IS NOT NULL THEN
     vTokenInd := q'{
                     AND NOT EXISTS (SELECT 1 FROM plpadmin.policy_email_token pet WHERE sw.insurance_policy_id = pet.insurance_policy_id AND pet.end_ts IS NOT NULL AND event_trigger_cd = 'BROKER_LEAD') 
                  }';
  else 
     vTokenInd := '';
  end if;

  -----------------------------------------------------
  -- t_line_of_business_cd
  -----------------------------------------------------
  declare
     v_line_of_business_cd_list  varchar2(4000);
  begin
     v_line_of_business_cd := NULL;
     IF t_line_of_business_cd IS NOT NULL THEN
        v_line_of_business_cd_list := replace(t_line_of_business_cd, cif_class.c_separator, ''','''); 
        v_line_of_business_cd := q'{              AND ip.line_of_business_cd in (':line_of_business_cd_list') }';
        v_line_of_business_cd := replace (v_line_of_business_cd, ':line_of_business_cd_list', v_line_of_business_cd_list);
     END IF;
  end;

  --VM05 debut ajout
  ----------------------------------------------------------------------
  -- t_inactive_pos_only pour trouver point de vente inactive seulement.
  -- Les valeures possibles sont 'Y' = inactice seulement 
  -- 		             		 ''  = Les deux, inactive et active. 
  ----------------------------------------------------------------------
  IF UPPER(t_inactive_pos_only) = 'Y' THEN
     v_inactive_pos_only := q'{ and decode(NVL(csb.web_access_type,'NONE'), 'NONE', 'Y' , 'N') = 'Y' and csb.sub_broker = sb.sub_broker and csb.application_id = decode(NVL(sw.application_mode_cd,'Q'),'Q','AQQK','R','AQRG') and csb.line_of_business_code = ip.line_of_business_cd }';
  else
     v_inactive_pos_only := '';
  end if;
--VM05 fin ajout
  
  vWhereClause :=   
   q'{
               WHERE ip.insurance_policy_id           = sw.insurance_policy_id 
                 AND follow.insurance_policy_id(+)    = sw.insurance_policy_id     
                 AND ipnv.insurance_policy_id(+)      = sw.insurance_policy_id 
                 AND ipnv.insurance_policy_note_id    = ipn.insurance_policy_note_id(+)
                 AND sw.cif_sub_broker_id             = sb.sub_broker
                 AND sw.party_id                      = pa.party_id 
                 AND sw.business_transaction_id       = bt.business_transaction_id 
                 AND pa.party_id                      = adr.party_id 
                 --AND sb.sub_broker                    = csb.sub_broker                  --KH02 KH03
                 --AND sysdate between csb.start_date and nvl(csb.end_date, sysdate+1)    --KH02 KH03
   }';

   vWhereClause := replace (vWhereClause, ':v_manufacturing_context_id', v_manufacturing_context_id);  


   vWhereClause := 
      vWhereClause ||
      v_quote_ref_legacy_number ||
      v_selected_client_followUp || 
      v_synchro_discount ||
      v_inception_date_from ||
      v_inception_date_to ||
      v_email ||
      vValidationDates ||
      vCreationDates ||        -- PT06 
      v_quote_ref_number ||
      vSelectedQuoteStatus ||
      vExternalSystemOrigin || -- PT01 
      v_postal_code ||
      v_telephone ||
      vSearchName || 
      q'{
      }'||
      vUnstructuredNname ||
      v_inactive_pos_only    || --VM05
      v_line_of_business_cd  ||
      q'{ 
                 AND bta.business_trx_activity_id = (SELECT MAX(business_trx_activity_id) FROM plpadmin.business_trx_activity 
                                                      WHERE sw.business_transaction_id = business_transaction_id
                                                     )
                 AND sw.manufacturer_company_cd      = ':t_manuf_company'
                 AND sb.search_province              = ':t_province'
                 AND sb.company_number               = ':t_sub_company_nbr'
                 AND nvl(ipnv.activity_type,'OTHER') = 'OTHER' 
                 AND sw.manufacturing_context_id     = :v_manufacturing_context_id
      }'
      ;

      vWhereClause := replace (vWhereClause, ':t_manuf_company', t_manuf_company);
      vWhereClause := replace (vWhereClause, ':t_province', t_province);
      vWhereClause := replace (vWhereClause, ':t_sub_company_nbr', t_sub_company_nbr);
      vWhereClause := replace (vWhereClause, ':v_manufacturing_context_id', v_manufacturing_context_id);

      vWhereClause := vWhereClause ||
      vcancelled_broker ||
      v_selected_point_of_sale ||
      vUserWebAccessSecurity || 
      vTokenInd ||
      q'{ 
                 AND ( exists( SELECT 1 FROM plpadmin.consent co WHERE sw.party_id = co.party_id AND co.consent_ind = 'Y' AND co.consent_type_cd = 'PR' )
                     OR not exists( SELECT 1 FROM plpadmin.consent co WHERE sw.party_id = co.party_id AND co.consent_type_cd = 'PR' )
                     ) 
               ORDER BY sw.ip_system_create_ts desc
      ) A}';

  -- HS07    
   IF t_manuf_company in ('A','6') 
   THEN
       vWhereClause :=  vWhereClause ||
        q'{ 
      WHERE ROWNUM <= 501         
      ORDER BY btLastUpdateTs desc
        }';  
   ELSE
       vWhereClause :=  vWhereClause ||
        q'{ 
      WHERE ROWNUM <= 201         
      ORDER BY btLastUpdateTs desc
        }';  
   END IF;

   -- RL02 ----- END --------------------------------------------


    vRequest := vSelectstatement || vFromstatement || vWhereclause;
    vLeft := vRequest;

    -- Output SQL text.
    while (vLeft is not null) 
    loop
        dbms_output.put(substr(vLeft,0,255));
        vLeft := substr(vLeft,256);
    end loop;

    -- Open cursor...
    declare
       l_quote_ref_number    varchar2(100);
       l_creation_date_from  date;
       l_creation_date_to    date;
    begin

       -- Quote Reference Number
       if t_quote_ref_number is not null 
          then l_quote_ref_number := t_quote_ref_number||'%'; 
       end if;

       -- Quote Creation Date From
       if t_creation_date_from is null 
          then l_creation_date_from := to_date('1900-01-01','YYYY-MM-DD'); 
          else l_creation_date_from := to_date(t_creation_date_from,'YYYY-MM-DD'); 
       end if;
       -- Quote Creation Date To
       if t_creation_date_to is null 
          then l_creation_date_to := sysdate+1; 
          else l_creation_date_to := to_date(t_creation_date_to,'YYYY-MM-DD')+86399/86400; -- 1 jour moins une seconde.
       end if;

       utiladmin.util_procedure_audit_sql.g_sql_bind_variable.delete;
       utiladmin.util_procedure_audit_sql.g_sql_bind_variable('b3') := to_char(l_creation_date_from, 'yyyy-mm-dd  hh24:mi:ss');
       utiladmin.util_procedure_audit_sql.g_sql_bind_variable('b4') := to_char(l_creation_date_to, 'yyyy-mm-dd hh24:mi:ss');
       utiladmin.util_procedure_audit_sql.g_sql_bind_variable('b5') := to_char(l_creation_date_from, 'yyyy-mm-dd  hh24:mi:ss');
       utiladmin.util_procedure_audit_sql.g_sql_bind_variable('b6') := to_char(l_creation_date_to, 'yyyy-mm-dd hh24:mi:ss');
       utiladmin.util_procedure_audit_sql.g_sql_bind_variable('b7') := l_quote_ref_number;

       utiladmin.util_procedure_audit_sql.g_sql_text := vRequest;

       execute immediate 'truncate table wrk_search_quote';

       utiladmin.util_procedure_audit_sql.g_stime := dbms_utility.get_time;

       execute immediate 'insert into  wrk_search_quote '|| vRequest 
         using l_creation_date_from, l_creation_date_to, l_creation_date_from, l_creation_date_to, l_quote_ref_number
       ;
       utiladmin.util_procedure_audit_sql.g_rowcount := sql%rowcount;      -- Cette instruction doit suivre immediatement l'ordre execute ci-haut.
       utiladmin.util_procedure_audit_sql.g_etime    := dbms_utility.get_time;

       utiladmin.util_procedure_audit_sql.save_info;

       commit; -- autonomous_transaction

       open t_search_return for 'select * from wrk_search_quote';

    end;


END search_quote_broker;

END search_quote;
/

