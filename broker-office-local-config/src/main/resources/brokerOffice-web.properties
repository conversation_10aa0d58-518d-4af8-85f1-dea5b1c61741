#
# BrokerOfficeWeb external configuration file
#
ldap-group-broker=WebZone-Broker-Advisors
ldap-group-broker-reassign=WebZone-Broker-Reassignment
ldap-group-underwritter=WebZone-Intact-Underwriters
ldap-group-quote-admins=WebZone-Intact-Quote-Admins
ldap-group-program-admins=WebZone-Program-Admins-Intact
ldap-group-admins=WebZone-Intact-Admins

spoe-mode=true
#same for the quote uploader (a test tool utility), it also must be turned to false outside of the dev environment
quote-uploader-enabled=true

# No logout URLs for local env
logout-url-b2b=
logout-url-b2e=

list-broker-notallowed=UA7710;UC0587;
list-subbroker-notallowed=

nfs-url-dir=/webzone/fsa_files/
nfs-server-mode=false

#ontario new interest rate date
interest_date_on=2016-04-01

### pdf url properties
pdf.url=https://ccqq-uat-qqcl-web.apps.np.iad.ca.inet/qqcl-web/v1/print/quote
pdf.applicationKey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.QkJPWg.WKh8YMx5ik4AW7vUyeUsDsdgVyn3xyqNBLk-r3lCUfw

## RQQ Upload REST Service
homeUploadRestURL=https://quoters-uat-webquote-transfer-api.apps.np.iad.ca.inet/transferToPendingQuote
homeUploadRestAPIKey=4fx2fEqzhBjrU40VPOgA1HPZ

## Dialer REST Service
dialerRestURL=https://quoters-uat-broker-dialer-service.apps.np.iad.ca.inet/v1/dialer/send
dialerRestAPIKey=les4chiens4aboient4mais4la4caravane4passe

## Bloom mq hanler service
bloomMqServiceUrl=https://quoters-uat-bloom-mq-handler.apps.np.iad.ca.inet/mqhandler/v1/message/send

###business-service-broker.xml
thread.max.timeout=300
