<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<!--
		This module should not inherit from parent POM because:
		1- It does not work at build time because we declare this module as a dependency to all child modules, so it would
		be self-referencing
		2- It is not really part of the project, it's just a streamlined, maven-style extra classpath
	-->
	<groupId>intact.web.brokeroffice</groupId>
	<artifactId>broker-office-local-config</artifactId>
	<!-- Fix version here because this module will never make it to Nexus (local only) -->
	<version>1.0.0</version>
	<packaging>jar</packaging>

	<name>Intact BROKER OFFICE LOCAL CONFIG</name>
	<description>
		Maven submodule containing configuration and properties file to build and deploy Webzone locally
	</description>
</project>