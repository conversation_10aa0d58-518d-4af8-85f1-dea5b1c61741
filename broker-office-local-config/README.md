# Broker office local config

This Maven submodule includes resources to run webzone locally. For other remote environments, resources have been moved
to the delivery repository. See https://githubifc.iad.ca.inet/lab-se/webzone-delivery.

This practice is to comply with the _build once, run anywhere_ philosophy. It means the application should not contain
any per-environment configuration files or properties and all these are the responsibility of the delivery process.

This submodule should only be included in the build with compiling with the _local_ maven profile.