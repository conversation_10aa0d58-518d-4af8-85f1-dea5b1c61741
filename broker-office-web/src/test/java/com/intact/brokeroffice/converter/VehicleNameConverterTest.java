package com.intact.brokeroffice.converter;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class VehicleNameConverterTest {
	
	private VehicleNameConverter converter = new VehicleNameConverter();

	@BeforeEach
	void setUp() throws Exception {
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@Test
	void getAsString() {
		String vehicle = "VehiculeTestNameConverter";
		
		String convertedString = converter.getAsString(null, null, vehicle);
		
		assertEquals("VehiculeTestNameConver...", convertedString);
	}

}
