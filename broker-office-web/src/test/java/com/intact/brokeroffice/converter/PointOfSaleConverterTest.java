package com.intact.brokeroffice.converter;

import static org.junit.jupiter.api.Assertions.*;

import jakarta.faces.convert.Converter;

import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class PointOfSaleConverterTest {

	private Converter converter;

	@BeforeEach
	void setUp() throws Exception {
		converter = new PointOfSaleConverter();
	}

	@AfterEach
	void tearDown() throws Exception {
		converter = null;
	}

	@Test
	void getAsStringPosContainsAWordLESSTHAN30CaractersLongNoSpace() {
		// Prepare
		String posName = StringUtils.repeat("A", 29);
		
		//Verify length
		assertTrue(posName.length() < 30);

		// Execute
		String result = converter.getAsString(null, null, posName);

		// Verify
		assertNotNull(result);
		assertEquals(29, posName.length());
		assertEquals("AAAAAAAAAAAAAAAAAAAAAAAAAAAAA", result);
	}

	@Test
	void getAsStringPosContainsAWordEXACTLY30CaractersLongNoSpace() {
		// Prepare
		String posName = StringUtils.repeat("A", 30);

		//Verify length
		assertEquals(30, posName.length());

		// Execute
		String result = converter.getAsString(null, null, posName);

		// Verify
		assertNotNull(result);
		assertEquals(30, posName.length());
		assertEquals("AAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", result);		
	}

	@Test
	void getAsStringPosContainsAWordMORETHAN30CaractersLongNoSpace() {
		// Prepare
		String posName = StringUtils.repeat("A", 31);

		//Verify length
		assertTrue(posName.length() > 30);

		// Execute
		String result = converter.getAsString(null, null, posName);

		// Verify
		assertNotNull(result);
		assertEquals(30, result.length());
		assertEquals("AAAAAAAAAAAAAAAAAAAAAAAAAAA...", result);	
	}

	@Test
	void getAsStringPosContainsAWordMORETHAN30CaractersLongWithHypen() {
		// Prepare
		String posName = StringUtils.repeat("A", 25) + "-" + StringUtils.repeat("A", 7);

		//Verify length
		assertEquals(33, posName.length());

		// Execute
		String result = converter.getAsString(null, null, posName);

		// Verify
		assertNotNull(result);
		assertEquals(30, result.length());
		assertEquals("AAAAAAAAAAAAAAAAAAAAAAAAA-A...", result);
	}

	@Test
	void getAsStringPosContainsALLWORDSLESSTHAN30Caracters() {
		// Prepare
		String posName = StringUtils.repeat("X", 25) + StringUtils.SPACE +
						 StringUtils.repeat("a", 27) + StringUtils.SPACE +
						 StringUtils.repeat("Z", 29);

		//Verify length
		assertEquals(posName.length(), (25 + 1 + 27 + 1 + 29));

		// Execute
		String result = converter.getAsString(null, null, posName);

		// Verify
		assertNotNull(result);
		assertEquals(result.length(), posName.length());
		assertEquals("XXXXXXXXXXXXXXXXXXXXXXXXX aaaaaaaaaaaaaaaaaaaaaaaaaaa ZZZZZZZZZZZZZZZZZZZZZZZZZZZZZ", result);
	}
}