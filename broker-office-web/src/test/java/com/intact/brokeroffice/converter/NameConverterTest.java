package com.intact.brokeroffice.converter;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class NameConverterTest {
	
	private NameConverter converter = new NameConverter();

	@BeforeEach
	void setUp() throws Exception {
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@Test
	void getAsString() {
		String name = "olivierMarcAntoine";
		
		String convertedString = converter.getAsString(null, null, name);
		
		assertEquals("Olivierma...", convertedString);
	}
}
