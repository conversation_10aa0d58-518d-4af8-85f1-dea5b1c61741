package com.intact.brokeroffice.listener;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;

import jakarta.faces.component.UIComponent;
import jakarta.faces.component.UIViewRoot;
import jakarta.faces.context.ExternalContext;
import jakarta.faces.context.FacesContext;
import jakarta.faces.event.SystemEvent;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RemoveResourcesListenerTest {

	private final RemoveResourcesListener listener = new RemoveResourcesListener();

	@Mock
	private UIViewRoot viewRoot;

	@Mock
	private SystemEvent event;

	@Mock
	private UIComponent uiComponent;

	@Mock
	ExternalContext externalContext;

	@BeforeEach
	void setUpStaticMocks() {
	}

	@AfterEach
	void tearDownStaticMocks() {
	}

	@Test
	void removeComponentCSS() {
		FacesContext context = ContextMocker.mockFacesContext();

		when(context.getViewRoot()).thenReturn(viewRoot);

		List<UIComponent> componentList = new ArrayList<>();
		Map<String, Object> attributes = new HashMap<>();

		componentList.add(uiComponent);

		attributes.put("library", "primefaces");
		attributes.put("name", "components.css");

		when(uiComponent.getAttributes()).thenReturn(attributes);
		when(viewRoot.getComponentResources(any(), anyString())).thenReturn(componentList);

		listener.processEvent(event);
		verify(viewRoot, times(1)).removeComponentResource(any(), any(), anyString());
	}

	@Test
	void dontRemoveComponentCSS() {
		FacesContext context = ContextMocker.mockFacesContext();

		when(context.getViewRoot()).thenReturn(viewRoot);

		List<UIComponent> componentList = new ArrayList<>();
		Map<String, Object> attributes = new HashMap<>();

		componentList.add(uiComponent);

		attributes.put("otherLibrary", "oldRichFaces");
		attributes.put("otherComponent", "otherComponent.css");

		when(uiComponent.getAttributes()).thenReturn(attributes);
		when(viewRoot.getComponentResources(any(), anyString())).thenReturn(componentList);

		listener.processEvent(event);
		verify(viewRoot, times(0)).removeComponentResource(any(), any(), anyString());
	}
}





















