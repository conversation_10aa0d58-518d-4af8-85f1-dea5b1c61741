package com.intact.brokeroffice.filter;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.io.IOException;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockHttpSession;

class SearchRefreshFilterTest {
	
	private SearchRefreshFilter searchFilter = new SearchRefreshFilter();
	
	private ServletRequest servletRequest;
	
	private ServletResponse servletResponse;
	
	private MockHttpSession session;
	
	@Mock
	private FilterChain filterChain;

	@InjectMocks
	private SearchRefreshFilter searchRefreshFilter;

	@BeforeEach
	void setUp() throws Exception {
		MockitoAnnotations.openMocks(this);
		session = new MockHttpSession();
		MockHttpServletRequest request = new MockHttpServletRequest();
		request.setSession(session);
		servletRequest = request;
		
		MockHttpServletResponse response = new MockHttpServletResponse();
		servletResponse = response;
		
	}

	@AfterEach
	void tearDown() throws Exception {
		servletRequest = null;
		servletResponse = null;
		session = null;
	}

	@BeforeEach
	void setUpStaticMocks() {
	}

	@AfterEach
	void tearDownStaticMocks() {
	}

	@Test
	void doFilterWithQuoteListFormNotNull() throws IOException, ServletException {	
		searchFilter.doFilter(servletRequest, servletResponse, filterChain);
		
		assertNotNull(((HttpServletRequest) servletRequest).getSession(false).getAttribute("lastRefreshTimestamp"));
		assertNotNull(((HttpServletRequest) servletRequest).getSession(false).getAttribute("autoSearchRefresh"));
	}

	@Test
	void doFilter() throws IOException, ServletException {
		MockHttpServletRequest request = new MockHttpServletRequest();
		request.setSession(session);
		request.addParameter("quoteListForm:searchPoll", "someContent");
		servletRequest = request;
		
		Object o = servletRequest.getParameter("quoteListForm:searchPoll");
		assertNotNull(o);
		
		((HttpServletRequest) servletRequest).getSession(false).setMaxInactiveInterval(0);
		
		searchFilter.doFilter(servletRequest, servletResponse, filterChain);
		
		assertNotNull(((HttpServletRequest) servletRequest).getSession(false).getAttribute("lastRefreshTimestamp"));
		assertNotNull(((HttpServletRequest) servletRequest).getSession(false).getAttribute("autoSearchRefresh"));
		assertTrue((Boolean)((HttpServletRequest) servletRequest).getSession(false).getAttribute("autoSearchRefresh"));
	}

}





















