package com.intact.brokeroffice.filter;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.Cookie;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.springframework.mock.web.MockFilterChain;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.common.web.api.auth.TamTokensEnum;
import com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;
import com.intact.brokeroffice.controller.language.LanguageController;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.canada.brm.domain.broker.BrokerWebOfficeAccess;
import com.intact.canada.brm.domain.user.UserAccount;

@ExtendWith(MockitoExtension.class)
//@MockitoSettings(strictness = Strictness.LENIENT)
class SessionTimeoutFilterTest {
    SessionTimeoutFilter sessionTimeoutFilter = new SessionTimeoutFilter();

    private IAccountsBusinessProcess accountsBusinessProcess;

    private ProvinceController provinceController;

    private String ldapGroupAdmins = "ldapGroupAdmins";

    private String ldapGroupProgramAdmins = "ldapGroupProgramAdmins";

    private String ldapGroupQuoteAdmins = "ldapGroupQuoteAdmins";

    private String ldapGroupUnderwritter = "ldapGroupUnderwritter";

    private String ldapGroupBroker = "ldapGroupBroker";

    private String ldapGroupBrokerReassign = "BrokerReassign";

	@BeforeEach
	void setUp() throws Exception {
        accountsBusinessProcess = mock(IAccountsBusinessProcess.class);
        provinceController = mock(ProvinceController.class);

        ReflectionTestUtils.setField(sessionTimeoutFilter, "accountsBusinessProcess", accountsBusinessProcess);
        ReflectionTestUtils.setField(sessionTimeoutFilter, "provinceController", provinceController);

        ReflectionTestUtils.setField(sessionTimeoutFilter, "ldapGroupAdmins", ldapGroupAdmins);
        ReflectionTestUtils.setField(sessionTimeoutFilter, "ldapGroupProgramAdmins", ldapGroupProgramAdmins);
        ReflectionTestUtils.setField(sessionTimeoutFilter, "ldapGroupQuoteAdmins", ldapGroupQuoteAdmins);
        ReflectionTestUtils.setField(sessionTimeoutFilter, "ldapGroupUnderwritter", ldapGroupUnderwritter);
        ReflectionTestUtils.setField(sessionTimeoutFilter, "ldapGroupBroker", ldapGroupBroker);
        ReflectionTestUtils.setField(sessionTimeoutFilter, "ldapGroupBrokerReassign", ldapGroupBrokerReassign);
    }

	@AfterEach
	void tearDown() throws Exception {
        accountsBusinessProcess = null;
        provinceController = null;
    }

	/**
	  Language not null isAjaxRequest true showTimeOutPage false
	 
	  @throws IOException
		 @throws ServletException
		*/
	@Test
	void doFilter() throws IOException, ServletException {

        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpSession session = new MockHttpSession();
        request.setSession(session);
        request.setRequestURI("test/index.jsf");
        request.addParameter("referenceNo", "testRefNumber");
        request.addParameter("AJAXREQUEST", "ajax test request");
        List<String> allowedLanguageList = new ArrayList<String>();
        allowedLanguageList.add("French");

        request.addParameter("language", "fr");
        Cookie cookie = new Cookie(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant(), "Some value");
        request.setCookies(cookie);
        ServletRequest servletRequest = request;

        MockHttpServletResponse response = new MockHttpServletResponse();
        ServletResponse servletResponse = response;

        FilterChain filterChain = new MockFilterChain();

        sessionTimeoutFilter.setLanguageController(new LanguageController());
        sessionTimeoutFilter.setProvinceController(new ProvinceController());
        sessionTimeoutFilter.doFilter(servletRequest, servletResponse, filterChain);

        assertEquals("testRefNumber",
                session.getAttribute(SessionConstantsEnum.EMAIL_REFERENCE_NUMBER.getSessionConstant()));
        assertEquals("fr", session.getAttribute(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant()));
        assertNotNull(response.getCookie(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant()));
        assertEquals("Some value", response.getCookie(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant())
                .getValue());
    }

	@Test
	void doFilterShowTimeOutPage() throws IOException, ServletException {

        ReflectionTestUtils.setField(sessionTimeoutFilter, "spoeMode", true);

        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpSession session = new MockHttpSession();
        request.setSession(session);
        request.setRequestedSessionId("testRequestSessioonID");
        request.setRequestURI("test/index.jsf");
        request.addParameter("referenceNo", "testRefNumber");
        request.addParameter("AJAXREQUEST", "ajax test request");
        List<String> allowedLanguageList = new ArrayList<String>();
        allowedLanguageList.add("fr");

        List<String> allowedCompaniesList = new ArrayList<String>();
        allowedCompaniesList.add("A");

        Cookie cookie = new Cookie("brokerDefaultCompany", "A");
        request.setCookies(cookie);
        request.setAttribute(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant(), null);
        ServletRequest servletRequest = request;

        MockHttpServletResponse response = new MockHttpServletResponse();
        ServletResponse servletResponse = response;

        FilterChain filterChain = new MockFilterChain();
        sessionTimeoutFilter.setLanguageController(new LanguageController());
        sessionTimeoutFilter.setProvinceController(new ProvinceController());
        sessionTimeoutFilter.doFilter(servletRequest, servletResponse, filterChain);

        assertEquals("testRefNumber",
                session.getAttribute(SessionConstantsEnum.EMAIL_REFERENCE_NUMBER.getSessionConstant()));
        assertEquals("A", session.getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant()));
    }

	/**
	  Language null isAjaxRequest false showTimeOutPage false
	  userRole ldapGroupBroker
	  @throws IOException
		 @throws ServletException
		 @throws BrokerServiceException
		*/
	@SuppressWarnings("unchecked")
	@Test
	void doFilter2() throws IOException, ServletException, BrokerServiceException {
        when(accountsBusinessProcess.getDefaultProvince(anyString())).thenReturn(null);

        UserAccount userAccount = new UserAccount();
        BrokerWebOfficeAccess aBrokerWebOfficeAccess = new BrokerWebOfficeAccess();
        aBrokerWebOfficeAccess.setMasterOwnerCode("MasterOwnerCode");
        userAccount.addBrokerWebOfficeAccess(aBrokerWebOfficeAccess);
        when(accountsBusinessProcess.findByUId(anyString())).thenReturn(userAccount);

        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpSession session = new MockHttpSession();

        List<String> companyList = new ArrayList<String>();
        companyList.add("A");
        session.setAttribute(SessionConstantsEnum.AVAILABLE_COMPANIES.getSessionConstant(), companyList);
        request.setSession(session);
        request.setRequestedSessionId("testRequestSessionId");
        request.setRequestURI("test/index.jsf");
        request.addParameter("referenceNo", "testRefNumber");

        Cookie cookie = new Cookie(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant(), "French");
        request.setCookies(cookie);
        request.addHeader(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "testUserName");
        request.addHeader(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), "testUserRole ldapGroupBroker -QC -ON");
        request.addHeader(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue(), "testClassicId");
        ServletRequest servletRequest = request;

        MockHttpServletResponse response = new MockHttpServletResponse();
        ServletResponse servletResponse = response;

        FilterChain filterChain = new MockFilterChain();

        sessionTimeoutFilter.setLanguageController(new LanguageController());
        sessionTimeoutFilter.setProvinceController(new ProvinceController());
        sessionTimeoutFilter.doFilter(servletRequest, servletResponse, filterChain);

        assertEquals("testRefNumber",
                session.getAttribute(SessionConstantsEnum.EMAIL_REFERENCE_NUMBER.getSessionConstant()));
        assertEquals("testUserName", session.getAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue()));
        assertEquals("ldapGroupBroker", session.getAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue()));
        assertEquals("testClassicId", session.getAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue()));
        assertEquals("A", session.getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant()));
        ArrayList<String> listMaster = (ArrayList<String>) session.getAttribute(SessionConstantsEnum.AVAILABLE_MASTERS.getSessionConstant());
        assertEquals("MasterOwnerCode", listMaster.get(0));
        assertEquals("A", response.getCookies()[0].getValue());
    }

	/**
	  Language null isAjaxRequest false showTimeOutPage false
	  userRole ldapGroupBrokerReassign
	  @throws IOException
		 @throws ServletException
		 @throws BrokerServiceException
		*/
	@SuppressWarnings("unchecked")
	@Test
	void doFilter3() throws IOException, ServletException, BrokerServiceException {
        when(accountsBusinessProcess.getDefaultProvince(anyString())).thenReturn(null);

        UserAccount userAccount = new UserAccount();
        BrokerWebOfficeAccess aBrokerWebOfficeAccess = new BrokerWebOfficeAccess();
        aBrokerWebOfficeAccess.setMasterOwnerCode("MasterOwnerCode");
        userAccount.addBrokerWebOfficeAccess(aBrokerWebOfficeAccess);
        when(accountsBusinessProcess.findByUId(anyString())).thenReturn(userAccount);

        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpSession session = new MockHttpSession();

        List<String> companyList = new ArrayList<String>();
        companyList.add("A");
        session.setAttribute(SessionConstantsEnum.AVAILABLE_COMPANIES.getSessionConstant(), companyList);
        request.setSession(session);
        request.setRequestedSessionId("testRequestSessionId");
        request.setRequestURI("test/index.jsf");
        request.addParameter("referenceNo", "testRefNumber");

        Cookie cookie = new Cookie(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant(), "French");
        request.setCookies(cookie);
        request.addHeader(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "testUserName");
        request.addHeader(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), "testUserRole BrokerReassign -QC -ON");
        request.addHeader(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue(), "testClassicId");
        ServletRequest servletRequest = request;

        MockHttpServletResponse response = new MockHttpServletResponse();
        ServletResponse servletResponse = response;

        FilterChain filterChain = new MockFilterChain();

        sessionTimeoutFilter.setLanguageController(new LanguageController());
        sessionTimeoutFilter.setProvinceController(new ProvinceController());
        sessionTimeoutFilter.doFilter(servletRequest, servletResponse, filterChain);

        assertEquals("testRefNumber",
                session.getAttribute(SessionConstantsEnum.EMAIL_REFERENCE_NUMBER.getSessionConstant()));
        assertEquals("testUserName", session.getAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue()));
        assertEquals("BrokerReassign", session.getAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue()));
        assertEquals("testClassicId", session.getAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue()));
        assertEquals("A", session.getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant()));
        ArrayList<String> listMaster = (ArrayList<String>) session.getAttribute(SessionConstantsEnum.AVAILABLE_MASTERS.getSessionConstant());
        assertEquals("MasterOwnerCode", listMaster.get(0));
        assertEquals("A", response.getCookies()[0].getValue());
    }

	/**
	  Test method for
	  {@link com.intact.brokeroffice.filter.SessionTimeoutFilter#doFilter(ServletRequest, ServletResponse, FilterChain)}
	  Case for broker language to be an invalid value
	  @throws IOException
		 @throws ServletException
		 @throws BrokerServiceException
		*/
  @Disabled
	@Test
	void do_filter_invalid_language() throws ServletException, IOException {
			MockHttpServletRequest mockRequest = new MockHttpServletRequest();
			ServletResponse mockResponse = new MockHttpServletResponse();
			FilterChain filterChain = new MockFilterChain();
			MockHttpSession mockSession = new MockHttpSession();
			mockRequest.setSession(mockSession);
			mockRequest.setRequestedSessionId("testRequestSessionID");
			mockRequest.setRequestURI("index.jsf");
			Cookie cookie = new Cookie(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant(), "TEST");
			mockRequest.setCookies(cookie);
			sessionTimeoutFilter.doFilter((ServletRequest) mockRequest, mockResponse, filterChain);
			assertThrows(NullPointerException.class, () ->
				fail("Servlet Exception should've been thrown - Invalid language"));
		}

	/**
	  Test method for
	  {@link com.intact.brokeroffice.filter.SessionTimeoutFilter#doFilter(ServletRequest, ServletResponse, FilterChain)}
	  Case for broker province to be an invalid value
	  @throws IOException
		 @throws ServletException
		 @throws BrokerServiceException
		*/
	@Test
	void do_filter_invalid_province() throws IOException, ServletException, BrokerServiceException {
        // General object setup
        MockHttpServletRequest mockRequest = new MockHttpServletRequest();
        ServletResponse mockResponse = new MockHttpServletResponse();
        FilterChain filterChain = new MockFilterChain();
        MockHttpSession mockSession = new MockHttpSession();

        mockRequest.setSession(mockSession);
        mockRequest.setRequestedSessionId("testRequestSessionID");
        ReflectionTestUtils.setField(sessionTimeoutFilter, "spoeMode", true); // Set spoe mode to access province validation

        // Set the cookie with a value that is not in the province whitelist
        Cookie cookie = new Cookie("brokerDefaultProvince", "TEST");
        mockRequest.setCookies(cookie);

        // Execute the method and validate the results
        sessionTimeoutFilter.doFilter((ServletRequest)mockRequest, mockResponse, filterChain);
        assertNull(mockSession.getAttribute(SessionConstantsEnum.PROVINCE.getSessionConstant()), "Province should not have been set in the session - invalid value");
    }

	/**
	  Test method for
	  {@link com.intact.brokeroffice.filter.SessionTimeoutFilter#doFilter(ServletRequest, ServletResponse, FilterChain)}
	  Case for a username (in this case the TAM username) to be of wrong format.
	  @throws IOException
		 @throws ServletException
		 @throws BrokerServiceException
		*/
  @Disabled
	@Test
	void do_filter_wrong_format_username() throws ServletException, IOException {
			MockHttpServletRequest mockRequest = new MockHttpServletRequest();
			ServletResponse mockResponse = new MockHttpServletResponse();
			FilterChain filterChain = new MockFilterChain();
			MockHttpSession mockSession = new MockHttpSession();
			mockRequest.setSession(mockSession);
			mockRequest.setRequestedSessionId("testRequestSessionID");
			mockRequest.setRequestURI("index.jsf");
			mockRequest.addHeader(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "@@@");
			sessionTimeoutFilter.doFilter((ServletRequest) mockRequest, mockResponse, filterChain);
			assertThrows(ServletException.class, () ->
				fail("Servlet Exception should've been thrown - Invalid user format"));
		}

}
