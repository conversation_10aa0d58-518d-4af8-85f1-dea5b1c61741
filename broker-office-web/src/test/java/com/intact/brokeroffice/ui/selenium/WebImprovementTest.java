package com.intact.brokeroffice.ui.selenium;

import org.openqa.selenium.By;
import org.openqa.selenium.By.ByClassName;
import org.openqa.selenium.By.ById;
import org.openqa.selenium.TimeoutException;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.phantomjs.PhantomJSDriver;
import org.openqa.selenium.support.ui.ExpectedConditions;
import org.openqa.selenium.support.ui.WebDriverWait;

import com.thoughtworks.selenium.Selenium;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import com.thoughtworks.selenium.webdriven.WebDriverBackedSelenium;

@SuppressWarnings("static-access")
public class WebImprovementTest {

	private static WebDriver driver = null;
	private static Selenium selenium = null;

	private final static String seleniumGridHub = "http://127.0.0.1:4444";
	private static final String applicationPath = "C:/Data/java/workspace/wiwz-workspace/broker-office-project/broker-office-web";
	protected static final String phantomPath = "C:/Apps/phantomjs/phantomjs.exe";
	protected static final String applicationUrl = "http://localhost:9088/WebZone/timeout.jsf";

	//@BeforeClass
	public static void initialize() {

		System.setProperty("phantomjs.binary.path", phantomPath);

		driver = new PhantomJSDriver();

		selenium = new WebDriverBackedSelenium(driver, seleniumGridHub);

		selenium.open("http://localhost:9088/WebZone/timeout.jsf");
		selenium.select("id=spoeForm:accessLevel",
				"label=WebZone-Intact-Admins-ON");
		selenium.click("id=spoeForm:submit");
		waitForTextByClass("tab_quotes", "QUOTES", 30);
		clickByClass("tab_accessManagement");
		waitForText("subBrokerListForm", "Point of Sale List", 30);

		selenium.click("id=subBrokerListForm:brokerList:0:modifySubBroker");
		waitForText("subBrokerForm", "Modify Point of Sale", 30);
	}

	//@AfterClass
	public static void terminate() {
		selenium.close();
		driver.quit();
	}

	//@Test	
	public void testBrokerageFirmPhoneEmpty() {

		// init
		selenium.type("id=subBrokerForm:brokeragePhoneToll", "1");
		selenium.type("id=subBrokerForm:brokeragePhoneArea", "111");
		selenium.type("id=subBrokerForm:brokeragePhoneExchange", "111");
		selenium.type("id=subBrokerForm:brokeragePhoneNumber", "1111");

		// phone area
		selenium.type("id=subBrokerForm:brokeragePhoneArea", "");
		selenium.click("id=subBrokerForm:updateSubBroker");
		this.waitForText(
				"subBrokerForm:errorBrokeragePhoneArea",
				"You must enter a complete telephone number (i.e.: ************ or **************).",
				30);
		selenium.type("id=subBrokerForm:brokeragePhoneArea", "111");

		// phone exchange
		selenium.type("id=subBrokerForm:brokeragePhoneExchange", "");
		selenium.click("id=subBrokerForm:updateSubBroker");
		this.waitForText(
				"subBrokerForm:errorBrokeragePhoneArea",
				"You must enter a complete telephone number (i.e.: ************ or **************).",
				30);
		selenium.type("id=subBrokerForm:brokeragePhoneExchange", "111");

		// phone number
		selenium.type("id=subBrokerForm:brokeragePhoneNumber", "");
		selenium.click("id=subBrokerForm:updateSubBroker");
		this.waitForText(
				"subBrokerForm:errorBrokeragePhoneArea",
				"You must enter a complete telephone number (i.e.: ************ or **************).",
				30);
		selenium.type("id=subBrokerForm:brokeragePhoneNumber", "111");

		// init
		selenium.type("id=subBrokerForm:brokeragePhoneToll", "1");
		selenium.type("id=subBrokerForm:brokeragePhoneArea", "111");
		selenium.type("id=subBrokerForm:brokeragePhoneExchange", "111");
		selenium.type("id=subBrokerForm:brokeragePhoneNumber", "1111");

	}

	//@Test
	public void testBrokerageFirmPhoneTooShort() {

		// init
		selenium.type("id=subBrokerForm:brokeragePhoneToll", "1");
		selenium.type("id=subBrokerForm:brokeragePhoneArea", "111");
		selenium.type("id=subBrokerForm:brokeragePhoneExchange", "111");
		selenium.type("id=subBrokerForm:brokeragePhoneNumber", "1111");

		// phone area
		selenium.type("id=subBrokerForm:brokeragePhoneArea", "1");
		selenium.click("id=subBrokerForm:updateSubBroker");
		this.waitForText(
				"subBrokerForm:errorBrokeragePhoneArea",
				"You must enter a complete telephone number (i.e.: ************ or **************).",
				30);
		selenium.type("id=subBrokerForm:brokeragePhoneArea", "111");

		// phone exchange
		selenium.type("id=subBrokerForm:brokeragePhoneExchange", "1");
		selenium.click("id=subBrokerForm:updateSubBroker");
		this.waitForText(
				"subBrokerForm:errorBrokeragePhoneArea",
				"You must enter a complete telephone number (i.e.: ************ or **************).",
				30);
		selenium.type("id=subBrokerForm:brokeragePhoneExchange", "111");

		// phone number
		selenium.type("id=subBrokerForm:brokeragePhoneNumber", "1");
		selenium.click("id=subBrokerForm:updateSubBroker");
		this.waitForText(
				"subBrokerForm:errorBrokeragePhoneArea",
				"You must enter a complete telephone number (i.e.: ************ or **************).",
				30);
		selenium.type("id=subBrokerForm:brokeragePhoneNumber", "111");

	}

	//@Test
	public void testBrokerageFirmPhoneOk() {

		selenium.type("id=subBrokerForm:brokeragePhoneToll", "1");
		selenium.type("id=subBrokerForm:brokeragePhoneArea", "234");
		selenium.type("id=subBrokerForm:brokeragePhoneExchange", "567");
		selenium.type("id=subBrokerForm:brokeragePhoneNumber", "8900");
		selenium.click("id=subBrokerForm:updateSubBroker");
		waitForText("subBrokerListForm", "was modified successfully.", 30);

		selenium.click("id=subBrokerListForm:brokerList:0:modifySubBroker");
		waitForText("subBrokerForm", "Modify Point of Sale", 30);

		assertEquals("1",
				selenium.getValue("id=subBrokerForm:brokeragePhoneToll"));
		assertEquals("234",
				selenium.getValue("id=subBrokerForm:brokeragePhoneArea"));
		assertEquals("567",
				selenium.getValue("id=subBrokerForm:brokeragePhoneExchange"));
		assertEquals("8900",
				selenium.getValue("id=subBrokerForm:brokeragePhoneNumber"));

	}

	//@Test
	public void testBrokerageFirmWebPhoneAlpha() {

		// init
		selenium.type("id=subBrokerForm:brokerageWebPhoneToll", "1");
		selenium.type("id=subBrokerForm:brokerageWebPhoneArea", "111");
		selenium.type("id=subBrokerForm:brokerageWebPhoneExchange", "111");
		selenium.type("id=subBrokerForm:brokerageWebPhoneNumber", "1111");

		// phone area
		selenium.type("id=subBrokerForm:brokerageWebPhoneArea", "A");
		selenium.click("id=subBrokerForm:updateSubBroker");
		this.waitForText(
				"subBrokerForm:errorBrokerageWebPhoneArea",
				"You must enter a complete telephone number (i.e.: ************ or **************).",
				30);
		selenium.type("id=subBrokerForm:brokerageWebPhoneArea", "111");

		// phone exchange
		selenium.type("id=subBrokerForm:brokerageWebPhoneExchange", "BB");
		selenium.click("id=subBrokerForm:updateSubBroker");
		this.waitForText(
				"subBrokerForm:errorBrokerageWebPhoneArea",
				"You must enter a complete telephone number (i.e.: ************ or **************).",
				30);
		selenium.type("id=subBrokerForm:brokerageWebPhoneExchange", "111");

		// phone number
		selenium.type("id=subBrokerForm:brokerageWebPhoneNumber", "BB");
		selenium.click("id=subBrokerForm:updateSubBroker");
		this.waitForText(
				"subBrokerForm:errorBrokerageWebPhoneArea",
				"You must enter a complete telephone number (i.e.: ************ or **************).",
				30);
		selenium.type("id=subBrokerForm:brokerageWebPhoneNumber", "111");

		// init
		selenium.type("id=subBrokerForm:brokerageWebPhoneToll", "1");
		selenium.type("id=subBrokerForm:brokerageWebPhoneArea", "111");
		selenium.type("id=subBrokerForm:brokerageWebPhoneExchange", "111");
		selenium.type("id=subBrokerForm:brokerageWebPhoneNumber", "1111");

	}

	//@Test
	public void testBrokerageFirmWebPhoneEmpty() {

		// init
		selenium.type("id=subBrokerForm:brokerageWebPhoneToll", "1");
		selenium.type("id=subBrokerForm:brokerageWebPhoneArea", "111");
		selenium.type("id=subBrokerForm:brokerageWebPhoneExchange", "111");
		selenium.type("id=subBrokerForm:brokerageWebPhoneNumber", "1111");

		// phone area
		selenium.type("id=subBrokerForm:brokerageWebPhoneArea", "");
		selenium.click("id=subBrokerForm:updateSubBroker");
		this.waitForText(
				"subBrokerForm:errorBrokerageWebPhoneArea",
				"You must enter a complete telephone number (i.e.: ************ or **************).",
				30);
		selenium.type("id=subBrokerForm:brokerageWebPhoneArea", "111");

		// phone exchange
		selenium.type("id=subBrokerForm:brokerageWebPhoneExchange", "");
		selenium.click("id=subBrokerForm:updateSubBroker");
		this.waitForText(
				"subBrokerForm:errorBrokerageWebPhoneArea",
				"You must enter a complete telephone number (i.e.: ************ or **************).",
				30);
		selenium.type("id=subBrokerForm:brokerageWebPhoneExchange", "111");

		// phone number
		selenium.type("id=subBrokerForm:brokerageWebPhoneNumber", "");
		selenium.click("id=subBrokerForm:updateSubBroker");
		this.waitForText(
				"subBrokerForm:errorBrokerageWebPhoneArea",
				"You must enter a complete telephone number (i.e.: ************ or **************).",
				30);
		selenium.type("id=subBrokerForm:brokerageWebPhoneNumber", "111");

		// init
		selenium.type("id=subBrokerForm:brokerageWebPhoneToll", "1");
		selenium.type("id=subBrokerForm:brokerageWebPhoneArea", "111");
		selenium.type("id=subBrokerForm:brokerageWebPhoneExchange", "111");
		selenium.type("id=subBrokerForm:brokerageWebPhoneNumber", "1111");

	}

	//@Test
	public void testBrokerageFirmWebPhoneTooShort() {

		// init
		selenium.type("id=subBrokerForm:brokerageWebPhoneToll", "1");
		selenium.type("id=subBrokerForm:brokerageWebPhoneArea", "111");
		selenium.type("id=subBrokerForm:brokerageWebPhoneExchange", "111");
		selenium.type("id=subBrokerForm:brokerageWebPhoneNumber", "1111");

		// phone area
		selenium.type("id=subBrokerForm:brokerageWebPhoneArea", "1");
		selenium.click("id=subBrokerForm:updateSubBroker");
		this.waitForText(
				"subBrokerForm:errorBrokerageWebPhoneArea",
				"You must enter a complete telephone number (i.e.: ************ or **************).",
				30);
		selenium.type("id=subBrokerForm:brokerageWebPhoneArea", "111");

		// phone exchange
		selenium.type("id=subBrokerForm:brokerageWebPhoneExchange", "1");
		selenium.click("id=subBrokerForm:updateSubBroker");
		this.waitForText(
				"subBrokerForm:errorBrokerageWebPhoneArea",
				"You must enter a complete telephone number (i.e.: ************ or **************).",
				30);
		selenium.type("id=subBrokerForm:brokerageWebPhoneExchange", "111");

		// phone number
		selenium.type("id=subBrokerForm:brokerageWebPhoneNumber", "1");
		selenium.click("id=subBrokerForm:updateSubBroker");
		this.waitForText(
				"subBrokerForm:errorBrokerageWebPhoneArea",
				"You must enter a complete telephone number (i.e.: ************ or **************).",
				30);
		selenium.type("id=subBrokerForm:brokerageWebPhoneNumber", "111");

		// init
		selenium.type("id=subBrokerForm:brokerageWebPhoneToll", "1");
		selenium.type("id=subBrokerForm:brokerageWebPhoneArea", "111");
		selenium.type("id=subBrokerForm:brokerageWebPhoneExchange", "111");
		selenium.type("id=subBrokerForm:brokerageWebPhoneNumber", "1111");

	}

	//@Test
	public void testBrokerageFirmWebPhoneOk() {

		selenium.type("id=subBrokerForm:brokerageWebPhoneToll", "1");
		selenium.type("id=subBrokerForm:brokerageWebPhoneArea", "234");
		selenium.type("id=subBrokerForm:brokerageWebPhoneExchange", "567");
		selenium.type("id=subBrokerForm:brokerageWebPhoneNumber", "8900");
		selenium.click("id=subBrokerForm:updateSubBroker");
		waitForText("subBrokerListForm", "was modified successfully.", 30);

		selenium.click("id=subBrokerListForm:brokerList:0:modifySubBroker");
		waitForText("subBrokerForm", "Modify Point of Sale", 30);

		assertEquals("1",
				selenium.getValue("id=subBrokerForm:brokerageWebPhoneToll"));
		assertEquals("234",
				selenium.getValue("id=subBrokerForm:brokerageWebPhoneArea"));
		assertEquals("567",
				selenium.getValue("id=subBrokerForm:brokerageWebPhoneExchange"));
		assertEquals("8900",
				selenium.getValue("id=subBrokerForm:brokerageWebPhoneNumber"));
	}

	//@Test
	public void testBrokerageImageWrongType() {

		// init
		this.attachFile("subBrokerForm:englishBrokerageImage", applicationPath
				+ "/src/test/resources/LogoBrokerageEng.png");
		this.attachFile("subBrokerForm:frenchBrokerageImage", applicationPath
				+ "/src/test/resources/LogoBrokerageFr.png");

		// french
		this.attachFile("subBrokerForm:frenchBrokerageImage", applicationPath
				+ "/src/test/resources/text-file.txt");
		selenium.click("id=subBrokerForm:updateSubBroker");
		waitForText(
				"subBrokerForm",
				"The image you are attempting to upload is not a recognized image format. You are required to use a file with a format of bmp, gif, jpg, or png.",
				30);

		// english
		this.attachFile("subBrokerForm:frenchBrokerageImage", applicationPath
				+ "/src/test/resources/text-file.txt");
		this.attachFile("subBrokerForm:englishBrokerageImage", applicationPath
				+ "/src/test/resources/LogoBrokerageEng.png");
		;
		selenium.click("id=subBrokerForm:updateSubBroker");
		waitForText(
				"subBrokerForm",
				"The image you are attempting to upload is not a recognized image format. You are required to use a file with a format of bmp, gif, jpg, or png.",
				30);

	}

	//@Test
	public void testBrokerageImageOK() {

		this.attachFile("subBrokerForm:frenchBrokerageImage", applicationPath
				+ "/src/test/resources/LogoBrokerageFr.png");
		this.attachFile("subBrokerForm:englishBrokerageImage", applicationPath
				+ "/src/test/resources/LogoBrokerageEn.png");
		selenium.click("id=subBrokerForm:updateSubBroker");
		waitForText("subBrokerListForm", "was modified successfully.", 30);

		selenium.click("id=subBrokerListForm:brokerList:0:modifySubBroker");
		waitForText("subBrokerForm", "Modify Point of Sale", 30);
	}

	protected static void waitForText(String id, String text, int timeout) {
		try {
			WebDriverWait wait = new WebDriverWait(driver, timeout);
			assertTrue(wait.until(ExpectedConditions
					.textToBePresentInElementLocated(ById.id(id), text)));
		} catch (TimeoutException te) {
			throw te;
		}
	}

	protected static void waitForTextByClass(String clazz, String text,
			int timeout) {
		try {
			WebDriverWait wait = new WebDriverWait(driver, timeout);
			assertTrue(wait.until(ExpectedConditions
					.textToBePresentInElementLocated(
							ByClassName.className(clazz), text)));
		} catch (TimeoutException te) {
			throw te;
		}
	}

	protected static void clickByClass(String clazz) {
		driver.findElement(By.className(clazz)).click();
	}

	protected static void attachFile(String id, String fileName) {
		driver.findElement(By.id(id)).sendKeys(fileName);
	}
}
