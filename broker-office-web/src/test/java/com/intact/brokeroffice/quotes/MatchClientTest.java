package com.intact.brokeroffice.quotes;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.intact.brokeroffice.business.quote.QuotesBean;
import com.intact.brokeroffice.service.search.MatchClient;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.joda.time.DateTime;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class MatchClientTest {

  private MatchClient matchClient;

	@BeforeEach
	void setUp() {
    matchClient = new MatchClient();
  }

	@Test
	void match_empty_list() {
    // Prepare
    // Execute
    // Verify
    assertEquals(0, matchClient.match(Collections.<QuotesBean>emptyList()).size());
  }

	@Test
	void match_null_list() {
    // Prepare
    // Execute
    // Verify
    assertEquals(0, matchClient.match(null).size());
  }

	@Test
	void match_no_parent_child_elements_personal_lines() {
    // Prepare
    QuotesBean parent = new QuotesBean();
    parent.setFirstName("Paul");
    parent.setLastName("Dupont");
    parent.setBirthDate(new DateTime(1980, 1, 1, 0, 0).toDate());
    parent.setPostalCode("H1H 1H1");
    parent.setAgreementNumber("parent");
    parent.setLineOfBusiness(LineOfBusinessCodeEnum.PERSONAL_LINES);
    parent.setInceptionDate(new DateTime(2017, 5, 5, 8, 0).toDate());

    QuotesBean parent2 = new QuotesBean();
    parent2.setFirstName("John");
    parent2.setLastName("Dupont");
    parent2.setBirthDate(new DateTime(1981, 1, 1, 0, 0).toDate());
    parent2.setPostalCode("H1H 1H1");
    parent2.setAgreementNumber("parent");
    parent2.setLineOfBusiness(LineOfBusinessCodeEnum.PERSONAL_LINES);
    parent2.setInceptionDate(new DateTime(2017, 5, 5, 8, 5).toDate());

    QuotesBean parent3 = new QuotesBean();
    parent3.setFirstName("Paul");
    parent3.setLastName("Messier");
    parent3.setBirthDate(new DateTime(1982, 1, 1, 0, 0).toDate());
    parent3.setPostalCode("H1H 1H1");
    parent3.setAgreementNumber("parent");
    parent3.setLineOfBusiness(LineOfBusinessCodeEnum.PERSONAL_LINES);
    parent3.setInceptionDate(new DateTime(2017, 5, 5, 9, 0).toDate());

    // Execute
    List<QuotesBean> result = matchClient
        .match(Arrays.asList(new QuotesBean[]{parent, parent2, parent3}));

    // Verify
    assertEquals(3, result.size());
  }

	@Test
	void match_no_parent_child_elements_commercial_lines() {
    // Prepare
    QuotesBean parent = new QuotesBean();
    parent.setUnstructuredName("unstructuredName");
    parent.setPostalCode("H1H 1H1");
    parent.setAgreementNumber("parent");
    parent.setLineOfBusiness(LineOfBusinessCodeEnum.COMMERCIAL_LINES);
    parent.setInceptionDate(new DateTime(2017, 5, 5, 8, 0).toDate());

    QuotesBean parent2 = new QuotesBean();
    parent2.setUnstructuredName("unstructuredName2");
    parent2.setPostalCode("H1H 1H1");
    parent2.setAgreementNumber("parent2");
    parent2.setLineOfBusiness(LineOfBusinessCodeEnum.COMMERCIAL_LINES);
    parent2.setInceptionDate(new DateTime(2017, 5, 5, 8, 5).toDate());

    QuotesBean parent3 = new QuotesBean();
    parent3.setUnstructuredName("unstructuredName3");
    parent3.setPostalCode("H1H 1H1");
    parent3.setAgreementNumber("parent3");
    parent3.setLineOfBusiness(LineOfBusinessCodeEnum.COMMERCIAL_LINES);
    parent3.setInceptionDate(new DateTime(2017, 5, 5, 9, 0).toDate());

    // Execute
    List<QuotesBean> result = matchClient
        .match(Arrays.asList(new QuotesBean[]{parent, parent2, parent3}));

    // Verify
    assertEquals(3, result.size());
  }

	@Test
	void match_one_parent_containing_achild_element_personal_lines() {
    // Prepare
    QuotesBean parent = new QuotesBean();
    parent.setId("449499994994");
    parent.setFirstName("Paul");
    parent.setLastName("Dupont");
    parent.setBirthDate(new DateTime(1980, 1, 1, 0, 0).toDate());
    parent.setPostalCode("H1H 1H1");
    parent.setAgreementNumber("parent");
    parent.setLineOfBusiness(LineOfBusinessCodeEnum.PERSONAL_LINES);
    parent.setInceptionDate(new DateTime(2017, 5, 5, 8, 0).toDate());

    QuotesBean child = new QuotesBean();
    child.setId("449499994995");
    child.setFirstName("Paul");
    child.setLastName("Dupont");
    child.setBirthDate(new DateTime(1980, 1, 1, 0, 0).toDate());
    child.setPostalCode("H1H 1H1");
    child.setAgreementNumber("child");
    child.setLineOfBusiness(LineOfBusinessCodeEnum.PERSONAL_LINES);
    child.setInceptionDate(new DateTime(2017, 5, 5, 8, 5).toDate());

    QuotesBean parent3 = new QuotesBean();
    parent3.setId("449499994996");
    parent3.setFirstName("Paul");
    parent3.setLastName("Messier");
    parent3.setBirthDate(new DateTime(1982, 1, 1, 0, 0).toDate());
    parent3.setPostalCode("H1H 1H1");
    parent3.setAgreementNumber("parent3");
    parent3.setLineOfBusiness(LineOfBusinessCodeEnum.PERSONAL_LINES);
    parent3.setInceptionDate(new DateTime(2017, 5, 5, 9, 0).toDate());

    // Execute
    List<QuotesBean> result = matchClient
        .match(Arrays.asList(new QuotesBean[]{parent, parent3, child}));

    // Verify
    assertEquals(2, result.size());

    assertEquals(parent, result.get(0));
    assertEquals(1, result.get(0).getChildrens().size());
    assertEquals(child, result.get(0).getChildrens().get(0));

    assertEquals(parent3, result.get(1));
    assertEquals(0, result.get(1).getChildrens().size());
  }

	@Test
	void match_one_parent_containing_achild_element_commercial_lines() {
    // Prepare
    QuotesBean parent = new QuotesBean();
    parent.setId("449499994994");
    parent.setUnstructuredName("unstructuredName");
    parent.setPostalCode("H1H 1H1");
    parent.setAgreementNumber("parent");
    parent.setLineOfBusiness(LineOfBusinessCodeEnum.COMMERCIAL_LINES);
    parent.setInceptionDate(new DateTime(2017, 5, 5, 8, 0).toDate());

    QuotesBean parent2 = new QuotesBean();
    parent2.setId("449499994995");
    parent2.setUnstructuredName("unstructuredName");
    parent2.setPostalCode("H1H 1H1");
    parent2.setAgreementNumber("parent2");
    parent2.setLineOfBusiness(LineOfBusinessCodeEnum.COMMERCIAL_LINES);
    parent2.setInceptionDate(new DateTime(2017, 5, 5, 8, 5).toDate());

    QuotesBean parent3 = new QuotesBean();
    parent3.setId("449499994996");
    parent3.setUnstructuredName("unstructuredName3");
    parent3.setPostalCode("H1H 1H1");
    parent3.setAgreementNumber("parent3");
    parent3.setLineOfBusiness(LineOfBusinessCodeEnum.COMMERCIAL_LINES);
    parent3.setInceptionDate(new DateTime(2017, 5, 5, 9, 0).toDate());

    // Execute
    List<QuotesBean> result = matchClient
        .match(Arrays.asList(new QuotesBean[]{parent2, parent3, parent}));

    // Verify
    assertEquals(2, result.size());
    assertEquals(parent2, result.get(0));
    assertEquals(parent3, result.get(1));

    assertEquals(1, result.get(0).getChildrens().size());
    assertEquals(parent, result.get(0).getChildrens().get(0));

    assertEquals(0, result.get(1).getChildrens().size());
  }
}
