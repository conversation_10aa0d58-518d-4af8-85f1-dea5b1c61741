package com.intact.brokeroffice.quotes.transformer;

import com.ing.canada.plp.report.insurancePolicy.InsurancePolicyBrokerInfo;
import com.intact.plt.search.service.client.util.HomeResponseType;

/**
 * Factory class used to create instances of {@link InsurancePolicyBrokerInfo} for testing purposes
 */
public class HomeResponseTypeFactory {

	public static HomeResponseType create(final String agreementNumber) {
		HomeResponseType home = new HomeResponseType();

		home.setProductNumber(agreementNumber);

		return home;
	}
}
