package com.intact.brokeroffice.controller.province;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;

import jakarta.faces.context.ExternalContext;
import jakarta.faces.context.FacesContext;
import jakarta.servlet.http.Cookie;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.common.web.api.auth.TamTokensEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.controller.authentification.AuthentificationController;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ProvinceControllerTest {

	private MockedStatic<FacesContext> mockedFacesContext;

	private ProvinceController provinceController = new ProvinceController();

	private FacesContext context;

	private MockHttpServletRequest request;

	private MockHttpSession session;

	private ExternalContext externalContext;
	
	private AuthentificationController authController;


	@BeforeEach
	void setUp() throws Exception {
		mockedFacesContext = Mockito.mockStatic(FacesContext.class);
		externalContext = mock(ExternalContext.class);
		context = mock(FacesContext.class);
		request = new MockHttpServletRequest();
		session = new MockHttpSession();
		request.setSession(session);

		authController = mock(AuthentificationController.class);
				
		ReflectionTestUtils.setField(provinceController, "extension", "");
		ReflectionTestUtils.setField(provinceController, "authController", authController);

		mockedFacesContext.when(FacesContext::getCurrentInstance).thenReturn(context);
		when(context.getExternalContext()).thenReturn(externalContext);
		when(externalContext.getSession(anyBoolean())).thenReturn(session);
		when(externalContext.getRequest()).thenReturn(request);
		when(authController.isMasterRole()).thenReturn(false);
		
		
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@AfterEach
	void tearDownStaticMocks() {
		mockedFacesContext.closeOnDemand();
	}

	@Test
	void getCompany() {
		session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), "A");

		String province = provinceController.getCompany();

		assertEquals("A", province);
		assertEquals("A", session.getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant()));
	}

	@Test
	void getProvinceDefault() {
		session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), null);	
		Cookie cookie = new Cookie("brokerDefaultCompany", "A");
		request.setCookies(cookie);
		
		String province = provinceController.getCompany();
		
		assertEquals("A", province);
	}

	@Test
	void isCompanyA() {
		session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), "A");
		
		boolean result = provinceController.isCompanyA();
		
		assertTrue(result);
	}

	@Test
	void isCompany6() {
		session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), "6");
		
		boolean result = provinceController.isCompany6();
		
		assertTrue(result);
	}

	@Test
	void isCompany3() {
		session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), "3");
		
		boolean result = provinceController.isCompany3();
		
		assertTrue(result);
	}

	@Test
	void numberDaysLeftForCieA() {
		ReflectionTestUtils.setField(provinceController, "company", "A");
		ReflectionTestUtils.setField(provinceController, "companyEnumCode", CifCompanyEnum.INTACT_QC);
		assertEquals(31, provinceController.getNumberOfDaysLeft().intValue());
	}

	@Test
	void numberDaysLeftForCie6() {
		ReflectionTestUtils.setField(provinceController, "company", "6");
		ReflectionTestUtils.setField(provinceController, "companyEnumCode", CifCompanyEnum.INTACT_HALIFAX);
		assertEquals(61, provinceController.getNumberOfDaysLeft().intValue());
	}

	@Test
	void numberDaysLeftForCie3() {
		ReflectionTestUtils.setField(provinceController, "company", "3");
		ReflectionTestUtils.setField(provinceController, "companyEnumCode", CifCompanyEnum.INTACT_AB);
		assertEquals(31, provinceController.getNumberOfDaysLeft().intValue());
	}

	@Test
	void getCifCompanyEnumQC() {
		this.session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber());
		assertEquals(CifCompanyEnum.INTACT_QC, provinceController.getCompanyEnumCode());
	}

	@Test
	void getCifCompanyEnumAB() {
		this.session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), CifCompanyEnum.INTACT_AB.getSubBrokerCompanyNumber());
		assertEquals(CifCompanyEnum.INTACT_AB, provinceController.getCompanyEnumCode());
	}

	@Test
	void getCifCompanyEnumON() {
		this.session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), CifCompanyEnum.INTACT_HALIFAX.getSubBrokerCompanyNumber());
		assertEquals(CifCompanyEnum.INTACT_HALIFAX, provinceController.getCompanyEnumCode());
	}

	@Test
	void getCifCompanyEnumOTHER() {
		this.session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), CifCompanyEnum.BELAIR.getSubBrokerCompanyNumber());
		assertEquals(CifCompanyEnum.INTACT_QC, provinceController.getCompanyEnumCode());
	}

	@Test
	void manufacturerCompanyCodeQC() {
		this.session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber());
		assertEquals(ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION, provinceController.getManufacturerCompanyCode());
	}

	@Test
	void manufacturerCompanyCodeAB() {
		this.session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), CifCompanyEnum.INTACT_AB.getSubBrokerCompanyNumber());
		assertEquals(ManufacturerCompanyCodeEnum.ING_WESTERN_REGION, provinceController.getManufacturerCompanyCode());
	}

	@Test
	void manufacturerCompanyCodeON() {
		this.session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), CifCompanyEnum.INTACT_HALIFAX.getSubBrokerCompanyNumber());
		assertEquals(ManufacturerCompanyCodeEnum.ING_CENTRAL_ATLANTIC_REGION,
				provinceController.getManufacturerCompanyCode());
	}

	@Test
	void manufacturerCompanyCodeOTHER() {
		this.session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), CifCompanyEnum.BELAIR.getSubBrokerCompanyNumber());
		assertEquals(ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION, provinceController.getManufacturerCompanyCode());
	}

	@Test
	void retreiveProvincesList(){
		List<String> listProvinces = new ArrayList<String>();
		listProvinces.add("QC");
		listProvinces.add("ON");
		listProvinces.add("AB");
		session.setAttribute(SessionConstantsEnum.AVAILABLE_PROVINCES
					.getSessionConstant(), listProvinces);
		
		List<ProvinceCodeEnum> resultProvinceList = provinceController.getProvinces();
		
		assertEquals(3, resultProvinceList.size());
	}

	@Test
	void retreiveCompaniesList(){
		List<String> listCompanies = new ArrayList<String>();
		listCompanies.add("A");
		listCompanies.add("6");
		listCompanies.add("3");
		session.setAttribute(SessionConstantsEnum.AVAILABLE_COMPANIES
				.getSessionConstant(), listCompanies);
		
		List<SubscriptionCompanyEnum> resultCompanyList = provinceController.getCompanies();
		
		assertEquals(3, resultCompanyList.size());
	}

	@Test
	void showUploadCieA(){
		this.session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber());
		session.setAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue(), "ClassicIdTest");
		session.setAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "halcionIdTest");
		
		assertTrue(provinceController.showUpload("QF999999999"));
	}

	@Test
	void showUploadCie6TargetQuote(){
		this.session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), CifCompanyEnum.INTACT_HALIFAX.getSubBrokerCompanyNumber());
		session.setAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue(), "ClassicIdTest");
		session.setAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "halcionIdTest");
		
		assertFalse(provinceController.showUpload("BA99999999"));
	}

	@Test
	void showUploadCie6NonTargetQuote(){
    this.session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), CifCompanyEnum.INTACT_HALIFAX.getSubBrokerCompanyNumber());
    session.setAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue(), "ClassicIdTest");
    session.setAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "halcionIdTest");

    assertTrue(provinceController.showUpload("QF999999999"));
  }

	@Test
	void showAssignCie6(){
    this.session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), CifCompanyEnum.INTACT_HALIFAX.getSubBrokerCompanyNumber());
    session.setAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue(), "ClassicIdTest");
    session.setAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "halcionIdTest");

    assertTrue(provinceController.getShowAssign());
  }

	@Test
	void showUploadCie3(){
		this.session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), CifCompanyEnum.INTACT_AB.getSubBrokerCompanyNumber());
		session.setAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue(), "ClassicIdTest");
		session.setAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "halcionIdTest");
		
		assertTrue(provinceController.showUpload("QF999999999"));
	}

	@Test
	void showUploadOTHER(){
		this.session.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), CifCompanyEnum.BELAIR.getSubBrokerCompanyNumber());
		session.setAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue(), "ClassicIdTest");
		session.setAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "halcionIdTest");
		
		assertFalse(provinceController.showUpload("QF999999999"));
	}
}



















