package com.intact.brokeroffice.controller.viewquote.offer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.List;
import java.util.Locale;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.common.domain.QuoteCalculationDetails;
import com.ing.canada.common.services.api.rating.IQuotationService;
import com.ing.canada.plp.domain.coverage.CoverageRepositoryEntry;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.CoverageTypeCodeEnum;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.domain.vehicle.VehicleDetailSpecificationRepositoryEntry;
import com.ing.canada.plp.domain.vehicle.VehicleRepositoryEntry;
import com.ing.canada.plp.helper.IInsuranceRiskOfferHelper;

class OfferAdapterQCTest {

	private OfferAdapterQC offerAdapterQC = new OfferAdapterQC();

	private IQuotationService mockQuotationService = mock(IQuotationService.class);

	protected IInsuranceRiskOfferHelper mockInsuranceRiskOfferHelper = mock(IInsuranceRiskOfferHelper.class);

	@BeforeEach
	void setUp() throws Exception {
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@Test
	void loadVehiculeOfferBeanList() {
		List<VehicleOfferBeanQC> listVehicule = new ArrayList<VehicleOfferBeanQC>();

		InsuranceRisk insuranceRisk = new InsuranceRisk();
		insuranceRisk.setInsuranceRiskSequence(Short.valueOf("1"));
		insuranceRisk.setInsuranceRiskOfferSystemSelectedIndicator(false);
		InsuranceRiskOffer insuranceRiskOffer = new InsuranceRiskOffer();
		insuranceRiskOffer.setAnnualPremium(1000);
		CoverageOffer coverageOffer = new CoverageOffer();
		CoverageRepositoryEntry coverageRepositoryEntry = new CoverageRepositoryEntry();
		coverageRepositoryEntry.setCoverageType(CoverageTypeCodeEnum.ENDORSEMENT);
		coverageRepositoryEntry.setCoverageCode("NV3");
		coverageOffer.setCoverageRepositoryEntry(coverageRepositoryEntry);
		insuranceRiskOffer.addCoverageOffer(coverageOffer);
		insuranceRisk.addInsuranceRiskOffer(insuranceRiskOffer);
		insuranceRisk.setSelectedInsuranceRiskOffer(insuranceRiskOffer);

		VehicleRepositoryEntry vehicleRepositoryEntry = new VehicleRepositoryEntry();
		vehicleRepositoryEntry.setVehicleMakeFrench("Hyundai");
		vehicleRepositoryEntry.setVehicleModelFrench("Accent");
		VehicleDetailSpecificationRepositoryEntry vehicleDetailSpecificationRepositoryEntry = new VehicleDetailSpecificationRepositoryEntry();
		vehicleDetailSpecificationRepositoryEntry.setVehicleYear(2013);
		vehicleDetailSpecificationRepositoryEntry.setVehicleRepositoryEntry(vehicleRepositoryEntry);
		Vehicle vehicle = new Vehicle();
		vehicle.setVehicleDetailSpecificationRepositoryEntry(vehicleDetailSpecificationRepositoryEntry);
		insuranceRisk.setVehicle(vehicle);

		PolicyVersion policyVersion = new PolicyVersion();
		policyVersion.addInsuranceRisk(insuranceRisk);

		com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy insPolicy = new com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy();
		insPolicy.setApplicationMode(ApplicationModeEnum.REGULAR_QUOTE);
		policyVersion.setInsurancePolicy(insPolicy);
		
		ReflectionTestUtils.setField(offerAdapterQC, "insuranceRiskOfferHelper", mockInsuranceRiskOfferHelper);

		offerAdapterQC.loadVehicleOfferBeanList(listVehicule, policyVersion, Locale.FRENCH);

		VehicleOfferBeanQC resultVehicule = listVehicule.get(0);

		assertEquals(1, listVehicule.size());
		assertTrue(insuranceRisk.getInsuranceRiskSequence() == resultVehicule.getSequence());
		assertEquals("2013", resultVehicule.getYear());
		assertEquals("Hyundai", resultVehicule.getMake());
		assertEquals("Accent", resultVehicule.getModel());
		assertEquals(insuranceRisk.getSelectedInsuranceRiskOffer().getOfferType(),
				resultVehicule.getSelectedOfferType());
	}

	@Test
	void resetEndorsement() {
		CoverageOfferBean coverageOfferBean = new CoverageOfferBean();
		offerAdapterQC.resetEndorsementRank("I90", coverageOfferBean);
		assertEquals(1, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterQC.resetEndorsementRank("NV1", coverageOfferBean);
		assertEquals(2, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterQC.resetEndorsementRank("NV2", coverageOfferBean);
		assertEquals(3, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterQC.resetEndorsementRank("33E", coverageOfferBean);
		assertEquals(4, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterQC.resetEndorsementRank("NBD", coverageOfferBean);
		assertEquals(5, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterQC.resetEndorsementRank("43V", coverageOfferBean);
		assertEquals(6, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterQC.resetEndorsementRank("UBI", coverageOfferBean);
		assertEquals(7, coverageOfferBean.getRank().intValue());
	}

	@Test
	void loadOfferBean() {
		OfferBean anOfferBean = new OfferBean(OfferTypeCodeEnum.CUSTOM);
		InsuranceRiskOffer insuranceRiskOffer = new InsuranceRiskOffer();
		ProvinceCodeEnum province = ProvinceCodeEnum.QUEBEC;
		
		ReflectionTestUtils.setField(offerAdapterQC, "quotationService", mockQuotationService);
		QuoteCalculationDetails quotationCalculationDetail = new QuoteCalculationDetails();
		quotationCalculationDetail.setAnnualAmountWithTaxes(1000.50);
		quotationCalculationDetail.setMonthlyPaymentWithTaxes(95.67);
		when(
				mockQuotationService.loadOfferBean(anyDouble(), (InsuranceRiskOffer) any(),
						(ProvinceCodeEnum) any(), anyDouble(), anyBoolean())).thenReturn(
				quotationCalculationDetail);
		offerAdapterQC.loadOfferBean(anOfferBean, insuranceRiskOffer, province);

		if (anOfferBean.getAnnualPremiumWithTaxes() != null) {
			assertTrue(1000.50 == anOfferBean.getAnnualPremiumWithTaxes());
			assertTrue(95.67 == anOfferBean.getMonthlyPremiumWithTaxes());
		}
	}
}
