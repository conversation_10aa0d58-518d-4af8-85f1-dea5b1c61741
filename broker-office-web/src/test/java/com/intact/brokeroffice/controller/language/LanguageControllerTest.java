package com.intact.brokeroffice.controller.language;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.Locale;
import java.util.Map;

import jakarta.faces.component.UIViewRoot;
import jakarta.faces.context.ExternalContext;
import jakarta.faces.context.FacesContext;

import org.apache.commons.collections4.map.HashedMap;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.mock.web.MockHttpSession;

import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class LanguageControllerTest {

	private MockedStatic<FacesContext> mockedFacesContext;

	private LanguageController languageController = new LanguageController();

	private FacesContext context;

	private MockHttpSession session;

	private ExternalContext externalContext;
	
	private UIViewRoot viewRoot;
	
	private final Locale locale = Locale.CANADA_FRENCH;
	
	private Map<String, Object> sessionMap;

	@BeforeEach
	void setUp() {
		mockedFacesContext = Mockito.mockStatic(FacesContext.class);
		context = mock(FacesContext.class);
		session = new MockHttpSession();
		externalContext = mock(ExternalContext.class);
		viewRoot = mock(UIViewRoot.class);
		
		mockedFacesContext.when(FacesContext::getCurrentInstance).thenReturn(context);
		when(context.getExternalContext()).thenReturn(externalContext);
		when(externalContext.getSession(anyBoolean())).thenReturn(session);
		when(context.getViewRoot()).thenReturn(viewRoot);
		when(viewRoot.getLocale()).thenReturn(locale);
		
		session.setAttribute(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant(), "fr");
		session.setAttribute(SessionConstantsEnum.PROVINCE.getSessionConstant(), "QC");
		
		sessionMap = new HashedMap<>();
		when(externalContext.getSessionMap()).thenReturn(sessionMap);
	}

	@AfterEach
	void tearDown(){
		context = null;
		session = null;
		externalContext = null;
		viewRoot = null;
		sessionMap = null;
	}

	@AfterEach
	void tearDownStaticMocks() {
		mockedFacesContext.closeOnDemand();
	}

	/**
	 * retreive local when sesisonMap local is null
	 */
	@Test
	void getLocale() {
		Locale expectedLocal = new Locale("fr", "CA", "QC");
		
		Locale resultLocal = languageController.getLocale();
		
		assertEquals(expectedLocal, resultLocal);
		assertEquals(expectedLocal, sessionMap.get("locale"));
	}

	@Test
	void setFrenchAsLocale(){
		
		languageController.french();
		
		Locale expectedLocal = new Locale("fr", "CA", "QC");
		
		Locale result = viewRoot.getLocale();
		
		assertEquals(expectedLocal, sessionMap.get("locale"));
	}

	@Test
	void setEnglishAsLocale(){
		
		languageController.english();
		
		Locale expectedLocal = new Locale("en", "CA", "QC");
		
		Locale result = viewRoot.getLocale();

		assertEquals(expectedLocal, sessionMap.get("locale"));
	}

}

















