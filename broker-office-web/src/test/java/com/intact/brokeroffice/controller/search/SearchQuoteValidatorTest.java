/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.controller.search;

import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.plp.domain.enums.LineOfInsuranceCodeEnum;
import com.ing.canada.plp.report.insurancePolicy.criteria.QuoteSearchCriteria;
import com.intact.brokeroffice.business.domain.BrokerQuoteSearchCriteria;
import com.intact.brokeroffice.helper.FaceMessageHelper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.powermock.reflect.Whitebox;

import java.util.Calendar;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * DESCRIPTION : UNIT TEST FOR VALIDATOR OF SEARCH
 * 
 * @author: selleing
 * 
 * 
 */

public class SearchQuoteValidatorTest {

	private MockedStatic<FaceMessageHelper> mockedFaceMessageHelper;

	private BrokerQuoteSearchCriteria searchBean;
	private SearchQuoteValidator searchQuoteValidator;

	@BeforeEach
	void setup() {
		this.searchQuoteValidator = new SearchQuoteValidator();
		this.searchBean = new BrokerQuoteSearchCriteria();
		
		// We make certain that the showErrorMessage attribute is set to true (for better coverage)
		this.searchQuoteValidator.setShowErrorMessage(true);

		this.searchBean.setSelectedLinesOfBusiness(new String[] {LineOfBusinessEnum.PERSONAL_LINE.getCode(), LineOfBusinessEnum.COMMERCIAL_LINE.getCode()});
		this.searchBean.setSelectedLinesOfInsurance(new String[] {LineOfInsuranceCodeEnum.AUTOMOBILE.getCode(), LineOfInsuranceCodeEnum.RESIDENTIAL.getCode()});
	}

	@BeforeEach
	void setUpStaticMocks() {
		mockedFaceMessageHelper = mockStatic(FaceMessageHelper.class);
	}

	@AfterEach
	void tearDownStaticMocks() {
		mockedFaceMessageHelper.closeOnDemand();
	}

	/**
	 * Tests for the isMandatory method.
	 * {@link com.intact.brokeroffice.controller.search.SearchQuoteValidator#isMandatory(QuoteSearchCriteria)}
	 */
	@Test
	void mandatoryFieldsNoInfo() {
		SearchController controller = mock(SearchController.class);
		
		// We call the isMandatory method without setting any information on the search bean
		assertFalse(this.searchQuoteValidator.isMandatory(this.searchBean, controller), "IsMandatory function should've returned false - no information given");
		assertFalse(this.searchQuoteValidator.validate(this.searchBean), "Validate function should've returned false - invalid mandatory fields");
	}

	@Test
	void isMandatoryOnlyFirstName() {
		
		// We only set the first name before calling the isMandatory method
		this.searchBean.setFirstName("Stephane");
		this.searchBean.setLastName(null);
		SearchController controller = mock(SearchController.class);
		assertFalse(this.searchQuoteValidator.isMandatory(this.searchBean, controller), "IsMandatory function should've returned false - only first name given");
		assertFalse(this.searchQuoteValidator.validate(this.searchBean), "Validate function should've returned false - invalid mandatory fields");
	}

	@Test
	void isMandatoryOnlyLastName() {
		SearchController controller = mock(SearchController.class);
		// We only set the last name before calling the isMandatory method
		this.searchBean.setFirstName(null);
		this.searchBean.setLastName("Gendron");
		assertFalse(this.searchQuoteValidator.isMandatory(this.searchBean, controller), "IsMandatory function should've returned false - only last name given");
		assertFalse(this.searchQuoteValidator.validate(this.searchBean), "Validate function should've returned false - invalid mandatory fields");
	}

	@Test
	void isMandatoryCompleteName() {

		// We set both the first and last name before calling the isMandatory method
		this.searchBean.setFirstName("Stephane");
		this.searchBean.setLastName("Gendron");
		SearchController controller = mock(SearchController.class);	
		
		assertTrue(this.searchQuoteValidator.isMandatory(this.searchBean, controller), "IsMandatory function should've returned true - first and last name given only");
		assertTrue(this.searchQuoteValidator.validate(this.searchBean, controller), "Validate function should've returned true - valid mandatory fields");
	}

	/**
	 * Tests for the isSevenDaysRangeLastUpdate private method. Tested through the isMandatory method.
	 * {@link com.intact.brokeroffice.controller.search.SearchQuoteValidator#isSevenDaysRangeLastUpdate(QuoteSearchCriteria)}
	 */
	@Test
	void isMandatorySevenDaysRangeLastUpdateTooLongRange() {
		SearchController controller = mock(SearchController.class);
		
		// We set the last update dates so the range is greater than 7 days
		Calendar lastUpdateStartDate = Calendar.getInstance();
		lastUpdateStartDate.set(2016, 2, 10);
		this.searchBean.setFrom(lastUpdateStartDate.getTime());
		Calendar lastUpdateEndDate = Calendar.getInstance();
		lastUpdateEndDate.set(2016, 2, 18);
		this.searchBean.setTo(lastUpdateEndDate.getTime());
		
		assertFalse(this.searchQuoteValidator.isMandatory(this.searchBean, controller), "IsMandatory function should've returned false - last update range > 7 days");
		assertFalse(this.searchQuoteValidator.validate(this.searchBean, controller), "Validate function should've returned false - invalid mandatory fields");
	}

	@Test
	void isMandatorySevenDaysRangeLastUpdateInvalidDates() {
		SearchController controller = mock(SearchController.class);
		// We set invalid last update range (empty to date)
		Calendar lastUpdateStartDate = Calendar.getInstance();
		lastUpdateStartDate.set(2012, 1, 10);
		this.searchBean.setFrom(lastUpdateStartDate.getTime());
		assertFalse(this.searchQuoteValidator.isMandatory(this.searchBean, controller), "IsMandatory function should've returned true - invalid last update dates");
		assertFalse(this.searchQuoteValidator.validate(this.searchBean, controller), "Validate function should've returned true - invalid mandatory fields");
	}

	@Test
	void isMandatorySevenDaysRangeLastUpdateValid() {
		SearchController controller = mock(SearchController.class);

		// We set the last update dates so the range is equal or less than 7 days
		Calendar lastUpdateStartDate = Calendar.getInstance();
		lastUpdateStartDate.add(Calendar.DAY_OF_MONTH, -6);
		this.searchBean.setFrom(lastUpdateStartDate.getTime());
		Calendar lastUpdateEndDate = Calendar.getInstance();
		this.searchBean.setTo(lastUpdateEndDate.getTime());
		assertTrue(this.searchQuoteValidator.isMandatory(this.searchBean, controller), "IsMandatory function should've returned true - valid last update range");
		//Assert.assertTrue("Validate function should've returned true - valid mandatory fields", this.searchQuoteValidator.validate(this.searchBean, controller));
	}

	/**
	 * Tests for the isSevenDaysRangeCreation private method. Tested through the isMandatory method.
	 * {@link com.intact.brokeroffice.controller.search.SearchQuoteValidator#isSevenDaysRangeCreation(QuoteSearchCriteria)}
	 */
	@Test
	void isMandatorySevenDaysRangeCreationTooLongRange() {
		SearchController controller = mock(SearchController.class);
		
		// We set the creation dates so the range is greater than 7 days
		Calendar lastUpdateStartDate = Calendar.getInstance();
		lastUpdateStartDate.add(Calendar.DAY_OF_MONTH, -8);
		this.searchBean.setCreationDateFrom(lastUpdateStartDate.getTime());
		Calendar lastUpdateEndDate = Calendar.getInstance();
		this.searchBean.setCreationDateTo(lastUpdateEndDate.getTime());
		
		assertFalse(this.searchQuoteValidator.isMandatory(this.searchBean, controller), "IsMandatory function should've returned false - creation range > 7 days");
		assertFalse(this.searchQuoteValidator.validate(this.searchBean, controller), "Validate function should've returned false - invalid mandatory fields");
	}

	@Test
	void isMandatorySevenDaysRangeCreationInvalidDates() {
		SearchController controller = mock(SearchController.class);
		
		// We set invalid creation range (empty to date)
		Calendar lastUpdateStartDate = Calendar.getInstance();
		lastUpdateStartDate.set(2012, 1, 10);
		this.searchBean.setCreationDateFrom(lastUpdateStartDate.getTime());
		assertFalse(this.searchQuoteValidator.isMandatory(this.searchBean, controller), "IsMandatory function should've returned true - invalid creation dates");
		assertFalse(this.searchQuoteValidator.validate(this.searchBean, controller), "Validate function should've returned true - invalid mandatory fields");
	}

	@Test
	void isMandatorySevenDaysRangeCreationValid() {
		SearchController controller = mock(SearchController.class);
		
		// We set the creation dates so the range is equal or less than 7 days
		Calendar lastUpdateStartDate = Calendar.getInstance();
		lastUpdateStartDate.add(Calendar.DAY_OF_MONTH, -6);
		this.searchBean.setCreationDateFrom(lastUpdateStartDate.getTime());
		Calendar lastUpdateEndDate = Calendar.getInstance();
		this.searchBean.setCreationDateTo(lastUpdateEndDate.getTime());
		assertTrue(this.searchQuoteValidator.isMandatory(this.searchBean, controller), "IsMandatory function should've returned true - valid creation range");
		//Assert.assertTrue("Validate function should've returned true - valid mandatory fields", this.searchQuoteValidator.validate(this.searchBean, controller));
	}

	/**
	 * Tests for the validateDates protected method. All tests are done with the last update dates.
	 * {@link com.intact.brokeroffice.controller.search.SearchQuoteValidator#validateDates(Date, Date, String, String, boolean)}
	 */
	@Test
	void validateDatesFutureFromDate() {
		SearchController controller = mock(SearchController.class);
		
		this.searchBean.setSelectedMaster("2633");
		assertTrue(this.searchQuoteValidator.validate(this.searchBean, controller));

		
		// We set a from date set in the future before calling the validateDates method
		Calendar fromCalendar = Calendar.getInstance();
		fromCalendar.setTime(new Date());
		fromCalendar.add(Calendar.DATE, 1);
		this.searchBean.setFrom(fromCalendar.getTime());
		this.searchBean.setTo(null);
		assertFalse(this.searchQuoteValidator.validateDates(this.searchBean.getFrom(), this.searchBean.getTo(), "mainTab:quoteSearchForm:calFrom","mainTab:quoteSearchForm:calTo", true, controller), 
				"Validate dates function should've returned false - future from date");
		//Assert.assertFalse("Validate function should've returned false - invalid dates", this.searchQuoteValidator.validate(this.searchBean, controller));
	}

	@Test
	void validateDatesFutureToDate() {
		
		SearchController controller = mock(SearchController.class);
		
		this.searchBean.setSelectedMaster("2633");
		assertTrue(this.searchQuoteValidator.validate(this.searchBean, controller));
		
		
		// We set a to date set in the future before calling the validateDates method
		Calendar toCalendar = Calendar.getInstance();
		toCalendar.setTime(new Date());
		toCalendar.add(Calendar.DATE, 1);
		this.searchBean.setTo(toCalendar.getTime());
		this.searchBean.setFrom(null);
		assertFalse(this.searchQuoteValidator.validateDates(this.searchBean.getFrom(), this.searchBean.getTo(), "mainTab:quoteSearchForm:calFrom","mainTab:quoteSearchForm:calTo", true, controller), 
				"Validate dates function should've returned false - future to date");
		//Assert.assertFalse("Validate function should've returned false - invalid dates", this.searchQuoteValidator.validate(this.searchBean, controller));
	}

	@Test
	void validateDatesInvalidRange() {
		
		SearchController controller = mock(SearchController.class);
		
		this.searchBean.setSelectedMaster("2633");
		assertTrue(this.searchQuoteValidator.validate(this.searchBean, controller));
		
		
		// We set the from date so that it is set after the to date
		Calendar fromCalendar = Calendar.getInstance();
		fromCalendar.set(2011, 0, 1);
		Calendar toCalendar = Calendar.getInstance();
		toCalendar.set(2010, 0, 1);
		this.searchBean.setFrom(fromCalendar.getTime());
		this.searchBean.setTo(toCalendar.getTime());
		assertFalse(this.searchQuoteValidator.validateDates(this.searchBean.getFrom(), this.searchBean.getTo(), "mainTab:quoteSearchForm:calFrom","mainTab:quoteSearchForm:calTo", true, controller), 
				"Validate dates function should've returned false - invalid date range");
		//Assert.assertFalse("Validate function should've returned false - invalid dates", this.searchQuoteValidator.validate(this.searchBean, controller));
	}

	@Test
	void validateDatesValid() {
		
		SearchController controller = mock(SearchController.class);
		
		this.searchBean.setSelectedMaster("2633");
		assertTrue(this.searchQuoteValidator.validate(this.searchBean, controller));
		
		// We set a from date set in the future before calling the validateDates method
		Calendar fromCalendar = Calendar.getInstance();
		Calendar toCalendar = Calendar.getInstance();
		this.searchBean.setFrom(fromCalendar.getTime());
		this.searchBean.setTo(toCalendar.getTime());
		assertTrue(this.searchQuoteValidator.validateDates(this.searchBean.getFrom(), this.searchBean.getTo(), "mainTab:quoteSearchForm:calFrom","mainTab:quoteSearchForm:calTo", true, controller), 
				"Validate dates function should've returned true");
		//Assert.assertTrue("Validate function should've returned true - valid dates", this.searchQuoteValidator.validate(this.searchBean, controller));
	}

	/**
	 * Tests for the validatePostalCode protected method.
	 * {@link com.intact.brokeroffice.controller.search.SearchQuoteValidator#validatePostalCode(String, boolean)}
	 */
	@Test
	void validatePostalCodeIncompleteCode() {
		
		SearchController controller = mock(SearchController.class);
		
		// We set an incomplete postal code before calling the validatePostalCode method
		this.searchBean.setPostalCode("A9A9A");
		assertFalse(this.searchQuoteValidator.validatePostalCode(this.searchBean.getPostalCode()), "Validate postal code function should've returned false - incomplete postal code");
		assertFalse(this.searchQuoteValidator.validate(this.searchBean, controller), "Validate function should've returned false - invalid postal codes");
	}

	@Test
	void validatePostalCodeInvalidCode() {
		
		SearchController controller = mock(SearchController.class);
		
		// We set an invalid postal code before calling the validatePostalCode method
		this.searchBean.setPostalCode("9A9A9A");
		assertFalse(this.searchQuoteValidator.validatePostalCode(this.searchBean.getPostalCode()), "Validate postal code function should've returned false - invalid postal code");
		assertFalse(this.searchQuoteValidator.validate(this.searchBean, controller), "Validate function should've returned false - invalid postal codes");
	}

	@Test
	void validatePostalCodeValidCode() {
		
		SearchController controller = mock(SearchController.class);
		
		// We set a valid postal code before calling the validatePostalCode method
		this.searchBean.setPostalCode("A9A9A9");
		assertTrue(this.searchQuoteValidator.validatePostalCode(this.searchBean.getPostalCode()), "Validate postal code function should've returned true");
		assertTrue(this.searchQuoteValidator.validate(this.searchBean, controller), "Validate function should've returned true - valid postal codes");
	}

	/**
	 * Tests for the validatePhoneNumber protected method.
	 * {@link com.intact.brokeroffice.controller.search.SearchQuoteValidator#validatePhoneNumber(QuoteSearchCriteria, boolean)}
	 */
	@Test
	void validatePhoneNumberInvalidAreaCode() {
		SearchController controller = mock(SearchController.class);
		
		// We set an invalid area code before calling the validatePhoneNumber method
		this.searchBean.setPhoneAreaCode("51");
		assertFalse(this.searchQuoteValidator.validatePhoneNumber(this.searchBean, true), "Validate phone number should've returned false - invalid area code");
		assertFalse(this.searchQuoteValidator.validate(this.searchBean, controller), "Validate function should've returned false - invalid phone number");
	}

	@Test
	void validatePhoneNumberInvalidPrefix() {
		SearchController controller = mock(SearchController.class);
		
		// We set a valid area code, but an invalid prefix
		this.searchBean.setPhoneAreaCode("514");
		this.searchBean.setPhoneNumberPrefix("44");
		assertFalse(this.searchQuoteValidator.validatePhoneNumber(this.searchBean, true), "Validate phone number should've returned false - invalid phone prefix");
		assertFalse(this.searchQuoteValidator.validate(this.searchBean, controller), "Validate function should've returned false - invalid phone number");
	}

	@Test
	void validatePhoneNumberInvalidSuffix() {
		SearchController controller = mock(SearchController.class);
		
		// We set a valid area code and prefix, but an invalid suffix
		this.searchBean.setPhoneAreaCode("514");
		this.searchBean.setPhoneNumberPrefix("444");
		this.searchBean.setPhoneNumberSuffix("555");
		assertFalse(this.searchQuoteValidator.validatePhoneNumber(this.searchBean, true), "Validate phone number should've returned false - invalid area code");
		assertFalse(this.searchQuoteValidator.validate(this.searchBean, controller), "Validate function should've returned false - invalid phone number");
	}

	@Test
	void validatePhoneNumberBadPattern() {
		
		// We set the phone number so it doesn't match the TELEPHONE_REGEXP
		this.searchBean.setPhoneAreaCode("514");
		this.searchBean.setPhoneNumberPrefix("444");
		this.searchBean.setPhoneNumberSuffix("5555invalid");
		
		assertFalse(this.searchQuoteValidator.validatePhoneNumber(this.searchBean, true), "Validate phone number method should've returned false - invalid phone number");
	}

	@Test
	void validatePhoneNumberValid() {
		SearchController controller = mock(SearchController.class);
		
		// We set an invalid area code before calling the validatePhoneNumber method
		this.searchBean.setPhoneAreaCode("514");
		this.searchBean.setPhoneNumberPrefix("444");
		this.searchBean.setPhoneNumberSuffix("5555");
		assertTrue(this.searchQuoteValidator.validatePhoneNumber(this.searchBean, true), "Validate phone number should've returned true");
		assertTrue(this.searchQuoteValidator.validate(this.searchBean, controller), "Validate function should've returned true - valid phone number");
	}

	/**
	 * Test for the validate method with the value of closedBrokerQuotes set to false.
	 * {@link com.intact.brokeroffice.controller.search.SearchQuoteValidator#validate(QuoteSearchCriteria, controller)}
	 */
	@Test
	void validateIsClosedBrokerQuotes() {
		SearchController controller = mock(SearchController.class);
		
		// Setting the closedBrokerQuotes attribute to true should stop the validation
		this.searchBean.setClosedBrokerQuotes(true);
		
		assertTrue(this.searchQuoteValidator.validate(this.searchBean, controller), "Validate function should have returned true - closed broker quotes");
		
	}

	/**
	 * Test for the validate method with a searchController passed as parameter.
	 * {@link com.intact.brokeroffice.controller.search.SearchQuoteValidator#validate(QuoteSearchCriteria, SearchController)}
	 */
	@Test
	void validateWithSearchController() {
		// We create a new search bean as well as mock a search controller
		SearchController controller = mock(SearchController.class);
		when(controller.getCheckSearchAccess()).thenReturn(false);
		
		// We call the validate method and we check if the correct attributes were modified
		this.searchQuoteValidator.validate(this.searchBean, controller);
		
		assertNull(this.searchBean.getCreationDateFrom(), "Attribute creationDateFrom should've been null");
		assertNull(this.searchBean.getCreationDateTo(), "Attribute creationDateTo should've been null");
		assertNull(this.searchBean.getFrom(), "Attribute from should've been null");
		assertNull(this.searchBean.getTo(), "Attribute to should've been null");
		assertNull(this.searchBean.getSelectedClientFollowUp(), "Attribute selectedClientFollowUp should've been null");
		assertNull(this.searchBean.getSelectedQuoteSource(), "Attribute selectedQuoteSource should've been null");
		assertNull(this.searchBean.getSelectedQuoteStatus(), "Attribute selectedQuoteStatus should've been null");
		assertFalse(this.searchBean.isClosedBrokerQuotes(), "Attribute closedBrokerQuotes should've been false");
	}

	/**
	 * Tests for the validateMasterBroker protected method.
	 * {@link com.intact.brokeroffice.controller.search.SearchQuoteValidator#validateMasterBroker(String, boolean)}
	 */
	@Test
	void validateMasterBrokerValid() {
		assertTrue(this.searchQuoteValidator.validateMasterBroker("value", true), "Validate master broker method should've returned true");
	}

	@Test
	void validateMasterBrokerEmpty() {
		assertFalse(this.searchQuoteValidator.validateMasterBroker("", true), "Validate master broker method should've returned false");
	}

	@Test
	void validate_lines_of_business_invalid() throws Exception {
		// Prepare
		this.searchBean.setSelectedLinesOfBusiness(null);

		// Execute
		// Verify
		assertFalse((Boolean) Whitebox.invokeMethod(searchQuoteValidator, "validateLinesOfBusiness", searchBean), "Validate lines of business[[validateLinesOfBusiness]] method should've returned false");
	}

	@AfterEach
	void tearDown() {
		this.searchQuoteValidator = null;
		this.searchBean = null;
	}

	/**
	 * Utility method need to run this test case in Maven 1.
	 * 
	 * @return Test suite
	 */
//	public static junit.framework.Test suite() {
//		return new JUnit4TestAdapter(SearchQuoteValidatorTest.class);
//	}
}
