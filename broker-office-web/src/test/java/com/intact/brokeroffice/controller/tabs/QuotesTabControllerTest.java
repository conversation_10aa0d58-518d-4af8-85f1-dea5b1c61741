package com.intact.brokeroffice.controller.tabs;

import java.util.Map;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;

import com.intact.brokeroffice.util.SearchState;

import static org.junit.jupiter.api.Assertions.assertEquals;

class QuotesTabControllerTest {

	private QuotesTabController quoteTabController = new QuotesTabController();
	
	@Mock
	private GeneralTabController generalTabController;

	@BeforeEach
	void setUp() throws Exception {
		ReflectionTestUtils.setField(quoteTabController, "generalTabController", generalTabController);
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@Test
	void getSearchState() {
		assertEquals(0, quoteTabController.getSearchStates().size());
		
		quoteTabController.getSearchState(0);
		
		 Map<Integer, SearchState> searchStates = quoteTabController.getSearchStates();
		
		assertEquals(1, searchStates.size());
	}

}
