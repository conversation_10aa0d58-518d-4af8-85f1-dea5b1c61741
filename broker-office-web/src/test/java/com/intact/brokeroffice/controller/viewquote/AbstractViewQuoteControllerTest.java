package com.intact.brokeroffice.controller.viewquote;

import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.cif.domain.impl.SubBrokers;
import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.common.services.api.policy.IPolicyVersionInfoService;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransaction;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransactionActivity;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransactionSubActivity;
import com.ing.canada.plp.domain.enums.*;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicyNote;
import com.ing.canada.plp.domain.party.Consent;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.party.Phone;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.subbrokerassignment.SubBrokerAssignment;
import com.ing.canada.plp.domain.usertype.AuditTrail;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.helper.enums.PaymentPlanEnum;
import com.ing.canada.plp.report.enums.QuoteStatusCodeEnum;
import com.ing.canada.plp.service.IBusinessTransactionSubActivityService;
import com.ing.canada.plp.service.IInsurancePolicyService;
import com.ing.canada.plp.service.IPolicyHolderService;
import com.intact.brokeroffice.business.common.ICommandBusinessProcess;
import com.intact.brokeroffice.business.domain.Quote;
import com.intact.brokeroffice.business.domain.dialer.PartyDTO;
import com.intact.brokeroffice.business.domain.dialer.PhoneDTO;
import com.intact.brokeroffice.business.domain.dialer.QuoteDialerDTO;
import com.intact.brokeroffice.business.domain.dialer.VehicleDTO;
import com.intact.brokeroffice.business.exception.DialerServiceException;
import com.intact.brokeroffice.business.helper.IQuoteDialerDTOHelper;
import com.intact.brokeroffice.business.quote.NoteBean;
import com.intact.brokeroffice.business.quote.QuotesBean;
import com.intact.brokeroffice.business.quote.util.PolicyInfo;
import com.intact.brokeroffice.controller.authentification.AuthentificationController;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;
import com.intact.brokeroffice.controller.language.LanguageController;
import com.intact.brokeroffice.controller.permission.PermissionController;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.brokeroffice.controller.search.SearchController;
import com.intact.brokeroffice.controller.viewquote.offer.OfferAdapterQC;
import com.intact.brokeroffice.controller.viewquote.offer.OfferBean;
import com.intact.brokeroffice.controller.viewquote.offer.VehicleOfferBean;
import com.intact.brokeroffice.dao.SystemDAO;
import com.intact.brokeroffice.helper.ResourceBundleHelper;
import com.intact.brokeroffice.service.IBrokerService;
import com.intact.brokeroffice.service.IDialerService;
import com.intact.brokeroffice.service.bloommqhandler.IBloomMqHandlerService;
import com.intact.brokeroffice.service.util.Configuration;
import com.intact.brokeroffice.util.SearchState;
import com.intact.business.service.broker.domain.IUserContext;
import com.intact.plt.information.service.client.util.InformationPieceTO;
import com.intact.tools.logging.service.LoggingApplicationService;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.util.ReflectionTestUtils;

import jakarta.faces.component.UIViewRoot;
import jakarta.faces.context.ExternalContext;
import jakarta.faces.context.FacesContext;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@Disabled
@ExtendWith(MockitoExtension.class)
//@MockitoSettings(strictness = Strictness.LENIENT)
class AbstractViewQuoteControllerTest {

	private MockedStatic<SearchController> mockedSearchController;

	private MockedStatic<FacesContext> mockedFacesContext;

	private MockedStatic<ResourceBundleHelper> mockedResourceBundleHelper;

	private MockedStatic<ResourceBundle> mockedResourceBundle;

  @Mock
  protected LanguageController languageController;
  @Mock
  protected IPartyHelper partyhelper;
  /**
   * Implementation of the class to be tested
   **/
  @InjectMocks
  private ViewQuoteControllerQC viewQuoteController;
  /**
   * General test objects
   **/
  private ViewQuoteAdapter quoteAdapter = new ViewQuoteAdapter();
  private MockHttpSession session;
  /**
   * Mocks
   **/
  @Mock
  private ICommandBusinessProcess commandBusinessProcess;
  @Mock
  private IBrokerService quoteBusinessProcess;
  @Mock
  private PermissionController permissionController;
  @Mock
  private AuthentificationController authentificationController;
  @Mock
  private ProvinceController provinceController;
  @Mock
  private IPolicyHolderService policyHolderService;
  @Mock
  private IPartyHelper partyHelper;

  @Mock
  private SystemDAO systemDAO;

  @Mock
  private IInsurancePolicyService insurancePolicyService;

  @Mock
  private ISubBrokersService subBrokersService;

  @Mock
  private IPolicyVersionHelper policyVersionHelper;

  @Mock
  private IBusinessTransactionSubActivityService businessTransactionSubActivityService;

  @Mock
  private IPolicyVersionInfoService policyVersionInfoService;

  @Mock
  private ExternalContext externalContext;

  @Mock
  private FacesContext context;

  @Mock
  private UIViewRoot viewRoot;

  @Mock
  private SearchController mockSearchController;

  @Mock
  private SearchState mockSearchState;

  @Mock
  private OfferAdapterQC offerAdapter;

  @Mock
  private QuotesBean mockQuote;

  @Mock
  private ClientContactBean mockClientContactBean;

  @Mock
  private IUserContext mockUserContext;

  @Mock
  private LoggingApplicationService mockLogService;

  @Mock
  private IQuoteDialerDTOHelper mockQuoteDialerDTOHelper;

  @Mock
  private IDialerService mockDialerService;

  @Mock
  private IBloomMqHandlerService bloomMqHandlerService;

  private ResourceBundle resourceBundle;

  private String testBrokerNumber = "4218";

	@BeforeEach
	void setUp() throws Exception {
    setUpStaticMocks();
    session = new MockHttpSession();

    ReflectionTestUtils.setField(viewQuoteController, "quoteAdapter", quoteAdapter);
    viewQuoteController.setPartyHelper(partyHelper);

    ReflectionTestUtils.setField(quoteAdapter, "partyHelper", partyHelper);
    ReflectionTestUtils.setField(quoteAdapter, "insurancePolicyService", insurancePolicyService);
    ReflectionTestUtils.setField(quoteAdapter, "quoteBusinessProcess", quoteBusinessProcess);
    ReflectionTestUtils.setField(quoteAdapter, "subBrokersService", subBrokersService);
    ReflectionTestUtils.setField(quoteAdapter, "policyVersionHelper", policyVersionHelper);
    ReflectionTestUtils.setField(quoteAdapter, "businessTransactionSubActivityService",
        businessTransactionSubActivityService);
    ReflectionTestUtils.setField(quoteAdapter, "policyHolderService", policyHolderService);
    ReflectionTestUtils
        .setField(quoteAdapter, "policyVersionInfoService", policyVersionInfoService);

    resourceBundle = new ResourceBundle() {

      @Override
      protected Object handleGetObject(String key) {
        return "mock resource value";
      }

      @Override
      public Enumeration<String> getKeys() {
        return null;
      }
    };
    mockedFacesContext.when(FacesContext::getCurrentInstance).thenReturn(context);
    when(context.getViewRoot()).thenReturn(viewRoot);
    when(viewRoot.getLocale()).thenReturn(Locale.CANADA_FRENCH);
    mockedResourceBundle.when(() -> ResourceBundle.getBundle(anyString(), (Locale) any(), (ClassLoader) any()))
        .thenReturn(
            resourceBundle);

    when(context.getExternalContext()).thenReturn(externalContext);
    when(externalContext.getSession(anyBoolean())).thenReturn(session);
  }

	@AfterEach
	void tearDown() throws Exception {
  }

	void setUpStaticMocks() {
		mockedSearchController = Mockito.mockStatic(SearchController.class);
		mockedFacesContext = Mockito.mockStatic(FacesContext.class);
		mockedResourceBundleHelper = Mockito.mockStatic(ResourceBundleHelper.class);
		mockedResourceBundle = Mockito.mockStatic(ResourceBundle.class);
	}

	@AfterEach
	void tearDownStaticMocks() {
		mockedResourceBundle.closeOnDemand();
		mockedResourceBundleHelper.closeOnDemand();
		mockedFacesContext.closeOnDemand();
		mockedSearchController.closeOnDemand();
	}

	/**
	* Test that the quote is correctly updated
	*
	* @throws Exception
	*/
	// TODO put back when update quote is back in the abstract controller
	@Test
	@Disabled
	void update() throws Exception {
    initViewQuoteAdapterInfo();

    // General object setup
    String expectedRefNumber = "testReferenced";
    LineOfBusinessCodeEnum expectedLoB = LineOfBusinessCodeEnum.PERSONAL_LINES;
    LineOfInsuranceCodeEnum expectedLoI = LineOfInsuranceCodeEnum.AUTOMOBILE;
    String applicationMode = "QF";

    // Set the base contact bean in the controller
    ClientContactBean baseContactBean = new ClientContactBean();
    String expectedNoteText = "This is a test note.";
    AgreementFollowUpStatusEnum expectedStatus = AgreementFollowUpStatusEnum.NOT_CONTACTED_NO_FOLLOWUP_REQUIRED;
    baseContactBean.addNote(new NoteBean(expectedNoteText, new Date()));
    baseContactBean.setNoteText(expectedNoteText);
    baseContactBean.setFollowupStatus(expectedStatus);
    ReflectionTestUtils.setField(viewQuoteController, "clientContactBean", baseContactBean);

    // Intercept new Quote object to use for validation
    Quote resultQuote = Mockito.mock(Quote.class);

    // Execute the tested method and validate the results
    viewQuoteController
        .update(expectedRefNumber, expectedLoB.getCode(), expectedLoI.getCode(), applicationMode);
    ClientContactBean resultClientContactBean = viewQuoteController.getClientContactBean();

    assertEquals(1, resultClientContactBean.getNotes().size());
    assertEquals(expectedStatus, resultClientContactBean.getFollowupStatus());
    assertEquals(expectedRefNumber, resultQuote.getId());
    assertEquals(expectedLoB, resultQuote.getLineOfBusiness());
    assertEquals(expectedLoI, resultQuote.getLineOfInsurance());
    assertEquals(expectedNoteText, resultQuote.getActivityNote());
  }

	@Test
	void getFollowpStatus() {
    String testNotContactedText = "Not Contacted";
    String testRequiredText = "Followup Required";
    String testNotRequiredText = "Followup Not Required";
    String testNonRequiredText = "No action required";
    String testDuplicateText = "Duplicate/Fake";
    mockedResourceBundleHelper.when(() -> ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client",
				this.viewQuoteController, "client.followup.notContacted")).thenReturn(testNotContactedText);
    mockedResourceBundleHelper.when(() -> ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client",
				this.viewQuoteController, "client.followup.required")).thenReturn(testRequiredText);
    mockedResourceBundleHelper.when(() -> ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client",
				this.viewQuoteController, "client.followup.notRequired")).thenReturn(testNotRequiredText);
    mockedResourceBundleHelper.when(() -> ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client",
				this.viewQuoteController, "client.followup.noneRequired")).thenReturn(testNonRequiredText);
    mockedResourceBundleHelper.when(() -> ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client",
				this.viewQuoteController, "client.followup.duplicate")).thenReturn(testDuplicateText);

    Map<String, AgreementFollowUpStatusEnum> resultMap = viewQuoteController
        .getFollowupStatusList();

    assertEquals(AgreementFollowUpStatusEnum.NOT_CONTACTED, resultMap.get(testNotContactedText));
    assertEquals(AgreementFollowUpStatusEnum.CONTACTED_FOLLOWUP_REQUIRED,
        resultMap.get(testRequiredText));
    assertEquals(AgreementFollowUpStatusEnum.CONTACTED_NO_FOLLOWUP_REQUIRED,
        resultMap.get(testNotRequiredText));
    assertEquals(AgreementFollowUpStatusEnum.NOT_CONTACTED_NO_FOLLOWUP_REQUIRED,
        resultMap.get(testNonRequiredText));
    assertEquals(AgreementFollowUpStatusEnum.DUPLICATE_FAKE, resultMap.get(testDuplicateText));
  }

	@Test
	void buildViewQuoteCommon() {
    initViewQuoteAdapterInfo();
    this.viewQuoteController.setClientContactBean(new ClientContactBean());

    session.setAttribute(SessionConstantsEnum.EMAIL_REFERENCE_NUMBER.getSessionConstant(),
        "emailRefNumber"); // assert
    // is
    // deleted
    // after
    PolicyVersion p = newPolicyVersion();
    when(commandBusinessProcess.findLatestPolicyVersion(anyString())).thenReturn(p);

    Configuration newConfig = new Configuration();
    newConfig.setConfigs(new HashMap<String, Object>());
    newConfig.getConfigs()
        .put(ApplicationModeEnum.REGULAR_QUOTE.getCode() + "-" + "UPL", (Boolean) true);
    ReflectionTestUtils.setField(viewQuoteController, "uploadStatuses", newConfig);

    viewQuoteController.setQuote(new QuotesBean());
    viewQuoteController.setPartyHelper(this.partyhelper);
    viewQuoteController.buildViewQuoteCommon("referencedNo");

    assertNull(
        session.getAttribute(SessionConstantsEnum.EMAIL_REFERENCE_NUMBER.getSessionConstant()));
    assertEquals("AgreementLegacyNumber", viewQuoteController.getReferenceLegacyNo());
    assertTrue(viewQuoteController.isQuoteUploaded());
    assertFalse(viewQuoteController.getUbiConsentInd());
    assertTrue(viewQuoteController.getCreditConsentInd());
    assertTrue(viewQuoteController.getCreateProfileConsentInd());

    ClientContactBean resultClientContactBean = viewQuoteController.getClientContactBean();

    assertEquals(1, resultClientContactBean.getNotes().size());
    assertEquals(AgreementFollowUpStatusEnum.NOT_CONTACTED_NO_FOLLOWUP_REQUIRED,
        resultClientContactBean.getFollowupStatus());
    assertEquals("Olivier Tremblay", resultClientContactBean.getPolicyHolder());
    assertEquals(QuoteStatusCodeEnum.UPLOADED, resultClientContactBean.getStatus());
    assertEquals("(514)444-4444 ext 83919", resultClientContactBean.getPhoneCell());
    assertEquals("NameLine1NameLine2", resultClientContactBean.getBrokerAdvisor());
    assertEquals("WEBBK", resultClientContactBean.getQuoteSource());
  }

	/**
	* Show be true because legacyNumber not null
	*/
	@Test
	void isRemoteSystemUrl() {
    viewQuoteController.setReferenceLegacyNo("legacyNu");
    assertTrue(viewQuoteController.isShowRemoteLink());
  }

	/**
	* Show be true because legacyNumber is null and remoteSystemUrl is null
	*/
	@Test
	void isNotRemoteSystemUrl() {
    viewQuoteController.setReferenceLegacyNo(null);
    viewQuoteController.setRemoteSystemUrl(null);

    when(systemDAO.getCompany()).thenReturn(CifCompanyEnum.INTACT_AB.getSubBrokerCompanyNumber());

    assertFalse(viewQuoteController.isShowRemoteLink());
  }

	@Test
	void buildErrorPiece() {
    String expectedMessage = "Cette section n'est pas disponible pour le moment.  SVP revenez plus tard.";
    mockedResourceBundleHelper.when(() -> ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client",
				this.viewQuoteController, "message.error.plt")).thenReturn(expectedMessage);

    InformationPieceTO resultPiece = viewQuoteController.buildErrorPiece();

    assertEquals("error", resultPiece.getValue());
    assertEquals(expectedMessage, resultPiece.getChildrens().get(0).getPieces().get(0).getValue());
  }

	/**
	* Retreive reference number from url
	*/
	@Test
	void retreiveReferenceNo() {
    Map<String, String> mapParameters = new HashMap<String, String>();
    mapParameters.put("referenceNo", null);

    when(context.getExternalContext()).thenReturn(externalContext);
    when(externalContext.getRequestParameterMap()).thenReturn(mapParameters);
  }

	@Test
	void loadTotalMonthlyPrenium() {
    List<VehicleOfferBean> someVehicleOfferBeanList = new ArrayList<VehicleOfferBean>();
    VehicleOfferBean vehicleOfferBean = new VehicleOfferBean();
    vehicleOfferBean.setOfferSelected(true);
    vehicleOfferBean.setSelectedOfferType(OfferTypeCodeEnum.CUSTOM);
    Map<String, OfferBean> theOffers = new HashMap<String, OfferBean>();
    OfferBean offerBean = new OfferBean(OfferTypeCodeEnum.CUSTOM);
    offerBean.setMonthlyPremiumWithTaxes(1000.0);
    theOffers.put(OfferTypeCodeEnum.CUSTOM.getCode(), offerBean);
    vehicleOfferBean.setOffers(theOffers);
    someVehicleOfferBeanList.add(vehicleOfferBean);
    viewQuoteController.setVehicleOfferBeanList(someVehicleOfferBeanList);

    Double monthlyPremium = viewQuoteController.loadTotalMonthlyPremium();

    assertTrue(1000.0 == monthlyPremium);
  }

	@Test
	void uploadSuccessfull() throws Exception {
    initViewQuoteAdapterInfo();

    PolicyVersion policyVersion = newPolicyVersion();
    when(commandBusinessProcess.findLatestPolicyVersion(anyString())).thenReturn(policyVersion);

    session.setAttribute(SessionConstantsEnum.EMAIL_REFERENCE_NUMBER.getSessionConstant(),
        "emailRefNumber");

    Configuration newConfig = new Configuration();
    newConfig.setConfigs(new HashMap<String, Object>());
    newConfig.getConfigs()
        .put(ApplicationModeEnum.REGULAR_QUOTE.getCode() + "-" + "UPL", (Boolean) false);
    ReflectionTestUtils.setField(viewQuoteController, "uploadStatuses", newConfig);

    QuotesBean testQuote = new QuotesBean();
    testQuote.setLineOfInsurance(LineOfInsuranceCodeEnum.AUTOMOBILE);
    testQuote.setAgreementNumber("QA999999999");
    viewQuoteController.setCreateProfileConsentInd(true);
    viewQuoteController.setQuote(testQuote);
    viewQuoteController.setPartyHelper(partyHelper);

    PolicyInfo quoteInfo = mock(PolicyInfo.class);
    when(quoteBusinessProcess.uploadQuote(any(IUserContext.class), any(Quote.class), eq("")))
        .thenReturn(quoteInfo);
    when(quoteInfo.getUri()).thenReturn("BillingPlanMessage");
    when(quoteInfo.getNumber()).thenReturn("policyNumber");
    policyVersion.getInsurancePolicy().setAgreementLegacyNumber("policyNumber");

    when(permissionController.getCheckQuoteAccess()).thenReturn(true);
    when(provinceController.getManufacturerCompanyCode())
        .thenReturn(ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION);

    // Setup for the quotes Map to obtain the detailed quote info
    HashMap<String, QuotesBean> quotesMap = new HashMap<String, QuotesBean>();
    quotesMap.put(quoteInfo.getNumber(), testQuote);
    mockSearchController = Mockito.mock(SearchController.class);
    when(mockSearchState.getQuotesMap()).thenReturn(quotesMap);
    when(mockSearchController.getSearchState()).thenReturn(mockSearchState);
    ReflectionTestUtils.setField(viewQuoteController, "searchController", mockSearchController);

    // Execute the tested method and validate the results
    viewQuoteController.upload(quoteInfo.getNumber());

    assertTrue(viewQuoteController.getUploadSucceed());
    assertEquals("BillingPlanMessage", viewQuoteController.getRemoteSystemUrl());
    assertEquals("policyNumber", viewQuoteController.getReferenceLegacyNo());
  }

  private void initViewQuoteAdapterInfo() {
    ISubBrokers subBroker = new SubBrokers();
    subBroker.setNameLine1("NameLine1");
    subBroker.setNameLine2("NameLine2");
    when(subBrokersService.getSubBrokerById(anyLong())).thenReturn(subBroker);

    PolicyHolder aPolicyHolder = new PolicyHolder();
    Party aParty = new Party();
    aParty.setEmailAddress("<EMAIL>");
    aParty.setFirstName("Olivier");
    aParty.setLastName("Tremblay");
    aPolicyHolder.setParty(aParty);
    when(policyVersionHelper.getPrincipalInsuredPolicyHolder((PolicyVersion) any()))
        .thenReturn(aPolicyHolder);

    Phone aPhone = new Phone();
    aPhone.setPhoneAreaCode("514");
    aPhone.setPhoneNumber("4444444");
    aPhone.setPhoneExtension("83919");
    when(partyHelper.getCurrentPhone((Party) any(), (PhoneTypeCodeEnum) any()))
        .thenReturn(aPhone);

    BusinessTransactionSubActivity aBusinessTransactionSubActivity = new BusinessTransactionSubActivity();
    aBusinessTransactionSubActivity
        .setSubActivityCode(BusinessTransactionSubActivityCodeEnum.ROADBLOCK);
    when(businessTransactionSubActivityService.findLastSubActivity(anyLong())).thenReturn(
        aBusinessTransactionSubActivity);

    when(policyHolderService.findPrincipalPolicyHolder((PolicyVersion) any()))
        .thenReturn(aPolicyHolder);

    List<InsurancePolicy> insuranceList = new ArrayList<InsurancePolicy>();
    // insuranceList.add(newInsurancePolicy("agreeNumber", "legaNumber"));
    // ViewQuoteAdapter/generateUploadedQuoteMessage throw missing ressource for: client.message.uploadQuote
    insuranceList.add(newInsurancePolicy("AgreementNumber", "lNumber"));
    insuranceList.add(newInsurancePolicy("AgreementNumber", "lNumber"));

    when(insurancePolicyService.findAllUnexpiredInsurancePolicyForEmail(anyString()))
        .thenReturn(insuranceList);

    Set<PaymentPlanEnum> setPaymentPlanEnum = new HashSet<PaymentPlanEnum>();
    setPaymentPlanEnum.add(PaymentPlanEnum.PLAN_A);
    // When setPaymentPlanEnum not contain PLAN_A, PLAN_E
    // ViewQuoteAdapter/generateEligibleWithdrawalsMessage throw missing ressource for:
    // client.message.eligible.withdrawals
    when(policyVersionInfoService.getPaymentPlans((PolicyVersion) any()))
        .thenReturn(setPaymentPlanEnum);

    when(commandBusinessProcess.findLatestPolicyVersion(anyString()))
        .thenReturn(newPolicyVersion());
    PolicyHolder policyHolder = new PolicyHolder();
    Party party = new Party();
    policyHolder.setParty(party);
    Consent consent = new Consent();
    consent.setConsentType(ConsentTypeCodeEnum.CLIENT_PROFILE_CREATION);
    consent.setConsentIndicator(true);
    party.addConsent(consent);
    when(policyHolderService.findPrincipalPolicyHolder((PolicyVersion) any()))
        .thenReturn(policyHolder);
    when(authentificationController.getCurrentAccountUId()).thenReturn("AccountUID");
    when(authentificationController.isMasterRole()).thenReturn(true);
  }

  private InsurancePolicy newInsurancePolicy(String agreementNumber, String agreementLegacyNumber) {
    InsurancePolicy insurancePolicy = new InsurancePolicy();
    insurancePolicy.setAgreementLegacyNumber(agreementLegacyNumber);
    insurancePolicy.setAgreementNumber(agreementNumber);
    return insurancePolicy;
  }

  private PolicyVersion newPolicyVersion() {
    PolicyVersion policyVersion = new PolicyVersion();
    InsurancePolicy aInsurancePolicy = new InsurancePolicy();
    InsurancePolicyNote insurancePolicyNote = new InsurancePolicyNote();
    insurancePolicyNote.setAuthorUID("AuthorUID");
    insurancePolicyNote.setUserActivityType(UserActivityTypeCodeEnum.VIEW_QUOTE);
    insurancePolicyNote.setInsurancePolicyNote(null);
    AuditTrail trail = new AuditTrail();
    trail.setCreateTimestamp(new Date());
    insurancePolicyNote.setAuditTrail(trail);
    aInsurancePolicy.addInsurancePolicyNote(insurancePolicyNote);

    aInsurancePolicy
        .setAgreementFollowUpStatus(AgreementFollowUpStatusEnum.NOT_CONTACTED_NO_FOLLOWUP_REQUIRED);
    aInsurancePolicy.setQuotationValidityExpiryDate(new Date());
    aInsurancePolicy.setAgreementLegacyNumber("AgreementLegacyNumber");

    ManufacturingContext aManufacturingContext = new ManufacturingContext();
    aManufacturingContext.setProvince(ProvinceCodeEnum.QUEBEC);
    aInsurancePolicy.setManufacturingContext(aManufacturingContext);

    SubBrokerAssignment aSubBrokerAssignment = new SubBrokerAssignment();
    aSubBrokerAssignment.setCifSubBrokerId(1l);
    aSubBrokerAssignment.setAuditTrail(trail);
    aInsurancePolicy.setAgreementNumber("AgreementNumber");
    aInsurancePolicy.addSubBrokerAssignment(aSubBrokerAssignment);

    aInsurancePolicy.setAuditTrail(trail);
    aInsurancePolicy.setExternalSystemOrigin(ExternalSystemOriginCodeEnum.WEB_BROKER);

    BusinessTransaction businessTransaction = new BusinessTransaction();
    BusinessTransactionActivity lastBusinessTransactionActivity = new BusinessTransactionActivity();
    lastBusinessTransactionActivity.setAuditTrail(trail);
    businessTransaction.addBusinessTransactionActivity(lastBusinessTransactionActivity);
    businessTransaction.setTransactionStatus(TransactionStatusCodeEnum.SUSPENDED);
    policyVersion.setBusinessTransaction(businessTransaction);
    policyVersion.setInsurancePolicy(aInsurancePolicy);
    policyVersion.setLanguageOfCommunication(LanguageCodeEnum.FRENCH);
    return policyVersion;
  }


	/*************************************************
	*************************************************
	*
	* Create dialer file tests
	*
	*************************************************
	*************************************************
	*/
	@Test
	void callDialerService_WithValidAutoQuote_shouldCallDialerServiceSuccessfully()
			throws Exception {
    String testConfirmationNumber = "**********";
    String testAgreementNumber = "QF00051475215";
    QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
    PartyDTO testDialerParty = new PartyDTO();
    this.setupBasicDialerCallTest(testQuoteDialerDTO, testDialerParty, testAgreementNumber,
        testConfirmationNumber, LineOfBusinessCodeEnum.PERSONAL_LINES,
        LineOfInsuranceCodeEnum.AUTOMOBILE);
    doNothing().when(bloomMqHandlerService).sendMessage(anyString(), anyString());
    this.viewQuoteController.createDialerFile();

    assertTrue(this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been true - valid auto quote");
    assertTrue(StringUtils.isNotEmpty(this.viewQuoteController.getDialerOutput()),
        "Dialer output text should not be empty - valid auto quote");
    assertNotNull(testQuoteDialerDTO.getListVehiclesDTO(),
        "QuoteDialerDTO object should not have a null vehicles list - auto quote");
    verify(this.mockDialerService, times(1)).sendToDialer(testQuoteDialerDTO);
    verify(this.mockClientContactBean, times(1)).setNoteText("Dialer: " + testConfirmationNumber);
    verify(this.quoteBusinessProcess, times(1))
        .updateQuote(eq(this.mockUserContext), any(Quote.class));
  }

	@Test
	void callDialerService_WithValidHomeQuote_shouldCallDialerServiceSuccessfully()
			throws Exception {
    String testConfirmationNumber = "**********";
    String testAgreementNumber = "QH00051475215";
    QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
    PartyDTO testDialerParty = new PartyDTO();
    this.setupBasicDialerCallTest(testQuoteDialerDTO, testDialerParty, testAgreementNumber,
        testConfirmationNumber, LineOfBusinessCodeEnum.PERSONAL_LINES,
        LineOfInsuranceCodeEnum.RESIDENTIAL);
    doNothing().when(bloomMqHandlerService).sendMessage(anyString(), anyString());

    this.viewQuoteController.createDialerFile();

    assertTrue(this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been true - valid home quote");
    assertTrue(StringUtils.isNotEmpty(this.viewQuoteController.getDialerOutput()),
        "Dialer output text should not be empty - valid home quote");
    assertNull(testQuoteDialerDTO.getListVehiclesDTO(),
        "QuoteDialerDTO object should not a null vehicles list - home quote");
    verify(this.mockDialerService, times(1)).sendToDialer(testQuoteDialerDTO);
    verify(this.mockClientContactBean, times(1)).setNoteText("Dialer: " + testConfirmationNumber);
    verify(this.quoteBusinessProcess, times(1))
        .updateQuote(eq(this.mockUserContext), any(Quote.class));
  }

	@Test
	void callDialerService_WithValidCommercialQuote_shouldCallDialerServiceSuccessfully()
			throws Exception {
    String testConfirmationNumber = "**********";
    String testAgreementNumber = "PC00051475215";
    QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
    PartyDTO testDialerParty = new PartyDTO();
    testDialerParty.setProvince("QC");
    this.setupBasicDialerCallTest(testQuoteDialerDTO, testDialerParty, testAgreementNumber,
        testConfirmationNumber, LineOfBusinessCodeEnum.COMMERCIAL_LINES,
        LineOfInsuranceCodeEnum.RESIDENTIAL);
    doNothing().when(bloomMqHandlerService).sendMessage(anyString(), anyString());

    this.viewQuoteController.createDialerFile();

    assertTrue(this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been true - valid commercial quote");
    assertTrue(StringUtils.isNotEmpty(this.viewQuoteController.getDialerOutput()),
        "Dialer output text should not be empty - valid commercial quote");
    verify(this.mockDialerService, times(1)).sendToDialer(testQuoteDialerDTO);
    verify(this.mockClientContactBean, times(1)).setNoteText("Dialer: " + testConfirmationNumber);
    verify(this.quoteBusinessProcess, times(1))
        .updateQuote(eq(this.mockUserContext), any(Quote.class));
  }

	@Test
	void callDialerService_WithValidQuoteWithSingleChildQuote_shouldSetChildQuoteNumberInNote()
			throws Exception {
    String testConfirmationNumber = "**********";
    String testAgreementNumber = "QF00051475215";
    List<String> relatedQuotes = new ArrayList<String>();
    relatedQuotes.add("QF00005328566");
    String testLinkedQuotesText = " linked quote:QF00005328566";
    QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
    PartyDTO testDialerParty = new PartyDTO();
    testQuoteDialerDTO.setListRelatedQuotes(relatedQuotes);
    this.setupBasicDialerCallTest(testQuoteDialerDTO, testDialerParty, testAgreementNumber,
        testConfirmationNumber, LineOfBusinessCodeEnum.PERSONAL_LINES,
        LineOfInsuranceCodeEnum.AUTOMOBILE);
    doNothing().when(bloomMqHandlerService).sendMessage(anyString(), anyString());

    this.viewQuoteController.createDialerFile();

    assertTrue(this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been true - valid quote");
    verify(this.mockDialerService, times(1)).sendToDialer(testQuoteDialerDTO);
    verify(this.mockClientContactBean, times(1))
        .setNoteText("Dialer: " + testConfirmationNumber + testLinkedQuotesText);
    verify(this.quoteBusinessProcess, times(1))
        .updateQuote(eq(this.mockUserContext), any(Quote.class));
  }

	@Test
	void callDialerService_WithValidQuoteWithMultipleChildQuotes_shouldSetChildQuoteNumbersInNote()
			throws Exception {
    String testConfirmationNumber = "**********";
    String testAgreementNumber = "QF00051475215";
    List<String> relatedQuotes = new ArrayList<String>();
    relatedQuotes.add("QF00005328566");
    relatedQuotes.add("QF00005328588");
    String testLinkedQuotesText = " linked quote:QF00005328566, QF00005328588";
    QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
    PartyDTO testDialerParty = new PartyDTO();
    testQuoteDialerDTO.setListRelatedQuotes(relatedQuotes);
    this.setupBasicDialerCallTest(testQuoteDialerDTO, testDialerParty, testAgreementNumber,
        testConfirmationNumber, LineOfBusinessCodeEnum.PERSONAL_LINES,
        LineOfInsuranceCodeEnum.AUTOMOBILE);
    doNothing().when(bloomMqHandlerService).sendMessage(anyString(), anyString());

    this.viewQuoteController.createDialerFile();

    assertTrue(this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been true - valid quote");
    verify(this.mockDialerService, times(1)).sendToDialer(testQuoteDialerDTO);
    verify(this.mockClientContactBean, times(1))
        .setNoteText("Dialer: " + testConfirmationNumber + testLinkedQuotesText);
    verify(this.quoteBusinessProcess, times(1))
        .updateQuote(eq(this.mockUserContext), any(Quote.class));
  }

	@Test
	void callDialerService_WithNullClientContactBean_shouldSetDialerSucceedToFalse()
			throws Exception {
    QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
    PartyDTO testDialerParty = new PartyDTO();
    this.setupBasicDialerCallTest(testQuoteDialerDTO, testDialerParty, "QF00051475215",
        "**********", LineOfBusinessCodeEnum.PERSONAL_LINES, LineOfInsuranceCodeEnum.AUTOMOBILE);
    this.viewQuoteController.setClientContactBean(null);

    this.viewQuoteController.createDialerFile();

    assertFalse(this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been false - missing ClientContactBean object.");
    verify(this.mockClientContactBean, times(0)).setNoteText("Failed Dialer creation file.");
    verify(this.quoteBusinessProcess, times(0))
        .updateQuote(eq(this.mockUserContext), any(Quote.class));
  }

	@Test
	void callDialerService_WithNullQuote_shouldSetDialerSucceedToFalseAndUpdateNote()
			throws Exception {
    QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
    PartyDTO testDialerParty = new PartyDTO();
    this.setupBasicDialerCallTest(testQuoteDialerDTO, testDialerParty, "QF00051475215",
        "**********", LineOfBusinessCodeEnum.PERSONAL_LINES, LineOfInsuranceCodeEnum.AUTOMOBILE);
    this.viewQuoteController.setQuote(null);

    this.viewQuoteController.createDialerFile();

    assertFalse(this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been false - missing QuotesBean object.");
    verify(this.mockClientContactBean, times(1)).setNoteText("Failed Dialer creation file.");
    verify(this.quoteBusinessProcess, times(0))
        .updateQuote(eq(this.mockUserContext), any(Quote.class));
  }

	@Test
	void callDialerService_WithNullAgreementNumber_shouldSetDialerSucceedToFalseAndUpdateNote()
			throws Exception {
    QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
    PartyDTO testDialerParty = new PartyDTO();
    when(this.mockQuote.getLineOfBusiness()).thenReturn(LineOfBusinessCodeEnum.PERSONAL_LINES);
    when(this.mockQuote.getLineOfInsurance()).thenReturn(LineOfInsuranceCodeEnum.AUTOMOBILE);
    when(this.mockQuote.getApplicationMode()).thenReturn("QA");
    this.setupBasicDialerCallTest(testQuoteDialerDTO, testDialerParty, null, "**********",
        LineOfBusinessCodeEnum.PERSONAL_LINES, LineOfInsuranceCodeEnum.AUTOMOBILE);

    this.viewQuoteController.createDialerFile();

    assertFalse(this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been false - missing agreement number.");
    verify(this.mockClientContactBean, times(1)).setNoteText("Failed Dialer creation file.");
    verify(this.quoteBusinessProcess, times(1))
        .updateQuote(eq(this.mockUserContext), any(Quote.class));
  }

	@Test
	void callDialerService_WithNullLocale_shouldSetDialerSucceedToFalseAndUpdateNote()
			throws Exception {
    QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
    PartyDTO testDialerParty = new PartyDTO();
    this.setupBasicDialerCallTest(testQuoteDialerDTO, testDialerParty, "QF00051475215",
        "**********", LineOfBusinessCodeEnum.PERSONAL_LINES, LineOfInsuranceCodeEnum.AUTOMOBILE);
    when(this.languageController.getLocale()).thenReturn(null);

    this.viewQuoteController.createDialerFile();

    assertFalse(this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been false - missing locale.");
    verify(this.mockClientContactBean, times(1)).setNoteText("Failed Dialer creation file.");
    verify(this.quoteBusinessProcess, times(1))
        .updateQuote(eq(this.mockUserContext), any(Quote.class));
  }

	@Test
	void callDialerService_WithNullUserContext_shouldSetDialerSucceedToFalseAndUpdateNote()
			throws Exception {
    QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
    PartyDTO testDialerParty = new PartyDTO();
    this.setupBasicDialerCallTest(testQuoteDialerDTO, testDialerParty, "QF00051475215",
        "**********", LineOfBusinessCodeEnum.PERSONAL_LINES, LineOfInsuranceCodeEnum.AUTOMOBILE);
    when(this.permissionController.getUserContext()).thenReturn(null);

    this.viewQuoteController.createDialerFile();

    assertFalse(this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been false - missing user context.");
    verify(this.mockClientContactBean, times(1)).setNoteText("Failed Dialer creation file.");
    verify(this.quoteBusinessProcess, times(1))
        .updateQuote(any(IUserContext.class), any(Quote.class));
  }

	@Test
	void callDialerService_WithNonNullPolicyHolderName_shouldSetItInParty() throws Exception {
    this.setupBasicDialerCallTest(new QuoteDialerDTO(), new PartyDTO(), "QF00051475215",
        "**********", LineOfBusinessCodeEnum.PERSONAL_LINES, LineOfInsuranceCodeEnum.AUTOMOBILE);
    String testPolicyHolderName = "Bob Burgington";
    when(this.mockClientContactBean.getPolicyHolder()).thenReturn(testPolicyHolderName);

    this.viewQuoteController.createDialerFile();

    verify(this.mockQuoteDialerDTOHelper, times(1))
        .buildDialerPartyDTO(any(InformationPieceTO.class),
            any(InformationPieceTO.class), eq(this.mockQuote), eq(testPolicyHolderName),
            anyString(), anyString());
  }

	@Test
	void callDialerService_WithNullPolicyHolderName_shouldUseQuoteFirstAndLastName()
			throws Exception {
    this.setupBasicDialerCallTest(new QuoteDialerDTO(), new PartyDTO(), "QF00051475215",
        "**********", LineOfBusinessCodeEnum.PERSONAL_LINES, LineOfInsuranceCodeEnum.AUTOMOBILE);
    String testFirstName = "Bob";
    String testLastName = "Burgington";
    when(this.mockClientContactBean.getPolicyHolder()).thenReturn(null);
    when(this.mockQuote.getFirstName()).thenReturn(testFirstName);
    when(this.mockQuote.getLastName()).thenReturn(testLastName);

    this.viewQuoteController.createDialerFile();

    verify(this.mockQuoteDialerDTOHelper, times(1))
        .buildDialerPartyDTO(any(InformationPieceTO.class),
            any(InformationPieceTO.class), eq(this.mockQuote),
            eq(testFirstName + " " + testLastName), anyString(), anyString());
  }

	@Test
	void callDialerService_WithNullTextForPolicyHolderName_shouldUseQuoteFirstAndLastName()
			throws Exception {
    this.setupBasicDialerCallTest(new QuoteDialerDTO(), new PartyDTO(), "QF00051475215",
        "**********", LineOfBusinessCodeEnum.PERSONAL_LINES, LineOfInsuranceCodeEnum.AUTOMOBILE);
    String testFirstName = "Bob";
    String testLastName = "Burgington";
    when(this.mockClientContactBean.getPolicyHolder()).thenReturn("null");
    when(this.mockQuote.getFirstName()).thenReturn(testFirstName);
    when(this.mockQuote.getLastName()).thenReturn(testLastName);

    this.viewQuoteController.createDialerFile();

    verify(this.mockQuoteDialerDTOHelper, times(1))
        .buildDialerPartyDTO(any(InformationPieceTO.class),
            any(InformationPieceTO.class), eq(this.mockQuote),
            eq(testFirstName + " " + testLastName), anyString(), anyString());
  }

	@Test
	void callDialerService_WithNullPolicyHolderNameAndQuoteName_shouldUseEmptyString()
			throws Exception {
    this.setupBasicDialerCallTest(new QuoteDialerDTO(), new PartyDTO(), "QF00051475215",
        "**********", LineOfBusinessCodeEnum.PERSONAL_LINES, LineOfInsuranceCodeEnum.AUTOMOBILE);
    when(this.mockClientContactBean.getPolicyHolder()).thenReturn(null);
    when(this.mockQuote.getFirstName()).thenReturn(null);
    when(this.mockQuote.getLastName()).thenReturn(null);

    this.viewQuoteController.createDialerFile();

    verify(this.mockQuoteDialerDTOHelper, times(1))
        .buildDialerPartyDTO(any(InformationPieceTO.class),
            any(InformationPieceTO.class), eq(this.mockQuote), eq(""), anyString(), anyString());
  }

	@Test
	void callDialerService_WithBuilderReturningNullQuoteDialerDTO_shouldNotCallDialerService()
			throws Exception {
    this.setupBasicDialerCallTest(new QuoteDialerDTO(), new PartyDTO(), "QF00051475215",
        "**********", LineOfBusinessCodeEnum.PERSONAL_LINES, LineOfInsuranceCodeEnum.AUTOMOBILE);
    when(this.mockQuoteDialerDTOHelper
        .buildQuoteDialerDTO(eq(this.mockQuote), eq(this.mockUserContext), anyString(), anyString(),
            any(InformationPieceTO.class)))
        .thenReturn(null);

    this.viewQuoteController.createDialerFile();

    verify(this.mockDialerService, times(0)).sendToDialer(any(QuoteDialerDTO.class));
  }

	@Test
	void callDialerService_WithBuilderReturningNullPartyDTO_shouldNotCallDialerService()
			throws Exception {
    this.setupBasicDialerCallTest(new QuoteDialerDTO(), new PartyDTO(), "QF00051475215",
        "**********", LineOfBusinessCodeEnum.PERSONAL_LINES, LineOfInsuranceCodeEnum.AUTOMOBILE);
    when(this.mockQuoteDialerDTOHelper
        .buildDialerPartyDTO(any(InformationPieceTO.class), any(InformationPieceTO.class),
            any(QuotesBean.class), anyString(), anyString(), anyString()))
        .thenReturn(null);

    this.viewQuoteController.createDialerFile();

    verify(this.mockDialerService, times(0)).sendToDialer(any(QuoteDialerDTO.class));
  }

	@Test
	void callDialerService_WithBuilderReturningEmptyProvinceForPC_shouldNotCallDialerService()
			throws Exception {
    QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
    PartyDTO testDialerParty = new PartyDTO();
    testDialerParty.setProvince(null);
    this.setupBasicDialerCallTest(testQuoteDialerDTO, testDialerParty, "PC0000214567", "0",
        LineOfBusinessCodeEnum.COMMERCIAL_LINES, LineOfInsuranceCodeEnum.RESIDENTIAL);

    this.viewQuoteController.createDialerFile();

    verify(this.mockDialerService, times(0)).sendToDialer(any(QuoteDialerDTO.class));
  }

	@Test
	void callDialerService_WithBuilderReturningNullPhoneDTO_shouldNotCallDialerService()
			throws Exception {
    QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
    PartyDTO testDialerParty = new PartyDTO();
    this.setupBasicDialerCallTest(testQuoteDialerDTO, testDialerParty, "QF0000214567", "0",
        LineOfBusinessCodeEnum.PERSONAL_LINES, LineOfInsuranceCodeEnum.AUTOMOBILE);
    testQuoteDialerDTO.setPhone(null);

    this.viewQuoteController.createDialerFile();

    verify(this.mockDialerService, times(0)).sendToDialer(any(QuoteDialerDTO.class));
  }

	@Test
	void callDialerService_WithBuilderReturningEmptyPhoneNumber_shouldNotCallDialerService()
			throws Exception {
    QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
    PartyDTO testDialerParty = new PartyDTO();
    this.setupBasicDialerCallTest(testQuoteDialerDTO, testDialerParty, "QF0000214567", "0",
        LineOfBusinessCodeEnum.PERSONAL_LINES, LineOfInsuranceCodeEnum.AUTOMOBILE);
    testQuoteDialerDTO.getPhone().setPhoneNumber(null);

    this.viewQuoteController.createDialerFile();

    verify(this.mockDialerService, times(0)).sendToDialer(any(QuoteDialerDTO.class));
  }

	@Test
	void callDialerService_WithServiceThrowingUnmatchException_shouldSetSucceedFlagToFalseAndLogBrokerNumber()
			throws Exception {
    QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
    PartyDTO testDialerParty = new PartyDTO();
    List<String> errorCodes = new ArrayList<String>();
    List<String> errorMessages = new ArrayList<String>();
    errorCodes.add(DialerServiceException.UNMATCH_BROKER_CODE);
    errorMessages.add("Test error.");
    DialerServiceException testException = new DialerServiceException(errorCodes, errorMessages,
        "Test error.", new Throwable());
    this.setupBasicDialerCallTest(testQuoteDialerDTO, testDialerParty, "QF0000214567", "0",
        LineOfBusinessCodeEnum.PERSONAL_LINES, LineOfInsuranceCodeEnum.AUTOMOBILE);
    when(this.mockDialerService.sendToDialer(testQuoteDialerDTO)).thenThrow(testException);

    this.viewQuoteController.createDialerFile();

    assertFalse(
        this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been false - unmatch custom exception thrown by service.");
    assertTrue(this.viewQuoteController.getDialerOutput().contains(this.testBrokerNumber),
        "Dialer output should contain unmatch broker number.");
    verify(this.mockDialerService, times(1)).sendToDialer(any(QuoteDialerDTO.class));
    verify(this.mockClientContactBean, times(1)).setNoteText("Failed Dialer creation file.");
    verify(this.quoteBusinessProcess, times(1))
        .updateQuote(any(IUserContext.class), any(Quote.class));
  }

	@Test
	void callDialerService_WithServiceThrowingGeneralCustomException_shouldSetSucceedFlagToFalse()
			throws Exception {
    QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
    PartyDTO testDialerParty = new PartyDTO();
    List<String> errorCodes = new ArrayList<String>();
    List<String> errorMessages = new ArrayList<String>();
    errorCodes.add("OTHER_ERROR");
    errorMessages.add("Test error.");
    DialerServiceException testException = new DialerServiceException(errorCodes, errorMessages,
        "Test error.", new Throwable());
    this.setupBasicDialerCallTest(testQuoteDialerDTO, testDialerParty, "QF0000214567", "0",
        LineOfBusinessCodeEnum.PERSONAL_LINES, LineOfInsuranceCodeEnum.AUTOMOBILE);
    when(this.mockDialerService.sendToDialer(testQuoteDialerDTO)).thenThrow(testException);

    this.viewQuoteController.createDialerFile();

    assertFalse(
        this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been false - general custom exception thrown by service.");
    verify(this.mockDialerService, times(1)).sendToDialer(any(QuoteDialerDTO.class));
    verify(this.mockClientContactBean, times(1)).setNoteText("Failed Dialer creation file.");
    verify(this.quoteBusinessProcess, times(1))
        .updateQuote(any(IUserContext.class), any(Quote.class));
  }

	@Test
	void callDialerService_WithServiceThrowingGeneralException_shouldSetSucceedFlagToFalse()
			throws Exception {
    QuoteDialerDTO testQuoteDialerDTO = new QuoteDialerDTO();
    PartyDTO testDialerParty = new PartyDTO();
    RuntimeException testException = new RuntimeException();
    this.setupBasicDialerCallTest(testQuoteDialerDTO, testDialerParty, "QF0000214567", "0",
        LineOfBusinessCodeEnum.PERSONAL_LINES, LineOfInsuranceCodeEnum.AUTOMOBILE);
    when(this.mockDialerService.sendToDialer(testQuoteDialerDTO)).thenThrow(testException);

    this.viewQuoteController.createDialerFile();

    assertFalse(this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been false - general exception thrown by service");
    verify(this.mockDialerService, times(1)).sendToDialer(any(QuoteDialerDTO.class));
    verify(this.mockClientContactBean, times(1)).setNoteText("Failed Dialer creation file.");
    verify(this.quoteBusinessProcess, times(1))
        .updateQuote(any(IUserContext.class), any(Quote.class));
  }

	/*************************************************
	*************************************************
	*
	* Log and display dialer errors tests
	*
	*************************************************
	*************************************************
	*/
	@Test
	void manageCreateDialerFileFailure_WithValidParams_ShouldLogErrorAndUpdateInfos()
			throws Exception {
    String testErrorMessage = "test message";
    String testLogMessage = "test log message";
    when(this.mockQuote.getLineOfBusiness()).thenReturn(LineOfBusinessCodeEnum.PERSONAL_LINES);
    when(this.mockQuote.getLineOfInsurance()).thenReturn(LineOfInsuranceCodeEnum.AUTOMOBILE);
    when(this.mockQuote.getApplicationMode()).thenReturn("QA");

    this.viewQuoteController.manageCreateDialerFileFailure(testErrorMessage, testLogMessage);

    assertFalse(this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been false - logging error");
    assertEquals(testErrorMessage, this.viewQuoteController.getDialerOutput());
    verify(this.mockClientContactBean, times(1)).setNoteText("Failed Dialer creation file.");
    verify(this.quoteBusinessProcess, times(1))
        .updateQuote(any(IUserContext.class), any(Quote.class));
  }

	@Test
	void manageCreateDialerFileFailure_WithNullErrorMessage_ShouldSetDialerOutputToNullMessage()
			throws Exception {
    String testErrorMessage = null;
    String testLogMessage = "test log message";
    when(this.mockQuote.getLineOfBusiness()).thenReturn(LineOfBusinessCodeEnum.PERSONAL_LINES);
    when(this.mockQuote.getLineOfInsurance()).thenReturn(LineOfInsuranceCodeEnum.AUTOMOBILE);
    when(this.mockQuote.getApplicationMode()).thenReturn("QA");

    this.viewQuoteController.manageCreateDialerFileFailure(testErrorMessage, testLogMessage);

    assertFalse(this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been false - logging error");
    assertEquals(testErrorMessage, this.viewQuoteController.getDialerOutput());
    verify(this.mockClientContactBean, times(1)).setNoteText("Failed Dialer creation file.");
    verify(this.quoteBusinessProcess, times(1))
        .updateQuote(any(IUserContext.class), any(Quote.class));
  }

	@Test
	void manageCreateDialerFileFailure_WithNullLogMessage_ShouldLogNullMessage()
			throws Exception {
    String testErrorMessage = "test message";
    String testLogMessage = null;
    when(this.mockQuote.getLineOfBusiness()).thenReturn(LineOfBusinessCodeEnum.PERSONAL_LINES);
    when(this.mockQuote.getLineOfInsurance()).thenReturn(LineOfInsuranceCodeEnum.AUTOMOBILE);
    when(this.mockQuote.getApplicationMode()).thenReturn("QA");

    this.viewQuoteController.manageCreateDialerFileFailure(testErrorMessage, testLogMessage);

    assertFalse(this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been false - logging error");
    assertEquals(testErrorMessage, this.viewQuoteController.getDialerOutput());
    verify(this.mockClientContactBean, times(1)).setNoteText("Failed Dialer creation file.");
    verify(this.quoteBusinessProcess, times(1))
        .updateQuote(any(IUserContext.class), any(Quote.class));
  }

	@Test
	void manageCreateDialerFileFailure_WithUpdateThrowingException_ShouldLogBothErrorAndException()
			throws Exception {
    String testErrorMessage = "test message";
    String testLogMessage = "test log message";
    when(this.mockQuote.getLineOfBusiness()).thenReturn(LineOfBusinessCodeEnum.PERSONAL_LINES);
    when(this.mockQuote.getLineOfInsurance()).thenReturn(LineOfInsuranceCodeEnum.AUTOMOBILE);
    when(this.mockQuote.getApplicationMode()).thenReturn("QA");
    RuntimeException testException = new RuntimeException();
    doThrow(testException).when(this.quoteBusinessProcess)
        .updateQuote(any(IUserContext.class), any(Quote.class));

    this.viewQuoteController.manageCreateDialerFileFailure(testErrorMessage, testLogMessage);

    assertFalse(this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been false - logging error");
    assertEquals(testErrorMessage, this.viewQuoteController.getDialerOutput());
    verify(this.mockClientContactBean, times(1)).setNoteText("Failed Dialer creation file.");
    verify(this.quoteBusinessProcess, times(1))
        .updateQuote(any(IUserContext.class), any(Quote.class));
  }

	@Test
	void manageCreateDialerFileFailure_WithNullClientContactBean_ShouldNotCallUpdate()
			throws Exception {
    String testErrorMessage = "test message";
    String testLogMessage = "test log message";
    when(this.mockQuote.getLineOfBusiness()).thenReturn(LineOfBusinessCodeEnum.PERSONAL_LINES);
    when(this.mockQuote.getLineOfInsurance()).thenReturn(LineOfInsuranceCodeEnum.AUTOMOBILE);
    when(this.mockQuote.getApplicationMode()).thenReturn("QF");
    this.viewQuoteController.setClientContactBean(null);

    this.viewQuoteController.manageCreateDialerFileFailure(testErrorMessage, testLogMessage);

    assertFalse(this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been false - logging error");
    assertEquals(testErrorMessage, this.viewQuoteController.getDialerOutput());
    verify(this.quoteBusinessProcess, times(0))
        .updateQuote(any(IUserContext.class), any(Quote.class));
  }

	@Test
	void manageCreateDialerFileFailure_WithNullQuote_ShouldSetNoteButNotUpdate()
			throws Exception {
    String testErrorMessage = "test message";
    String testLogMessage = "test log message";
    when(this.mockQuote.getLineOfBusiness()).thenReturn(LineOfBusinessCodeEnum.PERSONAL_LINES);
    when(this.mockQuote.getLineOfInsurance()).thenReturn(LineOfInsuranceCodeEnum.AUTOMOBILE);
    when(this.mockQuote.getApplicationMode()).thenReturn("QF");
    this.viewQuoteController.setQuote(null);

    this.viewQuoteController.manageCreateDialerFileFailure(testErrorMessage, testLogMessage);

    assertFalse(this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been false - logging error");
    assertEquals(testErrorMessage, this.viewQuoteController.getDialerOutput());
    verify(this.mockClientContactBean, times(1)).setNoteText("Failed Dialer creation file.");
    verify(this.quoteBusinessProcess, times(0))
        .updateQuote(any(IUserContext.class), any(Quote.class));
  }

	@Test
	void manageCreateDialerFileFailure_WithNullLineOfBusiness_ShouldSetNoteButNotUpdate()
			throws Exception {
    String testErrorMessage = "test message";
    String testLogMessage = "test log message";
    when(this.mockQuote.getLineOfBusiness()).thenReturn(null);
    when(this.mockQuote.getLineOfInsurance()).thenReturn(LineOfInsuranceCodeEnum.AUTOMOBILE);
    when(this.mockQuote.getApplicationMode()).thenReturn("QF");

    this.viewQuoteController.manageCreateDialerFileFailure(testErrorMessage, testLogMessage);

    assertFalse(this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been false - logging error");
    assertEquals(testErrorMessage, this.viewQuoteController.getDialerOutput());
    verify(this.mockClientContactBean, times(1)).setNoteText("Failed Dialer creation file.");
    verify(this.quoteBusinessProcess, times(0))
        .updateQuote(any(IUserContext.class), any(Quote.class));
  }

	@Test
	void manageCreateDialerFileFailure_WithNullLineOfInsurance_ShouldSetNoteButNotUpdate()
			throws Exception {
    String testErrorMessage = "test message";
    String testLogMessage = "test log message";
    when(this.mockQuote.getLineOfBusiness()).thenReturn(LineOfBusinessCodeEnum.PERSONAL_LINES);
    when(this.mockQuote.getLineOfInsurance()).thenReturn(null);
    when(this.mockQuote.getApplicationMode()).thenReturn("QF");

    this.viewQuoteController.manageCreateDialerFileFailure(testErrorMessage, testLogMessage);

    assertFalse(this.viewQuoteController.getDialerSucceed(),
        "DialerSucceed flag should've been false - logging error");
    assertEquals(testErrorMessage, this.viewQuoteController.getDialerOutput());
    verify(this.mockClientContactBean, times(1)).setNoteText("Failed Dialer creation file.");
    verify(this.quoteBusinessProcess, times(0))
        .updateQuote(any(IUserContext.class), any(Quote.class));
  }

  /*************************************************
   *************************************************
   *
   * Private methods
   *
   *************************************************
   *************************************************
   */
  private void setupBasicDialerCallTest(QuoteDialerDTO testQuoteDialerDTO, PartyDTO testDialerParty,
      String testAgreementNumber, String testConfirmationNumber,
      LineOfBusinessCodeEnum testLineOfBusiness, LineOfInsuranceCodeEnum testLineOfInsurance)
      throws Exception {
    Locale testLocale = Locale.CANADA_FRENCH;
    String testApplicationMode =
        testAgreementNumber != null && testAgreementNumber.length() > 2 ? testAgreementNumber
            .substring(0, 2) : null;

    if (LineOfInsuranceCodeEnum.AUTOMOBILE.equals(testLineOfInsurance)) {
      when(this.mockClientContactBean.getBrokerNbr()).thenReturn(this.testBrokerNumber);
    } else {
      when(this.mockQuote.getBrokerNumber()).thenReturn(this.testBrokerNumber);
    }

    when(this.languageController.getLocale()).thenReturn(testLocale);
    when(this.permissionController.getUserContext()).thenReturn(this.mockUserContext);
    when(this.mockQuote.getAgreementNumber()).thenReturn(testAgreementNumber);
    when(this.mockQuote.getLineOfBusiness()).thenReturn(testLineOfBusiness);
    when(this.mockQuote.getLineOfInsurance()).thenReturn(testLineOfInsurance);
    when(this.mockQuote.getApplicationMode()).thenReturn(testApplicationMode==null ? "QF" : testApplicationMode);
    when(this.mockUserContext.getCompany()).thenReturn("A");

    PhoneDTO testPhone = new PhoneDTO();
    testPhone.setPhoneNumber("5556666");
    testQuoteDialerDTO.setPhone(testPhone);
    when(this.mockQuoteDialerDTOHelper
        .buildQuoteDialerDTO(eq(this.mockQuote), eq(this.mockUserContext),
            eq(this.testBrokerNumber), eq(testApplicationMode), any(InformationPieceTO.class)))
        .thenReturn(testQuoteDialerDTO);
    when(this.mockQuoteDialerDTOHelper
        .buildDialerPartyDTO(any(InformationPieceTO.class), any(InformationPieceTO.class),
            eq(this.mockQuote), anyString(), eq(testApplicationMode), eq("QC")))
        .thenReturn(testDialerParty);
    when(this.mockQuoteDialerDTOHelper
        .buildDialerVehicleDTOList(any(InformationPieceTO.class), eq(testLocale.getLanguage())))
        .thenReturn(new ArrayList<VehicleDTO>());
    when(this.mockDialerService.sendToDialer(testQuoteDialerDTO))
        .thenReturn(testConfirmationNumber);
  }

}


