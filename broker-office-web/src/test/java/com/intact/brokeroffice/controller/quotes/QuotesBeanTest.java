package com.intact.brokeroffice.controller.quotes;

import org.joda.time.DateTime;
import org.junit.jupiter.api.Test;

import com.intact.brokeroffice.business.quote.QuotesBean;

import static org.junit.jupiter.api.Assertions.assertEquals;

class QuotesBeanTest {

	@Test
	void getChildrens() {
		// Prepare
		QuotesBean parent = new QuotesBean();
		parent.setAgreementNumber("parent");
		parent.setLastUpdate(new DateTime(2017, 5, 5, 8, 0).toDate());
		
		QuotesBean childOct = new QuotesBean();
		childOct.setAgreementNumber("childOct");
		childOct.setLastUpdate(new DateTime(2017, 10, 12, 8, 0).toDate());
		
		QuotesBean childOct2 = new QuotesBean();
		childOct2.setAgreementNumber("childOct2");
		childOct2.setLastUpdate(new DateTime(2017, 10, 12, 7, 30).toDate());
		
		QuotesBean childNov = new QuotesBean();
		childNov.setAgreementNumber("childNov");
		childNov.setLastUpdate(new DateTime(2017, 11, 12, 8, 0).toDate());
		
		// Execute		
		parent.addChild(childOct2);
		parent.addChild(childNov);
		parent.addChild(childOct);


		// Verify
		assertEquals(3, parent.getChildrens().size());
		assertEquals("childNov", parent.getChildrens().get(0).getAgreementNumber());
		assertEquals("childOct", parent.getChildrens().get(1).getAgreementNumber());
		assertEquals("childOct2", parent.getChildrens().get(2).getAgreementNumber());
	}
}
