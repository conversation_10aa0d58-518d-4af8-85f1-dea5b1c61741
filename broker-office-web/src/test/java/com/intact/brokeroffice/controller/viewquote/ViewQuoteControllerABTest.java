package com.intact.brokeroffice.controller.viewquote;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.ResourceBundle;
import java.util.Set;

import com.ing.canada.common.services.impl.java.policy.PolicyVersionInfoService;
import com.intact.brokeroffice.business.common.impl.CommandBusinessProcess;
import jakarta.faces.component.UIViewRoot;
import jakarta.faces.context.ExternalContext;
import jakarta.faces.context.FacesContext;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.domain.impl.SubBrokers;
import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.common.services.api.policy.IPolicyVersionInfoService;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransaction;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransactionActivity;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransactionSubActivity;
import com.ing.canada.plp.domain.enums.AgreementFollowUpStatusEnum;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.BusinessTransactionSubActivityCodeEnum;
import com.ing.canada.plp.domain.enums.ConsentTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ExternalSystemOriginCodeEnum;
import com.ing.canada.plp.domain.enums.LanguageCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.PhoneTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.enums.TransactionStatusCodeEnum;
import com.ing.canada.plp.domain.enums.UserActivityTypeCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicyNote;
import com.ing.canada.plp.domain.party.Consent;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.party.Phone;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.subbrokerassignment.SubBrokerAssignment;
import com.ing.canada.plp.domain.usertype.AuditTrail;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.helper.enums.PaymentPlanEnum;
import com.ing.canada.plp.report.enums.QuoteStatusCodeEnum;
import com.ing.canada.plp.service.IBusinessTransactionSubActivityService;
import com.ing.canada.plp.service.IInsurancePolicyService;
import com.ing.canada.plp.service.IPolicyHolderService;
import com.intact.brokeroffice.business.common.ICommandBusinessProcess;
import com.intact.brokeroffice.business.quote.QuotesBean;
import com.intact.brokeroffice.controller.authentification.AuthentificationController;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;
import com.intact.brokeroffice.controller.language.LanguageController;
import com.intact.brokeroffice.controller.permission.PermissionController;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.brokeroffice.controller.viewquote.offer.OfferAdapterAB;
import com.intact.brokeroffice.controller.viewquote.payment.PaymentAdapterAB;
import com.intact.brokeroffice.dao.SystemDAO;
import com.intact.brokeroffice.service.IBrokerService;
import com.intact.brokeroffice.service.util.Configuration;

@ExtendWith(MockitoExtension.class)
//@MockitoSettings(strictness = Strictness.LENIENT)
class ViewQuoteControllerABTest {

	private MockedStatic<FacesContext> mockedFacesContext;

	private MockedStatic<ResourceBundle> mockedResourceBundle;

	private ViewQuoteControllerAB viewQuoteAB = new ViewQuoteControllerAB();

	private OfferAdapterAB offerAdapterAB = new OfferAdapterAB();
 
	@Mock
	private PaymentAdapterAB paymentAdapterAB;

	@Mock
	private ICommandBusinessProcess commandBusinessProcess;

	@Mock
	private IBrokerService quoteBusinessProcess;

	@Mock
	private PermissionController permissionController;

	@Mock
	private ViewQuoteAdapter quoteAdapter = new ViewQuoteAdapter();

	@Mock
	private AuthentificationController authentificationController;

	@Mock
	protected LanguageController languageController;

	@Mock
	private ProvinceController provinceController;
	
	@Mock
	private IPolicyHolderService policyHolderService;

	@Mock
	protected IPartyHelper partyhelper;

	@Mock
	private SystemDAO systemDAO;

	@Mock
	private IPartyHelper partyHelper;

	@Mock
	private IInsurancePolicyService insurancePolicyService;

	@Mock
	private IInsurancePolicyService insurancePolicyService2;

	@Mock
	private ISubBrokersService subBrokersService;

	@Mock
	private IPolicyVersionHelper policyVersionHelper;

	@Mock
	private IBusinessTransactionSubActivityService businessTransactionSubActivityService;

	@Mock
	private IPolicyVersionInfoService policyVersionInfoService;

	@Mock
	private ResourceBundle resourceBundle;

	@Mock
	private FacesContext context;

	@Mock
	private ExternalContext externalContext;

	@Mock
	private MockHttpSession session;

	@Mock
	private UIViewRoot viewRoot;

	@BeforeEach
	void setUp() {
		MockitoAnnotations.openMocks(this);
		Mockito.lenient();

		mockedFacesContext = Mockito.mockStatic(FacesContext.class);
		mockedResourceBundle = Mockito.mockStatic(ResourceBundle.class);

		ReflectionTestUtils.setField(viewQuoteAB, "offerAdapterAB", offerAdapterAB);
		ReflectionTestUtils.setField(viewQuoteAB, "commandBusinessProcess", commandBusinessProcess);
		ReflectionTestUtils.setField(viewQuoteAB, "quoteBusinessProcess", quoteBusinessProcess);
		ReflectionTestUtils.setField(viewQuoteAB, "permissionController", permissionController);
		ReflectionTestUtils.setField(viewQuoteAB, "quoteAdapter", quoteAdapter);
		ReflectionTestUtils.setField(viewQuoteAB, "authentificationController", authentificationController);
		ReflectionTestUtils.setField(viewQuoteAB, "languageController", languageController);
		ReflectionTestUtils.setField(viewQuoteAB, "provinceController", provinceController);
		ReflectionTestUtils.setField(viewQuoteAB, "policyHolderService", policyHolderService);
		ReflectionTestUtils.setField(viewQuoteAB, "partyhelper", partyhelper);
		ReflectionTestUtils.setField(viewQuoteAB, "systemDAO", systemDAO);
		ReflectionTestUtils.setField(quoteAdapter, "partyHelper", partyHelper);
		// ReflectionTestUtils.setField(quoteBusinessProcess, "insurancePolicyService", insurancePolicyService);
		ReflectionTestUtils.setField(quoteAdapter, "insurancePolicyService", insurancePolicyService);
		ReflectionTestUtils.setField(quoteAdapter, "quoteBusinessProcess", quoteBusinessProcess);
		ReflectionTestUtils.setField(quoteAdapter, "subBrokersService", subBrokersService);
		ReflectionTestUtils.setField(quoteAdapter, "policyVersionHelper", policyVersionHelper);
		ReflectionTestUtils.setField(quoteAdapter, "businessTransactionSubActivityService",
				businessTransactionSubActivityService);
		ReflectionTestUtils.setField(quoteAdapter, "policyHolderService", policyHolderService);
		ReflectionTestUtils.setField(quoteAdapter, "policyVersionInfoService", policyVersionInfoService);

		resourceBundle = new ResourceBundle() {

			@Override
			protected Object handleGetObject(String key) {
				return "mock resource value";
			}

			@Override
			public Enumeration<String> getKeys() {
				return null;
			}
		};
		mockedFacesContext.when(FacesContext::getCurrentInstance).thenReturn(context);
		when(context.getViewRoot()).thenReturn(viewRoot);
		when(viewRoot.getLocale()).thenReturn(Locale.CANADA_FRENCH);
		mockedResourceBundle.when(() -> ResourceBundle.getBundle(anyString(), (Locale) any(), (ClassLoader) any())).thenReturn(
				resourceBundle);
	}

//	@AfterEach
//	void tearDown() {
//
//	}

	@AfterEach
	void tearDownStaticMocks() {
		mockedResourceBundle.closeOnDemand();
		mockedFacesContext.closeOnDemand();
	}

	@Disabled
	@Test
	void viewQuoteByRefNumber() throws Exception {
		initViewQuoteAdapterInfo();

		when(context.getExternalContext()).thenReturn(externalContext);
		when(externalContext.getSession(anyBoolean())).thenReturn(session);
		session.setAttribute(SessionConstantsEnum.EMAIL_REFERENCE_NUMBER.getSessionConstant(), "emailRefNumber");

		// Stubbing for both null and non-null arguments
		PolicyVersion p = newPolicyVersion();
		when(commandBusinessProcess.findLatestPolicyVersion(anyString())).thenReturn(p);

		Configuration newConfig = new Configuration();
		newConfig.setConfigs(new HashMap<String, Object>());
		newConfig.getConfigs().put(ApplicationModeEnum.REGULAR_QUOTE.getCode() + "-" + "UPL", true);
		ReflectionTestUtils.setField(viewQuoteAB, "uploadStatuses", newConfig);

		viewQuoteAB.setQuote(new QuotesBean());
		viewQuoteAB.viewQuote("");

		assertNull(session.getAttribute(SessionConstantsEnum.EMAIL_REFERENCE_NUMBER.getSessionConstant()));
		assertEquals("AgreementLegacyNumber", viewQuoteAB.getReferenceLegacyNo());
		assertTrue(viewQuoteAB.isQuoteUploaded());
		assertFalse(viewQuoteAB.getUbiConsentInd());
		assertTrue(viewQuoteAB.getCreditConsentInd());
		assertTrue(viewQuoteAB.getCreateProfileConsentInd());

		ClientContactBean resultClientContactBean = viewQuoteAB.getClientContactBean();

		assertEquals(1, resultClientContactBean.getNotes().size());
		assertEquals(AgreementFollowUpStatusEnum.NOT_CONTACTED_NO_FOLLOWUP_REQUIRED, resultClientContactBean.getFollowupStatus());
		assertEquals("Olivier Tremblay", resultClientContactBean.getPolicyHolder());
		assertEquals(QuoteStatusCodeEnum.UPLOADED, resultClientContactBean.getStatus());
		assertEquals("(514)444-4444 ext 83919", resultClientContactBean.getPhoneCell());
		assertEquals("NameLine1NameLine2", resultClientContactBean.getBrokerAdvisor());
		assertEquals("WEBBK", resultClientContactBean.getQuoteSource());
	}

	private void initViewQuoteAdapterInfo() {
		ISubBrokers subBroker = new SubBrokers();
		subBroker.setNameLine1("NameLine1");
		subBroker.setNameLine2("NameLine2");
		when(subBrokersService.getSubBrokerById(anyLong())).thenReturn(subBroker);

		PolicyHolder aPolicyHolder = new PolicyHolder();
		Party aParty = new Party();
		aParty.setEmailAddress("<EMAIL>");
		aParty.setFirstName("Olivier");
		aParty.setLastName("Tremblay");
		aPolicyHolder.setParty(aParty);
		when(policyVersionHelper.getPrincipalInsuredPolicyHolder((PolicyVersion) any()))
				.thenReturn(aPolicyHolder);

		Phone aPhone = new Phone();
		aPhone.setPhoneAreaCode("514");
		aPhone.setPhoneNumber("4444444");
		aPhone.setPhoneExtension("83919");
		when(partyHelper.getCurrentPhone((Party) any(), (PhoneTypeCodeEnum) any())).thenReturn(aPhone);

		BusinessTransactionSubActivity aBusinessTransactionSubActivity = new BusinessTransactionSubActivity();
		aBusinessTransactionSubActivity.setSubActivityCode(BusinessTransactionSubActivityCodeEnum.ROADBLOCK);
		when(businessTransactionSubActivityService.findLastSubActivity(anyLong())).thenReturn(
				aBusinessTransactionSubActivity);

		when(policyHolderService.findPrincipalPolicyHolder((PolicyVersion) any())).thenReturn(aPolicyHolder);

		List<InsurancePolicy> insuranceList = new ArrayList<InsurancePolicy>();
		// insuranceList.add(newInsurancePolicy("agreeNumber", "legaNumber"));
		// ViewQuoteAdapter/generateUploadedQuoteMessage throw missing ressource for: client.message.uploadQuote
		insuranceList.add(newInsurancePolicy("AgreementNumber", "lNumber"));
		insuranceList.add(newInsurancePolicy("AgreementNumber", "lNumber"));

		when(insurancePolicyService.findAllUnexpiredInsurancePolicyForEmail(anyString())).thenReturn(insuranceList);

		Set<PaymentPlanEnum> setPaymentPlanEnum = new HashSet<PaymentPlanEnum>();
		setPaymentPlanEnum.add(PaymentPlanEnum.PLAN_A);
		// When setPaymentPlanEnum not contain PLAN_A, PLAN_E
		// ViewQuoteAdapter/generateEligibleWithdrawalsMessage throw missing ressource for:
		// client.message.eligible.withdrawals
		when(policyVersionInfoService.getPaymentPlans((PolicyVersion) any())).thenReturn(setPaymentPlanEnum);

		when(commandBusinessProcess.findLatestPolicyVersion(anyString())).thenReturn(newPolicyVersion());
		PolicyHolder policyHolder = new PolicyHolder();
		Party party = new Party();
		policyHolder.setParty(party);
		Consent consent = new Consent();
		consent.setConsentType(ConsentTypeCodeEnum.CLIENT_PROFILE_CREATION);
		consent.setConsentIndicator(true);
		party.addConsent(consent);
		when(policyHolderService.findPrincipalPolicyHolder((PolicyVersion) any())).thenReturn(policyHolder);
		when(authentificationController.getCurrentAccountUId()).thenReturn("AccountUID");
		when(authentificationController.isMasterRole()).thenReturn(true);

		when(provinceController.getManufacturerCompanyCode()).thenReturn(ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION);
	}

	private InsurancePolicy newInsurancePolicy(String agreementNumber, String agreementLegacyNumber) {
		InsurancePolicy insurancePolicy = new InsurancePolicy();
		insurancePolicy.setAgreementLegacyNumber(agreementLegacyNumber);
		insurancePolicy.setAgreementNumber(agreementNumber);
		return insurancePolicy;
	}

	private PolicyVersion newPolicyVersion() {
		PolicyVersion policyVersion = new PolicyVersion();
		InsurancePolicy aInsurancePolicy = new InsurancePolicy();
		InsurancePolicyNote insurancePolicyNote = new InsurancePolicyNote();
		insurancePolicyNote.setAuthorUID("AuthorUID");
		insurancePolicyNote.setUserActivityType(UserActivityTypeCodeEnum.VIEW_QUOTE);
		insurancePolicyNote.setInsurancePolicyNote(null);
		AuditTrail trail = new AuditTrail();
		trail.setCreateTimestamp(new Date());
		insurancePolicyNote.setAuditTrail(trail);
		aInsurancePolicy.addInsurancePolicyNote(insurancePolicyNote);

		aInsurancePolicy.setAgreementFollowUpStatus(AgreementFollowUpStatusEnum.NOT_CONTACTED_NO_FOLLOWUP_REQUIRED);
		aInsurancePolicy.setQuotationValidityExpiryDate(new Date());
		aInsurancePolicy.setAgreementLegacyNumber("AgreementLegacyNumber");

		ManufacturingContext aManufacturingContext = new ManufacturingContext();
		aManufacturingContext.setProvince(ProvinceCodeEnum.QUEBEC);
		aInsurancePolicy.setManufacturingContext(aManufacturingContext);

		SubBrokerAssignment aSubBrokerAssignment = new SubBrokerAssignment();
		aSubBrokerAssignment.setCifSubBrokerId(1l);
		aSubBrokerAssignment.setAuditTrail(trail);
		aInsurancePolicy.setAgreementNumber("AgreementNumber");
		aInsurancePolicy.addSubBrokerAssignment(aSubBrokerAssignment);

		aInsurancePolicy.setAuditTrail(trail);
		aInsurancePolicy.setExternalSystemOrigin(ExternalSystemOriginCodeEnum.WEB_BROKER);

		BusinessTransaction businessTransaction = new BusinessTransaction();
		BusinessTransactionActivity lastBusinessTransactionActivity = new BusinessTransactionActivity();
		lastBusinessTransactionActivity.setAuditTrail(trail);
		businessTransaction.addBusinessTransactionActivity(lastBusinessTransactionActivity);
		businessTransaction.setTransactionStatus(TransactionStatusCodeEnum.SUSPENDED);
		policyVersion.setBusinessTransaction(businessTransaction);
		policyVersion.setInsurancePolicy(aInsurancePolicy);
		policyVersion.setLanguageOfCommunication(LanguageCodeEnum.FRENCH);
		return policyVersion;
	}

}
