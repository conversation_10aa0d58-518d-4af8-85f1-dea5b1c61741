package com.intact.brokeroffice.controller.authentification;

import com.ing.canada.common.web.api.auth.TamTokensEnum;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;
import com.intact.brokeroffice.helper.ResourceBundleHelper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;
import org.springframework.mock.web.MockHttpSession;

import jakarta.faces.context.ExternalContext;
import jakarta.faces.context.FacesContext;
import java.util.ArrayList;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class AuthentificationControllerTest {

	private MockedStatic<ResourceBundleHelper> mockedResourceBundleHelper;

	private MockedStatic<FacesContext> mockedFacesContext;

	@InjectMocks
	private AuthentificationController authentificationController;

	@Mock
	private FacesContext context;

	@Mock
	private ExternalContext externalContext;

	private MockHttpSession session;

	@BeforeEach
	void setUp() throws Exception {
		MockitoAnnotations.openMocks(this);

		mockedResourceBundleHelper = Mockito.mockStatic(ResourceBundleHelper.class);
		mockedFacesContext = Mockito.mockStatic(FacesContext.class);

		context = Mockito.mock(FacesContext.class);
		externalContext = Mockito.mock(ExternalContext.class);
		session = new MockHttpSession();

		mockedFacesContext.when(FacesContext::getCurrentInstance).thenReturn(context);
		when(context.getExternalContext()).thenReturn(externalContext);
		when(externalContext.getSession(anyBoolean())).thenReturn(session);
		mockedResourceBundleHelper.when(() -> ResourceBundleHelper.getMessage(anyString(), any(), anyString())).thenReturn("notAllowedSubbroker");

		session.setAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(), "olivier");
		session.setAttribute(SessionConstantsEnum.AVAILABLE_MASTERS.getSessionConstant(), initListMaster());
		session.setAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), "limited");
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@AfterEach
	void tearDownStaticMocks() {
		mockedFacesContext.closeOnDemand();
		mockedResourceBundleHelper.closeOnDemand();
	}

	@Test
	void logout() {
		assertFalse(session.isInvalid());
		String pageResult = authentificationController.logout();
		assertTrue(session.isInvalid());
		assertEquals("logout", pageResult);
	}

	@Test
	void getCurrentAccountUId() {
		String expectedAccountUId = "olivier";
		assertEquals(expectedAccountUId, authentificationController.getCurrentAccountUId());
	}

	@Test
	void getAvailableMasters() {
		String expectedMaster1 = "master1";
		String expectedMaster2 = "master2";

		List<String> resultMasters = authentificationController.getAvailableMasters();

		assertEquals(expectedMaster1, resultMasters.get(0));
		assertEquals(expectedMaster2, resultMasters.get(1));
	}

	@Test
	void getAccessLevel() {
		String expectedAccessLevel = "limited";

		assertEquals(expectedAccessLevel, authentificationController.getCurrentAccessLevel());
	}

	@Test
	void getBlockedSubbrokers() {
		String expectedBlockedSubbroker = "notAllowedSubbroker";
		
		List<String> blockedSubbrokerList = authentificationController.getBlockedSubbrokers();
		
		assertEquals(expectedBlockedSubbroker.toUpperCase(), blockedSubbrokerList.get(0));
	}

	private List<String> initListMaster() {
		List<String> availableMaster = new ArrayList<String>();
		availableMaster.add("master1");
		availableMaster.add("master2");
		return availableMaster;
	}
}















