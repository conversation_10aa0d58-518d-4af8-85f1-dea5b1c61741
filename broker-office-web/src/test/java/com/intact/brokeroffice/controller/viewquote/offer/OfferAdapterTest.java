package com.intact.brokeroffice.controller.viewquote.offer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

import java.util.HashMap;
import java.util.Map;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import com.ing.canada.plp.domain.coverage.CoverageRepositoryEntry;
import com.ing.canada.plp.domain.enums.BasicCoverageCodeEnum;
import com.ing.canada.plp.domain.enums.EndorsementCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;

class OfferAdapterTest {
	
	OfferAdapter<VehicleOfferBean> adapter = new OfferAdapter<VehicleOfferBean>() {
		@Override
		protected void resetEndorsementRank(String codeValueEnum, CoverageOfferBean aCoverageOfferBean) { }

		@Override
		protected String obtainCoverageCode(CoverageOffer aCoverageOffer) { return null; }
		
		@Override
		protected String mappingEndorsementKey(String anEndosementKey) { return null; }
		
		@Override
		public void loadOfferBean(OfferBean anOfferBean, InsuranceRiskOffer anInsuranceRiskOffer, ProvinceCodeEnum aProvinceCode) { }
		
		@Override
		protected void loadCoverageInformation(CoverageOfferBean covOfferBean, CoverageOffer covOff) { }
		
		@Override
		protected void loadAllEndorsementsSelectedFromOffer(VehicleOfferBean aVehicleOfferBean, Map<String, CoverageOfferBean> mapEndorsementOptions) { }
		
		@Override
		protected boolean isValidEndorsements(EndorsementCodeEnum codeEnum) { return false; }

		@Override
		protected void initEndorsement(VehicleOfferBean aVehicleOfferBean, ManufacturerCompanyCodeEnum aManufacturerCompanyCode) {}
	};

	@BeforeEach
	void setUp() throws Exception {
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@Test
	void test() {
		CoverageOffer covOff = new CoverageOffer();
		covOff.setCoverageSelectedIndicator(true);
		CoverageRepositoryEntry aCoverageRepositoryEntry = new CoverageRepositoryEntry();
		aCoverageRepositoryEntry.setCoverageCode(BasicCoverageCodeEnum.LIABILITY_A.getCode());
		covOff.setCoverageRepositoryEntry(aCoverageRepositoryEntry);
		covOff.setDeductibleAmount(100);
		covOff.setLimitOfInsurance(1000);
		
		VehicleOfferBean aVehicleOfferBean = new VehicleOfferBeanQC();
		Map<String, CoverageOfferBean> theCoverageOffers = new HashMap<String, CoverageOfferBean>();
		CoverageOfferBean aCoverageOfferBean = new CoverageOfferBean();
		theCoverageOffers.put(BasicCoverageCodeEnum.LIABILITY_A.getCode(), aCoverageOfferBean);
		aVehicleOfferBean.setCoverageOffers(theCoverageOffers);
		
		CoverageOfferBean resultBean = adapter.initSelectedCoverage(covOff, aVehicleOfferBean);
		
		assertEquals(1000, resultBean.getCoverageAmount().intValue());
		assertFalse(resultBean.getEndorsementIndicator());
	}

}
