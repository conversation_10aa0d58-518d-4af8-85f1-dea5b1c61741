package com.intact.brokeroffice.controller.viewquote.offer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.cif.domain.impl.InsurancePolicy;
import com.ing.canada.common.domain.QuoteCalculationDetails;
import com.ing.canada.common.services.api.rating.IQuotationService;
import com.ing.canada.plp.domain.billing.Billing;
import com.ing.canada.plp.domain.coverage.CoverageRepositoryEntry;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.BillingPlanCodeEnum;
import com.ing.canada.plp.domain.enums.CombinedPolicyCodeEnum;
import com.ing.canada.plp.domain.enums.CoverageTypeCodeEnum;
import com.ing.canada.plp.domain.enums.EndorsementCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.domain.vehicle.VehicleDetailSpecificationRepositoryEntry;
import com.ing.canada.plp.domain.vehicle.VehicleRepositoryEntry;
import com.ing.canada.plp.helper.IInsuranceRiskOfferHelper;
import com.intact.business.rules.quote.BR5883_HasFourPercentageSurcharge;
import com.intact.business.rules.quote.BR5883_HasTwoPercentageSurcharge;

@ExtendWith(MockitoExtension.class)
class OfferAdapterABTest {

	private OfferAdapterAB offerAdapterAb = new OfferAdapterAB();

	private IInsuranceRiskOfferHelper mockInsuranceRiskOfferHelper = mock(IInsuranceRiskOfferHelper.class);
	private IQuotationService mockQuotationService = mock(IQuotationService.class);
	private BR5883_HasFourPercentageSurcharge mockBr5883_4perc = mock(BR5883_HasFourPercentageSurcharge.class);
	private BR5883_HasTwoPercentageSurcharge mockBr5883_2perc = mock(BR5883_HasTwoPercentageSurcharge.class);

	@BeforeEach
	void setUp() throws Exception {
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@Test
	void noValidEndosement() {
		assertFalse(offerAdapterAb.isValidEndorsements(EndorsementCodeEnum._03_SAV));
	}

	@Test
	void validEndosement() {
		assertTrue(offerAdapterAb.isValidEndorsements(EndorsementCodeEnum._H18_SAV));
	}

	@Test
	void resetEndorsementRank() {
		CoverageOfferBean coverageOfferBean = new CoverageOfferBean();
		offerAdapterAb.resetEndorsementRank("50F", coverageOfferBean);
		assertEquals(9, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterAb.resetEndorsementRank("20", coverageOfferBean);
		assertEquals(1, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterAb.resetEndorsementRank("UE05", coverageOfferBean);
		assertEquals(10, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterAb.resetEndorsementRank("PPA", coverageOfferBean);
		assertEquals(2, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterAb.resetEndorsementRank("PPB", coverageOfferBean);
		assertEquals(2, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterAb.resetEndorsementRank("CSE", coverageOfferBean);
		assertEquals(3, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterAb.resetEndorsementRank("CSF", coverageOfferBean);
		assertEquals(3, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterAb.resetEndorsementRank("CFE", coverageOfferBean);
		assertEquals(4, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterAb.resetEndorsementRank("CFF", coverageOfferBean);
		assertEquals(4, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterAb.resetEndorsementRank("13", coverageOfferBean);
		assertEquals(5, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterAb.resetEndorsementRank("13D", coverageOfferBean);
		assertEquals(6, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterAb.resetEndorsementRank("H17", coverageOfferBean);
		assertEquals(7, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterAb.resetEndorsementRank("H18", coverageOfferBean);
		assertEquals(8, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterAb.resetEndorsementRank("49Y", coverageOfferBean);
		assertEquals(9, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterAb.resetEndorsementRank("50Y", coverageOfferBean);
		assertEquals(9, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterAb.resetEndorsementRank("49F", coverageOfferBean);
		assertEquals(9, coverageOfferBean.getRank().intValue());
	}

	@Test
	void initEndorsement() {
		ManufacturerCompanyCodeEnum manufacturerCompanyCodeEnum = ManufacturerCompanyCodeEnum.BELAIRDIRECT;
		VehicleOfferBeanAB vehicleOfferBeanAB = new VehicleOfferBeanAB();

		offerAdapterAb.initEndorsement(vehicleOfferBeanAB, manufacturerCompanyCodeEnum);
	}

	@Test
	void obtainCoverageCodeNull() {
		CoverageOffer coverageOffer = new CoverageOffer();
		CoverageRepositoryEntry coverageRepositoryEntry = new CoverageRepositoryEntry();
		coverageOffer.setCoverageRepositoryEntry(coverageRepositoryEntry);
		String result = offerAdapterAb.obtainCoverageCode(coverageOffer);
		assertNull(result);
	}

	@Test
	void obtainCoverageCodePPBSAV() {
		CoverageOffer coverageOffer = new CoverageOffer();
		CoverageRepositoryEntry coverageRepositoryEntry = new CoverageRepositoryEntry();
		coverageRepositoryEntry.setCoverageCode(EndorsementCodeEnum.PPB_SAV.getCode());
		coverageOffer.setCoverageRepositoryEntry(coverageRepositoryEntry);
		String result = offerAdapterAb.obtainCoverageCode(coverageOffer);
		assertEquals(EndorsementCodeEnum.PPA_SAV.getCode(), result);
	}

	@Test
	void obtainCoverageCodeCFFSAV() {
		CoverageOffer coverageOffer = new CoverageOffer();
		CoverageRepositoryEntry coverageRepositoryEntry = new CoverageRepositoryEntry();
		coverageRepositoryEntry.setCoverageCode(EndorsementCodeEnum.CFF_SAV.getCode());
		coverageOffer.setCoverageRepositoryEntry(coverageRepositoryEntry);
		String result = offerAdapterAb.obtainCoverageCode(coverageOffer);
		assertEquals(EndorsementCodeEnum.CFE_SAV.getCode(), result);
	}

	@Test
	void obtainCoverageCode39FSAV() {
		CoverageOffer coverageOffer = new CoverageOffer();
		CoverageRepositoryEntry coverageRepositoryEntry = new CoverageRepositoryEntry();
		coverageRepositoryEntry.setCoverageCode(EndorsementCodeEnum._39F_SAV.getCode());
		coverageOffer.setCoverageRepositoryEntry(coverageRepositoryEntry);
		String result = offerAdapterAb.obtainCoverageCode(coverageOffer);
		assertEquals(EndorsementCodeEnum._39_SAV.getCode(), result);
	}

	@Test
	void obtainCoverageCode49FSAV() {
		CoverageOffer coverageOffer = new CoverageOffer();
		CoverageRepositoryEntry coverageRepositoryEntry = new CoverageRepositoryEntry();
		coverageRepositoryEntry.setCoverageCode(EndorsementCodeEnum._49F_SAV.getCode());
		coverageOffer.setCoverageRepositoryEntry(coverageRepositoryEntry);
		String result = offerAdapterAb.obtainCoverageCode(coverageOffer);
		assertEquals(EndorsementCodeEnum._49Y_SAV.getCode(), result);
	}

	@Test
	void mappingEndorsementKeyPPBSAV() {
		assertEquals(EndorsementCodeEnum.PPA_SAV.getCode(), offerAdapterAb.mappingEndorsementKey("PPB"));
	}

	@Test
	void mappingEndorsementKeyCFFSAV() {
		assertEquals(EndorsementCodeEnum.CFE_SAV.getCode(), offerAdapterAb.mappingEndorsementKey("CFF"));
	}

	@Test
	void mappingEndorsementKey39FSAV() {
		assertEquals(EndorsementCodeEnum._39_SAV.getCode(), offerAdapterAb.mappingEndorsementKey("CSF"));
	}

	@Test
	void mappingEndorsementKey49FSAV() {
		assertEquals(EndorsementCodeEnum._49Y_SAV.getCode(), offerAdapterAb.mappingEndorsementKey("49F"));
	}

	@Test
	void loadAllEndorsementsSelectedFromOffer() {
		Map<String, CoverageOfferBean> resultEndorsementOptions = new HashMap<String, CoverageOfferBean>();
		Map<String, CoverageOfferBean> endorsements = new HashMap<String, CoverageOfferBean>();
		endorsements.put("1", newCoverageOfferBeanAB(EndorsementCodeEnum.PPB_SAV.getCode()));
		endorsements.put("2", newCoverageOfferBeanAB(EndorsementCodeEnum.CFF_SAV.getCode()));
		endorsements.put("3", newCoverageOfferBeanAB(EndorsementCodeEnum._39F_SAV.getCode()));
		endorsements.put("4", newCoverageOfferBeanAB(EndorsementCodeEnum._49F_SAV.getCode()));

		VehicleOfferBeanAB vehicleOfferBeanAB = new VehicleOfferBeanAB();
		vehicleOfferBeanAB.setMapEndorsements(endorsements);

		offerAdapterAb.loadAllEndorsementsSelectedFromOffer(vehicleOfferBeanAB, resultEndorsementOptions);

		assertEquals(4, resultEndorsementOptions.size());
		assertTrue(resultEndorsementOptions.containsKey(EndorsementCodeEnum.PPA_SAV.getCode()));
		assertTrue(resultEndorsementOptions.containsKey(EndorsementCodeEnum.CFE_SAV.getCode()));
		assertTrue(resultEndorsementOptions.containsKey(EndorsementCodeEnum._39_SAV.getCode()));
		assertTrue(resultEndorsementOptions.containsKey(EndorsementCodeEnum._49Y_SAV.getCode()));
	}

	private CoverageOfferBean newCoverageOfferBeanAB(String coverageCode) {
		CoverageOfferBean coverageOfferBean = new CoverageOfferBean();
		coverageOfferBean.setCoverageCode(coverageCode);
		coverageOfferBean.setCoverageDescription("Some description");
		coverageOfferBean.setManufacturerCompanyCode(ManufacturerCompanyCodeEnum.BELAIRDIRECT);
		coverageOfferBean.setRank(0);
		return coverageOfferBean;
	}

	@Test
	void loadCoverageInformation() {
		CoverageOffer coverageOffer = new CoverageOffer();
		coverageOffer.setCoverageSelectedIndicator(true);
		CoverageRepositoryEntry coverageRepositoryEntry = new CoverageRepositoryEntry();
		coverageRepositoryEntry.setCoverageCode(EndorsementCodeEnum.PPA_SAV.getCode());
		coverageOffer.setCoverageRepositoryEntry(coverageRepositoryEntry);

		CoverageOfferBean coverageOfferBean = new CoverageOfferBean();
		coverageOfferBean.setCoverageSelectedIndicator(null);

		offerAdapterAb.loadCoverageInformation(coverageOfferBean, coverageOffer);

		assertTrue(coverageOfferBean.getCoverageSelectedIndicator());
		assertEquals(EndorsementCodeEnum.PPA_SAV.getCode(), coverageOfferBean.getCoverageCode());
	}

	@Test
	void loadVehicleOfferBeanList(){
		InsuranceRisk insuranceRisk = new InsuranceRisk();
		insuranceRisk.setInsuranceRiskSequence(Short.valueOf("1"));
		insuranceRisk.setInsuranceRiskOfferSystemSelectedIndicator(false);
		InsuranceRiskOffer insuranceRiskOffer = new InsuranceRiskOffer();
		insuranceRiskOffer.setAnnualPremium(1000);
		CoverageOffer coverageOffer = new CoverageOffer();
		CoverageRepositoryEntry coverageRepositoryEntry = new CoverageRepositoryEntry();
		coverageRepositoryEntry.setCoverageType(CoverageTypeCodeEnum.ENDORSEMENT);
		coverageRepositoryEntry.setCoverageCode("PPA");
		coverageOffer.setCoverageRepositoryEntry(coverageRepositoryEntry);
		insuranceRiskOffer.addCoverageOffer(coverageOffer);
		insuranceRisk.addInsuranceRiskOffer(insuranceRiskOffer);
		insuranceRisk.setSelectedInsuranceRiskOffer(insuranceRiskOffer);
		
		VehicleRepositoryEntry vehicleRepositoryEntry = new VehicleRepositoryEntry();
		vehicleRepositoryEntry.setVehicleMakeFrench("Hyundai");
		vehicleRepositoryEntry.setVehicleModelFrench("Accent");
		VehicleDetailSpecificationRepositoryEntry vehicleDetailSpecificationRepositoryEntry = new VehicleDetailSpecificationRepositoryEntry();
		vehicleDetailSpecificationRepositoryEntry.setVehicleYear(2013);
		vehicleDetailSpecificationRepositoryEntry.setVehicleRepositoryEntry(vehicleRepositoryEntry);
		Vehicle vehicle = new Vehicle();
		vehicle.setVehicleDetailSpecificationRepositoryEntry(vehicleDetailSpecificationRepositoryEntry);
		insuranceRisk.setVehicle(vehicle);
		
		PolicyVersion policyVersion = new PolicyVersion();
		policyVersion.addInsuranceRisk(insuranceRisk);
		
		com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy insPolicy = new com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy();
		insPolicy.setApplicationMode(ApplicationModeEnum.REGULAR_QUOTE);
		policyVersion.setInsurancePolicy(insPolicy);
		
		ReflectionTestUtils.setField(offerAdapterAb, "insuranceRiskOfferHelper", mockInsuranceRiskOfferHelper);
		
		
		List<CoverageOffer> listCoverage = new LinkedList<CoverageOffer>();
		
		CoverageOffer coverageOffer2 = new CoverageOffer();
		
		CoverageRepositoryEntry coverageRepositoryEntry2 = new CoverageRepositoryEntry();
		coverageRepositoryEntry2.setCoverageType(CoverageTypeCodeEnum.ENDORSEMENT);
		coverageRepositoryEntry2.setCoverageCode("P1C");
		coverageOffer2.setCoverageRepositoryEntry(coverageRepositoryEntry2);
		
		coverageOffer2.setCoverageRepositoryEntry(coverageRepositoryEntry2);
		listCoverage.add(coverageOffer2);
		
		List<VehicleOfferBeanAB> listVehicule = new ArrayList<VehicleOfferBeanAB>();
		offerAdapterAb.loadVehicleOfferBeanList(listVehicule, policyVersion, Locale.FRENCH,listCoverage);
	
		VehicleOfferBeanAB resultVehicule = listVehicule.get(0);
		
		assertEquals(1, listVehicule.size());
		assertTrue(insuranceRisk.getInsuranceRiskSequence() == resultVehicule.getSequence());
		assertEquals("2013", resultVehicule.getYear());
		assertEquals("Hyundai", resultVehicule.getMake());
		assertEquals("Accent", resultVehicule.getModel());
		assertEquals(insuranceRisk.getSelectedInsuranceRiskOffer().getOfferType()
				, resultVehicule.getSelectedOfferType());
		
	}

	@Disabled
	@Test
	void loadOfferBean(){
		ProvinceCodeEnum province = ProvinceCodeEnum.ALBERTA;	
		InsuranceRiskOffer insuranceRiskOffer = new InsuranceRiskOffer();
		InsuranceRisk insuranceRisk = new InsuranceRisk();
		PolicyVersion policyVersion = new PolicyVersion();
		policyVersion.setCombinedPolicyCode(CombinedPolicyCodeEnum.COMBO_POLICY);
		Billing billing = new Billing();
		billing.setBillingPlan(BillingPlanCodeEnum.PRE_AUTHORIZED_CHEQUE_PLAN);
		policyVersion.setBilling(billing);
		insuranceRisk.setPolicyVersion(policyVersion);
		insuranceRiskOffer.setInsuranceRisk(insuranceRisk);
		
		OfferBean anOfferBean = new OfferBean(OfferTypeCodeEnum.CUSTOM);
		
		ReflectionTestUtils.setField(offerAdapterAb, "br5883_4perc", mockBr5883_4perc);
		ReflectionTestUtils.setField(offerAdapterAb, "br5883_2perc", mockBr5883_2perc);
		ReflectionTestUtils.setField(offerAdapterAb, "quotationService", mockQuotationService);
		
		when(mockBr5883_4perc.validate(policyVersion)).thenReturn(true);
		when(mockBr5883_2perc.validate(policyVersion)).thenReturn(false);
		
		QuoteCalculationDetails quotationCalculationDetail = new QuoteCalculationDetails();
		quotationCalculationDetail.setAnnualAmountWithTaxes(1000.50);
		quotationCalculationDetail.setMonthlyPaymentWithTaxes(95.67);
		when(mockQuotationService.loadOfferBean(anyDouble()
				, (InsuranceRiskOffer) any(), (ProvinceCodeEnum) any()
				, anyDouble(), anyBoolean())).thenReturn(quotationCalculationDetail);
		
		offerAdapterAb.loadOfferBean(anOfferBean, insuranceRiskOffer, province);
		
		assertTrue(1000.50 == anOfferBean.getAnnualPremiumWithTaxes());
		assertTrue(95.67 == anOfferBean.getMonthlyPremiumWithTaxes());
	}

}



























