package com.intact.brokeroffice.controller.spoe;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.faces.context.ExternalContext;
import jakarta.faces.context.FacesContext;
import jakarta.faces.model.SelectItem;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.mock.web.MockHttpSession;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.common.web.api.auth.TamTokensEnum;
import com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.canada.brm.domain.broker.BrokerWebOfficeAccess;
import com.intact.canada.brm.domain.user.UserAccount;

class SpoeBeanTest {

	private MockedStatic<FacesContext> mockedFacesContext;

	private SpoeController spoeControllerController = new SpoeController();

	private final String ldapGroupBroker = "GroupBroker -QC";

	private final String ldapGroupBrokerReassign = "GBrokerReassign -QC";

	private final String ldapGroupUnderwritter = "GBrokerUnderwritter -QC";

	private final String ldapGroupQuoteAdmins = "GQuoteAdmins -QC";

	private final String ldapGroupProgramAdmins = "GProgramAdmins -QC";

	private final String ldapGroupAdmins = "GoupAdmins -QC";

	private IAccountsBusinessProcess mockAccountsBusinessProcess;

	private ProvinceController mockProvinceController;

	@BeforeEach
	void setUp() throws Exception {
		mockProvinceController = mock(ProvinceController.class);
		mockAccountsBusinessProcess = mock(IAccountsBusinessProcess.class);

		ReflectionTestUtils.setField(spoeControllerController, "ldapGroupBroker", ldapGroupBroker);
		ReflectionTestUtils.setField(spoeControllerController, "ldapGroupBrokerReassign", ldapGroupBrokerReassign);
		ReflectionTestUtils.setField(spoeControllerController, "ldapGroupUnderwritter", ldapGroupUnderwritter);
		ReflectionTestUtils.setField(spoeControllerController, "ldapGroupQuoteAdmins", ldapGroupQuoteAdmins);
		ReflectionTestUtils.setField(spoeControllerController, "ldapGroupProgramAdmins", ldapGroupProgramAdmins);
		ReflectionTestUtils.setField(spoeControllerController, "ldapGroupAdmins", ldapGroupAdmins);
		ReflectionTestUtils.setField(spoeControllerController, "accountsBusinessProcess", mockAccountsBusinessProcess);
		ReflectionTestUtils.setField(spoeControllerController, "provinceController", mockProvinceController);
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@BeforeEach
	void setUpStaticMocks() {
		mockedFacesContext = mockStatic(FacesContext.class);
	}

	@AfterEach
	void tearDownStaticMocks() {
		mockedFacesContext.closeOnDemand();
	}

	@Test
	void initAccessLevel() {
		List<SelectItem> selectedItemListResult = spoeControllerController.getAccessLevel();
		assertEquals(25, selectedItemListResult.size());
	}

	@Test
	void getMasterBrokers() {
		Map<String, String> masterBrokerMap = new HashMap<String, String>();
		masterBrokerMap.put("1", "Broker1");
		masterBrokerMap.put("2", "Broker2");
		masterBrokerMap.put("3", "Broker3");
		ReflectionTestUtils.setField(spoeControllerController, "spoeBean", newSpoeBean(ldapGroupBroker));
		when(mockProvinceController.getCompanyEnumCode()).thenReturn(CifCompanyEnum.INTACT_QC);
		when(mockAccountsBusinessProcess.getAssignedMasterBrokers(any(CifCompanyEnum.class),
						anyList(), anyList())).thenReturn(masterBrokerMap);

	}

	@Disabled
	@Test
	void login() throws Exception {
		FacesContext context = Mockito.mock(FacesContext.class);
		MockHttpSession session = new MockHttpSession();
		ExternalContext externalContext = mock(ExternalContext.class);

		Map<String, String> masterBrokerMap = new HashMap<String, String>();
		masterBrokerMap.put("1", "Broker1");
		masterBrokerMap.put("2", "Broker2");
		masterBrokerMap.put("3", "Broker3");
		ReflectionTestUtils.setField(spoeControllerController, "spoeBean", newSpoeBean(ldapGroupBroker));
		when(mockProvinceController.getCompanyEnumCode()).thenReturn(CifCompanyEnum.BELAIR);
		when(mockProvinceController.getCompany()).thenReturn(CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber());
		when(mockAccountsBusinessProcess.getAssignedMasterBrokers(any(CifCompanyEnum.class),
						anyList(), anyList())).thenReturn(masterBrokerMap);

		BrokerWebOfficeAccess aBrokerWebOfficeAccess = new BrokerWebOfficeAccess();
		aBrokerWebOfficeAccess.setMasterOwnerCode("MasterOwnerCodeTest");

		UserAccount userAccount = new UserAccount();
		userAccount.addBrokerWebOfficeAccess(aBrokerWebOfficeAccess);
		mockedFacesContext.when(FacesContext::getCurrentInstance).thenReturn(context);
		when(context.getExternalContext()).thenReturn(externalContext);
		when(externalContext.getSession(true)).thenReturn(session);
		when(mockAccountsBusinessProcess.getDefaultProvince("1")).thenReturn(null);
		when(mockAccountsBusinessProcess.findByUId(Mockito.anyString())).thenReturn(userAccount);

		spoeControllerController.getMasterBrokers();
		String stringIndex = spoeControllerController.login();

		assertEquals("index", stringIndex);
		assertEquals(ldapGroupBroker, session.getAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue()));
		assertEquals("DA7143", session.getAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue()));
		assertEquals("1", session.getAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue()));
		assertEquals("A", session.getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant()));
		assertEquals("MasterOwnerCodeTest",
				((List<?>) session.getAttribute(SessionConstantsEnum.AVAILABLE_MASTERS.getSessionConstant())).get(0));
	}

	@Test
	void loginAdmin() throws Exception {
		FacesContext context = Mockito.mock(FacesContext.class);
		MockHttpSession session = new MockHttpSession();
		HttpServletRequest request = mock(HttpServletRequest.class);
		ExternalContext externalContext = mock(ExternalContext.class);

		ReflectionTestUtils.setField(spoeControllerController, "spoeBean", newSpoeBean(ldapGroupAdmins + "-ON"));

		Cookie cookies[] = new Cookie[1];
		Cookie cookie = new Cookie("brokerDefaultCompany", "A");
		cookies[0] = cookie;
		when(mockProvinceController.getCompanyEnumCode()).thenReturn(CifCompanyEnum.BELAIR);
		when(mockProvinceController.getCompany()).thenReturn(CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber());
		mockedFacesContext.when(FacesContext::getCurrentInstance).thenReturn(context);
		when(context.getExternalContext()).thenReturn(externalContext);
		when(externalContext.getSession(true)).thenReturn(session);
		when(externalContext.getRequest()).thenReturn(request);
		when(request.getCookies()).thenReturn(cookies);

		// Execute
		spoeControllerController.login();

		// Verify
		assertEquals("1", session.getAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue()));
		assertEquals(ldapGroupAdmins, session.getAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue()));
		assertEquals("A", session.getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant()));

	}

	@Test
	void onAccessLevelChanged() {
		SpoeBean spoeBean = newSpoeBean(ldapGroupUnderwritter + "-ON");
		ReflectionTestUtils.setField(spoeControllerController, "spoeBean", spoeBean);
		spoeControllerController.onAccessLevelChanged();
		assertEquals("m_webuwon", spoeBean.getUserId());

		spoeBean = newSpoeBean(ldapGroupUnderwritter + "-AB");
		ReflectionTestUtils.setField(spoeControllerController, "spoeBean", spoeBean);
		spoeControllerController.onAccessLevelChanged();
		assertEquals("m_webuwab", spoeBean.getUserId());
		
		spoeBean = newSpoeBean(ldapGroupAdmins + "-ON");
		ReflectionTestUtils.setField(spoeControllerController, "spoeBean", spoeBean);
		spoeControllerController.onAccessLevelChanged();
		assertEquals("WZ_ADMN", spoeBean.getUserId());
		
		spoeBean = newSpoeBean(ldapGroupAdmins + "-AB");
		ReflectionTestUtils.setField(spoeControllerController, "spoeBean", spoeBean);
		spoeControllerController.onAccessLevelChanged();
		assertEquals("WZ_ADMN", spoeBean.getUserId());
		
		spoeBean = newSpoeBean(ldapGroupProgramAdmins + "-ON");
		ReflectionTestUtils.setField(spoeControllerController, "spoeBean", spoeBean);
		spoeControllerController.onAccessLevelChanged();
		assertEquals("WZ_ADMN", spoeBean.getUserId());
		
		spoeBean = newSpoeBean(ldapGroupProgramAdmins + "-QC");
		ReflectionTestUtils.setField(spoeControllerController, "spoeBean", spoeBean);
		spoeControllerController.onAccessLevelChanged();
		assertEquals("WZ_ADMN", spoeBean.getUserId());
	}

	private SpoeBean newSpoeBean(String selectedAccessLevel) {
		SpoeBean spoeBean = new SpoeBean();
		spoeBean.setUserId("1");
		spoeBean.setSelectedAccessLevel(selectedAccessLevel);
		spoeBean.setSelectedProvince("QC");
		return spoeBean;
	}
}
