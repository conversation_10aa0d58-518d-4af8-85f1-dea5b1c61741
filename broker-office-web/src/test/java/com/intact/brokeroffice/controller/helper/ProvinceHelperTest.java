package com.intact.brokeroffice.controller.helper;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.util.ArrayList;

import jakarta.faces.model.SelectItem;
import jakarta.servlet.http.HttpSession;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.mock.web.MockHttpSession;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;

class ProvinceHelperTest {

	private final String PREFIX = "TEST";

	@BeforeEach
	void setUp() throws Exception {
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@Test
	void addItemAccessLevel() {
		SelectItem resultItems = ProvinceHelper.AddItemAccesLevel(PREFIX, ProvinceCodeEnum.QUEBEC,
				ProvinceCodeEnum.ALBERTA);
		String concatStringResult = (String) resultItems.getValue();
		assertEquals("TEST-QC + TEST-AB", concatStringResult);
	}

	@Test
	void manageCompanies() {
		HttpSession aSession = new MockHttpSession();
		
		String userRole = "TEST-QC + TEST-AB";
		ProvinceHelper.manageCompanies(aSession, userRole, null);
		@SuppressWarnings("unchecked")
		ArrayList<String> result = (ArrayList<String>) aSession.getAttribute(SessionConstantsEnum.AVAILABLE_COMPANIES
				.getSessionConstant());
		assertEquals(2, result.size());
		assertTrue(result.contains("A"));
		assertTrue(result.contains("3"));

		userRole = "TEST-QC";
		ProvinceHelper.manageCompanies(aSession, userRole, null);
		String cdResult = (String) aSession.getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant());
		assertEquals("A", cdResult);
		
		ProvinceHelper.manageCompanies(aSession, "", null);
		String resultEnum = (String) aSession.getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant());
		assertEquals(CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber(), resultEnum);
	}

	@Test
	void containAccessLevelAndProvinces(){
		String selectedAccessLevel = "TEST-QC + TEST-AB";
		String accessLevel = "TEST";
		
		assertTrue(ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel
				, accessLevel, ProvinceCodeEnum.QUEBEC, ProvinceCodeEnum.ALBERTA));	
	}
}









