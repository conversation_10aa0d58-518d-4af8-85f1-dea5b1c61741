package com.intact.brokeroffice.controller.viewquote.payment;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.common.domain.QuoteCalculationDetails;
import com.ing.canada.common.services.api.rating.IQuotationService;
import com.ing.canada.plp.crypto.IIngCipher;
import com.ing.canada.plp.domain.billing.Account;
import com.ing.canada.plp.domain.billing.Billing;
import com.ing.canada.plp.domain.enums.BillingPlanCodeEnum;
import com.ing.canada.plp.domain.enums.CombinedPolicyCodeEnum;
import com.ing.canada.plp.domain.enums.CreditCardCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.MethodOfPaymentCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.PolicyOfferRating;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;

class PaymentAdapterQCTest {
	
	
	private IQuotationService mockQuotationService;
	private IIngCipher mockIIngCipher;
	
	PaymentAdapterQC paymentAdapter = new PaymentAdapterQC();
	
	private final double FULL_TERM_TAXES_AND_SURCHARGE = 100.0;
	private final double MONTHLY_PAYMENT_TAXES_AND_SURCHARGE = 10.0;
	private final double ANNUAL_AMOUNT_TAXES = 15.0;
	private final double FIRST_MONTHLY_PAYMENT_TAXES_AND_SURCHARGE = 20.0;
	private final Integer ANNUAL_PREMIUM = 1000;
	
	private final String CARD_TOKEN = "*%URJU(%T%";
	private final String CREDIT_CARD_EXPIRY_DATE = "1225";
	private final String CREDIT_CARD_HOLDER_NAME = "Olivier";

	@BeforeEach
	void setUp() throws Exception {
		mockQuotationService = Mockito.mock(IQuotationService.class);
		mockIIngCipher = Mockito.mock(IIngCipher.class);
		
	ReflectionTestUtils.setField(paymentAdapter, "quotationService", mockQuotationService);
	ReflectionTestUtils.setField(paymentAdapter, "cipher", mockIIngCipher);
	
	Mockito.when(mockQuotationService
			.getQuoteCalculationDetails((PolicyVersion) Mockito.any()
					, (ProvinceCodeEnum) Mockito.any())).thenReturn(newQuoteCalculationDetails());
	
	Mockito.when(mockIIngCipher.decryptToString(CREDIT_CARD_EXPIRY_DATE)).thenReturn(CREDIT_CARD_EXPIRY_DATE);
	Mockito.when(mockIIngCipher.decryptToString(CREDIT_CARD_HOLDER_NAME)).thenReturn(CREDIT_CARD_HOLDER_NAME);
	}

	@AfterEach
	void tearDown() throws Exception {
		mockQuotationService = null;
		mockIIngCipher = null;	
	}

	private QuoteCalculationDetails newQuoteCalculationDetails(){
		QuoteCalculationDetails quoteCalculationDetails = new QuoteCalculationDetails();
		quoteCalculationDetails.setFullTermPremiumWithTaxesAndSurcharge(FULL_TERM_TAXES_AND_SURCHARGE);
		quoteCalculationDetails.setMonthlyPaymentWithTaxesAndSurcharge(MONTHLY_PAYMENT_TAXES_AND_SURCHARGE);
		quoteCalculationDetails.setAnnualAmountTaxes(ANNUAL_AMOUNT_TAXES);
		quoteCalculationDetails.setFirstMonthlyPaymentWithTaxesAndSurcharge(FIRST_MONTHLY_PAYMENT_TAXES_AND_SURCHARGE);
		quoteCalculationDetails.setPolicyTermInMonths(null);
		quoteCalculationDetails.setMonthlyPaymentsEligible(true);
		quoteCalculationDetails.setAnyPaymentsEligible(false);
		return quoteCalculationDetails;
	}
	
	private PolicyVersion newPolicyVersion(){
		PolicyVersion policyVersion = new PolicyVersion();
		policyVersion.setCombinedPolicyCode(CombinedPolicyCodeEnum.COMBO_POLICY);	
		Billing billing = new Billing();
		Account account = new Account();
		account.setCreditCardHolderName(CREDIT_CARD_HOLDER_NAME);
		account.setCreditCardCompanyCode(CreditCardCompanyCodeEnum.VISA);
		account.setCreditCardExpiryDate(CREDIT_CARD_EXPIRY_DATE);
		account.setCreditCardToken(CARD_TOKEN);
		account.setCreditCardAccountNumberMask(CARD_TOKEN);
		billing.setAccount(account);
		billing.setBillingPlan(BillingPlanCodeEnum.CREDIT_CARD_MONTHLY);
		billing.setMethodOfPayment(MethodOfPaymentCodeEnum.CREDIT_CARD);
		policyVersion.setBilling(billing);	
		InsuranceRisk insuranceRisk = new InsuranceRisk();
		InsuranceRiskOffer insuranceRiskOffer = new InsuranceRiskOffer();
		PolicyOfferRating policyOfferRating = new PolicyOfferRating();
		policyOfferRating.setAnnualPremium(ANNUAL_PREMIUM);
		insuranceRiskOffer.setPolicyOfferRating(policyOfferRating);
		insuranceRisk.addInsuranceRiskOffer(insuranceRiskOffer);
		insuranceRisk.setSelectedInsuranceRiskOffer(insuranceRiskOffer);
		policyVersion.addInsuranceRisk(insuranceRisk);
				
		return policyVersion;
	}

	@Test
	void loadPremiumInformation() {
		PaymentBean paymentBean = new PaymentBean();
		QuoteCalculationDetails quoteCalculationDetails = new QuoteCalculationDetails();
		quoteCalculationDetails.setAnnualAmountWithTaxes(ANNUAL_AMOUNT_TAXES);
		quoteCalculationDetails.setMonthlyPaymentWithTaxes(MONTHLY_PAYMENT_TAXES_AND_SURCHARGE);
		
		paymentAdapter.loadPremiumInformation(paymentBean, quoteCalculationDetails);
		
		assertTrue(ANNUAL_AMOUNT_TAXES == paymentBean.getAnnualAmountWithTaxes());
		assertTrue(MONTHLY_PAYMENT_TAXES_AND_SURCHARGE == paymentBean.getMonthlyPremium());
	}

	@Test
	void loadPaymentBean() {
		PaymentBean paymentBean = new PaymentBean();
		paymentBean.setMonthlyPaymentsEligible(true);
		
		paymentAdapter.loadPaymentBean(paymentBean, newPolicyVersion());
		
		assertTrue(paymentBean.isPaymentCC());
		assertTrue(paymentBean.isMonthlyPaymentsEligible());
		assertTrue(100.0 == paymentBean.getAnnualAmountWithTaxes());
		assertTrue(MONTHLY_PAYMENT_TAXES_AND_SURCHARGE == paymentBean.getMonthlyPremium());
		assertTrue(ANNUAL_AMOUNT_TAXES == paymentBean.getAnnualAmountTaxes());
		assertTrue(FIRST_MONTHLY_PAYMENT_TAXES_AND_SURCHARGE == paymentBean.getMonthlyFirstPayment());
		assertTrue(paymentBean.isMonthlyPaymentsEligible());
		assertFalse(paymentBean.isAnyPaymentsEligible());	
		assertTrue(paymentBean.isInsuranceHomeAndAutoInd());
		assertEquals(CARD_TOKEN, paymentBean.getPaymentMonthlyCC().getCardMaskedNumber());
		assertEquals("12", paymentBean.getPaymentMonthlyCC().getCardExpiryMonth());
		assertEquals("25", paymentBean.getPaymentMonthlyCC().getCardExpiryYear());
		assertEquals(CREDIT_CARD_HOLDER_NAME, paymentBean.getPaymentMonthlyCC().getCardHolderName());
		
	}

}
