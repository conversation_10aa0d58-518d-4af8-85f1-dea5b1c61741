package com.intact.brokeroffice.controller.viewquote;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Date;
import java.util.Enumeration;
import java.util.HashSet;
import java.util.List;
import java.util.Locale;
import java.util.ResourceBundle;
import java.util.Set;

import jakarta.faces.component.UIViewRoot;
import jakarta.faces.context.FacesContext;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.domain.impl.SubBrokers;
import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.common.services.api.policy.IPolicyVersionInfoService;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransaction;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransactionActivity;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransactionSubActivity;
import com.ing.canada.plp.domain.enums.AgreementFollowUpStatusEnum;
import com.ing.canada.plp.domain.enums.BusinessTransactionSubActivityCodeEnum;
import com.ing.canada.plp.domain.enums.ExternalSystemOriginCodeEnum;
import com.ing.canada.plp.domain.enums.LanguageCodeEnum;
import com.ing.canada.plp.domain.enums.PhoneTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.enums.TransactionStatusCodeEnum;
import com.ing.canada.plp.domain.enums.UserActivityTypeCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicyNote;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.party.Phone;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.subbrokerassignment.SubBrokerAssignment;
import com.ing.canada.plp.domain.usertype.AuditTrail;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.helper.enums.PaymentPlanEnum;
import com.ing.canada.plp.helper.impl.PartyHelper;
import com.ing.canada.plp.report.enums.QuoteStatusCodeEnum;
import com.ing.canada.plp.service.IBusinessTransactionSubActivityService;
import com.ing.canada.plp.service.IInsurancePolicyService;
import com.ing.canada.plp.service.IPolicyHolderService;
import com.intact.brokeroffice.service.IBrokerService;

class ViewQuoteAdapterTest {

	private MockedStatic<FacesContext> mockedFacesContext;

	private MockedStatic<ResourceBundle> mockedResourceBundle;

	private ViewQuoteAdapter viewQuoteAdapter = new ViewQuoteAdapter();

	private IPartyHelper partyHelper;

	private IInsurancePolicyService insurancePolicyService;
	
	private IBrokerService quoteBusinessProcess;

	private ISubBrokersService subBrokersService;

	private IPolicyVersionHelper policyVersionHelper;

	private IBusinessTransactionSubActivityService businessTransactionSubActivityService;

	private IPolicyHolderService policyHolderService;

	private IPolicyVersionInfoService policyVersionInfoService;

	private ResourceBundle resourceBundle;

	private FacesContext context;

	private UIViewRoot viewRoot;

	@BeforeEach
	void setUp() throws Exception {
		mockedFacesContext = Mockito.mockStatic(FacesContext.class);
		mockedResourceBundle = Mockito.mockStatic(ResourceBundle.class);
		partyHelper = mock(PartyHelper.class);
		insurancePolicyService = mock(IInsurancePolicyService.class);
		quoteBusinessProcess = mock(IBrokerService.class);
		subBrokersService = mock(ISubBrokersService.class);
		policyVersionHelper = mock(IPolicyVersionHelper.class);
		businessTransactionSubActivityService = mock(IBusinessTransactionSubActivityService.class);
		policyHolderService = mock(IPolicyHolderService.class);
		policyVersionInfoService = mock(IPolicyVersionInfoService.class);

		ReflectionTestUtils.setField(viewQuoteAdapter, "partyHelper", partyHelper);
		ReflectionTestUtils.setField(viewQuoteAdapter, "insurancePolicyService", insurancePolicyService);
		ReflectionTestUtils.setField(viewQuoteAdapter, "quoteBusinessProcess", quoteBusinessProcess);
		ReflectionTestUtils.setField(viewQuoteAdapter, "subBrokersService", subBrokersService);
		ReflectionTestUtils.setField(viewQuoteAdapter, "policyVersionHelper", policyVersionHelper);
		ReflectionTestUtils.setField(viewQuoteAdapter, "businessTransactionSubActivityService", businessTransactionSubActivityService);
		ReflectionTestUtils.setField(viewQuoteAdapter, "policyHolderService", policyHolderService);
		ReflectionTestUtils.setField(viewQuoteAdapter, "policyVersionInfoService", policyVersionInfoService);

		resourceBundle = new ResourceBundle() {

			@Override
			protected Object handleGetObject(String key) {
				return "mock resource value";
			}

			@Override
			public Enumeration<String> getKeys() {
				return null;
			}
		};
		context = mock(FacesContext.class);
		viewRoot = mock(UIViewRoot.class);
		mockedFacesContext.when(FacesContext::getCurrentInstance).thenReturn(context);
		when(context.getViewRoot()).thenReturn(viewRoot);
		when(viewRoot.getLocale()).thenReturn(Locale.CANADA_FRENCH);
		mockedResourceBundle.when(() -> ResourceBundle.getBundle(anyString(), (Locale) any(), (ClassLoader) any())).thenReturn(
				resourceBundle);
	}

	@AfterEach
	void tearDown() throws Exception {
		partyHelper = null;
		insurancePolicyService = null;
		quoteBusinessProcess = null;
		subBrokersService = null;
		policyVersionHelper = null;
		businessTransactionSubActivityService = null;
		policyHolderService = null;
		policyVersionInfoService = null;
	}

	@AfterEach
	void tearDownStaticMocks() {
		mockedResourceBundle.closeOnDemand();
		mockedFacesContext.closeOnDemand();
	}

	@Test
	void loadClientContactBean() {
		ClientContactBean resultClientContactBean = new ClientContactBean();

		ISubBrokers subBroker = new SubBrokers();
		subBroker.setNameLine1("NameLine1");
		subBroker.setNameLine2("NameLine2");
		when(subBrokersService.getSubBrokerById(anyLong())).thenReturn(subBroker);

		PolicyHolder aPolicyHolder = new PolicyHolder();
		Party aParty = new Party();
		aParty.setEmailAddress("<EMAIL>");
		aParty.setFirstName("Olivier");
		aParty.setLastName("Tremblay");
		aPolicyHolder.setParty(aParty);
		when(policyVersionHelper.getPrincipalInsuredPolicyHolder((PolicyVersion) any()))
				.thenReturn(aPolicyHolder);

		Phone aPhone = new Phone();
		aPhone.setPhoneAreaCode("514");
		aPhone.setPhoneNumber("4444444");
		aPhone.setPhoneExtension("83919");
		when(partyHelper.getCurrentPhone((Party) any(), (PhoneTypeCodeEnum) any())).thenReturn(aPhone);

		BusinessTransactionSubActivity aBusinessTransactionSubActivity = new BusinessTransactionSubActivity();
		aBusinessTransactionSubActivity.setSubActivityCode(BusinessTransactionSubActivityCodeEnum.ROADBLOCK);
		when(businessTransactionSubActivityService.findLastSubActivity(anyLong())).thenReturn(
				aBusinessTransactionSubActivity);

		when(policyHolderService.findPrincipalPolicyHolder((PolicyVersion) any())).thenReturn(aPolicyHolder);

		List<InsurancePolicy> insuranceList = new ArrayList<InsurancePolicy>();
		//insuranceList.add(newInsurancePolicy("agreeNumber", "legaNumber")); 
		//ViewQuoteAdapter/generateUploadedQuoteMessage throw missing ressource for: client.message.uploadQuote
		insuranceList.add(newInsurancePolicy("AgreementNumber", "lNumber"));
		insuranceList.add(newInsurancePolicy("AgreementNumber", "lNumber"));

		when(insurancePolicyService.findAllUnexpiredInsurancePolicyForEmail(anyString())).thenReturn(insuranceList);
		
		Set<PaymentPlanEnum> setPaymentPlanEnum = new HashSet<PaymentPlanEnum>();
		setPaymentPlanEnum.add(PaymentPlanEnum.PLAN_A);
		//When setPaymentPlanEnum not contain PLAN_A, PLAN_E
		//ViewQuoteAdapter/generateEligibleWithdrawalsMessage throw missing ressource for: client.message.eligible.withdrawals
		when(policyVersionInfoService.getPaymentPlans((PolicyVersion) any())).thenReturn(setPaymentPlanEnum);
		
		PolicyVersion aPolicyVersion = newPolicyVersion();

		viewQuoteAdapter.loadClientContactBean(resultClientContactBean, aPolicyVersion, true);
		
		assertEquals(1, resultClientContactBean.getNotes().size());
		assertEquals(AgreementFollowUpStatusEnum.NOT_CONTACTED_NO_FOLLOWUP_REQUIRED,resultClientContactBean.getFollowupStatus());
		assertEquals("Olivier Tremblay", resultClientContactBean.getPolicyHolder());
		assertEquals(QuoteStatusCodeEnum.UPLOADED, resultClientContactBean.getStatus());
		assertEquals("(514)444-4444 ext 83919", resultClientContactBean.getPhoneCell());
		assertEquals("NameLine1NameLine2", resultClientContactBean.getBrokerAdvisor());
		assertEquals("WEBBK", resultClientContactBean.getQuoteSource());
	}

	private InsurancePolicy newInsurancePolicy(String agreementNumber, String agreementLegacyNumber) {
		InsurancePolicy insurancePolicy = new InsurancePolicy();
		insurancePolicy.setAgreementLegacyNumber(agreementLegacyNumber);
		insurancePolicy.setAgreementNumber(agreementNumber);
		return insurancePolicy;
	}

	private PolicyVersion newPolicyVersion() {
		PolicyVersion policyVersion = new PolicyVersion();
		InsurancePolicy aInsurancePolicy = new InsurancePolicy();
		InsurancePolicyNote insurancePolicyNote = new InsurancePolicyNote();
		insurancePolicyNote.setAuthorUID("AuthorUID");
		insurancePolicyNote.setUserActivityType(UserActivityTypeCodeEnum.VIEW_QUOTE);
		insurancePolicyNote.setInsurancePolicyNote(null);
		AuditTrail trail = new AuditTrail();
		trail.setCreateTimestamp(new Date());
		insurancePolicyNote.setAuditTrail(trail);
		aInsurancePolicy.addInsurancePolicyNote(insurancePolicyNote);

		aInsurancePolicy.setAgreementFollowUpStatus(AgreementFollowUpStatusEnum.NOT_CONTACTED_NO_FOLLOWUP_REQUIRED);
		aInsurancePolicy.setQuotationValidityExpiryDate(new Date());
		aInsurancePolicy.setAgreementLegacyNumber("AgreementLegacyNumber");
		
		ManufacturingContext aManufacturingContext = new ManufacturingContext();
		aManufacturingContext.setProvince(ProvinceCodeEnum.QUEBEC);
		aInsurancePolicy.setManufacturingContext(aManufacturingContext);

		SubBrokerAssignment aSubBrokerAssignment = new SubBrokerAssignment();
		aSubBrokerAssignment.setCifSubBrokerId(1l);
		aSubBrokerAssignment.setAuditTrail(trail);
		aInsurancePolicy.setAgreementNumber("AgreementNumber");
		aInsurancePolicy.addSubBrokerAssignment(aSubBrokerAssignment);

		aInsurancePolicy.setAuditTrail(trail);
		aInsurancePolicy.setExternalSystemOrigin(ExternalSystemOriginCodeEnum.WEB_BROKER);

		BusinessTransaction businessTransaction = new BusinessTransaction();
		BusinessTransactionActivity lastBusinessTransactionActivity = new BusinessTransactionActivity();
		lastBusinessTransactionActivity.setAuditTrail(trail);
		businessTransaction.addBusinessTransactionActivity(lastBusinessTransactionActivity);
		businessTransaction.setTransactionStatus(TransactionStatusCodeEnum.SUSPENDED);
		policyVersion.setBusinessTransaction(businessTransaction);
		policyVersion.setInsurancePolicy(aInsurancePolicy);
		policyVersion.setLanguageOfCommunication(LanguageCodeEnum.FRENCH);
		return policyVersion;
	}
	
}



















