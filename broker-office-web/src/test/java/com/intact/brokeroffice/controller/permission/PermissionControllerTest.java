package com.intact.brokeroffice.controller.permission;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.HashMap;
import java.util.Map;

import jakarta.faces.component.UIViewRoot;
import jakarta.faces.context.ExternalContext;
import jakarta.faces.context.FacesContext;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.cif.domain.IBrokersInfos;
import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.cif.domain.impl.BrokersInfos;
import com.ing.canada.cif.service.IBrokerService;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.subbrokerassignment.SubBrokerAssignment;
import com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess;
import com.intact.brokeroffice.business.common.ICommandBusinessProcess;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.brokeroffice.controller.authentification.AuthentificationController;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.canada.brm.domain.broker.BrokerWebOfficeAccess;
import com.intact.canada.brm.domain.user.UserAccount;

class PermissionControllerTest {

	private MockedStatic<FacesContext> mockedFacesContext;

	PermissionController permissionController = new PermissionController();

	private AuthentificationController authentificationController;

	private IAccountsBusinessProcess accountsBusinessProcess;

	private ICommandBusinessProcess commandBusinessProcess;

	private ProvinceController provinceController;

	private IBrokerService brokerService;

	private String ldapGroupAdmins = "ldapGroupAdmins";

	private String ldapGroupProgramAdmins = "ldapGroupProgramAdmins";

	private String ldapGroupQuoteAdmins = "ldapGroupQuoteAdmins";

	private String ldapGroupUnderwritter = "ldapGroupUnderwritter";

	private String ldapGroupBroker = "ldapGroupBroker";

	private String ldapGroupBrokerReassign = "ldapGroupBrokerReassign";

	@BeforeEach
	void setUp() throws Exception {
		authentificationController = mock(AuthentificationController.class);
		accountsBusinessProcess = mock(IAccountsBusinessProcess.class);
		commandBusinessProcess = mock(ICommandBusinessProcess.class);
		provinceController = mock(ProvinceController.class);
		brokerService = mock(IBrokerService.class);

		ReflectionTestUtils.setField(permissionController, "authentificationController", authentificationController);
		ReflectionTestUtils.setField(permissionController, "accountsBusinessProcess", accountsBusinessProcess);
		ReflectionTestUtils.setField(permissionController, "commandBusinessProcess", commandBusinessProcess);
		ReflectionTestUtils.setField(permissionController, "provinceController", provinceController);
		ReflectionTestUtils.setField(permissionController, "brokerService", brokerService);

		ReflectionTestUtils.setField(permissionController, "ldapGroupAdmins", ldapGroupAdmins);
		ReflectionTestUtils.setField(permissionController, "ldapGroupProgramAdmins", ldapGroupProgramAdmins);
		ReflectionTestUtils.setField(permissionController, "ldapGroupQuoteAdmins", ldapGroupQuoteAdmins);
		ReflectionTestUtils.setField(permissionController, "ldapGroupUnderwritter", ldapGroupUnderwritter);
		ReflectionTestUtils.setField(permissionController, "ldapGroupBroker", ldapGroupBroker);
		ReflectionTestUtils.setField(permissionController, "ldapGroupBrokerReassign", ldapGroupBrokerReassign);
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@BeforeEach
	void setUpStaticMocks() {
		mockedFacesContext = Mockito.mockStatic(FacesContext.class);
	}

	@AfterEach
	void tearDownStaticMocks() {
		mockedFacesContext.closeOnDemand();
	}

	@Test
	void abbreviateGroup() {
		when(authentificationController.getCurrentAccessLevel()).thenReturn("ldapGroupQuoteAdmins");
		assertEquals(" (INTACT)", permissionController.getAbbreviatedGroup());

		when(authentificationController.getCurrentAccessLevel()).thenReturn("");
		assertEquals("", permissionController.getAbbreviatedGroup());
	}

	@Test
	void checkAddFollowNotes() {
		when(authentificationController.getCurrentAccessLevel()).thenReturn("ldapGroupBrokerReassign - test");
		assertTrue(permissionController.getCheckAddFollowupNotes());

		when(authentificationController.getCurrentAccessLevel()).thenReturn(" - test");
		assertFalse(permissionController.getCheckAddFollowupNotes());

		when(authentificationController.getCurrentAccessLevel()).thenReturn(null);
		assertFalse(permissionController.getCheckAddFollowupNotes());
	}

	@Test
	void checkUpload() {
		when(authentificationController.getCurrentAccessLevel()).thenReturn("ldapGroupBroker - test");
		assertTrue(permissionController.getCheckUpload());

		when(authentificationController.getCurrentAccessLevel()).thenReturn(" - test");
		assertFalse(permissionController.getCheckUpload());

		when(authentificationController.getCurrentAccessLevel()).thenReturn(null);
		assertFalse(permissionController.getCheckUpload());
	}

	@Test
	void checkReassign() {
		when(authentificationController.getCurrentAccessLevel()).thenReturn("ldapGroupBrokerReassign - test");
		assertTrue(permissionController.getCheckReassign());

		when(authentificationController.getCurrentAccessLevel()).thenReturn(" - test");
		assertFalse(permissionController.getCheckReassign());

		when(authentificationController.getCurrentAccessLevel()).thenReturn(null);
		assertFalse(permissionController.getCheckReassign());
	}

	@Test
	void checkAccountManagement() {
		when(authentificationController.getCurrentAccessLevel()).thenReturn("ldapGroupAdmins - test");
		assertTrue(permissionController.getCheckAccountManagement());

		when(authentificationController.getCurrentAccessLevel()).thenReturn(" - test");
		assertFalse(permissionController.getCheckAccountManagement());

		when(authentificationController.getCurrentAccessLevel()).thenReturn(null);
		assertFalse(permissionController.getCheckAccountManagement());
	}

	@Test
	void checkManageAccountAccess() {
		when(authentificationController.getCurrentAccessLevel()).thenReturn("ldapGroupAdmins - test");
		assertTrue(permissionController.getCheckManageAccountAccess());

		when(authentificationController.getCurrentAccessLevel()).thenReturn(null);
		assertFalse(permissionController.getCheckManageAccountAccess());
	}

	@Test
	void checkManageSubBrokerProfile() {
		when(authentificationController.getCurrentAccessLevel()).thenReturn("ldapGroupAdmins - test");
		assertTrue(permissionController.getCheckManageSubBrokerProfile());

		when(authentificationController.getCurrentAccessLevel()).thenReturn(" - test");
		assertFalse(permissionController.getCheckManageSubBrokerProfile());

		when(authentificationController.getCurrentAccessLevel()).thenReturn(null);
		assertFalse(permissionController.getCheckManageSubBrokerProfile());
	}

	@Test
	void checkManageFsa() {
		when(authentificationController.getCurrentAccessLevel()).thenReturn("ldapGroupAdmins - test");
		assertTrue(permissionController.getCheckManageFsa());

		when(authentificationController.getCurrentAccessLevel()).thenReturn(null);
		assertFalse(permissionController.getCheckManageFsa());
	}

	@Test
	void checkReports() {
		when(authentificationController.getCurrentAccessLevel()).thenReturn("ldapGroupAdmins - test");
		assertTrue(permissionController.getCheckReports());

		when(authentificationController.getCurrentAccessLevel()).thenReturn(null);
		assertFalse(permissionController.getCheckReports());
	}

	@Test
	void checkViewChangeReport() {
		when(authentificationController.getCurrentAccessLevel()).thenReturn("ldapGroupAdmins - test");
		assertTrue(permissionController.getCheckViewChangeReport());

		when(authentificationController.getCurrentAccessLevel()).thenReturn(null);
		assertFalse(permissionController.getCheckViewChangeReport());
	}

	@Test
	void checkViewKpiReport() {
		when(authentificationController.getCurrentAccessLevel()).thenReturn("ldapGroupAdmins - test");
		assertTrue(permissionController.getCheckViewKpiReport());

		when(authentificationController.getCurrentAccessLevel()).thenReturn(null);
		assertFalse(permissionController.getCheckViewKpiReport());
	}

	@Test
	void checkQuoteAccess() throws BrokerServiceException {
		FacesContext context = mock(FacesContext.class);
		mockedFacesContext.when(FacesContext::getCurrentInstance).thenReturn(context);
		ExternalContext externalContext = mock(ExternalContext.class);
		when(context.getExternalContext()).thenReturn(externalContext);
		UIViewRoot viewRoot = mock(UIViewRoot.class);
		when(context.getViewRoot()).thenReturn(viewRoot);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("referenceNo", "referenceNoValue");
		when(viewRoot.getAttributes()).thenReturn(map);
		PolicyVersion policyVersion = mock(PolicyVersion.class);
		when(commandBusinessProcess.findLatestPolicyVersion("referenceNoValue")).thenReturn(policyVersion);
		InsurancePolicy insurancePolicy = mock(InsurancePolicy.class);
		when(policyVersion.getInsurancePolicy()).thenReturn(insurancePolicy);
		SubBrokerAssignment subBrokerAssignment = mock(SubBrokerAssignment.class);
		when(insurancePolicy.getLatestSubBrokerAssignment()).thenReturn(subBrokerAssignment);
		when(subBrokerAssignment.getCifSubBrokerId()).thenReturn(1l);
		when(insurancePolicy.getApplicationMode()).thenReturn(ApplicationModeEnum.REGULAR_QUOTE);
		when(insurancePolicy.getManufacturerCompany()).thenReturn(ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION);
		when(insurancePolicy.getLineOfBusiness()).thenReturn(LineOfBusinessCodeEnum.PERSONAL_LINES);
		when(provinceController.getCompanyEnumCode()).thenReturn(CifCompanyEnum.BELAIR);
		IBrokersInfos brokerInfos = new BrokersInfos();
		brokerInfos.setMasterOwnerNo("MasterOwnerNo");
		when(brokerService.getBrokersInfosBySubBrokerId(anyLong(), anyString())).thenReturn(brokerInfos);
		when(authentificationController.getCurrentAccountUId()).thenReturn("Olivier");

		BrokerWebOfficeAccess aBrokerWebOfficeAccess = new BrokerWebOfficeAccess();
		aBrokerWebOfficeAccess.setMasterOwnerCode("MasterOwnerNo");
		UserAccount userAccount = new UserAccount();
		userAccount.addBrokerWebOfficeAccess(aBrokerWebOfficeAccess);
		when(accountsBusinessProcess.findByUId(anyString())).thenReturn(userAccount);
		
		assertTrue(permissionController.getCheckQuoteAccess());
	}

	@Test
	void checkQuoteAccessFalse() throws BrokerServiceException {
		FacesContext context = mock(FacesContext.class);
		mockedFacesContext.when(FacesContext::getCurrentInstance).thenReturn(context);
		ExternalContext externalContext = mock(ExternalContext.class);
		when(context.getExternalContext()).thenReturn(externalContext);
		UIViewRoot viewRoot = mock(UIViewRoot.class);
		when(context.getViewRoot()).thenReturn(viewRoot);
		Map<String, Object> map = new HashMap<String, Object>();
		map.put("referenceNo", "referenceNoValue");
		when(viewRoot.getAttributes()).thenReturn(map);
		PolicyVersion policyVersion = mock(PolicyVersion.class);
		when(commandBusinessProcess.findLatestPolicyVersion("referenceNoValue")).thenReturn(policyVersion);
		InsurancePolicy insurancePolicy = mock(InsurancePolicy.class);
		when(policyVersion.getInsurancePolicy()).thenReturn(insurancePolicy);
		SubBrokerAssignment subBrokerAssignment = mock(SubBrokerAssignment.class);
		when(insurancePolicy.getLatestSubBrokerAssignment()).thenReturn(subBrokerAssignment);
		when(subBrokerAssignment.getCifSubBrokerId()).thenReturn(1l);
		when(insurancePolicy.getApplicationMode()).thenReturn(ApplicationModeEnum.REGULAR_QUOTE);
		when(insurancePolicy.getManufacturerCompany()).thenReturn(ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION);
		when(insurancePolicy.getLineOfBusiness()).thenReturn(LineOfBusinessCodeEnum.PERSONAL_LINES);
		when(provinceController.getCompanyEnumCode()).thenReturn(CifCompanyEnum.BELAIR);
		IBrokersInfos brokerInfos = new BrokersInfos();
		brokerInfos.setMasterOwnerNo("MasterOwnerNo");
		when(brokerService.getBrokersInfosBySubBrokerId(anyLong(), anyString())).thenReturn(brokerInfos);
		when(authentificationController.getCurrentAccountUId()).thenReturn("Olivier");

		BrokerWebOfficeAccess aBrokerWebOfficeAccess = new BrokerWebOfficeAccess();
		aBrokerWebOfficeAccess.setMasterOwnerCode("");
		UserAccount userAccount = new UserAccount();
		userAccount.addBrokerWebOfficeAccess(aBrokerWebOfficeAccess);
		when(accountsBusinessProcess.findByUId(anyString())).thenReturn(userAccount);
		
		assertFalse(permissionController.getCheckQuoteAccess());
	}
}








