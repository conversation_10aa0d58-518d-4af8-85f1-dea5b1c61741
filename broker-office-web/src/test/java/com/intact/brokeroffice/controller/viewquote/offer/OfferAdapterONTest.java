package com.intact.brokeroffice.controller.viewquote.offer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Locale;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.common.domain.QuoteCalculationDetails;
import com.ing.canada.common.services.api.rating.IQuotationService;
import com.ing.canada.plp.domain.billing.Billing;
import com.ing.canada.plp.domain.coverage.CoverageRepositoryEntry;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.BillingPlanCodeEnum;
import com.ing.canada.plp.domain.enums.CombinedPolicyCodeEnum;
import com.ing.canada.plp.domain.enums.CoverageTypeCodeEnum;
import com.ing.canada.plp.domain.enums.EndorsementCodeEnum;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.vehicle.Vehicle;
import com.ing.canada.plp.domain.vehicle.VehicleDetailSpecificationRepositoryEntry;
import com.ing.canada.plp.domain.vehicle.VehicleRepositoryEntry;
import com.ing.canada.plp.helper.IInsuranceRiskOfferHelper;
import com.intact.business.rules.quote.BR5883_HasThreePercentageSurcharge;
import com.intact.business.rules.quote.BR5883_HasTwoPercentageSurcharge;

@ExtendWith(MockitoExtension.class)
class OfferAdapterONTest {

	@InjectMocks
	private OfferAdapterON offerAdapterON;

	private IQuotationService mockQuotationService = mock(IQuotationService.class);

	private BR5883_HasThreePercentageSurcharge mockBr5883_3perc = mock(BR5883_HasThreePercentageSurcharge.class);

	private BR5883_HasTwoPercentageSurcharge mockBr5883_2perc = mock(BR5883_HasTwoPercentageSurcharge.class);

	protected IInsuranceRiskOfferHelper mockInsuranceRiskOfferHelper = mock(IInsuranceRiskOfferHelper.class);
	
	private Date ontarioInterestChangeDate = new Date();

	@BeforeEach
	void setUp() throws Exception {
		MockitoAnnotations.openMocks(this);
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@Test
	void loadVehicleOfferBeanList() {
		InsuranceRisk insuranceRisk = new InsuranceRisk();
		insuranceRisk.setInsuranceRiskSequence(Short.valueOf("1"));
		insuranceRisk.setInsuranceRiskOfferSystemSelectedIndicator(false);
		InsuranceRiskOffer insuranceRiskOffer = new InsuranceRiskOffer();
		insuranceRiskOffer.setAnnualPremium(1000);
		CoverageOffer coverageOffer = new CoverageOffer();
		CoverageRepositoryEntry coverageRepositoryEntry = new CoverageRepositoryEntry();
		coverageRepositoryEntry.setCoverageType(CoverageTypeCodeEnum.ENDORSEMENT);
		coverageRepositoryEntry.setCoverageCode("PPA");
		coverageOffer.setCoverageRepositoryEntry(coverageRepositoryEntry);
		insuranceRiskOffer.addCoverageOffer(coverageOffer);
		insuranceRisk.addInsuranceRiskOffer(insuranceRiskOffer);
		insuranceRisk.setSelectedInsuranceRiskOffer(insuranceRiskOffer);

		VehicleRepositoryEntry vehicleRepositoryEntry = new VehicleRepositoryEntry();
		vehicleRepositoryEntry.setVehicleMakeFrench("Hyundai");
		vehicleRepositoryEntry.setVehicleModelFrench("Accent");
		VehicleDetailSpecificationRepositoryEntry vehicleDetailSpecificationRepositoryEntry = new VehicleDetailSpecificationRepositoryEntry();
		vehicleDetailSpecificationRepositoryEntry.setVehicleYear(2013);
		vehicleDetailSpecificationRepositoryEntry.setVehicleRepositoryEntry(vehicleRepositoryEntry);
		Vehicle vehicle = new Vehicle();
		vehicle.setVehicleDetailSpecificationRepositoryEntry(vehicleDetailSpecificationRepositoryEntry);
		insuranceRisk.setVehicle(vehicle);

		PolicyVersion policyVersion = new PolicyVersion();
		policyVersion.addInsuranceRisk(insuranceRisk);

		com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy insPolicy = new com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy();
		insPolicy.setApplicationMode(ApplicationModeEnum.REGULAR_QUOTE);
		policyVersion.setInsurancePolicy(insPolicy);
		
		ReflectionTestUtils.setField(offerAdapterON, "insuranceRiskOfferHelper", mockInsuranceRiskOfferHelper);

		List<CoverageOffer> listCoverage = new LinkedList<CoverageOffer>();
		
		CoverageOffer coverageOffer2 = new CoverageOffer();
		
		CoverageRepositoryEntry coverageRepositoryEntry2 = new CoverageRepositoryEntry();
		coverageRepositoryEntry2.setCoverageType(CoverageTypeCodeEnum.ENDORSEMENT);
		coverageRepositoryEntry2.setCoverageCode("PPC");
		coverageOffer2.setCoverageRepositoryEntry(coverageRepositoryEntry2);
		
		coverageOffer2.setCoverageRepositoryEntry(coverageRepositoryEntry2);
		listCoverage.add(coverageOffer2);
		
		List<VehicleOfferBeanON> listVehicule = new ArrayList<VehicleOfferBeanON>();
		offerAdapterON.loadVehicleOfferBeanList(listVehicule, policyVersion, Locale.FRENCH,listCoverage);

		VehicleOfferBeanON resultVehicule = listVehicule.get(0);

		assertEquals(1, listVehicule.size());
		assertTrue(insuranceRisk.getInsuranceRiskSequence() == resultVehicule.getSequence());
		assertEquals("2013", resultVehicule.getYear());
		assertEquals("Hyundai", resultVehicule.getMake());
		assertEquals("Accent", resultVehicule.getModel());
		assertEquals(insuranceRisk.getSelectedInsuranceRiskOffer().getOfferType(),
				resultVehicule.getSelectedOfferType());
	}

	@Test
	void resetEndorsementRank(){
		CoverageOfferBean coverageOfferBean = new CoverageOfferBean();
		offerAdapterON.resetEndorsementRank("20", coverageOfferBean);
		assertEquals(1, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterON.resetEndorsementRank("PPC", coverageOfferBean);
		assertEquals(2, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterON.resetEndorsementRank("EPCE", coverageOfferBean);
		assertEquals(2, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterON.resetEndorsementRank("PPD", coverageOfferBean);
		assertEquals(2, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterON.resetEndorsementRank("RDP", coverageOfferBean);
		assertEquals(3, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterON.resetEndorsementRank("ACHV", coverageOfferBean);
		assertEquals(4, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterON.resetEndorsementRank("UE05", coverageOfferBean);
		assertEquals(5, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterON.resetEndorsementRank("49Y", coverageOfferBean);
		assertEquals(6, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterON.resetEndorsementRank("50Y", coverageOfferBean);
		assertEquals(6, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterON.resetEndorsementRank("49F", coverageOfferBean);
		assertEquals(6, coverageOfferBean.getRank().intValue());
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterON.resetEndorsementRank("50F", coverageOfferBean);
		assertEquals(6, coverageOfferBean.getRank().intValue());	
		coverageOfferBean = new CoverageOfferBean();
		offerAdapterON.resetEndorsementRank("", coverageOfferBean);
		assertNull(coverageOfferBean.getRank());	
	}

	@Test
	void obtainCoverageCodePPDSAV(){
		CoverageOffer coverageOffer = new CoverageOffer();
		CoverageRepositoryEntry coverageRepositoryEntry = new CoverageRepositoryEntry();
		coverageRepositoryEntry.setCoverageCode(EndorsementCodeEnum.PPD_SAV.getCode());
		coverageOffer.setCoverageRepositoryEntry(coverageRepositoryEntry);
		assertEquals(EndorsementCodeEnum.PPC_SAV.getCode(), offerAdapterON.obtainCoverageCode(coverageOffer));		
	}

	@Test
	void obtainCoverageCodeEPCESAV(){
		CoverageOffer coverageOffer = new CoverageOffer();
		CoverageRepositoryEntry coverageRepositoryEntry = new CoverageRepositoryEntry();
		coverageRepositoryEntry.setCoverageCode(EndorsementCodeEnum.EPCE_SAV.getCode());
		coverageOffer.setCoverageRepositoryEntry(coverageRepositoryEntry);
		assertEquals(EndorsementCodeEnum.PPC_SAV.getCode(), offerAdapterON.obtainCoverageCode(coverageOffer));		
	}

	@Test
	void obtainCoverageCode50FSAV(){
		CoverageOffer coverageOffer = new CoverageOffer();
		CoverageRepositoryEntry coverageRepositoryEntry = new CoverageRepositoryEntry();
		coverageRepositoryEntry.setCoverageCode(EndorsementCodeEnum._50F_SAV.getCode());
		coverageOffer.setCoverageRepositoryEntry(coverageRepositoryEntry);
		assertEquals(EndorsementCodeEnum._49Y_SAV.getCode(), offerAdapterON.obtainCoverageCode(coverageOffer));		
	}

	@Test
	void mappingEdorsementKeyEPCESAV(){
		assertEquals(EndorsementCodeEnum.PPD_SAV.getCode()
				, offerAdapterON.mappingEndorsementKey(EndorsementCodeEnum.EPCE_SAV.getCode()));
	}

	@Test
	void mappingEdorsementKey50FSAV(){
		assertEquals(EndorsementCodeEnum._49Y_SAV.getCode()
				, offerAdapterON.mappingEndorsementKey(EndorsementCodeEnum._50F_SAV.getCode()));
	}


	@Test
	void loadCoverageInformation() {
		CoverageOffer coverageOffer = new CoverageOffer();
		coverageOffer.setCoverageSelectedIndicator(true);
		CoverageRepositoryEntry coverageRepositoryEntry = new CoverageRepositoryEntry();
		coverageRepositoryEntry.setCoverageCode(EndorsementCodeEnum.PPD_SAV.getCode());
		coverageOffer.setCoverageRepositoryEntry(coverageRepositoryEntry);

		CoverageOfferBean coverageOfferBean = new CoverageOfferBean();
		coverageOfferBean.setCoverageSelectedIndicator(null);

		offerAdapterON.loadCoverageInformation(coverageOfferBean, coverageOffer);

		assertTrue(coverageOfferBean.getCoverageSelectedIndicator());
		assertEquals(EndorsementCodeEnum.PPD_SAV.getCode(), coverageOfferBean.getCoverageCode());
	}

	@Disabled
	@Test
	void loadOfferBean() {
		ProvinceCodeEnum province = ProvinceCodeEnum.ALBERTA;
		InsuranceRiskOffer insuranceRiskOffer = new InsuranceRiskOffer();
		InsuranceRisk insuranceRisk = new InsuranceRisk();
		PolicyVersion policyVersion = new PolicyVersion();
		policyVersion.setCombinedPolicyCode(CombinedPolicyCodeEnum.COMBO_POLICY);
		Billing billing = new Billing();
		billing.setBillingPlan(BillingPlanCodeEnum.PRE_AUTHORIZED_CHEQUE_PLAN);
		policyVersion.setBilling(billing);
		policyVersion.setPolicyInceptionDate(new Date());
		insuranceRisk.setPolicyVersion(policyVersion);
		insuranceRiskOffer.setInsuranceRisk(insuranceRisk);

		OfferBean anOfferBean = new OfferBean(OfferTypeCodeEnum.CUSTOM);

		ReflectionTestUtils.setField(offerAdapterON, "br5883_3perc", mockBr5883_3perc);
		ReflectionTestUtils.setField(offerAdapterON, "br5883_2perc", mockBr5883_2perc);
		ReflectionTestUtils.setField(offerAdapterON, "quotationService", mockQuotationService);
		ReflectionTestUtils.setField(offerAdapterON, "ontarioInterestChangeDate", ontarioInterestChangeDate);

		when(mockBr5883_3perc.validate(policyVersion)).thenReturn(true);
		when(mockBr5883_2perc.validate(policyVersion)).thenReturn(false);

		QuoteCalculationDetails quotationCalculationDetail = new QuoteCalculationDetails();
		quotationCalculationDetail.setAnnualAmountWithTaxes(1000.50);
		quotationCalculationDetail.setMonthlyPaymentWithTaxes(95.67);

		when(mockQuotationService.loadOfferBean(anyDouble(), any(InsuranceRiskOffer.class), any(ProvinceCodeEnum.class), anyDouble(), anyBoolean())).thenReturn(quotationCalculationDetail);

		offerAdapterON.loadOfferBean(anOfferBean, insuranceRiskOffer, province);

		assertTrue(1000.50 == anOfferBean.getAnnualPremiumWithTaxes());
		assertTrue(95.67 == anOfferBean.getMonthlyPremiumWithTaxes());
	}

}


















