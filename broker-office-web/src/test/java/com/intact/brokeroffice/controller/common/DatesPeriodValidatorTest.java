package com.intact.brokeroffice.controller.common;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;

import java.text.ParseException;
import java.util.Date;

import org.joda.time.LocalDate;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

class DatesPeriodValidatorTest {
	
	private DatesPeriodValidator dateValidator  = new DatesPeriodValidator();

	@BeforeEach
	void setUp() throws Exception {
		dateValidator.setShowErrorMessage(false);
	}

	@AfterEach
	void tearDown() throws Exception {
	}

	@Test
	void validateNull() {	
		assertFalse(dateValidator.validate(null, null, null));
	}

	/**
	 * from after today
	 * @throws ParseException
	 */
	@Test
	void validateFromAfterToday() throws ParseException {	
		LocalDate from = new LocalDate();
		Date dateFrom = from.plusDays(7).toDate();
		
		LocalDate to = new LocalDate();
		Date dateTo = to.toDate();
		
		assertFalse(dateValidator.validate(dateFrom, dateTo, null));
	}

	/**
	 * to after today
	 * @throws ParseException
	 */
	@Test
	void validateToAfterToday() throws ParseException {	
		LocalDate from = new LocalDate();
		Date dateFrom = from.toDate();
		
		LocalDate to = new LocalDate();
		Date dateTo = to.plusDays(7).toDate();
		
		assertFalse(dateValidator.validate(dateFrom, dateTo, null));
	}

	/**
	 * from after todate
	 * @throws ParseException
	 */
	@Test
	void validateFromAfterToDate() throws ParseException {	
		LocalDate from = new LocalDate();
		Date dateFrom = from.minusDays(7).toDate();
		
		LocalDate to = new LocalDate();
		Date dateTo = to.minusDays(14).toDate();
		
		assertFalse(dateValidator.validate(dateFrom, dateTo, null));
	}


	@Test
	void validateValide() throws ParseException {	
		LocalDate from = new LocalDate();
		Date dateFrom = from.toDate();
		
		LocalDate to = new LocalDate();
		Date dateTo = to.toDate();
		
		assertTrue(dateValidator.validate(dateFrom, dateTo, null));
	}

	/**
	 * from null
	 * @throws ParseException
	 */
	@Test
	void validateFromNull() throws ParseException {	
		Date dateFrom = null;
		
		LocalDate to = new LocalDate();
		Date dateTo = to.minusDays(14).toDate();
		
		assertFalse(dateValidator.validate(dateFrom, dateTo, null));
	}

	/**
	 * to null
	 * @throws ParseException
	 */
	@Test
	void validateToNull() throws ParseException {	
		LocalDate from = new LocalDate();
		Date dateFrom = from.toDate();
		
		Date dateTo = null;
		
		assertFalse(dateValidator.validate(dateFrom, dateTo, null));
	}


}
