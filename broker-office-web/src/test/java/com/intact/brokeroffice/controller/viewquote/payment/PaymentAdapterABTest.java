package com.intact.brokeroffice.controller.viewquote.payment;

import static org.junit.jupiter.api.Assertions.*;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.common.domain.QuoteCalculationDetails;
import com.ing.canada.common.services.api.rating.IQuotationService;
import com.ing.canada.plp.crypto.IIngCipher;
import com.ing.canada.plp.domain.billing.Account;
import com.ing.canada.plp.domain.billing.Billing;
import com.ing.canada.plp.domain.enums.BillingPlanCodeEnum;
import com.ing.canada.plp.domain.enums.MethodOfPaymentCodeEnum;
import com.ing.canada.plp.domain.enums.PolicyTermInMonthsEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.PolicyOfferRating;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.business.rules.offer.BR6418_MonthlyPaymentAvailable;
import com.intact.business.rules.quote.BR5883_HasFourPercentageSurcharge;
import com.intact.business.rules.quote.BR5883_HasTwoPercentageSurcharge;

class PaymentAdapterABTest {
	private IQuotationService mockQuotationService;
	private IIngCipher mockIIngCipher;
	
	private BR5883_HasFourPercentageSurcharge mockBR5883_4;
	private BR5883_HasTwoPercentageSurcharge mockBR5883_2;
	private BR6418_MonthlyPaymentAvailable mockBR6418;
	
	PaymentAdapterAB paymentAdapter = new PaymentAdapterAB();
	
	private final String FINANCIAL_INSTITUTION_NUMBER = "5678";
	private final String BANK_ACCOUNT_HOLDER_NAME = "Jacques";
	private final String ACCOUNT_NUMBER = "12345";
	private  final String ROUTING_NUMBER = "890";
	
	private final double FULL_TERM_TAXES_AND_SURCHARGE = 100.0;
	private final double MONTHLY_PAYMENT_TAXES_AND_SURCHARGE = 10.0;
	private final double ANNUAL_AMOUNT_TAXES = 15.0;
	private final double FIRST_MONTHLY_PAYMENT_TAXES_AND_SURCHARGE = 20.0;
	private final Integer ANNUAL_PREMIUM = 1000;

	@BeforeEach
	void setUp() throws Exception {
		mockQuotationService = Mockito.mock(IQuotationService.class);
		mockIIngCipher = Mockito.mock(IIngCipher.class);
		
		mockBR5883_4 = Mockito.mock(BR5883_HasFourPercentageSurcharge.class);
		mockBR5883_2 = Mockito.mock(BR5883_HasTwoPercentageSurcharge.class);
		mockBR6418 = Mockito.mock(BR6418_MonthlyPaymentAvailable.class);
		
	ReflectionTestUtils.setField(paymentAdapter, "quotationService", mockQuotationService);
	ReflectionTestUtils.setField(paymentAdapter, "br5883_4perc", mockBR5883_4);
	ReflectionTestUtils.setField(paymentAdapter, "br5883_2perc", mockBR5883_2);
	ReflectionTestUtils.setField(paymentAdapter, "br6418", mockBR6418);
	ReflectionTestUtils.setField(paymentAdapter, "cipher", mockIIngCipher);
	
	Mockito.when(mockQuotationService
			.getQuoteCalculationDetails((PolicyVersion) Mockito.any()
					, (ProvinceCodeEnum) Mockito.any())).thenReturn(newQuoteCalculationDetails());
	
	Mockito.when(mockIIngCipher.decryptToString(ACCOUNT_NUMBER)).thenReturn(ACCOUNT_NUMBER);
	Mockito.when(mockIIngCipher.decryptToString(FINANCIAL_INSTITUTION_NUMBER)).thenReturn(FINANCIAL_INSTITUTION_NUMBER);
	Mockito.when(mockIIngCipher.decryptToString(ROUTING_NUMBER)).thenReturn(ROUTING_NUMBER);
	Mockito.when(mockBR6418.validate((PolicyVersion) Mockito.any())).thenReturn(true);
	}

	@AfterEach
	void tearDown() throws Exception {
		mockQuotationService = null;
		mockIIngCipher = null;	
		mockBR5883_4 = null;
		mockBR5883_2 = null;
		mockBR6418 = null;
	}
	
	private QuoteCalculationDetails newQuoteCalculationDetails(){
		QuoteCalculationDetails quoteCalculationDetails = new QuoteCalculationDetails();
		quoteCalculationDetails.setFullTermPremiumWithTaxesAndSurcharge(FULL_TERM_TAXES_AND_SURCHARGE);
		quoteCalculationDetails.setMonthlyPaymentWithTaxesAndSurcharge(MONTHLY_PAYMENT_TAXES_AND_SURCHARGE);
		quoteCalculationDetails.setAnnualAmountTaxes(ANNUAL_AMOUNT_TAXES);
		quoteCalculationDetails.setFirstMonthlyPaymentWithTaxesAndSurcharge(FIRST_MONTHLY_PAYMENT_TAXES_AND_SURCHARGE);
		quoteCalculationDetails.setPolicyTermInMonths(PolicyTermInMonthsEnum.TWELVE_MONTHS);
		quoteCalculationDetails.setMonthlyPaymentsEligible(true);
		quoteCalculationDetails.setAnyPaymentsEligible(false);
		return quoteCalculationDetails;
	}
	
	private PolicyVersion newPolicyVersion(){
		PolicyVersion policyVersion = new PolicyVersion();
		policyVersion.setCombinedPolicyCode(null);			
		Billing billing = new Billing();
		Account account = new Account();
		account.setRoutingNumber(ROUTING_NUMBER);
		account.setAccountNumber(ACCOUNT_NUMBER);
		account.setFinancialInstitutionNumber(FINANCIAL_INSTITUTION_NUMBER);
		account.setBankAccountHolderName(BANK_ACCOUNT_HOLDER_NAME);
		billing.setAccount(account);
		billing.setBillingPlan(BillingPlanCodeEnum.PRE_AUTHORIZED_CHEQUE_PLAN);
		billing.setMethodOfPayment(MethodOfPaymentCodeEnum.ELECTRONIC_FUNDS_TRANSFER);
		policyVersion.setBilling(billing);	
		InsuranceRisk insuranceRisk = new InsuranceRisk();
		InsuranceRiskOffer insuranceRiskOffer = new InsuranceRiskOffer();
		PolicyOfferRating policyOfferRating = new PolicyOfferRating();
		policyOfferRating.setAnnualPremium(ANNUAL_PREMIUM);
		insuranceRiskOffer.setPolicyOfferRating(policyOfferRating);
		insuranceRisk.addInsuranceRiskOffer(insuranceRiskOffer);
		insuranceRisk.setSelectedInsuranceRiskOffer(insuranceRiskOffer);
		policyVersion.addInsuranceRisk(insuranceRisk);
				
		return policyVersion;
	}

	@Test
	void loadPremiumInformation() {
		PaymentBean paymentBean = new PaymentBean();
		QuoteCalculationDetails quoteCalculationDetails = new QuoteCalculationDetails();
		quoteCalculationDetails.setFullTermPremiumWithTaxesAndSurcharge(FULL_TERM_TAXES_AND_SURCHARGE);
		quoteCalculationDetails.setMonthlyPaymentWithTaxesAndSurcharge(MONTHLY_PAYMENT_TAXES_AND_SURCHARGE);
		
		paymentAdapter.loadPremiumInformation(paymentBean, quoteCalculationDetails);
		
		assertTrue(100.0 == paymentBean.getAnnualAmountWithTaxes());
		assertTrue(10.0 == paymentBean.getMonthlyPremium());
	}

	@Test
	void loadPaymentBean(){
		PaymentBean paymentBean = new PaymentBean();
		paymentBean.setMonthlyPaymentsEligible(true);
		
		paymentAdapter.loadPaymentBean(paymentBean, newPolicyVersion());
		
		assertTrue(paymentBean.isPaymentBank());
		assertTrue(paymentBean.isMonthlyPaymentsEligible());
		assertTrue(FULL_TERM_TAXES_AND_SURCHARGE == paymentBean.getAnnualAmountWithTaxes());
		assertTrue(MONTHLY_PAYMENT_TAXES_AND_SURCHARGE == paymentBean.getMonthlyPremium());
		assertTrue(ANNUAL_AMOUNT_TAXES == paymentBean.getAnnualAmountTaxes());
		assertTrue(FIRST_MONTHLY_PAYMENT_TAXES_AND_SURCHARGE == paymentBean.getMonthlyFirstPayment());
		assertEquals(1, paymentBean.getNbYears());
		assertTrue(paymentBean.isMonthlyPaymentsEligible());
		assertFalse(paymentBean.isAnyPaymentsEligible());	
		assertTrue(ACCOUNT_NUMBER == paymentBean.getPaymentMonthlyBank().getAccountNumber());
		assertTrue(FINANCIAL_INSTITUTION_NUMBER == paymentBean.getPaymentMonthlyBank().getInstitution());
		assertTrue(ROUTING_NUMBER == paymentBean.getPaymentMonthlyBank().getTransit());
	}

}
























