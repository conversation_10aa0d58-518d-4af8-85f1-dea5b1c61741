package com.intact.brokeroffice.controller.quotes;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

import java.util.HashMap;
import java.util.Locale;
import java.util.Map;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.test.util.ReflectionTestUtils;

import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfInsuranceCodeEnum;
import com.intact.brokeroffice.controller.language.LanguageController;
import com.intact.brokeroffice.helper.ResourceBundleHelper;
import com.intact.plt.information.service.client.ClientInformationApplicationService;
import com.intact.plt.information.service.client.util.InformationPieceTO;
import com.intact.plt.information.service.client.util.InformationSearchTO;

class QuoteSummaryControllerTest {

	private MockedStatic<ResourceBundleHelper> mockedResourceBundleHelper;

  @InjectMocks
  private QuoteToolTipController quoteSummaryController;

  @Mock
  private LanguageController languageController;

  @Mock
  private ClientInformationApplicationService clientInformationApplicationService;

  private InformationPieceTO pieceValueAuto;

  private InformationPieceTO pieceValueResidential;

	@BeforeEach
	void setUp() throws Exception {

    Map<String, String> quoteSummaryInfo = new HashMap<String, String>(2);
    quoteSummaryInfo.put(LineOfInsuranceCodeEnum.AUTOMOBILE.name(), "VALUE FOR summary.Automobile");
    quoteSummaryInfo
        .put(LineOfInsuranceCodeEnum.RESIDENTIAL.name(), "VALUE FOR summary.Residential");

    ReflectionTestUtils.setField(quoteSummaryController, "languageController", languageController);
    ReflectionTestUtils.setField(quoteSummaryController, "informationService",
        clientInformationApplicationService);
    ReflectionTestUtils.setField(quoteSummaryController, "informationKeys", quoteSummaryInfo);

    pieceValueAuto = new InformationPieceTO();
    pieceValueAuto.setValue("automobile value");

    pieceValueResidential = new InformationPieceTO();
    pieceValueResidential.setValue("home value");
  }

	@AfterEach
	void tearDown() throws Exception {
  }

	@BeforeEach
	void setUpStaticMocks() {
		mockedResourceBundleHelper = Mockito.mockStatic(ResourceBundleHelper.class);
	}

	@AfterEach
	void tearDownStaticMocks() {
		mockedResourceBundleHelper.closeOnDemand();
	}

	@Test
	@Disabled
	void getSummaryInfoPieceAUTOMOBILE() throws Exception {
    when(clientInformationApplicationService
        .getInformation(eq("VALUE FOR summary.Automobile"), any(InformationSearchTO.class)))
        .thenReturn(pieceValueAuto);

    when(languageController.getLocale()).thenReturn(Locale.CANADA);

    quoteSummaryController
        .setQuote("someRefNumber", LineOfBusinessCodeEnum.PERSONAL_LINES.getCode(),
            LineOfInsuranceCodeEnum.AUTOMOBILE.getCode(), "someAgreementNumber");

    InformationPieceTO result = quoteSummaryController.getTooltipInfo();

    assertEquals(pieceValueAuto, result);
  }

	@Test
	@Disabled
	void getSummaryInfoPieceRESIDENTIAL() throws Exception {
    when(clientInformationApplicationService
        .getInformation(eq("VALUE FOR summary.Residential"), any(InformationSearchTO.class)))
        .thenReturn(pieceValueResidential);

    when(languageController.getLocale()).thenReturn(Locale.CANADA);

    quoteSummaryController
        .setQuote("someRefNumber", LineOfBusinessCodeEnum.PERSONAL_LINES.getCode(),
            LineOfInsuranceCodeEnum.RESIDENTIAL.getCode(), "someAgreementNumber");

    InformationPieceTO result = quoteSummaryController.getTooltipInfo();

    assertEquals(pieceValueResidential, result);
  }

	@Test
	@Disabled
	void buildErrorPiece() throws Exception {
    mockedResourceBundleHelper.when(() -> ResourceBundleHelper.getMessage(anyString(), any(), anyString()))
        .thenReturn("errorPieceTestMessage");

    when(clientInformationApplicationService
        .getInformation(anyString(), (InformationSearchTO) any())).thenThrow(new Exception());

    when(languageController.getLocale()).thenReturn(Locale.CANADA);

    quoteSummaryController
        .setQuote("someRefNumber", LineOfBusinessCodeEnum.PERSONAL_LINES.getCode(),
            LineOfInsuranceCodeEnum.RESIDENTIAL.getCode(), "someAgreementNumber");

    InformationPieceTO result = quoteSummaryController.getTooltipInfo();

    InformationPieceTO child = (InformationPieceTO) result.getChildrens().get(0);

    assertEquals("error", result.getValue());
    assertEquals("errorPieceTestMessage", child.getPieces().get(0).getValue());
  }
}
