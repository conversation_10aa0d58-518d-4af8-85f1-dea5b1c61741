## Important messages
client.messages.title=Messages importants 

client.message.roadblock=La soumission du client a été bloquée sur la page {0} pour la raison suivante :<br> {1}

client.message.recentActivity=Il y a eu des activités récentes pour cette soumission. Vous référer au Journal d'activités ci-dessous pour plus de détails.
client.message.recentQuote=Il y a d'autres soumissions existantes pour ce client. Pour les voir, veuillez aller à la page <b>Rechercher Soumissions</b> et effectuez une recherche de client par nom.
client.message.consent.quote.contacted=Le client n'a pas fourni son consentement à être contacté concernant cette soumission.
client.message.consent.marketing=Le client n'a pas fourni son consentement à être contacté concernant le marketing des produits d'Intact Assurance.

#MSG506
message.upload.to.goBrio.confirmation=La soumission a été téléchargée avec succès. 
#MSG507
message.upload.to.goBrio.error=Un échec technique s'étant produit, veuillez contacter le Centre d'assistance d'Intact 1-888-495-6911.
#MSG527
message.quote.has.been.reassigned=La soumission que vous tentez de visualiser / mettre à jour / télécharger a été réassignée. Elle n'est plus disponible pour l'affichage.

#MSG5568
message.region.changed=Avez-vous changé votre région récemment ? La soumission que vous tentez de mettre à jour / télécharger n'est pas dans votre région actuelle. Elle n'est plus disponible pour l'affichage.


## IRCA specific messages
client.message.roadblock.irca=La soumission du client a été bloquée pour la raison suivante :<br> {1}
client.message.consent.credit=Le client n'a pas donné l'autorisation à Intact Assurance d'obtenir de l'information relative au dossier de crédit. 

####

client.general.title=Information générale
client.general.info=Renseignements du client

client.refNo=N° de référence de la soumission:
client.roadBlock=Road Block:
client.policyHolder=Nom du titulaire de la police:
client.language=Langue:
client.brokerAdvisor=Point de vente:
client.status=Statut de la soumission:

client.quote.source=Provenance système:

client.lastUpdate=Dernière mise à jour par le client:
client.creationDate=Date de la soumission:
client.start.this.insurance.coverage=Pour quelle date aimeriez-vous assurer le(s) véhicule(s)?

client.followup=Statut du suivi:
client.followup.notContacted=Jamais contacté.
client.followup.required=Contact, suivi nécessaire.
client.followup.notRequired=Contact, suivi non nécessaire.
client.followup.noneRequired=Aucun contact, suivi non nécessaire.
client.followup.duplicate=Doublon/Test

client.followup.note=Statut changé de "{0}" à "{1}".
client.addNotes=Ajout des notes de contact:

client.filenamePDF={0} Soumission Intact Assurance pour entreprises.pdf

client.phoneHome=N° de téléphone préféré:
client.phoneCell=Numéro de téléphone (cell):
client.phoneWork=Numéro de téléphone (travail):

client.email.address=Courriel
client.email.address.label=Courriel:

client.activityLog.title=Journal d'activités
client.activityLog.date=Date / heure
client.activityLog.account=Nom d'utilisateur
client.activityLog.activity=Activité
client.activityLog.notes=Notes

VIEW_QUO=Soumission visualisée
UPLOAD_QUO=Soumission téléchargée
CHG_FLW_ST=Suivi
ADD_NOTE=Mise à jour de soumission
REAS_QUO=Réassignée

VEHICLE=vehicule
OFFER=offre
BIND=achat
DRIVER=conducteur
VEHICLE_USAGE=usage du véhicle
ADDRESS_CHANGE=changement d'adresse
PURCHASE=achat
INVALID_OFFER=offre invalide

NOT_CONTACTED=Jamais contacté
CONTACTED_FOLLOWUP_REQUIRED=Contact, suivi nécessaire
CONTACTED_NO_FOLLOWUP_REQUIRED=Contact, suivi non nécessaire
NOT_CONTACTED_NO_FOLLOWUP_REQUIRED=Aucun contact, suivi non nécessaire
CALLBACK_REQUESTED=Retour d'appel demandé
DUPLICATE_FAKE=Doublon/Test

NOT_CONTACTED.FOLLOWUP=Le client n'a pas encore été contacté
CONTACTED_FOLLOWUP_REQUIRED.FOLLOWUP=Le client a été contacté mais un suivi est requis
CONTACTED_NO_FOLLOWUP_REQUIRED.FOLLOWUP=Le client a été contacté, aucun autre suivi n'est requis

prop.timezone=EST
fr=Français
en=Anglais

message.url.here=ici
message.error.plt=Cette section n'est pas disponible pour le moment.  SVP revenez plus tard.

INT= Site Internet Intact
WEBBK= Site Internet courtier

### info pour intact
QC.QF.QI.value=2
QC.QF.QI.description=QC - Auto rapide - Intact
QC.QA.QI.value=1
QC.QA.QI.description=QC - Auto détaillé - Intact
QC.IR.QI.value=5
QC.IR.QI.description=QC - IRCA rapide - Intact
QC.QH.QI.value=4
QC.QH.QI.description=QC - Habitation rapide - Intact
QC.QT.QI.value=4
QC.QT.QI.description=QC - Habitation rapide - Intact
QC.QC.QI.value=4
QC.QC.QI.description=QC - Habitation rapide - Intact
QC.PC.QI.value=22
QC.PC.QI.description=QC - Entreprise PC - Intact

ON.QF.QI.value=19
ON.QF.QI.description=ON - Auto rapide - Intact
ON.QA.QI.value=18
ON.QA.QI.description=ON - Auto détaillé - Intact
ON.IR.QI.value=25
ON.IR.QI.description=ON - IRCA rapide - Intact
ON.PC.QI.value=24
ON.PC.QI.description=ON - Entreprise PC - Intact

AB.QF.QI.value=21
AB.QF.QI.description=AB - Auto rapide - Intact
AB.QA.QI.value=20
AB.QA.QI.description=AB - Auto détaillé - Intact
AB.IR.QI.value=28
AB.IR.QI.description=AB - IRCA rapide - Intact
AB.PC.QI.value=26
AB.PC.QI.description=AB - Entreprise PC - Intact


### info pour courtier
QC.QF.QB.value=8
QC.QF.QB.description=QC - Auto rapide - Courtier
QC.QA.QB.value=7
QC.QA.QB.description=QC - Auto détaillé - Courtier
QC.IR.QB.value=10
QC.IR.QB.description=QC - IRCA rapide - Courtier
QC.QH.QB.value=9
QC.QH.QB.description=QC - Habitation rapide - Courtier
QC.QT.QB.value=9
QC.QT.QB.description=QC - Habitation rapide - Courtier
QC.QC.QB.value=9
QC.QC.QB.description=QC - Habitation rapide - Courtier
QC.PC.QB.value=23
QC.PC.QB.description=QC - Entreprise PC - Courtier

ON.QF.QB.value=19
ON.QF.QB.description=ON - Auto rapide - Intact
ON.QA.QB.value=18
ON.QA.QB.description=ON - Auto détaillé - Intact
ON.IR.QB.value=25
ON.IR.QB.description=ON - IRCA rapide - Intact
ON.PC.QB.value=24
ON.PC.QB.description=ON - Entreprise PC - Intact

AB.QF.QB.value=21
AB.QF.QB.description=AB - Auto rapide - Intact
AB.QA.QB.value=20
AB.QA.QB.description=AB - Auto détaillé - Intact
AB.IR.QB.value=28
AB.IR.QB.description=AB - IRCA rapide - Intact
AB.PC.QB.value=26
AB.PC.QB.description=AB - Entreprise PC - Intact

Risk.IR=Auto commerciale
Risk.QH=Propriétaire
Risk.QC=Condo
Risk.QT=Locataire
Risk.QF=Auto
Risk.QA=Auto
Risk.PC=Entreprise

dialerSuccessMessage=Fiche d'appel créé avec le numéro de confirmation suivant :
dialerFailedMessage=La création d'une fiche d'appel a échoué. SVP créer une fiche manuellement ou ré-essayer à nouveau.

# Message when dialer is not match dialer_broker json file
dialer.failed.match.broker.message=Le fichier d'appel ne peut pas être créé : aucun courtier ne correspond aux critères de cette soumission. Merci de bien vouloir notifier votre gestionnaire de cette erreur. Numéro de courtier :