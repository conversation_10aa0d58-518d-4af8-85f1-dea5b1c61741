package com.intact.business.rules.quote;

import com.ing.canada.plp.domain.enums.CombinedPolicyCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.business.rules.stereotype.Rule;

@Rule
public class BR5883_HasFourPercentageSurcharge {

	public boolean validate(PolicyVersion aPolicy) {
		boolean hasSurcharge = false;

		//
		if (aPolicy.getCombinedPolicyCode() == null || !aPolicy.getCombinedPolicyCode().equals(
				CombinedPolicyCodeEnum.COMBO_POLICY)) {
			hasSurcharge = true;
		}

		return hasSurcharge;
	}
}
