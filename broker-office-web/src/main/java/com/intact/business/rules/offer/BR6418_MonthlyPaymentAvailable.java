/*
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 *  without the written permission of Intact Insurance
 *
 * Copyright (c) 2010 Intact Insurance, All rights reserved.
 */
package com.intact.business.rules.offer;

import com.ing.canada.common.services.api.rating.IQuotationService;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.enums.ConsentTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.NumberTimesCanceledRefusedNonPaymentEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.enums.ServiceStatusCodeEnum;
import com.ing.canada.plp.domain.party.Consent;
import com.ing.canada.plp.domain.party.CreditScore;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IConsentHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.intact.business.rules.stereotype.Rule;
import com.intact.business.util.PlpHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * BR6418 - Monthly payment available
 */
@Rule
public class BR6418_MonthlyPaymentAvailable {

	@Autowired
	protected IConsentHelper consentHelper;

	@Autowired
	protected IPolicyVersionHelper policyVersionHelper;

	private static final int CREDIT_SCORE_LIMIT_MAX = 625;

	@Autowired
	private IQuotationService quotationService;

	@Autowired
	private PlpHelper plpHelper;

	/**
	 * BR6418_MonthlyPaymentAvailable.
	 * This uses the default Credit Score Max limit of 625
	 * <p/>
	 *
	 * @deprecated please use the signature with the provided max limit.
	 *
	 * @param aPolicyVersion the policy version
	 * @return true, if policy monthly payment is allowed, false otherwise
	 */
	@Deprecated
	public boolean validate(PolicyVersion aPolicyVersion) {
		return validate(aPolicyVersion, CREDIT_SCORE_LIMIT_MAX);
	}

	public boolean validate(PolicyVersion aPolicyVersion, int creditScoreLimitThreshold) {
		ManufacturingContext mc = aPolicyVersion.getInsurancePolicy().getManufacturingContext();
		if (ProvinceCodeEnum.ALBERTA.equals(mc.getProvince()) && ManufacturerCompanyCodeEnum.ING_WESTERN_REGION.equals(mc.getManufacturerCompany())) {
			PolicyHolder policyHolder = this.policyVersionHelper.getPrincipalInsuredPolicyHolder(aPolicyVersion);
			Consent consent = this.consentHelper.getConsent(policyHolder.getParty(), ConsentTypeCodeEnum.GENERAL_CONSENT);
			if (consent != null && consent.getConsentIndicator() != null && Boolean.FALSE.equals(consent.getConsentIndicator())) {
				return false;
			}
			if (this.quotationService.isCustomerNoHitNoIndCheck(aPolicyVersion)) {
				return false;
			}
			CreditScore score = this.plpHelper.getCreditScore(aPolicyVersion);
			// a numeric value will be needed
			int numericScore = 0;
			if (score != null
					&& score.getCreditScoreServiceStatus() == ServiceStatusCodeEnum.HIT
					&& StringUtils.isNumeric(score.getCreditScore())) {
				numericScore = Integer.parseInt(score.getCreditScore());
			}
			if (numericScore <= creditScoreLimitThreshold) {
				return false;
			}
			if (aPolicyVersion.getPriorCarrierPolicyInfo() != null) {
				NumberTimesCanceledRefusedNonPaymentEnum nbrTimes = aPolicyVersion.getPriorCarrierPolicyInfo().getNumberCanceledForNonPayment();
				return !NumberTimesCanceledRefusedNonPaymentEnum.TWO_OR_MORE.equals(nbrTimes);
			}
		}
		return true;
	}

}
