package com.intact.business.util;

import com.ing.canada.plp.domain.enums.PolicyHolderTypeCodeEnum;
import com.ing.canada.plp.domain.party.CreditScore;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import org.springframework.stereotype.Component;

import java.util.Set;

@Component
public class PlpHelper {

	/**
	 * Get the credit score from provided policy version's policy holder.
	 *
	 * @param aPolicyVersion {@link PolicyVersion}
	 * @return {@link CreditScore}
	 */
	public CreditScore getCreditScore(PolicyVersion aPolicyVersion) {
		if (aPolicyVersion != null) {
			for (PolicyHolder policyHolder : aPolicyVersion.getPolicyHolders()) {
				if (PolicyHolderTypeCodeEnum.PRINCIPAL_INSURED.equals(policyHolder.getPolicyHolderType())) {
					Party party = policyHolder.getParty();
					if (party != null) {
						Set<CreditScore> creditScores = party.getCreditScores();
						if (creditScores != null && creditScores.size() > 0) {
							return (CreditScore) party.getCreditScores().toArray()[0];
						}
					}
				}
			}
		}
		return null;
	}
}
