package com.intact.brokeroffice.listener;

import jakarta.faces.component.UIComponent;
import jakarta.faces.component.UIViewRoot;
import jakarta.faces.context.FacesContext;
import jakarta.faces.event.AbortProcessingException;
import jakarta.faces.event.SystemEvent;
import jakarta.faces.event.SystemEventListener;
import java.util.List;
import java.util.stream.Collectors;

/**
 * This class is used to remove the primefaces components.css from the head of the page.
 * Only custom css should be included in the page.
 *
 */
public class RemoveResourcesListener implements SystemEventListener {
    private static final String HEAD = "head";

    @Override
    public void processEvent(SystemEvent event)
            throws AbortProcessingException {

        FacesContext context = FacesContext.getCurrentInstance();

        // Check if we should remove the current resource.
        // Here we may check for any library and name combination.
        // (JSF, Primefaces, Richfaces, etc)
        List<UIComponent> resources = context.getViewRoot().getComponentResources(context, HEAD).stream().filter(resource -> {
            // Fetch resource library and resource name
            String resourceLibrary = (String) resource.getAttributes().get("library");
            String resourceName = (String) resource.getAttributes().get("name");
            return "primefaces".equals(resourceLibrary) && "components.css".equals(resourceName);
        }).collect(Collectors.toList());

        resources.forEach(resource -> context.getViewRoot().removeComponentResource(context, resource, HEAD));
    }

    @Override
    public boolean isListenerForSource(Object source) {
        return (source instanceof UIViewRoot);
    }
}
