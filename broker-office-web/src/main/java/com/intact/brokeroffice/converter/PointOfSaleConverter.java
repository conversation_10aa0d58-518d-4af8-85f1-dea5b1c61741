package com.intact.brokeroffice.converter;

import java.util.StringTokenizer;

import jakarta.faces.component.UIComponent;
import jakarta.faces.context.FacesContext;
import jakarta.faces.convert.Converter;

/**
 * The Class NameConverter to limit the size of the name to show in the UI.
 * 
 * BR5127  If there is insufficient space to display the entire name, 
 * the name will be truncated after the 12th character and an ellipse () will be displayed. 
 * The complete name will be displayed upon mouse-over.:
 * 	
 */
public class PointOfSaleConverter implements Converter {

	private static int POS_TOKEN_LENGTH = 30;
	private static String ELLIPSIS = "...";
	
	/**
	 * @see jakarta.faces.convert.Converter#getAsObject(jakarta.faces.context.FacesContext, jakarta.faces.component.UIComponent, java.lang.String)
	 */
	public Object getAsObject(FacesContext arg0, UIComponent arg1, String arg2) {
		return null;
	}

	/**
	 * @see jakarta.faces.convert.Converter#getAsString(jakarta.faces.context.FacesContext, jakarta.faces.component.UIComponent, java.lang.Object)
	 */
	public String getAsString(FacesContext arg0, UIComponent arg1, Object arg2) {
		String str = String.valueOf(arg2);

		StringTokenizer tokens = new StringTokenizer(str);

		// Verify if any given token is longer than restricted length
		for( ; tokens.hasMoreElements(); ) {
			String s = tokens.nextToken();
			if(s.length() > POS_TOKEN_LENGTH) {
				return str.substring(0, POS_TOKEN_LENGTH - ELLIPSIS.length()) + ELLIPSIS; 
			}
		}

		return str;
	}
}
