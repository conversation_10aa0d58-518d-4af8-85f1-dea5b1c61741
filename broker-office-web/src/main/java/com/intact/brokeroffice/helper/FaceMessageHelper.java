/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.helper;

import jakarta.faces.application.FacesMessage;
import jakarta.faces.application.FacesMessage.Severity;
import jakarta.faces.context.FacesContext;

/**
 * The Class FaceMessageHelper.
 */
public final class FaceMessageHelper {
	
	/**
	 * Instantiates a new face message helper.
	 */
	private FaceMessageHelper() {
		// NOOP
	}
	
	/**
	 * Adds the error message.
	 * 
	 * @param id the id
	 * @param key the key
	 * @param obj the obj
	 */
	public static void addErrorMessage(String id, String key, Object obj, String... parameters) {
		addMessage(id, key, obj, FacesMessage.SEVERITY_ERROR, parameters);
	}
	
	/**
	 * Adds the error message.
	 * 
	 * @param id the id
	 * @param key the key
	 * @param obj the obj
	 * @param path the path
	 */
	public static void addErrorMessage(String id, String path, String key, Object obj) {
		addMessage(id, path, key, obj, FacesMessage.SEVERITY_ERROR);
	}
	

	/**
	 * Adds the info message.
	 * 
	 * @param id the id
	 * @param key the key
	 * @param obj the obj
	 */
	public static void addInfoMessage(String id, String key, Object obj) {
		addMessage(id, key, obj, FacesMessage.SEVERITY_INFO);
	}

	/**
	 * Adds the info message.
	 * 
	 * @param id the id
	 * @param key the key
	 * @param obj the obj
	 * @param path the path
	 */
	public static void addInfoMessage(String id, String path, String key, Object obj) {
		addMessage(id, path, key, obj, FacesMessage.SEVERITY_INFO);
	}

	/**
	 * Adds the message.
	 * 
	 * @param id the id
	 * @param key the key
	 * @param obj the obj
	 * @param facesMessage the faces message
	 */
	private static void addMessage(String id, String key, Object obj, Severity facesMessage, String... parameters) {
		FacesMessage message = new FacesMessage();
		message.setSeverity(facesMessage);
		
		if (parameters == null) {
			message.setDetail(ResourceBundleHelper.getMessage(obj, key));
		} else {
			message.setDetail(ResourceBundleHelper.getMessage(obj, key, parameters));
		}
		
		FacesContext.getCurrentInstance().addMessage(id, message);
	}

	/**
	 * Adds the message.
	 * 
	 * @param id the id
	 * @param key the key
	 * @param obj the obj
	 * @param facesMessage the faces message
	 * @param path the path
	 */
	private static void addMessage(String id, String path, String key, Object obj, Severity facesMessage) {
		FacesMessage message = new FacesMessage();
		message.setSeverity(facesMessage);
		message.setDetail(ResourceBundleHelper.getMessage(path, obj, key));
		FacesContext.getCurrentInstance().addMessage(id, message);
	}
}
