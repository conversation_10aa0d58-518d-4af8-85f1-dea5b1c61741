package com.intact.brokeroffice.helper;

import java.text.MessageFormat;
import java.util.Enumeration;
import java.util.Locale;
import java.util.ResourceBundle;

import jakarta.faces.context.FacesContext;

/**
 * The Class ResourceBundleHelper.
 */
public class ResourceBundleHelper {

	/**
	 * Instantiates a new resource bundle helper.
	 */
	private ResourceBundleHelper(){
		//Default constructor
	}
	
	/**
	 * Gets the message.
	 * 
	 * @param path the path
	 * @param obj the obj
	 * @param key the key
	 * 
	 * @return the message
	 */
	public static String getMessage(String path, Object obj, String key){
		
		String value = null;
		Locale locale = FacesContext.getCurrentInstance().getViewRoot().getLocale();
		ResourceBundle bundle = ResourceBundle.getBundle(path, locale, obj.getClass().getClassLoader());
		try {
			value = bundle.getString(key);
		} catch (Exception ignored) {
		}
		
		return value;
	}	
	
	/**
	 * Gets the message.
	 * 
	 * @param obj the obj
	 * @param key the key
	 * 
	 * @return the message
	 */
	public static String getMessage(Object obj, String key){
		
		String path = obj.getClass().getPackage().getName();
		String name = path.substring(path.lastIndexOf("."));
		return getMessage(path+name, obj, key);
	}
	
	/**
	 * Gets the message.
	 * 
	 * @param obj the obj
	 * @param key the key
	 * @param params the params
	 * 
	 * @return the message
	 */
	public static String getMessage(Object obj, String key, Object... params){
		
		String resource = getMessage(obj, key);
		if(resource == null){
			return "";
		}
		MessageFormat formatter = new MessageFormat(resource);      
	    return formatter.format(params);

	}
	
	/**
	 * Gets the message.
	 * 
	 * @param path the path
	 * @param obj the obj
	 * @param key the key
	 * @param params the params
	 * 
	 * @return the message
	 */
	public static String getMessage(String path, Object obj, String key, Object... params){
		
		String resource = getMessage(path, obj, key);
		if(resource == null){
			return "";
		}
		MessageFormat formatter = new MessageFormat(resource);      
	    return formatter.format(params);

	}
	
	/**
	 * Helper to validate the key existence 
	 * @param path
	 * @param obj
	 * @param key
	 * @return
	 */
	public static boolean validateKeyExistence(String path, Object obj, String key)
	{					
		for(Enumeration<String> keysItems = getAllKeys(path, obj);  keysItems.hasMoreElements();)
		{			
			if(key.equals(keysItems.nextElement())){
				return true;
			}
		}
		
		return false;
	}
	
	/**
	 * Gets all keys
	 * @param path
	 * @param obj
	 * @return all keys
	 */
	public static Enumeration<String> getAllKeys(String path, Object obj)
	{	
		Locale locale = FacesContext.getCurrentInstance().getViewRoot().getLocale();
		ResourceBundle bundle = ResourceBundle.getBundle(path, locale, obj.getClass().getClassLoader());		
		return bundle.getKeys();
	}	
	
}
