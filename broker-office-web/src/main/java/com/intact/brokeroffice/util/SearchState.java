package com.intact.brokeroffice.util;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import jakarta.faces.model.SelectItem;

import com.intact.brokeroffice.business.domain.BrokerQuoteSearchCriteria;
import com.intact.brokeroffice.business.quote.QuotesBean;

public class SearchState {

	private String type = "VIEW";
 
	private BrokerQuoteSearchCriteria criteria = null;
	private List<QuotesBean> searchResults = null;
	private Map<String, QuotesBean> quotesMap = new HashMap<String, QuotesBean>();
	private List<SelectItem> masters = new ArrayList<SelectItem>();
	private List<SelectItem> subBrokers = new ArrayList<SelectItem>();

	private int perPage = 0;
	private int page = 0;
	private int nbQuotesSelected = 0;
	private Date timestamp = null;
	private int searchResultSize = 0;

	private Boolean allSelected = Boolean.FALSE;
	private Boolean reassignConfirmation = Boolean.FALSE;
	private Boolean alternateCompanyConfirmed = Boolean.FALSE;
	private int index = 0;
	private Map<String, Boolean> failed = null;

	public SearchState() {
		this.failed = new HashMap<String, Boolean>();
	}

	public final String getType() {
		return this.type;
	}

	public final void setType(String type) {
		this.type = type;
	}
	
	public void setFailed(String value, boolean failed) {
		this.failed.put(value, failed);
	}
	
	public boolean getFailed(String value) {
		return this.failed.get(value) != null ? this.failed.get(value) : false;
	}
	
	public Map<String, Boolean> getFailed() {
		return this.failed;
	}


	public final BrokerQuoteSearchCriteria getCriteria() {
		return this.criteria;
	}

	public final void setCriteria(BrokerQuoteSearchCriteria criteria) {
		this.criteria = criteria;
	}

	public final List<QuotesBean> getSearchResults() {
		return searchResults;
	}

	public final void setSearchResults(List<QuotesBean> searchResults) {
		this.searchResults = searchResults;
	}

	public Map<String, QuotesBean> getQuotesMap() {
		return quotesMap;
	}

	public void setQuotesMap(Map<String, QuotesBean> quotesMap) {
		this.quotesMap = quotesMap;
	}

	public final int getPerPage() {
		return this.perPage;
	}

	public final void setPerPage(int perPage) {
		this.perPage = perPage;
	}

	public final int getPage() {
		return this.page;
	}

	public final void setPage(int page) {
		this.page = page;
	}

	public final Date getTimestamp() {
		return this.timestamp;
	}

	public final void setTimestamp(Date timestamp) {
		this.timestamp = timestamp;
	}

	public final int getNbQuotesSelected() {
		return this.nbQuotesSelected;
	}

	public final void setNbQuotesSelected(int nbQuotesSelected) {
		this.nbQuotesSelected = nbQuotesSelected;
	}

	public final Boolean getAllSelected() {
		return this.allSelected;
	}

	public final void setAllSelected(Boolean allSelected) {
		this.allSelected = allSelected;
	}

	public final int getSearchResultSize() {
		return this.searchResultSize;
	}

	public final void setSearchResultSize(int searchResultSize) {
		this.searchResultSize = searchResultSize;
	}

	public final List<SelectItem> getMasters() {
		return this.masters;
	}

	public final void setMasters(List<SelectItem> masters) {
		this.masters = masters;
	}

	public final List<SelectItem> getSubBrokers() {
		return this.subBrokers;
	}

	public final void setSubBrokers(List<SelectItem> subBrokers) {
		this.subBrokers = subBrokers;
	}

	public final Boolean getReassignConfirmation() {
		return this.reassignConfirmation;
	}

	public final void setReassignConfirmation(Boolean reassignConfirmation) {
		this.reassignConfirmation = reassignConfirmation;
	}

	public final Boolean getAlternateCompanyConfirmed() {
		return this.alternateCompanyConfirmed;
	}

	public final void setAlternateCompanyConfirmed(Boolean alternateCompanyConfirmed) {
		this.alternateCompanyConfirmed = alternateCompanyConfirmed;
	}

	public final int getIndex() {
		return index;
	}

	public final void setIndex(int index) {
		this.index = index;
	}

	public String toString() {
		return "[type=" + this.getType() + ", criteria=" + this.getCriteria() + "]";
	}

}
