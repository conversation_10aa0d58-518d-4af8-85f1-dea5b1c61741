package com.intact.brokeroffice.config;

import jakarta.servlet.ServletContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.WebApplicationInitializer;

@Configuration
public class WebzoneContextInitializer implements WebApplicationInitializer {
    private static final String SEQUENCE_INCREMENT_SIZE_MISMATCH_STRATEGY = "hibernate.id.sequence.increment_size_mismatch_strategy";
    @Override
    public void onStartup(ServletContext servletContext) {
        System.setProperty(SEQUENCE_INCREMENT_SIZE_MISMATCH_STRATEGY, "FIX");
    }
}
