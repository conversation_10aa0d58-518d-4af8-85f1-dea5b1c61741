/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.filter;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.common.web.api.auth.TamTokensEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;
import com.intact.brokeroffice.controller.helper.ProvinceHelper;
import com.intact.brokeroffice.controller.language.LanguageController;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.brokeroffice.util.ProvinceCompanyConverter;
import com.intact.canada.brm.domain.broker.BrokerWebOfficeAccess;
import com.intact.canada.brm.domain.user.UserAccount;
import com.intact.common.security.exception.SecurityUtilityException;
import com.intact.common.security.exception.SecurityUtilityInvalidValueException;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;

import jakarta.servlet.*;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * The SessionTimeoutFilter will verify if the user session is still active, if it is not it will redirect the user to
 * the index page to avoid refering the user to invalid session beans
 */
public class SessionTimeoutFilter implements Filter {

	/**
	 * The Constant log.
	 */
	private static final Log log = LogFactory.getLog(SessionTimeoutFilter.class);

	private static String TIMEOUT = "timeout.jsf";

	private static String INDEX = "index.jsf";

	private Boolean spoeMode;

	/**
	 * The ldap group admins.
	 */
	private String ldapGroupAdmins;

	/**
	 * The ldap group program admins.
	 */
	private String ldapGroupProgramAdmins;

	/**
	 * The ldap group quote admins.
	 */
	private String ldapGroupQuoteAdmins;

	/**
	 * The ldap group underwritter.
	 */
	private String ldapGroupUnderwritter;

	/**
	 * The ldap group broker.
	 */
	private String ldapGroupBroker;

	/**
	 * The ldap group broker re-assign.
	 */
	private String ldapGroupBrokerReassign;

	private IAccountsBusinessProcess accountsBusinessProcess;

	private ProvinceController provinceController = null;

	private LanguageController languageController = null;

	private static final Set<String> ALLOWED_CHARACTERS = new HashSet<String>();

	public SessionTimeoutFilter() {
		SessionTimeoutFilter.ALLOWED_CHARACTERS.add(":");
	}

	public ProvinceController getProvinceController() {
		return this.provinceController;
	}

	public void setProvinceController(ProvinceController provinceController) {
		this.provinceController = provinceController;
	}

	public LanguageController getLanguageController() {
		return this.languageController;
	}

	public void setLanguageController(LanguageController languageController) {
		this.languageController = languageController;
	}

	/**
	 * @see jakarta.servlet.Filter#destroy()
	 */
	public void destroy() {
		// nothing
	}

	/**
	 * @see jakarta.servlet.Filter#init(jakarta.servlet.FilterConfig)
	 */
	public void init(FilterConfig arg0) throws ServletException {
		WebApplicationContext springContext = WebApplicationContextUtils.getWebApplicationContext(arg0
				.getServletContext());

		this.spoeMode = Boolean.FALSE;

		// LDAP GROUPS
		this.ldapGroupAdmins = (String) springContext.getBean("ldap-group-admins");
		this.ldapGroupProgramAdmins = (String) springContext.getBean("ldap-group-program-admins");
		this.ldapGroupQuoteAdmins = (String) springContext.getBean("ldap-group-quote-admins");
		this.ldapGroupUnderwritter = (String) springContext.getBean("ldap-group-underwritter");
		this.ldapGroupBroker = (String) springContext.getBean("ldap-group-broker");
		this.ldapGroupBrokerReassign = (String) springContext.getBean("ldap-group-broker-reassign");

		this.accountsBusinessProcess = (IAccountsBusinessProcess) springContext.getBean("accountsBusinessProcess");
		this.setProvinceController(new ProvinceController());
		this.setLanguageController(new LanguageController());
	}

	/**
	 * @see jakarta.servlet.Filter#doFilter(jakarta.servlet.ServletRequest, jakarta.servlet.ServletResponse,
	 * jakarta.servlet.FilterChain)
	 */
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain)
			throws IOException, ServletException {

		if ((request instanceof HttpServletRequest httpServletRequest) && (response instanceof HttpServletResponse httpServletResponse)) {

			// check for reference no from email

			processReferenceNo(httpServletRequest);
			// check for url language
			processLanguage(httpServletRequest, response);
			// check for province
			// processProvince(httpServletRequest);
			// Check for single sign on

			try {
				processSSO(httpServletRequest, response);
			} catch (Exception e) {
				e.printStackTrace();
				throw new ServletException(e);
			}

			// is session valid?
			if (showTimeoutPage(httpServletRequest)) {
				// PM11540. Get Cookie value only if session language is not set
				if (httpServletRequest.getSession()
						.getAttribute((String) SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant()) == null) {
					Cookie[] cookies = httpServletRequest.getCookies();
					if (cookies != null) {
						for (int i = 0; i < cookies.length; i++) {
							String name = cookies[i].getName();
							if (SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant().equals(name)) {
								String value = this.getLanguageController().filterValue(cookies[i].getValue());

								httpServletRequest.getSession()
										.setAttribute(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant(), value);
							}
						}
					}
				}

				// Get defaultCompany from cookie and assign it to newest session.
				if (httpServletRequest.getSession()
						.getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant()) == null) {
					String defaultCompany = null;
					if (ProvinceController.searchForBrokerDefaultCompany(httpServletRequest) != null) {
						Cookie aCookie = ProvinceController.searchForBrokerDefaultCompany(httpServletRequest);
						defaultCompany = aCookie.getValue();
					}

					String newValue = this.getProvinceController().filterValue(defaultCompany);
					httpServletRequest.getSession().setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(),
							newValue);

				}
				// String timeoutUrl = httpServletRequest.getContextPath() + "/" + TIMEOUT;
				String timeoutUrl = "/";
				if (httpServletRequest.getSession().getAttribute("autorelog") != null
						&& httpServletRequest.getSession().getAttribute("autorelog").equals("TRUE")) {
					timeoutUrl = httpServletRequest.getContextPath() + "/" + INDEX;
					httpServletRequest.getSession().removeAttribute("autorelog");
				} else {
					timeoutUrl = httpServletRequest.getContextPath() + "/" + TIMEOUT;
				}

				// session is invalid return the user to the timeout page
				httpServletResponse.sendRedirect(timeoutUrl);
				return;

			}
		}

		filterChain.doFilter(request, response);
	}

	/**
	 * We will process the Single sign on when the user accesses the login page
	 *
	 * @param httpServletRequest the http servlet request
	 * @return true, if is session control required for this resource
	 * @throws SecurityUtilityInvalidValueException
	 * @throws SecurityUtilityException
	 * @throws BrokerServiceException
	 */
	protected void processSSO(HttpServletRequest httpServletRequest, ServletResponse response) throws Exception {
		String requestPath = httpServletRequest.getRequestURI();
		boolean indexPage = StringUtils.contains(requestPath, INDEX);
		boolean ajaxRequest = isAjaxRequest(httpServletRequest);

		// When we are at the index page and this is not an ajax request then we will process the users login
		if (BooleanUtils.isNotTrue(this.spoeMode) && indexPage && !ajaxRequest) {
			this.login(httpServletRequest, response);
		}
	}

	protected void processLanguage(HttpServletRequest httpServletRequest, ServletResponse response) {
		String requestPath = httpServletRequest.getRequestURI();
		boolean indexPage = StringUtils.contains(requestPath, INDEX);
		String language = httpServletRequest.getParameter("language");
		if (indexPage && language != null) {

			language = this.getLanguageController().filterValue(language);
			httpServletRequest.getSession().setAttribute(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant(),
					language);

			// PM11540 - Create a cookie if required, to keep the broker
			// language value
			HttpServletResponse httpServletResponse = (HttpServletResponse) response;
			Cookie[] cookies = httpServletRequest.getCookies();
			if (cookies != null) {
				for (int i = 0; i < cookies.length; i++) {
					String name = cookies[i].getName();
					if (SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant().equals(name)) {
						// erase actual cookie to replace it
						cookies[i].setMaxAge(0);
						cookies[i].setSecure(true);
						cookies[i].setHttpOnly(true);
						String path = cookies[i].getPath() != null ? cookies[i].getPath() : "";
						cookies[i].setPath(path);
						httpServletResponse.addCookie(cookies[i]);
					}
				}
			}

			Cookie languageSessionCookie = new Cookie(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant(),
					language);
			languageSessionCookie.setSecure(true);
			languageSessionCookie.setHttpOnly(true);
			languageSessionCookie.setPath("/");
			httpServletResponse.addCookie(languageSessionCookie);
		} else if (indexPage && language == null) {
			// PM11540 - Create a cookie if required, to keep the broker
			// language value
			// If there is no no language define in the session, the we reset it
			// from the cookies
			if (httpServletRequest.getSession()
					.getAttribute((String) SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant()) == null) {
				Cookie[] cookies = httpServletRequest.getCookies();
				if (cookies != null) {
					for (int i = 0; i < cookies.length; i++) {
						String name = cookies[i].getName();
						if (SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant().equals(name)) {

							String cookieLanguage = this.getLanguageController().filterValue(cookies[i].getValue());

							httpServletRequest.getSession().setAttribute(
									SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant(), cookieLanguage);

						}
					}
				}
			}

		}
	}

	protected void processReferenceNo(HttpServletRequest httpServletRequest) {
		String requestPath = httpServletRequest.getRequestURI();
		boolean indexPage = StringUtils.contains(requestPath, INDEX);
		String referenceNo = httpServletRequest.getParameter("referenceNo");
		if (indexPage && referenceNo != null) {

			httpServletRequest.getSession().setAttribute(
					SessionConstantsEnum.EMAIL_REFERENCE_NUMBER.getSessionConstant(),
					this.filterValue(referenceNo));

		}
	}

	protected String filterValue(String value) {

		String newValue = value;

		char[] charArray = value.toCharArray();

		for (char c : charArray) {
			if (!Character.isLetterOrDigit(c)) {
				newValue = null;
				break;
			}
		}

		return newValue;
	}

	/**
	 * This method checks if we should display the timeout page or not
	 *
	 * @param httpServletRequest the http servlet request
	 * @return true, if is session control required for this resource
	 */
	private boolean showTimeoutPage(HttpServletRequest httpServletRequest) {

		if (httpServletRequest.getRequestedSessionId() == null) {
			return false;
		}

		String requestPath = httpServletRequest.getRequestURI();
		boolean timeoutPage = StringUtils.contains(requestPath, TIMEOUT);
		boolean sessionIdValid = httpServletRequest.isRequestedSessionIdValid();
		boolean userCredentialValid = httpServletRequest.getSession().getAttribute(
				TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue()) != null;

		// When we are using spoe we need to redirect to the timeout page if we dont have a user in session
		if (BooleanUtils.isTrue(this.spoeMode) && !userCredentialValid && !timeoutPage) {
			return true;
		}

		return !sessionIdValid;
	}

	/**
	 * Checks if this is a ajax request.
	 *
	 * @param request the request
	 * @return true, if checks if is ajax request
	 */
	private boolean isAjaxRequest(ServletRequest request) {
		try {
			return null != request.getParameter("AJAXREQUEST");
		} catch (Exception e) {
			return false;
		}
	}

	/**
	 * Logins the user when passing with a SSO and sets the available master broker account in memory if the type of
	 * access is of type broker.
	 *
	 * @param httpServletRequest the http servlet request
	 * @throws SecurityUtilityInvalidValueException
	 * @throws SecurityUtilityException
	 * @throws BrokerServiceException
	 */
	private void login(HttpServletRequest httpServletRequest, ServletResponse response) throws Exception {

		String userName = (String) httpServletRequest.getSession().getAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue());
		String userRole = (String) httpServletRequest.getSession().getAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue());
		String classicId = (String) httpServletRequest.getSession().getAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue());

		if (StringUtils.isNotEmpty(userName) && StringUtils.isNotEmpty(userRole) && StringUtils.isNotEmpty(classicId)) {
			return;
		}

		userName = httpServletRequest.getHeader(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue());
		userRole = httpServletRequest.getHeader(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue());
		classicId = httpServletRequest.getHeader(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue());

		System.out.println("GROUP: " + userRole);

		if (StringUtils.isNotEmpty(userName)) {
			String userTam = TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue();
			if (httpServletRequest.getSession().getAttribute(userTam) != null && !httpServletRequest.getSession().getAttribute(userTam).equals(userName)) {
				httpServletRequest.getSession().invalidate();
				httpServletRequest.getSession(true);
				httpServletRequest.getSession().setAttribute("autorelog", "TRUE");
			}

			httpServletRequest.getSession().setAttribute(userTam,
					userName);

			if (StringUtils.isNotEmpty(userRole)) {

				// BR5939 : Verify User Access
				// After verifying the user's credentials, the system will verify the user profile type in TAM and
				// if they have a "Broker" profile type, will validate their access rights, (owner or owners to
				// which they are assigned) in the BRM. They will only be allowed to view and manage quotes
				// associated with the owner(s) to which they are assigned.
				this.manageCompanies(httpServletRequest, userRole, this.ldapGroupAdmins);

				// We manage only one ldap group for the permissions
				if (userRole.contains(this.ldapGroupAdmins)) {
					httpServletRequest.getSession().setAttribute(
							TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), this.ldapGroupAdmins);

				} else if (userRole.contains(this.ldapGroupProgramAdmins)) {
					httpServletRequest.getSession().setAttribute(
							TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), this.ldapGroupProgramAdmins);
					manageCompanies(httpServletRequest, userRole, this.ldapGroupProgramAdmins);

				} else if (userRole.contains(this.ldapGroupQuoteAdmins)) {
					httpServletRequest.getSession().setAttribute(
							TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), this.ldapGroupQuoteAdmins);
					manageCompanies(httpServletRequest, userRole, this.ldapGroupQuoteAdmins);

				} else if (userRole.contains(this.ldapGroupUnderwritter)) {
					httpServletRequest.getSession().setAttribute(
							TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), this.ldapGroupUnderwritter);
					manageCompanies(httpServletRequest, userRole, this.ldapGroupUnderwritter);

				} else if (userRole.contains(this.ldapGroupBroker)) {
					httpServletRequest.getSession().setAttribute(
							TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), this.ldapGroupBroker);

					String defaultCompany = null;

					// implement "WebZone-Broker-Advisors-QC" and a "WebZone-Broker-Advisors-ON".
					for (ProvinceCodeEnum provinceCodeEnum : ProvinceHelper.activeProvinces) {
						if (userRole.contains(ProvinceHelper.AddProvinceSuffix(this.ldapGroupBroker, provinceCodeEnum))) {
							defaultCompany = ProvinceCompanyConverter.convertProvinceToSubBrokerCompany(provinceCodeEnum.getCode());
							break;
						}
					}

					httpServletRequest.getSession().setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(),
							defaultCompany);
					if (defaultCompany == null) {
						manageCompanies(httpServletRequest, userRole, this.ldapGroupBroker);
					}

					// This is for the B2B only
					UserAccount userAccount = this.accountsBusinessProcess.findByUId(userName);
					List<String> selectedMasters = new ArrayList<String>();
					for (BrokerWebOfficeAccess brokerWebOfficeAccess : userAccount.getBrokerWebOfficeAccesses()) {
						// Only add valid master owner codes
						if (this.filterValue(brokerWebOfficeAccess.getMasterOwnerCode().toLowerCase()) != null) {
							selectedMasters.add(brokerWebOfficeAccess.getMasterOwnerCode());
						}
					}
					httpServletRequest.getSession().setAttribute(
							SessionConstantsEnum.AVAILABLE_MASTERS.getSessionConstant(), selectedMasters);
				} else if (userRole.contains(this.ldapGroupBrokerReassign)) {

					httpServletRequest.getSession().setAttribute(
							TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), this.ldapGroupBrokerReassign);

					String defaultCompany = null;

					// implement "WebZone-Broker-Advisors-QC" and a "WebZone-Broker-Advisors-ON".
					for (ProvinceCodeEnum provinceCodeEnum : ProvinceHelper.activeProvinces) {
						if (userRole.contains(ProvinceHelper.AddProvinceSuffix(this.ldapGroupBrokerReassign,
								provinceCodeEnum))) {
							defaultCompany = ProvinceCompanyConverter.convertProvinceToSubBrokerCompany(provinceCodeEnum.getCode());
							break;
						}
					}

					httpServletRequest.getSession().setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(),
							defaultCompany);
					if (defaultCompany == null) {
						manageCompanies(httpServletRequest, userRole, this.ldapGroupBrokerReassign);
					}
					// This is for the B2B only
					UserAccount userAccount = this.accountsBusinessProcess.findByUId(userName);
					List<String> selectedMasters = new ArrayList<String>();
					for (BrokerWebOfficeAccess brokerWebOfficeAccess : userAccount.getBrokerWebOfficeAccesses()) {
						// Only add valid master owner codes
						if (this.filterValue(brokerWebOfficeAccess.getMasterOwnerCode().toLowerCase()) != null) {
							selectedMasters.add(brokerWebOfficeAccess.getMasterOwnerCode());
						}
					}
					httpServletRequest.getSession().setAttribute(
							SessionConstantsEnum.AVAILABLE_MASTERS.getSessionConstant(), selectedMasters);
				}

			}
		}

		/*
		 * If the classicId was not found we don't want it in the session.
		 */
		if (StringUtils.isNotEmpty(classicId) && !classicId.equalsIgnoreCase("NOT_FOUND")) {
			httpServletRequest.getSession().setAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue(),
					classicId);
		}

		/*
		 * If the halcionId was not found we don't want it in the session.
		 */
		// if (StringUtils.isNotEmpty(halcionId) && !halcionId.equalsIgnoreCase("NOT_FOUND")) {
		// }


		// PM11018 - Create a cookie to keep the broker default province
		if (this.provinceController != null) {
			this.provinceController.reset();
		}


		Cookie newCookie = this.getProvinceController().buildBrokerDefaultCompany((String) httpServletRequest.getSession().getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant()));
		((HttpServletResponse) response).addCookie(newCookie);

	}

	public void manageCompanies(HttpServletRequest httpServletRequest, String userRole, String ldapSecurityGroup) {
		int nbProvinces = ProvinceHelper.manageCompanies(httpServletRequest.getSession(), userRole, ldapSecurityGroup);
		if (nbProvinces > 1) {
			retrieveDefaultCompany(httpServletRequest);
		}
	}

	/**
	 * retrieves the cookie for the default company BR5787 - Use st Attribute in TAM as Default Region:
	 */
	private void retrieveDefaultCompany(HttpServletRequest httpServletRequest) {

		String userId = httpServletRequest.getHeader(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue());
		String defaultCompany = null;
		// COOKIE TO SET THE DEFAULT COMPANY
		if (ProvinceController.searchForBrokerDefaultCompany(httpServletRequest) != null) {
			Cookie aCookie = ProvinceController.searchForBrokerDefaultCompany(httpServletRequest);
			defaultCompany = aCookie.getValue();
		}

		// ST PARAMETER TO SET THE DEFAULT COMPANY
		else {
			defaultCompany = ProvinceCompanyConverter.convertProvinceToSubBrokerCompany(this.accountsBusinessProcess.getDefaultProvince(userId));
			// manage scenario WHERE ST is NULL
			if (defaultCompany == null) {
				List<String> companiesAvailable = (List<String>) httpServletRequest.getSession().getAttribute(
						SessionConstantsEnum.AVAILABLE_COMPANIES.getSessionConstant());
				if (companiesAvailable != null && !companiesAvailable.isEmpty()) {
					defaultCompany = companiesAvailable.get(0);
				}
			}
		}

		// In rare case of no province found, then the default one will be quebec
		if (defaultCompany == null) {
			defaultCompany = (String) httpServletRequest.getSession().getAttribute(
					SessionConstantsEnum.COMPANY.getSessionConstant()) == null ? CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber()
					: (String) httpServletRequest.getSession().getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant());
		}
		try {
			defaultCompany = this.getProvinceController().filterValue(defaultCompany);

			httpServletRequest.getSession().setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(),
					defaultCompany);
		} catch (Exception e) {
			log.error(e.getMessage());
			e.printStackTrace();
		}
	}

}
