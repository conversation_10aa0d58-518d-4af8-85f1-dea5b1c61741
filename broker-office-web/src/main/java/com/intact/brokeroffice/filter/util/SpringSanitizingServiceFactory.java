package com.intact.brokeroffice.filter.util;

import com.intact.tools.common.exception.FactoryException;
import com.intact.tools.sanitization.service.SanitizationApplicationService;
import com.intact.tools.spring.service.SpringApplicationService;

public class SpringSanitizingServiceFactory extends SanitizingServiceFactory {

	public SanitizationApplicationService getSanitizingApplicationService(String sanitizingService)
			throws FactoryException {

		SanitizationApplicationService service = null;

		try {
			service = (SanitizationApplicationService) SpringApplicationService
					.getInstance("Sanitizing Service Factory", "tools-sanitization-service.xml")
					.getObject("service.sanitizing." + sanitizingService);
		} catch (Exception ex) {
			throw new FactoryException(this.toString(), ex);
		}

		return service;
	}

	public String toString() {
		return "Spring Security Service Factory";
	}
}
