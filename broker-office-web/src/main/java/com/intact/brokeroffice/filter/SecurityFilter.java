/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.filter;

import java.io.IOException;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

import org.apache.commons.lang.StringUtils;
import org.springframework.web.context.WebApplicationContext;
import org.springframework.web.context.support.WebApplicationContextUtils;

import com.ing.canada.common.web.api.auth.TamTokensEnum;


/**
 * Checks the security of the user and will redirect to the access denied page when necesary
 */
public class SecurityFilter implements Filter {
	private static String BROKER_LIST = "/brokers/subbroker/list.jsf";

	private static String BROKER_MODIFY = "/brokers/subbroker/modify.jsf";

	private static String ACCOUNT_LIST = "/brokers/account/list.jsf";

	private static String ACCOUNT_MODIFY = "/brokers/account/modify.jsf";

	private static String BROKER_CHANGE_REPORT = "/reports/brokerchanges.jsf";

	private static String PERFORMANCE_REPORT = "/reports/performancemetrics.jsf";
	
	private static String ACCESS_DENIED = "accessdenied.jsf";

	private String ldapGroupProgramAdmins;
	private String ldapGroupAdmins;	

	/**
	 * @see jakarta.servlet.Filter#destroy()
	 */
	public void destroy() {
	}

	/**
	 * @see jakarta.servlet.Filter#init(jakarta.servlet.FilterConfig)
	 */
	public void init(FilterConfig arg0) throws ServletException {
		WebApplicationContext springContext = WebApplicationContextUtils.getWebApplicationContext(arg0
				.getServletContext());
		this.ldapGroupProgramAdmins = (String) springContext.getBean("ldap-group-program-admins");
		this.ldapGroupAdmins = (String) springContext.getBean("ldap-group-admins");		
	}

	/**
	 * @see jakarta.servlet.Filter#doFilter(jakarta.servlet.ServletRequest, jakarta.servlet.ServletResponse,
	 *      jakarta.servlet.FilterChain)
	 */
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain filterChain) throws IOException,
			ServletException {
		
		boolean accessIsDenied = false;
		if ((request instanceof HttpServletRequest httpServletRequest) && (response instanceof HttpServletResponse httpServletResponse)) {
			
			String requestPath = httpServletRequest.getRequestURI();
			String role = (String) httpServletRequest.getSession().getAttribute(
					TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue());

			boolean brokerList = StringUtils.contains(requestPath, BROKER_LIST);
			boolean brokerModify = StringUtils.contains(requestPath, BROKER_MODIFY);
			boolean accountList = StringUtils.contains(requestPath, ACCOUNT_LIST);
			boolean accountModify = StringUtils.contains(requestPath, ACCOUNT_MODIFY);
			boolean brokerChangeReport = StringUtils.contains(requestPath, BROKER_CHANGE_REPORT);
			boolean performanceReport = StringUtils.contains(requestPath, PERFORMANCE_REPORT);
		
			/*if ((brokerChangeReport || performanceReport || brokerModify || brokerList) && !role.equals(this.ldapGroupProgramAdmins) && !role.equals(this.ldapGroupAdmins) && !role.equals(this.ldapGroupProgramAdminsON) && !role.equals(this.ldapGroupAdminsON)) {*/
			if ((brokerChangeReport || performanceReport || brokerModify || brokerList) && !role.contains(this.ldapGroupProgramAdmins) && !role.contains(this.ldapGroupAdmins)) {			
				accessIsDenied = true;
			}
			
			if ((accountList || accountModify) && !role.contains(this.ldapGroupAdmins)) {
				accessIsDenied = true;
			}			
			
			if (accessIsDenied) {
				String timeoutUrl = httpServletRequest.getContextPath() + "/" + ACCESS_DENIED;

				// session is invalid return the user to the timeout page
				httpServletResponse.sendRedirect(timeoutUrl);
				return;
			}

		}		
	
		filterChain.doFilter(request, response);
	}
}
