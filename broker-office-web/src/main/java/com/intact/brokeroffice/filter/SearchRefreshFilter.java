package com.intact.brokeroffice.filter;

import java.io.IOException;
import java.util.Calendar;
import java.util.Date;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;

/**
 * This class is used to filter out all access to any file of type XHTML and
 * redirect the user to the index.jsf of the site
 * 
 * <AUTHOR>
 * 
 */
public class SearchRefreshFilter implements Filter {
	/**
	 * @see jakarta.servlet.Filter#destroy()
	 */
	public void destroy() {
	}

	/**
	 * @see jakarta.servlet.Filter#init(jakarta.servlet.FilterConfig)
	 */
	public void init(FilterConfig arg0) throws ServletException {
	}

	/**
	 * @see jakarta.servlet.Filter#doFilter(jakarta.servlet.ServletRequest,
	 *      jakarta.servlet.ServletResponse, jakarta.servlet.FilterChain)
	 */
	public void doFilter(ServletRequest req, ServletResponse resp, FilterChain chain) throws IOException, ServletException {

		if (req.getParameter("quoteListForm:searchPoll") == null) {
			this.setLastAccessTimestamp(req, new Date());
			this.setAutoSearchRefresh(req, true);
		} else {
			if (this.getLastAccessTimestamp(req) == null) {
				this.setLastAccessTimestamp(req, new Date());
				this.setAutoSearchRefresh(req, true);
			}

			if (this.isMoreThanSessionTimeout(req)) {
				this.setAutoSearchRefresh(req, false);
			}

		}

		chain.doFilter(req, resp);
	}

	protected boolean isMoreThanSessionTimeout(ServletRequest req) {

		Calendar now = Calendar.getInstance();

		long delta = now.getTime().getTime() - this.getLastAccessTimestamp(req).getTime();

		delta = delta / 1000;

		return delta > this.getSessionTimeout(req);
	}

	protected int getSessionTimeout(ServletRequest req) {

		int sessionTimeout = 0;

		if (req instanceof HttpServletRequest request && request.getSession(false) != null) {
			sessionTimeout = request.getSession(false).getMaxInactiveInterval();
		}

		return sessionTimeout;

	}

	protected Date getLastAccessTimestamp(ServletRequest req) {
		Date timestamp = null;

		if (req instanceof HttpServletRequest request && request.getSession(false) != null) {
			timestamp = (Date) request.getSession(false).getAttribute("lastRefreshTimestamp");
		}

		return timestamp;
	}

	protected void setLastAccessTimestamp(ServletRequest req, Date timestamp) {

		if (req instanceof HttpServletRequest request && request.getSession(false) != null) {
			request.getSession(false).setAttribute("lastRefreshTimestamp", timestamp);
		}
	}

	protected Date getAutoSearchRefresh(ServletRequest req) {
		Date timestamp = null;

		if (req instanceof HttpServletRequest request && request.getSession(false) != null) {
			timestamp = (Date) request.getSession(false).getAttribute("autoSearchRefresh");
		}

		return timestamp;
	}

	protected void setAutoSearchRefresh(ServletRequest req, boolean value) {

		if (req instanceof HttpServletRequest request && request.getSession(false) != null) {
			request.getSession(false).setAttribute("autoSearchRefresh", value);
		}
	}
}
