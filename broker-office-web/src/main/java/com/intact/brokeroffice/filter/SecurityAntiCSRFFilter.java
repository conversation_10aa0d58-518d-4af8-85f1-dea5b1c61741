package com.intact.brokeroffice.filter;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.ResourceBundle;
import java.util.Set;
import java.util.StringTokenizer;

import jakarta.servlet.Filter;
import jakarta.servlet.FilterChain;
import jakarta.servlet.FilterConfig;
import jakarta.servlet.ServletException;
import jakarta.servlet.ServletRequest;
import jakarta.servlet.ServletResponse;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;

public class SecurityAntiCSRFFilter implements Filter {

	protected static final String SECURITY_ANTI_CSRF_SESSION = "SECURITY_ANTI_CSRF_ATTRIBUTE";
	public static final String SECURITY_ANTI_CSRF_REQUEST = "AntiCSRFToken";
	// List of URL from where the CSRFToken should be present
	private HashMap<String, List<CSRFUrl>> pathList = new HashMap<String, List<CSRFUrl>>();

	@Override
	public void destroy() {
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response, FilterChain chain)
			throws IOException, ServletException {

		// If in use

		if (request instanceof HttpServletRequest req) {

			// Get the servlet PATH
			String servletPath = req.getServletPath(); 
			
			// If we have a Path
			if (servletPath != null) {

				// Get Current configuration for this path
				List<CSRFUrl> theList = pathList.get(servletPath.toUpperCase());

				// If we have configuration
				if (theList != null) {
					boolean found = false;

					// Go Through Configuration
					for (int curs = 0; curs < theList.size(); curs++) {
						// Get current configuration
						CSRFUrl current = theList.get(curs);
						// Do we have a match
						if (current.match(req)) {
							found = true;
							break;
						}
					}

					if (found) {

						// CHECK IF SESSION EXISTS
						HttpSession ses = req.getSession();
						if (ses == null) {
							throw new ServletException("IntactCSRFToken check - Invalid Session");
						}

						// VALIDATE THE TOKEN

						if (!this.validateAntiCSRFValue(req)) {

							throw new ServletException(
									"IntactCSRFToken check - Token in request and in session does not match");
						}

					}
				}
			}

		}

		chain.doFilter(request, response);
	}

	public boolean validateAntiCSRFValue(HttpServletRequest request) {

		String reqParam = request.getParameter(SECURITY_ANTI_CSRF_REQUEST);

		if (reqParam == null) {
			Enumeration<String> theList = request.getParameterNames();

			while (theList.hasMoreElements()) {
				String cur = theList.nextElement();

				if (cur.endsWith(SECURITY_ANTI_CSRF_REQUEST)) {
					reqParam = request.getParameter(cur);
					break;
				}
			}
		}

		String sessionParam = (String) (request.getSession().getAttribute(SECURITY_ANTI_CSRF_SESSION));
		
		return reqParam.equals(sessionParam);
	}

	@Override
	public void init(FilterConfig config) throws ServletException {

		pathList = new HashMap<String, List<CSRFUrl>>();

		String theBundle = config.getInitParameter("BundleFile");
		ResourceBundle bundle = ResourceBundle.getBundle(theBundle);
		Set<String> keys = bundle.keySet();

		Iterator<String> iterator = keys.iterator();

		while (iterator.hasNext()) {
			String key = iterator.next();
			String value = bundle.getString(key);
			StringTokenizer tokenizer = new StringTokenizer(value, "|");
			int count = tokenizer.countTokens();
			if (count < 2) {
				continue;
			}
			String path = tokenizer.nextToken().toUpperCase();
			String method = tokenizer.nextToken();
			String param = null;
			String paramValue = null;

			if (count > 2) {
				param = tokenizer.nextToken();
				paramValue = "*";
			}

			if (count > 3) {
				paramValue = tokenizer.nextToken();
			}

			CSRFUrl current = new CSRFUrl(method, param, paramValue);
			List<CSRFUrl> theList = pathList.get(path);

			if (theList == null) {
				List<CSRFUrl> newList = new ArrayList<CSRFUrl>();
				newList.add(current);
				pathList.put(path, newList);
			} else {
				theList.add(current);
			}

		}
	}

	private class CSRFUrl {

		private String method;

		private String param;

		private String paramValue;

		public boolean match(HttpServletRequest request) {

			// Method Check
			String reqMethod = request.getMethod().toUpperCase().trim();

			if (reqMethod == null) {
				return false;
			} else {
				if (!method.equals("*") && !reqMethod.equals(method)) {
					return false;
				}
			}

			// Param Check
			if (param != null) {
				String paramCheck = request.getParameter(param);

				if (paramCheck == null) {
					return false;
				} else {
					if (paramValue == null || paramValue.equals("*")) {
						return true;
					} else {
						return paramValue.equalsIgnoreCase(paramValue);
					}
				}
			}

			return true;

		}

		public CSRFUrl(String method, String param, String paramValue) {
			super();
			this.method = method.trim().toUpperCase();

			if (param != null) {
				this.param = param.trim();
			} else {
				this.param = null;
			}

			if (paramValue != null) {
				this.paramValue = paramValue.trim();
			} else {
				this.paramValue = null;
			}
		}
	}

}
