package com.intact.brokeroffice.dao.web;

import java.util.List;

import jakarta.faces.context.FacesContext;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.servlet.http.HttpSession;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.common.web.api.auth.TamTokensEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;
import com.intact.brokeroffice.dao.SystemDAO;

@Component
public class WebSystemDAO implements SystemDAO {

	private static String URL_TO_GOBRIO_B2E = "url.to.gobrio";

	private static String URL_TO_GOBRIO_B2B = "url.to.gobrio.b2b";

	/** The ldap group broker. */
	@Autowired
	@Qualifier("ldap-group-broker")
	private String ldapGroupBroker;

	@Autowired
	@Qualifier("ldap-group-broker-reassign")
	private String ldapGroupBrokerReassign;

	public WebSystemDAO() {

	}

//	@Override
//	public String getProvince() {
//
//		HttpSession session = this.getSession();
//
//		return session.getAttribute(SessionConstantsEnum.PROVINCE.getSessionConstant()) == null ? ProvinceCodeEnum.QUEBEC
//				.getCode() : (String) session.getAttribute(SessionConstantsEnum.PROVINCE.getSessionConstant());
//	}

	@Override
	public String getUploadUser() {

		String user = null;
		HttpSession session = this.getSession();
		
		String company = this.getCompany();

		String classicId = (String) session.getAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue());
		String halcionId = (String) session.getAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue());

		if (classicId != null && company.equals(CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber())) {
			user = classicId;
		} else if (halcionId != null) {
			user = halcionId;
		}

		return user;
	}

	@Override
	public String getUploadSystem() {

		String system = "";
		HttpSession session = this.getSession();

		String company = this.getCompany();

		String classicId = (String) session.getAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue());
		String halcionId = (String) session.getAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue());

		if (classicId != null && company.equals(CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber())) {
			system = "classic";
		} else if (halcionId != null) {
			system = "halcion";
		}

		return system;
	}

	@Override
	public String getSourceUser() {
		return (String) this.getSession().getAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue());
	}

	@Override
	public String getLanguage() {
		return (String) this.getSession().getAttribute(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant());
	}

	@Override
	public String getRemoteSystemUrl() {

		String remoteSystemUrl = null;

		if (this.getUploadSystem().equals("classic")) {
			remoteSystemUrl = this.isMasterRole() ? URL_TO_GOBRIO_B2E : URL_TO_GOBRIO_B2B;
		}

		return remoteSystemUrl;
	}

	protected boolean isMasterRole() {
		return getCurrentAccessLevel() != null && !getCurrentAccessLevel().startsWith(this.ldapGroupBroker)
				&& !getCurrentAccessLevel().startsWith(this.ldapGroupBrokerReassign);
	}

	protected String getCurrentAccessLevel() {
		return (String) this.getSession().getAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue());
	}

	public String getMasterBroker() {

		String broker = null;

		List<String> brokers = (List<String>) this.getSession().getAttribute(
				SessionConstantsEnum.AVAILABLE_MASTERS.getSessionConstant());

		if (brokers != null && brokers.size() > 0) {
			broker = brokers.get(0);
		}

		return broker;
	}

	public String getCompany() {

		HttpSession session = this.getSession();

		return session.getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant()) == null ? CifCompanyEnum.INTACT_QC
				.getSubBrokerCompanyNumber() : (String) session.getAttribute(SessionConstantsEnum.COMPANY.getSessionConstant());
		
//		if ("QC".equals(province)) {
//			company = CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber();
//		} else if ("ON".equals(province)) {
//			company = CifCompanyEnum.INTACT_HALIFAX.getSubBrokerCompanyNumber();
//		} else if ("AB".equals(province)) {
//			company = CifCompanyEnum.INTACT_AB.getSubBrokerCompanyNumber();
//		} else {
//			company = CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber();
//		}

	}

	protected HttpSession getSession() {
		return (HttpSession) FacesContext.getCurrentInstance().getExternalContext().getSession(false);
	}

}
