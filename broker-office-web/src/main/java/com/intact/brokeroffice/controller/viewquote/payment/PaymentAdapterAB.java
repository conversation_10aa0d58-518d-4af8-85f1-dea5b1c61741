package com.intact.brokeroffice.controller.viewquote.payment;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ing.canada.common.domain.QuoteCalculationDetails;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.business.rules.offer.BR6418_MonthlyPaymentAvailable;
import com.intact.business.rules.quote.BR5883_HasFourPercentageSurcharge;
import com.intact.business.rules.quote.BR5883_HasTwoPercentageSurcharge;

@Component
public class PaymentAdapterAB extends PaymentAdapter<PaymentBean>  {

	private static final double SURCHARGE_4PERC_PERCENTAGE_AB = 0.04;
	private static final double SURCHARGE_2PERC_PERCENTAGE_AB = 0.02;
	
	@Autowired
	private BR5883_HasFourPercentageSurcharge br5883_4perc;
	@Autowired
	private BR5883_HasTwoPercentageSurcharge  br5883_2perc;
	
	@Autowired
	private BR6418_MonthlyPaymentAvailable br6418;
	
	@Override
	protected void loadPremiumInformation(PaymentBean aPaymentBean,
			QuoteCalculationDetails quoteCalculationDetails) {

		aPaymentBean.setAnnualAmountWithTaxes(quoteCalculationDetails.getFullTermPremiumWithTaxesAndSurcharge());
		aPaymentBean.setMonthlyPremium(quoteCalculationDetails.getMonthlyPaymentWithTaxesAndSurcharge());
		
	}
	
	/**
	 * @see com.intact.brokeroffice.controller.viewquote.payment.IPaymentAdapter#loadPaymentBean(
	 * 		com.ing.canada.common.domain.QuoteCalculationDetails,
	 *      com.intact.brokeroffice.controller.payment.PaymentBean, 
	 *      com.ing.canada.plp.domain.policyversion.PolicyVersion,
	 *      com.ing.canada.plp.domain.enums.ProvinceCodeEnum
	 *      )
	 */
	
	public void loadPaymentBean(PaymentBean aPaymentBean, PolicyVersion aPolicyVersion)	
	{		
		super.loadPaymentBean(aPaymentBean, aPolicyVersion, ProvinceCodeEnum.ALBERTA);	
		validateMonthlyPayment(aPaymentBean, aPolicyVersion);
		this.loadMethodPaymentBean(aPaymentBean,  aPolicyVersion);
	}
	/* (non-Javadoc)
	 * @see com.intact.brokeroffice.controller.viewquote.payment.PaymentAdapter#applySpecificCalculationRules(com.ing.canada.common.domain.QuoteCalculationDetails, com.ing.canada.plp.domain.policyversion.PolicyVersion)
	 */
	@Override
	protected void applySpecificCalculationRules(QuoteCalculationDetails quotationDetails,PolicyVersion policyVersion) {	
		this.quotationService.calculateFirstMonthlyPayment(quotationDetails, 1);
		
		if ( br5883_4perc.validate(policyVersion) && validateBillingPlan(policyVersion, ProvinceCodeEnum.ALBERTA)) {
			this.quotationService.addSurchargeToQuoteDetails(quotationDetails, SURCHARGE_4PERC_PERCENTAGE_AB, true);
		}
		else  {
			if ( br5883_2perc.validate(policyVersion) && validateBillingPlan(policyVersion, ProvinceCodeEnum.ALBERTA)) {
				this.quotationService.addSurchargeToQuoteDetails(quotationDetails, SURCHARGE_2PERC_PERCENTAGE_AB, true);
			}
			else {
				this.quotationService.addSurchargeToQuoteDetails(quotationDetails, 0, false);
			}
		}
		
		quotationDetails.setMonthlyPaymentWithTaxes(quotationDetails.getMonthlyPaymentWithTaxesAndSurcharge());
		quotationDetails.setAnnualAmountWithTaxes(quotationDetails.getFullTermPremiumWithTaxesAndSurcharge());

	}
	
	private void validateMonthlyPayment(PaymentBean aPaymentBean, PolicyVersion aPolicyVersion){
		
		//BR263_1 is already checked in QuotationService
		//BR6418 need to be checked if BR263_1 is true
		if(aPaymentBean.isMonthlyPaymentsEligible()){
			boolean monthlyPaymentAvailable = this.br6418.validate(aPolicyVersion);
			aPaymentBean.setMonthlyPaymentsEligible(monthlyPaymentAvailable);			
		}		
	}

}
