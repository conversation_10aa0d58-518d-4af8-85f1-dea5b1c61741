package com.intact.brokeroffice.controller.search;

import com.ing.canada.plp.report.insurancePolicy.criteria.QuoteSearchCriteria;
import com.intact.brokeroffice.business.domain.BrokerQuoteSearchCriteria;
import com.intact.brokeroffice.helper.FaceMessageHelper;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;
import java.util.regex.Pattern;

@Component
public class SearchQuoteValidator {

	private boolean showErrorMessage = true;

	private static String POSTALCODE_REGEXP = "^([A-Z]\\d)(([A-Z]\\d){0,2}([A-Z]){0,1})$";

	// [A-Z]\\ d [A-Z]\d [A-Z]\d
	// complex validation
	// "^([ABCEGHJKLMNPRSTVXY]\\d)(([ABCEGHJKLMNPRSTVWXYZ]\\d){0,2}([ABCEGHJKLMNPRSTVWXYZ]){0,1})$";

	private static String TELEPHONE_REGEXP = "\\d{10}";

	public boolean validate(BrokerQuoteSearchCriteria searchBean) {
		return validate(searchBean, null);
	}

	/**
	 * validate method
	 * 
	 * @param searchBean
	 * @return
	 */
	public boolean validate(BrokerQuoteSearchCriteria searchBean, SearchController searchController) {

		boolean valid = true;
		
		/** do not validate when this option is selected **/
		if(searchBean.isClosedBrokerQuotes()) {
			return valid;
		}

		if (searchController != null && !searchController.getCheckSearchAccess()) {
			searchBean.setCreationDateFrom(null);
			searchBean.setCreationDateTo(null);
			searchBean.setFrom(null);
			searchBean.setTo(null); 
			searchBean.setSelectedClientFollowUp(null);
			searchBean.setSelectedQuoteSource(null);
			searchBean.setSelectedQuoteStatus(null);
			searchBean.setClosedBrokerQuotes(false); 
		}

		// validate that at least one of the following mandatory fields is not
		// empty
		if (!isMandatory(searchBean, searchController)) {
			// MSG498
			if (this.showErrorMessage) {
				FaceMessageHelper.addErrorMessage(null,
						(searchController != null && searchController.getCheckSearchAccess() ? "search.select.mandatory.info.error.msg"
								: "search.select.mandatory.info.error.min.msg"), this);
			}

			return false;
		}

		// validate exclusive usage of business name | first and last name
		valid = validateNames(searchBean);

		// Validate the lines of business
		valid = valid & validateLinesOfBusiness(searchBean);

		// Validate the lines of insurance
		valid = valid & validateLinesOfInsurance(searchBean);

		// validate postal code
		valid = valid & validatePostalCode(searchBean.getPostalCode());

		// validate phone number
		valid = validatePhoneNumber(searchBean, valid);

		// validate creation dates
		valid = validateDates(searchBean.getCreationDateFrom(), searchBean.getCreationDateTo(), "mainTab:quoteSearchForm:createFrom", "mainTab:quoteSearchForm:createTo", valid, searchController);

		// validate last update dates
		valid = validateDates(searchBean.getFrom(), searchBean.getTo(), "mainTab:quoteSearchForm:calFrom", "mainTab:quoteSearchForm:calTo", valid, searchController);

		return valid;
	}

	/**
	 * Validates postal code BR5159 The complete postal code must be entered.
	 * BR5160 The field will not accept more than 6 alphanumeric characters
	 * BR5161 Postal codes must be entered in the appropriate format BR5162 If
	 * the format is incorrect, display the Postal Code error message BR5178
	 * Field Validation
	 * 
	 * @param aPostalCode
	 * @param valid
	 * @return true, if successful
	 */
	protected boolean validatePostalCode(String aPostalCode) {
		// last update from must be < = today date
		if (!StringUtils.isBlank(aPostalCode)) {
			String postalCode = StringUtils.deleteWhitespace(aPostalCode).toUpperCase();
			if (postalCode.length() != 6 || !Pattern.matches(POSTALCODE_REGEXP, postalCode)) {

				// DE29/MSG213
				if (this.showErrorMessage) {
					// MSG213 Invalid Postal Code Format:
					FaceMessageHelper.addErrorMessage("mainTab:quoteSearchForm:postalCode", "search.postal.code.error.msg", this);
				}
				return false;
			}
		}
		return true;
	}

	/**
	 * Validate LastUpdate or Creation Date BR5168 The selected date must be
	 * less than or equal to the Last Update (To) date otherwise display date
	 * range error message. BR5593 Search Quotes - Last update period BR5169 The
	 * selected date cannot be in the future otherwise display future date error
	 * message BR5178 Field Validation: BR5179 When user clicks on the button,
	 * validates the data entered and if there are no errors detected, user is
	 * redirected to the Search Results page displaying all records that match
	 * the search criteria
	 * 
	 * @param aDateFrom
	 *            :::: aLastUpdateFrom / aCreationDateFrom
	 * @param aDateTo
	 *            :::: aLastUpdateTo / aCreationDateTo
	 * @param aDomIdFrom
	 * @param aDomIdTo
	 * @param valid
	 * @return true, if successful
	 */
	protected boolean validateDates(Date aDateFrom, Date aDateTo, String aDomIdFrom, String aDomTo, boolean valid, SearchController searchController) {

		boolean validReturn = valid;
		// creation date from must be selected
		if (aDateFrom == null && aDateTo != null) {
			// DE717/MSGxxx
			if (this.showErrorMessage) {
				FaceMessageHelper.addErrorMessage(aDomIdFrom, "search.enter.both.dates.error.msg", this);
			}
			validReturn = false;
		}

		// creation date to must be selected
		if (aDateTo == null && aDateFrom != null) {
			// DE717/MSGxxx
			if (this.showErrorMessage) {
				FaceMessageHelper.addErrorMessage(aDomTo, "search.enter.both.dates.error.msg", this);
			}
			validReturn = false;
		}

		// creation date from must be < = today date
		if (aDateFrom != null && aDateFrom.compareTo(new Date()) > 0) {
			// DE717/MSG500
			if (this.showErrorMessage) {
				FaceMessageHelper.addErrorMessage(aDomIdFrom, "search.future.date.error.msg", this);
			}
			validReturn = false;
		}

		// creation date to must be < = today date
		if (aDateTo != null && aDateTo.compareTo(new Date()) > 0) {
			// DE717/MSG500
			if (this.showErrorMessage) {
				FaceMessageHelper.addErrorMessage(aDomTo, "search.future.date.error.msg", this);
			}
			validReturn = false;
		}

		// creation date from
		if (aDateFrom != null && aDateTo != null && aDateFrom.compareTo(aDateTo) > 0) {
			// DE717/MSG499
			if (this.showErrorMessage) {
				FaceMessageHelper.addErrorMessage(aDomIdFrom, "search.date.range.error.msg", this);
			}
			validReturn = false;
		}
		
		// date must not be more than X days in the past
		int nbdays = searchController.isCompany6() ? 60 : 30;	
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DAY_OF_MONTH, -nbdays);
		if (aDateFrom != null && aDateTo != null && (aDateFrom.compareTo(calendar.getTime()) < 0 || aDateTo.compareTo(calendar.getTime()) < 0)) {
			// DE717/MSGXXX
			if (this.showErrorMessage) { 
				FaceMessageHelper.addErrorMessage(aDomIdFrom, "search.select.maximum.range.past.msg", this);
				FaceMessageHelper.addErrorMessage(null, "search.select.maximum.range.error", this);
			}
			validReturn = false;
		}
		
		
		
		return validReturn;
	}

	/**
	 * Validate a phone number field BR5163 If this field is entered, the
	 * complete phone number must be entered. BR5164 The exact number of digits
	 * must be entered in each field as follows: Area code = 3 Exchange = 3
	 * Number = 4 BR5165 If less than the required number of digits are entered
	 * in any of the three input areas, display the Phone Number Validation
	 * error message. BR5178 Field Validation
	 * 
	 * @param searchBean
	 *            the SearchBean
	 * @param valid
	 *            the valid
	 * 
	 * @return true, if successful
	 */

	protected boolean validatePhoneNumber(QuoteSearchCriteria searchBean, boolean valid) {
		if (this.getPhoneEmpty(searchBean)) {
			return valid;
		}

		if ((!StringUtils.isBlank(searchBean.getPhoneAreaCode()) && searchBean.getPhoneAreaCode().length() < 3)
				|| (!StringUtils.isBlank(searchBean.getPhoneNumberPrefix()) && searchBean.getPhoneNumberPrefix().length() < 3)
				|| (!StringUtils.isBlank(searchBean.getPhoneNumberSuffix()) && searchBean.getPhoneNumberSuffix().length() < 4)) {
			// DE191/DE192/DE193/MSG445
			if (this.showErrorMessage) {
				FaceMessageHelper.addErrorMessage("mainTab:quoteSearchForm:phoneNumberSuffix", "search.phone.number.validation.error.msg", this);
			}
			return false;
		}

		String telephoneEntered = searchBean.getPhoneAreaCode().concat(searchBean.getPhoneNumberPrefix()).concat(searchBean.getPhoneNumberSuffix());

		if (!StringUtils.isBlank(telephoneEntered) && !Pattern.matches(TELEPHONE_REGEXP, telephoneEntered)) {
			// DE191/DE192/DE193/MSG445
			if (this.showErrorMessage) {
				FaceMessageHelper.addErrorMessage("mainTab:quoteSearchForm:phoneNumberSuffix", "search.phone.number.validation.error.msg", this);
			}
			return false;
		}

		return valid;
	}

	/**
	 * Validate master broker
	 * 
	 * @param value
	 * @param valid
	 * @return
	 */
	protected boolean validateMasterBroker(String value, boolean valid) {
		if (StringUtils.isBlank(value)) {
			// DE692/MSG498
			if (this.showErrorMessage) {
				// MSG498 Error: Select a broker:
				FaceMessageHelper.addErrorMessage("mainTab:quoteSearchForm:master", "search.select.a.broker.error.msg", this);
			}
			return false;
		}
		return valid;
	}

	public boolean isShowErrorMessage() {
		return this.showErrorMessage;
	}

	public void setShowErrorMessage(boolean aShowErrorMessage) {
		this.showErrorMessage = aShowErrorMessage;
	}

	/*
	 * Validate reference number
	 * 
	 * @param value
	 * 
	 * @param valid
	 * 
	 * @return
	 * 
	 * 
	 * 
	 * private boolean validateReferenceNumber(String value, boolean valid){
	 * 
	 * if(!StringUtils.isBlank(value) && value.length() > 13){
	 * if(this.showErrorMessage){
	 * FaceMessageHelper.addErrorMessage("quoteSearchForm:referenceNo",
	 * "error.referenceNo", this); } valid = false; } return valid; }
	 */

	/**
	 * Validate an email field
	 * 
	 * @param anEmail
	 * @param valid
	 * @return true, if successful
	 * 
	 *         private boolean validateEmail(String anEmail, boolean valid) {
	 * 
	 *         // The proper email address format must be [C]@[C].[C].
	 *         if(!StringUtils.isBlank(anEmail) &&
	 *         (!GenericValidator.isEmail(anEmail.trim()) || anEmail.length() >
	 *         73)) { FaceMessageHelper.addErrorMessage("quoteSearchForm:email",
	 *         "error.email.message", this); return false; } return valid; }
	 */

	/**
	 * This method verifies that a field is not empty by introspection.
	 * 
	 * @param errorId
	 *            the errorId
	 * @param field
	 *            the field
	 * @param valid
	 *            the valid
	 * @param subBrokerBean
	 *            the sub broker bean
	 * 
	 * @return true, if successful
	 * 
	 * @throws Exception
	 *             the exception
	 * 
	 *             private boolean validateMissingInfo(String errorId, String
	 *             field, boolean valid, SearchBean aSearchBean) throws
	 *             Exception {
	 * 
	 *             try { Method method = aSearchBean.getClass().getMethod("get"
	 *             + StringUtils.capitalize(field)); String value = (String)
	 *             method.invoke(aSearchBean);
	 * 
	 *             if (StringUtils.isBlank(value)) {
	 *             FaceMessageHelper.addErrorMessage("quoteSearchForm:" +
	 *             errorId, "error.missingInfo", this); return false; } } catch
	 *             (NoSuchMethodException nsme) { throw new
	 *             Exception("Could not find the approriate getter for " +
	 *             field, nsme); } catch (InvocationTargetException ex) { throw
	 *             new Exception("Error while invoking the getter method", ex);
	 *             } catch (IllegalAccessException ex) { throw new
	 *             Exception("Access error on method", ex); }
	 * 
	 *             return valid; }
	 */

	/**
	 * Checks if phone number is empty
	 * 
	 * @param searchBean
	 * @return
	 */
	private boolean getPhoneEmpty(QuoteSearchCriteria searchBean) {

		return searchBean.getPhoneAreaCode() == null && searchBean.getPhoneNumberPrefix() == null && searchBean.getPhoneNumberSuffix() == null;
	}

	private boolean validatePhoneEmpty(QuoteSearchCriteria searchBean) {
		return StringUtils.isBlank(searchBean.getPhoneAreaCode()) && StringUtils.isBlank(searchBean.getPhoneNumberPrefix())
				&& StringUtils.isBlank(searchBean.getPhoneNumberSuffix());
	}

	/**
	 * Checks that at least one of the following mandatory fields is not empty
	 * Telephone, agreementNumber, last name & first name, business name, postal code, email
	 * address, 7 days last update range, 7 days creation range BR5591 Search
	 * Quotes - Mandatory criteria
	 * 
	 * @param searchBean
	 * @return
	 */
	public boolean isMandatory(QuoteSearchCriteria searchBean, SearchController searchController) {
		return !validatePhoneEmpty(searchBean) || // Telephone
				(!StringUtils.isBlank(searchBean.getSelectedMaster()) && !"all".equals(searchBean.getSelectedMaster())) || // Master
				(!StringUtils.isBlank(searchBean.getLastName()) && !StringUtils.isBlank(searchBean.getFirstName())) || // Last
				!StringUtils.isBlank(searchBean.getBusinessName()) || // Business name
				!StringUtils.isBlank(searchBean.getPostalCode()) || // Postal
				!StringUtils.isBlank(searchBean.getEmail()) || // Email
				!StringUtils.isBlank(searchBean.getAgreementNumber()) || // Agreement
				this.isSevenDaysRangeLastUpdate(searchBean, searchController) || // 7 days range
				this.isSevenDaysRangeCreation(searchBean, searchController); // 7 days range
	}

	private boolean isSevenDaysRangeLastUpdate(QuoteSearchCriteria searchBean, SearchController searchController) {

		if (searchBean.getFrom() == null && searchBean.getTo() == null) {
			return false;
		}

		boolean validInfo = validateDates(searchBean.getFrom(), searchBean.getTo(), "mainTab:quoteSearchForm:calFrom", "mainTab:quoteSearchForm:calTo", true, searchController);

		if (validInfo) {

			long startTime = searchBean.getFrom().getTime();
			long endTime = searchBean.getTo().getTime();
			long diffTime = endTime - startTime;
			long diffDays = diffTime / (1000 * 60 * 60 * 24);

			return diffDays < 7;
		}
		return validInfo;
	}

	private boolean isSevenDaysRangeCreation(QuoteSearchCriteria searchBean, SearchController searchController) {

		if (searchBean.getCreationDateFrom() == null && searchBean.getCreationDateTo() == null) {
			return false;
		}

		boolean validInfo = validateDates(searchBean.getCreationDateFrom(), searchBean.getCreationDateTo(), "mainTab:quoteSearchForm:createFrom",
				"mainTab:quoteSearchForm:createTo", true, searchController);

		if (validInfo) {

			long startTime = searchBean.getCreationDateFrom().getTime();
			long endTime = searchBean.getCreationDateTo().getTime();
			long diffTime = endTime - startTime;
			long diffDays = diffTime / (1000 * 60 * 60 * 24);

			return diffDays < 7;
		}
		return validInfo;
	}


	/**
	 * BR14790 
	 * The search against  the Business Name field must be run independently from the First Name or Last Name fields.  In the event it is used in combination with one 
	 * of these two fields, an error messages will appear. 
	 * @param searchBean
	 * @return
	 */
	public boolean validateNames(QuoteSearchCriteria searchBean) {
		boolean valid = true;

		if (!StringUtils.isBlank(searchBean.getBusinessName())) {
			if (!StringUtils.isBlank(searchBean.getFirstName()) || !StringUtils.isBlank(searchBean.getLastName())) {
				valid = false;
				FaceMessageHelper.addErrorMessage("mainTab:quoteSearchForm:businessName", "search.exclusive.name.type.error.msg", this);
			}
		}

		return valid;
	}


	/**
	 * Ensure that there exists at least one line of business
	 * @param searchBean The search bean
	 */
	private boolean validateLinesOfBusiness(BrokerQuoteSearchCriteria searchBean) {
		if(searchBean.getSelectedLinesOfBusiness() == null || searchBean.getSelectedLinesOfBusiness().length == 0) {
			FaceMessageHelper.addErrorMessage("mainTab:quoteSearchForm:lineOfBusiness", "search.select.mandatory.lineofbusiness.msg", this);
			return false;
		}
		
		return true;
	}
	
	/**
	 * Ensure that there exists at least one line of insurance
	 * @param searchBean The search bean
	 */
	private boolean validateLinesOfInsurance(BrokerQuoteSearchCriteria searchBean) {
		if(searchBean.getSelectedLinesOfInsurance() == null || searchBean.getSelectedLinesOfInsurance().length == 0) {
			FaceMessageHelper.addErrorMessage("mainTab:quoteSearchForm:lineOfInsurance", "search.select.mandatory.lineofinsurance.msg", this);
			return false;
		}
		
		return true;
	}
}
