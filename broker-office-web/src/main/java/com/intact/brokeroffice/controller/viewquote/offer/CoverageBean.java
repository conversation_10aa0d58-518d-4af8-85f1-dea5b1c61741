package com.intact.brokeroffice.controller.viewquote.offer;

import java.util.ArrayList;
import java.util.List;


public class CoverageBean {
	
	private List<VehicleOfferBeanQC> vehicleOfferBeanQCList = new ArrayList<VehicleOfferBeanQC>();	
	private Boolean selected;
	
	/**
	 * Checks if coverage is selected or not
	 * @return Boolean.TRUE if coverage selected or Boolean.FALSE  
	 */
	public Boolean getSelected() {
		return this.selected;
	}
	
	
	/**
	 * Sets selected  
	 * @param aSelected the selected to set
	 */
	public void setSelected(Boolean aSelected) {
		this.selected = aSelected;
	}
	
	
	/**
	 * Gets the VehicleOfferBeanQC objects elements related to a CoverageBean object
	 * @return List 
	 */
	public List<VehicleOfferBeanQC> getVehicleOfferBeanQCList() {
		return this.vehicleOfferBeanQCList;
	}
	
	/**
	 * Sets the VehicleOfferBeanQC objects elements related to a CoverageBean object
	 * @param someVehicleOfferBeanQCList the vehicleOfferBeanQCList to set
	 */
	public void setVehicleOfferBeanQCList(
			List<VehicleOfferBeanQC> someVehicleOfferBeanQCList) {
		this.vehicleOfferBeanQCList = someVehicleOfferBeanQCList;
	}	

}
