package com.intact.brokeroffice.controller;

/**
 * The Class AbstractScrollerController manages the scrolling parameters of the interface.
 */
public abstract class AbstractScrollerController extends AntiCSRFController{
	
	/** The per page values. */
	// BR5101  Limits or expands the number of visible entries per page based on the value selected. 
	private static Integer[] PER_PAGE = new Integer[]{10, 15, 25, 50};
	
	/** The scroller per page. */	
	protected Integer scrollerPerPage = null;
	
	/** The scroller page. */
	private Integer scrollerPage;
	
	private static Integer DEFAULT_SCROLLER_PER_PAGE = 10;
	
	/**
	 * Gets the scroller per page.
	 * 
	 * @return the scroller per page
	 */
	public Integer getScrollerPerPage() {
		
		if(this.scrollerPerPage == null)
		{
			this.setScrollerPage(DEFAULT_SCROLLER_PER_PAGE);
		}
		return this.scrollerPerPage;
	}

	/**
	 * Sets the scroller per page.
	 * 
	 * @param aScrollerPerPage the new scroller per page
	 */
	public void setScrollerPerPage(Integer aScrollerPerPage) {
		this.scrollerPerPage = aScrollerPerPage;
	}
	
	/**
	 * Gets the scroller page.
	 * 
	 * BR5102  Default value is 10.
	 * 
	 * @return the scroller page
	 */
	public Integer getScrollerPage() {
		return this.scrollerPage;
	}
	
	/**
	 * Sets the scroller page.
	 * 
	 * @param scrollerPage the new scroller page
	 */
	public void setScrollerPage(Integer aScrollerPage) {
		this.scrollerPage = aScrollerPage;
	}
	
	/**
	 * Gets the scroller per page list.
	 * 
	 * @return the scroller per page list
	 */
	public Integer[] getScrollerPerPageList() {
        return PER_PAGE;
	}
	
}
