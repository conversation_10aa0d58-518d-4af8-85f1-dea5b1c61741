package com.intact.brokeroffice.controller.common.builder;

import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.intact.brokeroffice.controller.authentification.AuthentificationController;
import com.intact.brokeroffice.controller.language.LanguageController;
import com.intact.brokeroffice.controller.permission.PermissionController;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.business.service.broker.common.domain.UserContext;
import com.intact.business.service.broker.domain.IUserContext;
import com.intact.business.service.broker.domain.Language;
import com.intact.business.service.broker.domain.LineOfBusiness;
import com.intact.business.service.broker.domain.UserType;

import java.util.HashSet;
import java.util.Set;

public class UserContextBuilder {

	public static IUserContext build(AuthentificationController authController,
									 PermissionController permissionController, ProvinceController provinceController,
									 LanguageController languageController) {

		UserContext userContext = new UserContext();

		userContext.setUser(authController.getCurrentAccountUId());
		userContext.setCompany(provinceController.getSelectedCompany() == null ?
									   provinceController.getCompany() : provinceController.getSelectedCompany().getManufacturerCode().getCode());
		userContext.setLanguage(Language.fromCode(languageController.getLocale().getLanguage()));
		userContext.setType(authController.isMasterRole() ? UserType.ADMIN : UserType.BROKER);
		userContext.setLinesOfBusiness(
				UserContextBuilder.buildLinesOfBusiness(permissionController.getAvailableLinesOfBusiness()));

		return userContext;
	}

	private static Set<LineOfBusiness> buildLinesOfBusiness(Set<LineOfBusinessEnum> linesOfBusiness) {
		Set<LineOfBusiness> newBusinesses = new HashSet<>();

		if (linesOfBusiness != null) {
			for (LineOfBusinessEnum lob : linesOfBusiness) {
				newBusinesses.add(LineOfBusiness.fromCode(lob.getCode()));
			}
		}

		return newBusinesses;
	}

}
