package com.intact.brokeroffice.controller.search;

import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.domain.enums.*;
import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.plp.domain.enums.AgreementFollowUpStatusEnum;
import com.ing.canada.plp.report.enums.QuoteStatusCodeEnum;
import com.ing.canada.plp.report.insurancePolicy.criteria.QuoteSearchCriteria;
import com.intact.brokeroffice.business.brokers.IBrokersBusinessProcess;
import com.intact.brokeroffice.business.domain.BrokerQuoteSearchCriteria;
import com.intact.brokeroffice.business.domain.Quote;
import com.intact.brokeroffice.business.quote.QuotesBean;
import com.intact.brokeroffice.business.quote.QuotesDTO;
import com.intact.brokeroffice.controller.AbstractScrollerController;
import com.intact.brokeroffice.controller.authentification.AuthentificationController;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;
import com.intact.brokeroffice.controller.language.LanguageController;
import com.intact.brokeroffice.controller.permission.PermissionController;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.brokeroffice.controller.tabs.QuotesTabController;
import com.intact.brokeroffice.helper.FaceMessageHelper;
import com.intact.brokeroffice.service.IBrokerService;
import com.intact.brokeroffice.service.util.Configuration;
import com.intact.brokeroffice.util.SearchState;
import com.intact.common.security.exception.SecurityUtilityException;
import com.intact.common.security.exception.SecurityUtilityInvalidValueException;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.primefaces.component.api.UIColumn;
import org.primefaces.component.columngroup.ColumnGroup;
import org.primefaces.component.datatable.DataTable;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import jakarta.faces.component.UIComponent;
import jakarta.faces.context.FacesContext;
import jakarta.faces.event.ValueChangeEvent;
import jakarta.faces.model.SelectItem;
import jakarta.servlet.http.HttpSession;
import java.util.*;

import static com.intact.brokeroffice.business.common.LineOfBusinessUtils.toStringArray;
import static com.intact.brokeroffice.business.common.LineOfInsuranceUtils.toLinesOfInsurance;
import static com.intact.brokeroffice.business.common.LineOfInsuranceUtils.toStringArray;

@Component
@Scope("request")
public class SearchController extends AbstractScrollerController {

	private static final Log LOG = LogFactory.getLog(SearchController.class);

	@Inject
	private ISubBrokersService subBrokersService;

	@Inject
	private IBrokerService brokerService = null;

	@Inject
	@Named("subBrokersService")
	private ISubBrokersService cifBrokerService = null;

	@Inject
	private IBrokersBusinessProcess brokersBusinessProcess;

	@Inject
	private SearchQuoteValidator searchQuoteValidator;

	@Inject
	private AuthentificationController authentificationController;

	@Inject
	private QuotesTabController quotesTabController;

	@Inject
	@Named("pages.view.quote")
	private Configuration pages = null;

	@Inject
	private PermissionController permissionController;

	@Inject
	private int autoSearchRefreshInterval = 0;

	@Inject
	@Named("max.row.company")
	private Configuration maximumRows = null;

	@Inject
	protected LanguageController languageController;

	@Inject
	protected ProvinceController provinceController;

	/*** VD249 List of Master Brokers *****/

	private List<String> agreementFollowUpStatus = new ArrayList<String>();

	private List<String> quoteStatusItems;

	private List<String> quoteSources = new ArrayList<String>();

	private static String REFERENCE_NUMBER_IDENTIFIER_PL = "Q";

	private static String REFERENCE_NUMBER_IDENTIFIER_CL = "IR";

	private int index = 0;

	private SearchIndex nextIndex = null;

	private final String QUOTE_SOURCE_FROM_INTACT_WEBSITE = "INT";

	private final String QUOTE_SOURCE_FROM_WEBBROKER_WEBSITE = "WEBBK";

	private final String QUOTE_SOURCE_FROM_BOTH = "BOTH";

	private List<BrokerWebAccessTypeEnum> webAccessTypeList;

	
	/**
	 * SearchController CONSTRUCTOR
	 */
	public SearchController() {
		initFollowUpsStatus();
		initQuoteSources();
	}

	/**
	 * creates the list of follow up status used in the UI checkboxes selection
	 */
	private void initFollowUpsStatus() {
		for (AgreementFollowUpStatusEnum followUpStatus : AgreementFollowUpStatusEnum.values()) {
			this.agreementFollowUpStatus.add(followUpStatus.getCode());
		}
	}

	/**
	 * creates the list of quote status used in the UI checkboxes selection
	 */
	private void initQuoteStatus() {
		for (QuoteStatusCodeEnum aQuoteStatus : QuoteStatusCodeEnum.values()) {

			// FOR ALL PROVINCE BUT QC REMOVE REFUSED STATUS
			if (!shouldRemoveRefuseStatusON(aQuoteStatus)) {
				this.quoteStatusItems.add(aQuoteStatus.getCode());
			}
		}
	}

	private void initQuoteSources() {
		this.quoteSources.add(QUOTE_SOURCE_FROM_INTACT_WEBSITE);
		this.quoteSources.add(QUOTE_SOURCE_FROM_WEBBROKER_WEBSITE);
		this.quoteSources.add(QUOTE_SOURCE_FROM_BOTH);
	}

	public ISubBrokersService getCIFBrokerService() {
		return this.cifBrokerService;
	}

	public void setCIFBrokerService(ISubBrokersService cifBrokerService) {
		this.cifBrokerService = cifBrokerService;
	}

	public AuthentificationController getAuthentificationController() {
		return authentificationController;
	}

	public ProvinceController getProvinceController() {
		return provinceController;
	}

	public void setProvinceController(ProvinceController provinceController) {
		this.provinceController = provinceController;
	}

	public IBrokerService getBrokerService() {
		return brokerService;
	}

	public void setBrokerService(IBrokerService brokerService) {
		this.brokerService = brokerService;
	}

	/**
	 * Gets the list of brokers (use for brokers checkboxes to select) BR5774
	 * Display Point of Sale in List
	 * 
	 * @return List<SelectItem>
	 */
	public List<SelectItem> getSubBrokers() {
		return this.getSearchState().getSubBrokers();
	}

	/**
	 * Gets the list of masters
	 * 
	 * BR5592 Search Quotes - Accessible Owners: BR5594 Search Quotes - Value to use
	 * when default value is selected:
	 * 
	 * @return List<SelectItem>
	 */
	public List<SelectItem> getMasters() {

		List<SelectItem> masters = this.getSearchState().getMasters();

		if (masters.isEmpty()) {
			List<String> subBrokerNbrList = this.authentificationController.getAvailableMasters();
			boolean adminRole = this.authentificationController.isMasterRole();

			Map<String, String> theBrokersMap = this.brokersBusinessProcess.getAssignedMasterBrokers(subBrokerNbrList,
					adminRole, this.provinceController.getCompanyEnumCode(),
					this.provinceController.getManufacturerCompanyCode().getCode());
			SelectItem item = null;

			// PM9765, dropdown duplicate value, Check before loading if not
			// already load
			// If we will load the list again, so clear the actual one
			if (theBrokersMap.keySet().size() != 0) {
				masters.clear();
			}

			for (String key : theBrokersMap.keySet()) {
				item = new SelectItem(key, key + "   " + theBrokersMap.get(key));
				if (item.getLabel() != null) {
					masters.add(item);
				}
			}
			Collections.sort(masters, BROKERS_ORDER);
			if (masters.size() == 1 && this.getQuoteSearchCriteria().getSelectedMaster() == null) {
				SelectItem defaultMastertItem = (SelectItem) masters.get(0);
				this.getQuoteSearchCriteria().setSelectedMaster(defaultMastertItem.getValue().toString());
				this.getQuoteSearchCriteria().setSelectedDefaultMaster(defaultMastertItem.getLabel());
				this.onMasterChanged();
			}
		}

		return masters;
	}

	public boolean getCheckSearchAccess() {
		return this.permissionController.getCheckSearchAccess();
	}

	public boolean isOnlyOneMaster() {
		return !this.getMasters().isEmpty() && this.getMasters().size() == 1;
	}

	/**
	 * Comparator to order the brokers list
	 * 
	 */
	public static final Comparator<SelectItem> BROKERS_ORDER = new Comparator<SelectItem>() {
		public int compare(SelectItem e1, SelectItem e2) {
			return e1.getLabel().compareTo(e2.getLabel());
		}
	};

	/**
	 * Comparator to order the subbrokers list
	 */
	public static final Comparator<SelectItem> SUBBROKERS_ORDER = new Comparator<SelectItem>() {
		public int compare(SelectItem e1, SelectItem e2) {
			return ((String) e1.getValue()).compareTo((String) e2.getValue());
		}
	};

	/**
	 * method search based on searchBean criteria informations
	 * 
	 */
	public void search() {

		boolean valid = false;
		QuotesDTO quotes = null;

		BrokerQuoteSearchCriteria criteria = this.getQuoteSearchCriteria();

		if (!valid) {
			// full validation for complete search form
			valid = this.searchQuoteValidator.validate(criteria, this);
		}

		if (!valid) {
			return;
		}

		try {
			quotes = this.getBrokerService().searchQuotes(this.permissionController.getUserContext(),
					this.getQuoteSearchCriteria());
			this.buildResult(quotes);

		} catch (Exception ex) {
			FaceMessageHelper.addErrorMessage(null, "search.error.unknown", this);
			return;
		}

		if (this.getSearchState().getSearchResults().isEmpty()) {
			FaceMessageHelper.addErrorMessage(null, "error.noresults.message", this);
			return;
		}

		if (this.getSearchState().getSearchResults().size() > this.getMaximumRow()) {
			FaceMessageHelper.addErrorMessage(null, "search.refine.search.criteria.error.msg", this,
					String.valueOf(this.getMaximumRow()));
			return;
		}

		this.getSearchState().setType("RESULT");
		this.setScrollerPage(1);
		this.setScrollerPerPage(50);
	}

	/**
	 * collects the list of sub brokers available for a selected master broker
	 * BR5774 Display Point of Sale in List BR5149 List should have Please
	 * choose/Veuillez sélectionner as the first value, followed by all active
	 * point of sales preceded by their POS number sorted in ascending order
	 */
	public void onMasterChanged() {

		this.getSearchState().getSubBrokers().clear();

		List<SelectItem> subBrokers = this.getSearchState().getSubBrokers();
		if (this.getQuoteSearchCriteria().getSelectedMaster() != null) {
			HashMap<String, Object> map = new HashMap<String, Object>();
			boolean adminRole = this.authentificationController.isMasterRole();

			SelectItem item = null;
			List<ISubBrokers> subBrokersList = this.getCIFBrokerService().getWebSubBrokers(
					this.provinceController.getCompanyEnumCode().getSubBrokerCompanyNumber(),
					this.provinceController.getCompanyEnumCode().getMasterBrokerCompanyNumber(),
					this.getQuoteSearchCriteria().getSelectedMaster(), adminRole,
					ApplicationIdEnum.asList(), LineOfBusinessEnum.asList());
			for (ISubBrokers b : subBrokersList) {
				String subBrokerName = b.getAttendingBrokerNameLine1() != null ? b.getAttendingBrokerNameLine1()
						: b.getNameLine1();
				String city = b.getCity() != null ? b.getCity() : "";
				item = new SelectItem(b.getSubBrokerNumber(),
						b.getSubBrokerNumber() + " - " + subBrokerName + " " + city + " " + b.getSearchProvince());
				if (!map.containsKey(b.getSubBrokerNumber())) {
					map.put(b.getSubBrokerNumber(), null);
					subBrokers.add(item);
				}
			}
		}
		Collections.sort(subBrokers, SUBBROKERS_ORDER);
	}

	/**
	 * method reset
	 */
	private void reset() {
		this.getSearchState().getMasters().clear();
		this.getSearchState().getSubBrokers().clear();
		this.quotesTabController.getSearchStates().clear();
		this.setIndex(0);
		this.quoteStatusItems = null;
	}

	/**
	 * 
	 * @param aCriteria
	 *            {@link QuoteSearchCriteria}
	 * @return the number of search result found.
	 */
	protected int getSearchResult(BrokerQuoteSearchCriteria aCriteria) throws Exception {

		QuotesDTO quotes = this.getBrokerService().searchQuotes(this.permissionController.getUserContext(),
				aCriteria);
		return this.buildResult(quotes);
	}

	protected int buildResult(QuotesDTO quotes) {

		this.getSearchState().setSearchResults(quotes.getQuotes());
		this.getSearchState().setSearchResultSize(quotes.getQuotes().size());

		Map<String, QuotesBean> quotesMap = new HashMap<String, QuotesBean>();

		// Add quotes and childrens to the quotes map
		for (QuotesBean quote : quotes.getQuotes()) {			
			quotesMap.put(quote.getId(), quote);
			for (QuotesBean child : quote.getChildrens()) {
				quotesMap.put(child.getId(), child);
			}
		}

		this.getSearchState().setQuotesMap(quotesMap);
		this.getSearchState().getFailed().clear();
		
		for (String exception : quotes.getExceptions()) {
			// Remove service prefix and company
			this.getSearchState().setFailed(exception.replace("webzone.", "").substring(0, 4), true);
		}

		return this.getSearchState().getSearchResults().size();
	}

	/**
	 * Method to create a quotes Map based on a list of search results
	 * 
	 * @param resultsList
	 *            The list to add to the map
	 */
	protected HashMap<String, QuotesBean> createQuotesMap(List<QuotesBean> resultsList) {
		HashMap<String, QuotesBean> quotesMap = new HashMap<String, QuotesBean>();

		for (QuotesBean quote : resultsList) {
			quotesMap.put(quote.getId(), quote);
		}

		return quotesMap;
	}

	public QuoteSearchCriteria getSearchBean() {
		return this.getQuoteSearchCriteria();
	}

	public BrokerQuoteSearchCriteria getQuoteSearchCriteria() {
		BrokerQuoteSearchCriteria criteria = this.getSearchState().getCriteria();

		if (criteria == null) {
			criteria = new BrokerQuoteSearchCriteria();

			Set<LineOfBusinessEnum> businessLines = permissionController.getAvailableLinesOfBusiness();
			criteria.setSelectedLinesOfBusiness(toStringArray(businessLines));
			criteria.setSelectedLinesOfInsurance(toStringArray(toLinesOfInsurance(businessLines)));

			this.getSearchState().setCriteria(criteria);
		}

		this.formatQuoteSearchCriteria(this.permissionController.getAccess());

		return criteria;
	}

	/**
	 * Identify the search criteria
	 * 
	 */
	protected void formatQuoteSearchCriteria(String currentAccess) {

		BrokerQuoteSearchCriteria criteria = this.getSearchState().getCriteria();

		if (!StringUtils.isBlank(criteria.getSelectedMaster())) {
			criteria.setSelectedMaster(criteria.getSelectedMaster());
		} else {
			criteria.setSelectedMaster(null);
		}

		// Selected Point of Sale
		if (!StringUtils.isBlank(criteria.getSelectedPointOfSale())) {
			criteria.setSelectedPointOfSale(criteria.getSelectedPointOfSale());
		} else {
			criteria.setSelectedPointOfSale(null);
		}

		if (!StringUtils.isBlank(criteria.getAgreementNumber())) {
			String agreementNumber = StringUtils.deleteWhitespace(criteria.getAgreementNumber()).toUpperCase();
			boolean val = agreementNumber.startsWith(REFERENCE_NUMBER_IDENTIFIER_PL)
					|| agreementNumber.startsWith(REFERENCE_NUMBER_IDENTIFIER_CL);
			criteria.setAgreementLegacyNumber(!val);
			criteria.setAgreementNumber(agreementNumber);
		}

		if (!StringUtils.isBlank(criteria.getPostalCode())) {
			criteria.setPostalCode(StringUtils.deleteWhitespace(criteria.getPostalCode()).toUpperCase());
		}

		if (!StringUtils.isBlank(criteria.getPhoneAreaCode())) {
			criteria.setPhoneAreaCode(StringUtils.deleteWhitespace(criteria.getPhoneAreaCode()).toUpperCase());
		}
		if (!StringUtils.isBlank(criteria.getPhoneNumberPrefix())) {
			criteria.setPhoneNumberPrefix(StringUtils.deleteWhitespace(criteria.getPhoneNumberPrefix()).toUpperCase());
		}
		if (!StringUtils.isBlank(criteria.getPhoneNumberSuffix())) {
			criteria.setPhoneNumberSuffix(StringUtils.deleteWhitespace(criteria.getPhoneNumberSuffix()).toUpperCase());
		}
		// BR5158 At least one character must be entered if this field is to be
		// included in the search criteria.:
		if (!StringUtils.isBlank(criteria.getLastName())) {
			criteria.setLastName(criteria.getLastName().toUpperCase());
		}
		// BR5158 At least one character must be entered if this field is to be
		// included in the search criteria.:
		if (!StringUtils.isBlank(criteria.getFirstName())) {
			criteria.setFirstName(criteria.getFirstName().toUpperCase());
		}

		// Business name
		criteria.setBusinessName(StringUtils.upperCase(StringUtils.trimToNull(criteria.getBusinessName())));

		// Last update From
		if (this.getCheckSearchAccess()) {
			if (criteria.getFrom() != null) {
				criteria.setFrom(criteria.getFrom());
			}
		}
		// Last update To
		if (this.getCheckSearchAccess()) {
			if (criteria.getTo() != null) {
				criteria.setTo(criteria.getTo());
			}
		}
		// Email
		// BR5158 At least one character must be entered if this field is to be
		// included in the search criteria.:
		if (!StringUtils.isBlank(criteria.getEmail())) {
			criteria.setEmail(criteria.getEmail().trim());
		}

		// Selected Client Follow Ups
		// BR5175 When selected, only return quotes with the corresponding
		// Client Contact status
		// BR5176 Multiple selections cannot be made
		if (this.getCheckSearchAccess()) {
			if (!StringUtils.isBlank(criteria.getSelectedClientFollowUp())) {
				criteria.setSelectedClientFollowUp(criteria.getSelectedClientFollowUp());
			}
		}

		// system origin
		// BR5384 When selected, only return quotes with the corresponding quote
		// source.
		// 1. Intact Web Site: Quotes initiated from the Intact Insurance web
		// site.
		// 2. Broker Web Site: Quotes initiated from the web site of a
		// participating broker.
		// 3. Both: Quote ini:
		// BR5176 Multiple selections cannot be made
		if (this.getCheckSearchAccess()) {
			criteria.setSelectedQuoteSource(criteria.getQuoteSource());

			// Receiving "NONE" as quote source means "Inactive" POS only was selected
			if (criteria.getQuoteSource() != null) {
				criteria.setInactivePOSOnly(criteria.getQuoteSource().equals(WebAccessTypeEnum.INACTIVE.getCode()));
			}
		}

		// Selected Quote Status
		// BR5176 Multiple selections cannot be made.:
		// BR5177 When selected, only return quotes with the corresponding Quote
		// Status.
		// BR5176 Multiple selections cannot be made
		if (this.getCheckSearchAccess()) {
			if (!StringUtils.isBlank(criteria.getSelectedQuoteStatus())) {
				criteria.setSelectedQuoteStatus(criteria.getSelectedQuoteStatus());
			}
		}

		if (!this.authentificationController.isMasterRole()) {
			criteria.setUserId(this.authentificationController.getCurrentAccountUId().toUpperCase());
		}

		// Creation Date From
		if (this.getCheckSearchAccess()) {
			if (criteria.getCreationDateFrom() != null) {
				criteria.setCreationDateFrom(criteria.getCreationDateFrom());
			}
		}

		// Creation Date To
		if (this.getCheckSearchAccess()) {
			if (criteria.getCreationDateTo() != null) {
				criteria.setCreationDateTo(criteria.getCreationDateTo());
			}
		}
	}

	public final SearchState getSearchState() {

		int index = this.getIndex();
		SearchState searchState = this.quotesTabController.getSearchState(index);

		return searchState;
	}

	/**
	 * Gets the agreement followUp status
	 * 
	 * @return the List<AgreementFollowUpStatusEnum>
	 */
	public List<String> getAgreementFollowUpStatus() {
		return this.agreementFollowUpStatus;
	}

	/**
	 * Set the agreement followUp status
	 * 
	 * @param someAgreementFollowUpStatus
	 *            the new agreement followUp status to set
	 */
	public void setAgreementFollowUpStatus(List<String> someAgreementFollowUpStatus) {
		this.agreementFollowUpStatus = someAgreementFollowUpStatus;
	}

	/**
	 * Gets the quote status items
	 * 
	 * @return List<QuoteStatusCodeEnum>
	 */
	public List<String> getQuoteStatusItems() {

		if (this.quoteStatusItems == null) {
			this.quoteStatusItems = new ArrayList<String>();
			this.initQuoteStatus();
		}
		return this.quoteStatusItems;
	}

	/**
	 * Sets the quote status items
	 * 
	 * @param someQuoteStatusItems
	 *            the new quote status items to set
	 */
	public void setQuoteStatusItems(List<String> someQuoteStatusItems) {
		this.quoteStatusItems = someQuoteStatusItems;
	}

	/**
	 * validate reset
	 * 
	 * @return true
	 */
	public boolean getValidateReset() {
		reset();
		return true;
	}

	/**
	 * Gets the quote sources
	 * 
	 * @return the quote sources options
	 */
	public List<String> getQuoteSources() {
		return this.quoteSources;
	}

	/**
	 * Sets the quote sources
	 * 
	 * @param aQuoteSources
	 *            the quote sources to set
	 */
	public void setQuoteSources(List<String> aQuoteSources) {
		this.quoteSources = aQuoteSources;
	}

	/**
	 * check if we need to remove refused status for all province but QC
	 * 
	 * @param aQuoteStatusCodeEnum
	 * @return
	 */
	private boolean shouldRemoveRefuseStatusON(QuoteStatusCodeEnum aQuoteStatusCodeEnum) {
		// As we persist the upload to savers as an UPLOADED_ACCEPTED we need to remove
		// QuoteStatusCodeEnum.UPLOADED_REFUSED and QuoteStatusCodeEnum.UPLOADED
		// but change the ON properties to display UPLOADED FOR ACCEPTED
		// (global_en_CA_ON , search_en_CA_ON)
		return this.provinceController != null
				&& !(CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber().equals(this.provinceController.getCompany()))
				&& (QuoteStatusCodeEnum.UPLOADED_REFUSED.equals(aQuoteStatusCodeEnum)
						|| QuoteStatusCodeEnum.UPLOADED.equals(aQuoteStatusCodeEnum));
	}

	public Integer getQuotesBeansSize() {
		return (this.getSearchState() != null && this.getSearchState().getSearchResults() != null
				? this.getSearchState().getSearchResultSize()
				: 0);
	}

	/**
	 * Gets the page title key.
	 * 
	 * @return the page title key
	 */
	public String getPageTitleKey() {
		return "title." + this.getSearchState().getType();
	}

	/**
	 * Gets the search link key.
	 * 
	 * @return the search link key
	 */
	public String getSearchLinkKey() {
		return "link.search." + this.getSearchState().getType();
	}

	public boolean isSearchFailed(String value) {
		return this.getSearchState() != null && this.getSearchState().getFailed(value);
	}

	/**
	 * Gets the checks if is search result.
	 * 
	 * BR5139 Only displayed when viewing the Search Results
	 * 
	 * @return the checks if is search result
	 */
	public Boolean getIsSearchResult() {
		return this.getSearchState() != null && this.getSearchState().getType().equals("RESULT");
	}

	protected List<QuotesBean> getSearchResults() {

		SearchState state = this.getSearchState();
		List<QuotesBean> result = null;

		if (state != null) {
			result = state.getSearchResults();

			if (result == null) {
				result = new ArrayList<QuotesBean>();
				state.setSearchResults(result);
			}
		}

		return result;
	}

	public boolean getLanguageCommunicationDisplay() {
		return !provinceController.isCompany3();
	}

	public boolean getCVIDisplay() {
		return provinceController.isCompany3() || provinceController.isCompany6();
	}

	public boolean getAutoPlusDisplay() {
		return provinceController.isCompany3() || provinceController.isCompany6();
	}

	/**
	 * Gets the quotes beans.
	 * 
	 * @return the quotes beans
	 */

	public List<QuotesBean> getQuotesBeans() throws Exception {

		QuotesDTO quotes = null;

		if (this.getSearchState() != null) {
			if (CollectionUtils.isEmpty(this.getSearchState().getSearchResults())) {
				if (!this.getIsSearchResult() && !this.getIsSearch()) {
					quotes = this.getBrokerService().listQuotes(this.permissionController.getUserContext());					
					this.buildResult(quotes);
				} else if (this.getIsSearchResult()) {
					this.getSearchResult(this.getSearchState().getCriteria());
				}
			}
		}

		return this.getSearchState() != null ? this.getSearchState().getSearchResults() : null;
	}

	/***************************************************************
	 * Refresh.
	 * 
	 * BR5141 When clicked : 1. Refreshes the Quotes List/Search Results page. 2.
	 * List reverts to default sort order. 3. Previous display parameters are
	 * maintained. 4. New quotes received since page was initially displayed and
	 * meet the display parameters are ad:
	 * 
	 **************************************************************/
	public void refresh() {
		this.getSearchState().setSearchResults(null);
		this.search();
	}

	public void expandQuote(QuotesBean quote) {
		quote.setExpanded(!quote.isExpanded());
	}

	/**
	 * Toggle selection.
	 * 
	 * BR5105 The check box will be checked to indicate quotes to be reassigned.
	 * 
	 * @param event
	 *            the event
	 */
	public void toggleSelection(ValueChangeEvent event) {
		if ((Boolean) event.getNewValue()) {
			this.getSearchState().setNbQuotesSelected(this.getSearchState().getNbQuotesSelected() + 1);
		} else {
			this.getSearchState().setNbQuotesSelected(this.getSearchState().getNbQuotesSelected() - 1);
		}
	}

	/**
	 * Toggle selection all.
	 * 
	 * 
	 */
	public void toggleSelectionAll() {
		for (QuotesBean qb : this.getSearchResults()) {
			qb.setSelected(this.getSearchState().getAllSelected());
		}

		this.getSearchState()
				.setNbQuotesSelected(this.getSearchState().getAllSelected() ? this.getSearchResults().size() : 0);
	}

	/**
	 * toggle to select all quotes
	 */
	public void selectAllQuotes() {
		this.setAllSelected(Boolean.TRUE);
		this.toggleSelectionAll();
	}

	/**
	 * toggle to deselect all quotes
	 */
	public void deselectAllQuotes() {
		this.setAllSelected(Boolean.FALSE);
		this.toggleSelectionAll();
	}

	/**
	 * Gets the selected quotes.
	 * 
	 * @return the selected quotes
	 */
	public List<Quote> getSelectedQuotes() {
		List<Quote> selectedQuotes = new ArrayList<Quote>();
		for (QuotesBean qb : this.getSearchResults()) {
			if (qb.getSelected()) {
				selectedQuotes.add(new Quote(qb.getId(), qb.getLineOfBusiness(), qb.getLineOfInsurance(), this.getApplicationMode(qb)));
			}

			if (qb.getChildrens() != null) {
				for (QuotesBean child : qb.getChildrens()) {
					if (child.getSelected()) {
						selectedQuotes.add(new Quote(child.getAgreementNumber(), child.getLineOfBusiness(),
								child.getLineOfInsurance(), this.getApplicationMode(child)));
					}
				}
			}
		}
		return selectedQuotes;
	}

	/**
	 * Gets the checks for quotes selected.
	 * 
	 * BR5106 The Reassign Quotes button will be displayed whenever one of the
	 * checkboxes is checked.
	 * 
	 * @return the checks for quotes selected
	 */
	public Boolean getHasQuotesSelected() {
		return this.getSearchState() != null && this.getSearchState().getNbQuotesSelected() != 0;
	}

	/**
	 * Gets the nb quotes selected.
	 * 
	 * @return the nb quotes selected
	 */
	public Integer getNbQuotesSelected() {
		return this.getSearchState() != null ? this.getSearchState().getNbQuotesSelected() : 0;
	}

	/**
	 * Gets the all selected.
	 * 
	 * @return the all selected
	 */
	public Boolean getAllSelected() {
		return this.getSearchState() != null && this.getSearchState().getAllSelected();
	}

	/**
	 * Sets the all selected.
	 * 
	 * @param anAllSelected
	 *            the new all selected
	 */
	public void setAllSelected(Boolean anAllSelected) {
		if (this.getSearchState() != null) {
			this.getSearchState().setAllSelected(anAllSelected);
		}
	}

	/**
	 * Gets the reassign confirmation.
	 * 
	 * @return the reassign confirmation
	 */
	public Boolean getReassignConfirmation() {
		return this.getSearchState() != null && this.getSearchState().getReassignConfirmation();
	}

	/**
	 * Sets the reassign confirmation.
	 * 
	 * @param bool
	 *            the new reassign confirmation
	 */
	public void setReassignConfirmation(Boolean bool) {
		if (this.getSearchState() != null) {
			this.getSearchState().setReassignConfirmation(bool);
		}
	}

	/**
	 * Clear Sorting in dataTable when refreshing list
	 */
	public void resetSorting() {
		DataTable table = (DataTable) FacesContext.getCurrentInstance().getViewRoot()
				.findComponent("quoteListForm:quoteList");
		if (table != null) {
			for (UIComponent column : (List<UIComponent>) table.getChildren()) {
				if (column instanceof UIColumn) {
					//TODO: fix the sort order
//					((UIColumn) column).setSortOrder(Ordering.UNSORTED);
				} else if (column instanceof ColumnGroup) {
//					((ColumnGroup) column).setSortOrder(Ordering.UNSORTED);
				}
			}
		}
	}

	/**
	 * Gets the email reference number
	 * 
	 * @return the email reference numero
	 */
	public String getEmailReferenceNo() {
		FacesContext context = FacesContext.getCurrentInstance();
		HttpSession session = (HttpSession) context.getExternalContext().getSession(true);
		return (String) session.getAttribute(SessionConstantsEnum.EMAIL_REFERENCE_NUMBER.getSessionConstant());
	}

	public void makeNewSearch() {

		SearchIndex searchIndex = new SearchIndex();

		if (!this.isMaxSearchReached()) {
			searchIndex.setIndex(this.findNewIndex());
			searchIndex.setType("new");
			this.buildSearchState(searchIndex.getIndex());
		} else {
			searchIndex.setIndex(this.findOldestIndex());
			searchIndex.setType("force");
		}

		this.setNextIndex(searchIndex);

	}

	public void makeForceNewSearch() {

		SearchIndex searchIndex = new SearchIndex();
		searchIndex.setIndex(this.findOldestIndex());
		searchIndex.setType("new");
		this.setNextIndex(searchIndex);
		this.buildSearchState(searchIndex.getIndex());
	}

	/**
	 * reset the scroller page index to page 1
	 */
	public void resetScrollerPage() {
		this.setScrollerPage(1);
	}

	/**
	 * Modify province by selecting province on region dropdown list
	 * 
	 * BR5801 Redirect User to List of Quotes When Changing Regions: When a user
	 * opts to change their Web Zone region, they will be redirected to the List of
	 * Quotes page for the newly selected region.
	 * 
	 * 
	 * @return the index page
	 * @throws SecurityUtilityInvalidValueException
	 * @throws SecurityUtilityException
	 */
//	public void modifyProvince() throws Exception {
//
//		FacesContext context = FacesContext.getCurrentInstance();
//
//		Cookie newCookie = this.provinceController
//				.buildBrokerDefaultProvince(this.provinceController.getSelectedProvince());
//		HttpServletResponse response = (HttpServletResponse) context.getExternalContext().getResponse();
//		response.addCookie(newCookie);
//
//		HttpSession session = (HttpSession) context.getExternalContext().getSession(true);
//		session.setAttribute(SessionConstantsEnum.PROVINCE.getSessionConstant(),
//				this.provinceController.getSelectedProvince());
//
//		this.provinceController.reset();
//
//		this.quotesTabController.getSearchStates().clear();
//		this.getSearchState().setAlternateProvinceConfirmed(Boolean.TRUE);
//	}

	public void modifyCompany() throws Exception {		
		this.provinceController.modifyCompany();
		this.quotesTabController.getSearchStates().clear();
		this.getSearchState().setAlternateCompanyConfirmed(Boolean.TRUE);
	}

	public boolean isMaxSearchReached() {
		return (this.quotesTabController.getSearchStates().size() >= this.quotesTabController.getMaxSearchRequests()
				+ 1);
	}

	/**
	 * Indicator to verify if user change current company
	 * 
	 * @return the alternate company confirmed indicator
	 */
	public Boolean getAlternateCompanyConfirmed() {
		return this.getSearchState().getAlternateCompanyConfirmed();
	}

	/**
	 * Sets the alternate company confirmed indicator
	 * 
	 * @param aAlternateCompanyConfirmed
	 *            alternate company confirmed indicator to set
	 */
	public void setAlternateProvinceConfirmed(Boolean aAlternateCompanyConfirmed) {
		this.getSearchState().setAlternateCompanyConfirmed(aAlternateCompanyConfirmed);
	}

	public boolean getIsSearch() {
		this.getIndex();
		return this.getSearchState() != null && this.getSearchState().getType().equals("SEARCH");
	}

	public final int getIndex() {

		String newIndex = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap()
				.get("searchIndex");

		if (newIndex == null) {
			newIndex = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap()
					.get("mainTab:quoteListForm:index");
		}

		if (newIndex == null) {
			newIndex = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap()
					.get("mainTab:quoteSearchForm:index");
		}

		if (newIndex != null) {
			try {
				this.index = Integer.parseInt(newIndex);

			} catch (Exception ignored) {
			}
		}

		return this.index;
	}

	protected void buildSearchState(int newIndex) {

		if (newIndex <= this.quotesTabController.getMaxSearchRequests()) {
			SearchState state = new SearchState();
			state.setType(newIndex > 0 ? "SEARCH" : "VIEW");
			state.setTimestamp(new Date());
			state.setIndex(newIndex);
			this.quotesTabController.getSearchStates().put(newIndex, state);
		}
	}

	public final SearchIndex getNextIndex() {
		return nextIndex;
	}

	public final void setNextIndex(SearchIndex nextIndex) {
		this.nextIndex = nextIndex;
	}

	public final void setIndex(int index) {
		this.index = index;
	}

	public void scrollData() {
	}

	/**
	 * Return to list. BR5140 Redirects the user to the default List of Quotes page
	 * from which the Quote Search page was called.
	 */
	public void returnToList() {
		this.resetSearchState();
	}

	public void backToSearch() {
		this.getSearchState().setType("SEARCH");
		this.getSearchState().setSearchResults(null);
		this.onMasterChanged();
	}

	public void resetSearchState() {
		this.quotesTabController.getSearchStates().remove(this.getIndex());
	}

	protected int findNewIndex() {

		int newIndex = -1;

		if (this.quotesTabController.getSearchStates().size() < this.quotesTabController.getMaxSearchRequests() + 1) {
			for (int i = 1; i <= this.quotesTabController.getMaxSearchRequests(); i++) {
				if (!this.quotesTabController.getSearchStates().containsKey(i)) {
					newIndex = i;
					break;
				}
			}
		}

		return newIndex;
	}

	protected int findOldestIndex() {

		int oldIndex = -1;
		SearchState oldestState = null;
		SearchState currentState = null;

		if (this.quotesTabController.getSearchStates().size() <= this.quotesTabController.getMaxSearchRequests() + 1) {
			for (int i = 1; i <= this.quotesTabController.getMaxSearchRequests(); i++) {
				currentState = this.quotesTabController.getSearchStates().get(i);

				if (oldestState == null) {
					oldestState = currentState;
					oldIndex = i;
				} else if (oldestState.getTimestamp() != null
						&& oldestState.getTimestamp().after(currentState.getTimestamp())) {
					oldestState = currentState;
					oldIndex = i;
				}
			}
		}

		return oldIndex;
	}

	@Override
	public Integer getScrollerPerPage() {
		int perPage = 0;

		if (this.getSearchState() != null) {

			perPage = this.getSearchState().getPerPage();

			if (perPage == 0) {
				perPage = 50;
				this.getSearchState().setPerPage(perPage);
			}
		}

		return perPage;
	}

	@Override
	public void setScrollerPerPage(Integer perPage) {
		this.getSearchState().setPerPage(perPage);
	}

	@Override
	public Integer getScrollerPage() {
		return this.getSearchState() != null ? this.getSearchState().getPage() : 0;
	}

	@Override
	public void setScrollerPage(Integer page) {
		this.getSearchState().setPage(page);
	}

	public int getMaximumRow() {
		return (Integer) this.getMaximumRows().getValue(this.provinceController.getCompany());
	}

	public final int getAutoSearchRefreshInterval() {
		return this.autoSearchRefreshInterval;
	}

	public final void setAutoSearchRefreshInterval(int autoSearchRefreshInterval) {
		this.autoSearchRefreshInterval = autoSearchRefreshInterval;
	}

	public Configuration getMaximumRows() {
		return this.maximumRows;
	}

	public void setMaximumRows(Configuration maximumRows) {
		this.maximumRows = maximumRows;
	}

	public int getNumDays() {
		return provinceController.getNumberOfDaysLeft();
	}

	public boolean isCompany6() {
		return provinceController.isCompany6();
	}

	/**
	 * Gets the broker web access type list.
	 * 
	 * @return the broker web access type list
	 */
	public List<BrokerWebAccessTypeEnum> getWebAccessTypeList() {
		if (this.webAccessTypeList == null) {
			this.webAccessTypeList = new ArrayList<BrokerWebAccessTypeEnum>();
			for (BrokerWebAccessTypeEnum accessType : BrokerWebAccessTypeEnum.values()) {
				this.webAccessTypeList.add(accessType);
			}
		}

		return this.webAccessTypeList;
	}

	public Configuration getPages() {
		return this.pages;
	}

	public void setPages(Configuration pages) {
		this.pages = pages;
	}

	public String getPage(String lineOfInsurance) {
		return (String) this.getPages().getValue(lineOfInsurance);
	}

  public String getApplicationMode(QuotesBean quote) {
    return quote.getAgreementNumber().substring(0, 2);
  }
}
