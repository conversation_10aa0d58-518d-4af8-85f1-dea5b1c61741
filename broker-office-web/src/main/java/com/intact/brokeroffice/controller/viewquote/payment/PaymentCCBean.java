package com.intact.brokeroffice.controller.viewquote.payment;

import com.ing.canada.plp.domain.enums.CreditCardCompanyCodeEnum;

public class PaymentCCBean implements IPaymentBean {	
	
	private String cardHolderName;	
	private String cardMaskedNumber;
	private String cardExpiryMonth;
	private String cardExpiryYear;
	private String cardToken;
	private CreditCardCompanyCodeEnum creditCardCompany;
	
	private static String CREDIT_CARD_TYPE = "payment.cc.type.";
	private static String CREDIT_CARD_TYPE_DEFAULT = "payment.cc.type.DEFAULT";
	/**
	 * BR5759  Display Only When Credit Card Selected: 
	 * @return the cardExpiryMonth
	 */
	public String getCardExpiryMonth() {
		return this.cardExpiryMonth;
	}

	/**
	 * BR5759  Display Only When Credit Card Selected: 
	 * @param aCardExpiryMonth the cardExpiryMonth to set
	 */
	public void setCardExpiryMonth(String aCardExpiryMonth) {
		this.cardExpiryMonth = aCardExpiryMonth;
	}

	/**
	 * BR5759  Display Only When Credit Card Selected: 
	 * @return the cardExpiryYear
	 */
	public String getCardExpiryYear() {
		return this.cardExpiryYear;
	}

	/**
	 * @param aCardExpiryYear the cardExpiryYear to set
	 */
	public void setCardExpiryYear(String aCardExpiryYear) {
		this.cardExpiryYear = aCardExpiryYear;
	}

	/**
	 * @return the cardHolderName
	 */
	public String getCardHolderName() {
		return this.cardHolderName;
	}

	/**
	 * @param aCardHolderName the cardHolderName to set
	 */
	public void setCardHolderName(String aCardHolderName) {
		this.cardHolderName = aCardHolderName;
	}

	/**
	 * @return the cardMaskedNumber
	 */
	public String getCardMaskedNumber() {
		return this.cardMaskedNumber;
	}

	/**
	 * @param aCardMaskedNumber the cardMaskedNumber to set
	 */
	public void setCardMaskedNumber(String aCardMaskedNumber) {
		this.cardMaskedNumber = aCardMaskedNumber;
	}

	/**
	 * BR5759  Display Only When Credit Card Selected: 
	 * @return the cardToken
	 */
	public String getCardToken() {
		return this.cardToken;
	}

	/**
	 * @param aCardToken the cardToken to set
	 */
	public void setCardToken(String aCardToken) {
		this.cardToken = aCardToken;
	}

	public CreditCardCompanyCodeEnum getCreditCardCompany() {
		return this.creditCardCompany;
	}

	public void setCreditCardCompany(CreditCardCompanyCodeEnum aCreditCardCompany) {
		this.creditCardCompany = aCreditCardCompany;
	}
	
	public String getCreditCardType(){
		
		if(this.creditCardCompany !=null){
			return CREDIT_CARD_TYPE + this.creditCardCompany.getCode();
		}
		return CREDIT_CARD_TYPE_DEFAULT;
	}
	
}
