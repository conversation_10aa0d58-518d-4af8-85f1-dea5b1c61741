package com.intact.brokeroffice.controller.viewquote.payment;

public class PaymentCheckBean implements IPaymentBean {
	
	/** bank account holder name */
	private String bankAccountHolderName = "";

	/** transit */
	private String transit = "";

	/** institution */
	private String institution = "";

	/** account number */
	private String accountNumber = "";
	
	private String checkNumber;
	
	private Boolean twoChecksOption;

	public String getAccountNumber() {
		return this.accountNumber;
	}

	public void setAccountNumber(String anAccountNumber) {
		this.accountNumber = anAccountNumber;
	}

	public String getBankAccountHolderName() {
		return this.bankAccountHolderName;
	}

	public void setBankAccountHolderName(String aBankAccountHolderName) {
		this.bankAccountHolderName = aBankAccountHolderName;
	}

	public String getCheckNumber() {
		return this.checkNumber;
	}

	public void setCheckNumber(String aCheckNumber) {
		this.checkNumber = aCheckNumber;
	}

	public String getInstitution() {
		return this.institution;
	}

	public void setInstitution(String anInstitution) {
		this.institution = anInstitution;
	}

	public String getTransit() {
		return this.transit;
	}

	public void setTransit(String aTransit) {
		this.transit = aTransit;
	}

	public Boolean getTwoChecksOption() {
		return this.twoChecksOption;
	}

	public void setTwoChecksOption(Boolean aTwoChecksOption) {
		this.twoChecksOption = aTwoChecksOption;
	}
}
