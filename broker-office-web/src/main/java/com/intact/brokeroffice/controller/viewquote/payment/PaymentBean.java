package com.intact.brokeroffice.controller.viewquote.payment;

/**
 * <AUTHOR>
 * 
 */
public class PaymentBean {

	private int paymentTypeIndex = -1;

	/** information for the credit card monthly payment */
	private PaymentCCBean paymentMonthlyCC;

	/** information for the credit card full payment */
	private PaymentCCBean paymentFullCC;
	
	/** information for the one check payment */
	private PaymentCheckBean paymentOneCheck;

	/** information for the two checks payment */
	private PaymentCheckBean paymentTwoChecks;

	/** information for the preauthorized payment */
	private PaymentBankBean paymentMonthlyBank;

	private Double monthlyPremium;
	
	private Double totalMonthlyPremium;

	private Double monthlyFirstPayment;

	private Double annualAmountTaxes;

	private Double annualAmountWithTaxes;

	private boolean available;

	private int nbYears = 0;

	private boolean monthlyPaymentsEligible;

	private boolean anyPaymentsEligible;

	private boolean paymentCC;
	
	private boolean paymentBank;
	
	private boolean InsuranceHomeAndAutoInd ;
	
	private boolean newInterest;
	
	private String preferredPaymentMethod;
	
	/**
	 * Reset.
	 * 
	 * @param arg0 the arg0
	 * @param arg1 the arg1
	 * 
	 * @see org.apache.struts.validator.ValidatorForm#reset(org.apache.struts.action.ActionMapping,
	 *      javax.servlet.http.HttpServletRequest)
	 */

	public void reset() {
		this.paymentTypeIndex = -1;

		//credit card payment
		this.paymentFullCC = null;
		this.paymentMonthlyCC = null;
		
		// check payment
		this.paymentOneCheck = null;
		this.paymentTwoChecks = null;		

		// bank account withdrawals
		this.paymentMonthlyBank = null;
		
		this.InsuranceHomeAndAutoInd = false;
	}

	/**
	 * Gets the payment type index.
	 * 
	 * @return the paymentTypeIndex
	 */
	public int getPaymentTypeIndex() {
		return this.paymentTypeIndex;
	}

	/**
	 * Sets the payment type index.
	 * 
	 * @param aPaymentTypeIndex the paymentTypeIndex to set
	 */
	public void setPaymentTypeIndex(int aPaymentTypeIndex) {
		this.paymentTypeIndex = aPaymentTypeIndex;
	}

	/**
	 * Gets the payment full cc.
	 * 
	 * @return the paymentFullCC
	 */
	public PaymentCCBean getPaymentFullCC() {
		if (this.paymentFullCC == null) {
			this.paymentFullCC = new PaymentCCBean();
		}
		return this.paymentFullCC;
	}

	/**
	 * Sets the payment monthly cc.
	 * 
	 * @param aPaymentFullCC the paymentFullCCVisa to set
	 */
	public void setPaymentFullCC(PaymentCCBean aPaymentFullCC) {
		this.paymentFullCC = aPaymentFullCC;
	}
	
	/**
	 * Gets the payment monthly cc.
	 * 
	 * @return the paymentMonthlyCC
	 */
	public PaymentCCBean getPaymentMonthlyCC() {
		if (this.paymentMonthlyCC == null) {
			this.paymentMonthlyCC = new PaymentCCBean();
		}
		return this.paymentMonthlyCC;
	}

	/**
	 * Sets the payment monthly cc.
	 * 
	 * @param aPaymentMonthlyCC the paymentMonthlyCCVisa to set
	 */
	public void setPaymentMonthlyCC(PaymentCCBean aPaymentMonthlyCC) {
		this.paymentMonthlyCC = aPaymentMonthlyCC;
	}
	
	/**
	 * Gets the payment one check .
	 * 
	 * @return the paymentOneCheck
	 */
	public PaymentCheckBean getPaymentOneCheck() {
		if (this.paymentOneCheck == null) {
			this.paymentOneCheck = new PaymentCheckBean();
		}
		return this.paymentOneCheck;
	}

	/**
	 * Sets the payment one checks.
	 * 
	 * @param aPaymentOneCheck the paymentOneCheck to set
	 */
	public void setPaymentOneCheck(PaymentCheckBean aPaymentOneCheck) {
		this.paymentOneCheck = aPaymentOneCheck;
	}

	/**
	 * Gets the payment monthly bank.
	 * 
	 * @return the paymentMonthlyBank
	 */
	public PaymentBankBean getPaymentMonthlyBank() {
		if (this.paymentMonthlyBank == null) {
			this.paymentMonthlyBank = new PaymentBankBean();
		}
		return this.paymentMonthlyBank;
	}

	/**
	 * Sets the payment monthly bank.
	 * 
	 * @param aPaymentMonthlyBank the paymentMonthlyBank to set
	 */
	public void setPaymentMonthlyBank(PaymentBankBean aPaymentMonthlyBank) {
		this.paymentMonthlyBank = aPaymentMonthlyBank;
	}

	/**
	 * Gets the payment two checks.
	 * 
	 * @return the paymentTwoChecks
	 */
	public PaymentCheckBean getPaymentTwoChecks() {
		if (this.paymentTwoChecks == null) {
			this.paymentTwoChecks = new PaymentCheckBean();
		}
		return this.paymentTwoChecks;
	}

	/**
	 * Sets the payment two checks.
	 * 
	 * @param aPaymentTwoChecks the paymentTwoChecks to set
	 */
	public void setPaymentTwoChecks(PaymentCheckBean aPaymentTwoChecks) {
		this.paymentTwoChecks = aPaymentTwoChecks;
	}

	/**
	 * Gets the nb years.
	 * 
	 * @return the nbYears
	 */
	public int getNbYears() {
		return this.nbYears;
	}

	/**
	 * Sets the nb years.
	 * 
	 * @param aNbYears the nbYears to set
	 */
	public void setNbYears(int aNbYears) {
		this.nbYears = aNbYears;
	}

	/**
	 * Gets the monthly first payment.
	 * BR5221  Display calculated value.: 
	 * BR5758  Display Only When Monthly Withdrawal Selected: 
	 * BR5761  Rounding of dollar amounts including tax: 
	 * @return the monthlyFirstPayment
	 */
	public Double getMonthlyFirstPayment() {
		return this.monthlyFirstPayment;
	}

	/**
	 * Gets the monthly premium.
	 * BR5221  Display calculated value.: 
	 * BR5761  Rounding of dollar amounts including tax: 
	 * @return the monthlyPremium
	 */
	public Double getMonthlyPremium() {
		return this.monthlyPremium;
	}

	/**
	 * Sets the monthly first payment.
	 * 
	 * @param aMonthlyFirstPayment the monthlyFirstPayment to set
	 */
	public void setMonthlyFirstPayment(Double aMonthlyFirstPayment) {
		this.monthlyFirstPayment = aMonthlyFirstPayment;
	}

	/**
	 * Sets the monthly premium.
	 * 
	 * @param aMonthlyPremium the monthlyPremium to set
	 */
	public void setMonthlyPremium(Double aMonthlyPremium) {
		this.monthlyPremium = aMonthlyPremium;
	}

	/**
	 * Gets the payment bean.
	 * BR5193  Display client response entered in the On-line Auto Quote application.: 
	 * @param paymentMode the payment mode
	 * 
	 * @return the payment bean
	 */

	public IPaymentBean getPaymentBean(PaymentModeEnum paymentMode) {
		switch (paymentMode) {
		case PAYMENT_MONTHLY_BANK:
			return getPaymentMonthlyBank();
		case PAYMENT_ONE_CKECK:
			return this.getPaymentOneCheck();
		case PAYMENT_TWO_CKECKS:
			return this.getPaymentTwoChecks();	
		case PAYMENT_FULL_CC_MC:
		case PAYMENT_FULL_CC_VISA:
			return getPaymentFullCC();
		case PAYMENT_MONTHLY_CC_MC:
		case PAYMENT_MONTHLY_CC_VISA:
			return getPaymentMonthlyCC();			
		default:
			return null;
		}
	}

	/**
	 * Checks if payment is available for selected quote
	 * 
	 * @return boolean true if avalaible else return false
	 */
	public boolean isAvailable() {
		return this.available;
	}

	/**
	 * Sets the available
	 * 
	 * @param anAvailable the available to set
	 */
	public void setAvailable(boolean anAvailable) {
		this.available = anAvailable;
	}

	/**
	 * Gets the annual amount taxes.
	 * 
	 * @return
	 */
	public Double getAnnualAmountTaxes() {
		return this.annualAmountTaxes;
	}

	/**
	 * Sets the annual amount taxes.
	 * 
	 * @param annualAmountTaxes
	 */
	public void setAnnualAmountTaxes(Double annualAmountTaxes) {
		this.annualAmountTaxes = annualAmountTaxes;
	}

	/**
	 * Gets the annual amount with taxes.
	 * BR5221  Display calculated value.: 
	 * BR5761  Rounding of dollar amounts including tax: 
	 * @return
	 */
	public Double getAnnualAmountWithTaxes() {
		return this.annualAmountWithTaxes;
	}

	/**
	 * Sets the annual amount with taxes.
	 * 
	 * @param anAnnualAmountWithTaxes the annualAmountWithTaxes to set
	 */
	public void setAnnualAmountWithTaxes(Double anAnnualAmountWithTaxes) {
		this.annualAmountWithTaxes = anAnnualAmountWithTaxes;
	}

	/**
	 * Checks if available a monthly payment for selected quote
	 * 
	 * @return boolean true if available else return false
	 */
	public boolean isMonthlyPaymentsEligible() {
		return this.monthlyPaymentsEligible;
	}

	/**
	 * Sets the monthlyPaymentsEligible
	 * 
	 * @param anMonthlyPaymentsEligible the available to set
	 */
	public void setMonthlyPaymentsEligible(boolean anMonthlyPaymentsEligible) {
		this.monthlyPaymentsEligible = anMonthlyPaymentsEligible;
	}

	/**
	 * Checks if available a any payment for selected quote
	 * 
	 * @return boolean true if available else return false
	 */
	public boolean isAnyPaymentsEligible() {
		return this.anyPaymentsEligible;
	}

	/**
	 * Sets the anyPaymentsEligible
	 * 
	 * @param anAnyPaymentsEligible the available to set
	 */
	public void setAnyPaymentsEligible(boolean anAnyPaymentsEligible) {
		this.anyPaymentsEligible = anAnyPaymentsEligible;
	}

	/**
	 * Gets the payment by CC indicator
	 * @return the payment by CC indicator
	 */
	public boolean isPaymentCC() {
		return this.paymentCC;
	}

	/**
	 * Sets the payment by CC indicator
	 * @param aPaymentCC the payment by CC indicator
	 */
	public void setPaymentCC(boolean aPaymentCC) {
		this.paymentCC = aPaymentCC;
	}

	/**
	 * Gets the payment by CC indicator
	 * @return payment bank monthly indicator
	 */
	public boolean isPaymentBank() {
		return this.paymentBank;
	}

	/**
	 * Sets the payment bank monthly indicator
	 * @param aPaymentBank the payment bank monthly indicator
	 */
	public void setPaymentBank(boolean aPaymentBank) {
		this.paymentBank = aPaymentBank;
	}	
	
	public boolean isTwoYearsPolicyEligible(){
		
		return !this.isPaymentBank() && !this.isPaymentCC() && this.nbYears == 2;
	}

	public Double getTotalMonthlyPremium() {
		return totalMonthlyPremium;
	}

	public void setTotalMonthlyPremium(Double totalMonthlyPremium) {
		this.totalMonthlyPremium = totalMonthlyPremium;
	}
	
	public void setInsuranceHomeAndAutoInd(boolean insuranceHomeAndAutoInd) {
		InsuranceHomeAndAutoInd = insuranceHomeAndAutoInd;
	}

	public boolean isInsuranceHomeAndAutoInd() {
		return InsuranceHomeAndAutoInd;
	}

	public boolean isNewInterest() {
		return newInterest;
	}

	public void setNewInterest(boolean newInterest) {
		this.newInterest = newInterest;
	}
	
	public String getPreferredPaymentMethod() {
		return preferredPaymentMethod;
	}

	public void setPreferredPaymentMethod(String preferredPaymentMethod) {
		this.preferredPaymentMethod = preferredPaymentMethod;
	}
	
}
