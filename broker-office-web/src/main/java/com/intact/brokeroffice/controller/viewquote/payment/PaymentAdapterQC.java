

/*
 * 
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */

package com.intact.brokeroffice.controller.viewquote.payment;

import org.springframework.stereotype.Component;

import com.ing.canada.common.domain.QuoteCalculationDetails;
import com.ing.canada.plp.domain.enums.PolicyTermInMonthsEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;

/**
 * Adapter for a PaymentBeanQC
 *  
 * <AUTHOR>
 */
@Component
public class PaymentAdapterQC extends PaymentAdapter<PaymentBean> 
{
	
	private static final int FIRST_PAYMENT_RATIO = 2;
	
	private static final double SURCHARGE_PERCENTAGE_QC = 0.0175;
	
	/**
	 * @see com.intact.brokeroffice.controller.viewquote.payment.IPaymentAdapter#loadPaymentBean(
	 * 		com.ing.canada.common.domain.QuoteCalculationDetails,
	 *      com.intact.brokeroffice.controller.payment.PaymentBean, 
	 *      com.ing.canada.plp.domain.policyversion.PolicyVersion,
	 *      com.ing.canada.plp.domain.enums.ProvinceCodeEnum
	 *      )
	 */
	/**
	 * @param aPaymentBean
	 * @param aPolicyVersion
	 */
	public void loadPaymentBean(PaymentBean aPaymentBean, PolicyVersion aPolicyVersion)	
	{		
		super.loadPaymentBean(aPaymentBean, aPolicyVersion, ProvinceCodeEnum.QUEBEC);
		this.loadMethodPaymentBean(aPaymentBean,  aPolicyVersion);
	}
	
	/* (non-Javadoc)
	 * @see com.intact.brokeroffice.controller.viewquote.payment.PaymentAdapter#loadPremiumInformation(com.intact.brokeroffice.controller.viewquote.payment.PaymentBean, com.ing.canada.common.domain.QuoteCalculationDetails)
	 */
	@Override
	protected void loadPremiumInformation(PaymentBean aPaymentBean, QuoteCalculationDetails quoteCalculationDetails){
		aPaymentBean.setAnnualAmountWithTaxes(quoteCalculationDetails.getAnnualAmountWithTaxes());
		aPaymentBean.setMonthlyPremium(quoteCalculationDetails.getMonthlyPaymentWithTaxes());		
	}
	/* (non-Javadoc)
	 * @see com.intact.brokeroffice.controller.viewquote.payment.PaymentAdapter#applySpecificCalculationRules(com.ing.canada.common.domain.QuoteCalculationDetails, com.ing.canada.plp.domain.policyversion.PolicyVersion)
	 */
	public void applySpecificCalculationRules(QuoteCalculationDetails quotationDetails, PolicyVersion policyVersion) {	
		
		this.quotationService.calculateFirstMonthlyPayment(quotationDetails, FIRST_PAYMENT_RATIO);
		
		if (quotationDetails.isMonthlyPaymentsEligible()) {
			this.quotationService.addSurchargeToQuoteDetailsBeforeTaxes(quotationDetails, SURCHARGE_PERCENTAGE_QC, true);
			quotationDetails.setMonthlyPaymentWithTaxes(quotationDetails.getMonthlyPaymentWithTaxesAndSurcharge());
			// 152827 not always divide by two ... only if 24 months policy
			if (quotationDetails.getPolicyTermInMonths()==PolicyTermInMonthsEnum.TWENTYFOUR_MONTHS) {
				quotationDetails.setAnnualAmountWithTaxes(quotationDetails.getFullTermPremiumWithTaxesAndSurcharge() / 2);
			}
			else {
				quotationDetails.setAnnualAmountWithTaxes(quotationDetails.getFullTermPremiumWithTaxesAndSurcharge());
			}
		} else {
			this.quotationService.addSurchargeToQuoteDetails(quotationDetails, 0, false);
		}
		
		this.quotationService.roundUpTheQuoteNumber(quotationDetails);
	}
}
