package com.intact.brokeroffice.controller.viewquote.offer;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ing.canada.common.domain.QuoteCalculationDetails;
import com.ing.canada.common.services.api.rating.IQuotationService;
import com.ing.canada.plp.domain.enums.BasicCoverageCodeEnum;
import com.ing.canada.plp.domain.enums.CoverageTypeCodeEnum;
import com.ing.canada.plp.domain.enums.EndorsementCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.business.rules.quote.BR5883_HasFourPercentageSurcharge;
import com.intact.business.rules.quote.BR5883_HasTwoPercentageSurcharge;

@Component
public class OfferAdapterAB extends OfferAdapter<VehicleOfferBeanAB> {

	@Autowired
	private IQuotationService quotationService;

	private static final double SURCHARGE_4PERC_PERCENTAGE_AB = 0.04;
	private static final double SURCHARGE_2PERC_PERCENTAGE_AB = 0.02;

	@Autowired
	private BR5883_HasFourPercentageSurcharge br5883_4perc;
	@Autowired
	private BR5883_HasTwoPercentageSurcharge br5883_2perc;

	@Override
	protected boolean isValidEndorsements(EndorsementCodeEnum codeEnum) {
		return (EndorsementCodeEnum._20_SAV.equals(codeEnum) || EndorsementCodeEnum.PPA_SAV.equals(codeEnum) || EndorsementCodeEnum.PPB_SAV.equals(codeEnum)
				|| EndorsementCodeEnum._39_SAV.equals(codeEnum) || EndorsementCodeEnum._39F_SAV.equals(codeEnum)
				|| EndorsementCodeEnum.CFE_SAV.equals(codeEnum) || EndorsementCodeEnum.CFF_SAV.equals(codeEnum)
				|| EndorsementCodeEnum._13D_SAV.equals(codeEnum) || EndorsementCodeEnum._49Y_SAV.equals(codeEnum)
				|| EndorsementCodeEnum._50Y_SAV.equals(codeEnum) || EndorsementCodeEnum._49F_SAV.equals(codeEnum)
				|| EndorsementCodeEnum._50F_SAV.equals(codeEnum) || EndorsementCodeEnum._13_SAV.equals(codeEnum) // || EndorsementCodeEnum.UE05.equals(codeEnum));
				|| EndorsementCodeEnum._H17_SAV.equals(codeEnum) || EndorsementCodeEnum._H18_SAV.equals(codeEnum)
				|| EndorsementCodeEnum.P1C_SAV.equals(codeEnum) || EndorsementCodeEnum.P7C_SAV.equals(codeEnum)
				|| EndorsementCodeEnum.H1C_SAV.equals(codeEnum) || EndorsementCodeEnum.H7C_SAV.equals(codeEnum)
				|| EndorsementCodeEnum.H1A_SAV.equals(codeEnum) || EndorsementCodeEnum.H1B_SAV.equals(codeEnum)); 

	}

	@Override
	protected void resetEndorsementRank(String codeValue, CoverageOfferBean aCoverageOfferBean) {

		EndorsementCodeEnum codeEnum = EndorsementCodeEnum.valueOfCode(codeValue, ManufacturerCompanyCodeEnum.ING_WESTERN_REGION);

		if (EndorsementCodeEnum._20_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(1);
		} else if (EndorsementCodeEnum.UE05.equals(codeEnum)) {
			aCoverageOfferBean.setRank(10);
		} else if (EndorsementCodeEnum.PPA_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(2);
		} else if (EndorsementCodeEnum.PPB_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(2);
		}
		
		else if (EndorsementCodeEnum.P1C_SAV.equals(codeEnum)) {
				aCoverageOfferBean.setRank(2);
		} else if (EndorsementCodeEnum.P7C_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(2);
		}else if (EndorsementCodeEnum.H1C_SAV.equals(codeEnum)) {
				aCoverageOfferBean.setRank(2);
		} else if (EndorsementCodeEnum.H7C_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(2);
		}else if (EndorsementCodeEnum.H1A_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(2);
		} else if (EndorsementCodeEnum.H1B_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(2);
		}

		// CSE_SAV = CSF_SAV
		else if (EndorsementCodeEnum._39_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(3);
		} else if (EndorsementCodeEnum._39F_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(3);
		}

		// CFE_SAV = CFF_SAV
		else if (EndorsementCodeEnum.CFE_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(4);
		} else if (EndorsementCodeEnum.CFF_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(4);
		}

		else if (EndorsementCodeEnum._13_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(5);
		} else if (EndorsementCodeEnum._13D_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(6);
		}
		
		else if (EndorsementCodeEnum._H17_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(7);
		} else if (EndorsementCodeEnum._H18_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(8);
		}

		// CLAIMS ADVANTAGE: _49Y_SAV = _50Y_SAV = _49F_SAV = _50F_SAV
		else if (EndorsementCodeEnum._49Y_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(9);
		} else if (EndorsementCodeEnum._50Y_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(9);
		} else if (EndorsementCodeEnum._49F_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(9);
		} else if (EndorsementCodeEnum._50F_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(9);
		}
	}
	
	public void initEndorsementWithList(VehicleOfferBeanAB aVehicleOfferBean, ManufacturerCompanyCodeEnum aManufacturerCompanyCode, List<CoverageOffer> listSelectedOfferCoverage) {
		initEndorsement(aVehicleOfferBean, aManufacturerCompanyCode);
		
		CoverageOfferBean aCoverageOfferBean = null;
		
		for(CoverageOffer coverage : listSelectedOfferCoverage) {
			
			if (CoverageTypeCodeEnum.ENDORSEMENT.equals(coverage.getCoverageRepositoryEntry().getCoverageType())
					&& EndorsementCodeEnum.valueOfCode(coverage.getCoverageRepositoryEntry().getCoverageCode(), aManufacturerCompanyCode) != null) {
				
				if(StringUtils.equalsIgnoreCase(EndorsementCodeEnum.valueOfCode(coverage.getCoverageRepositoryEntry()
						.getCoverageCode(), aManufacturerCompanyCode).getCode(), EndorsementCodeEnum.P1C_SAV.getCode()) || 
					StringUtils.equalsIgnoreCase(EndorsementCodeEnum.valueOfCode(coverage.getCoverageRepositoryEntry()
						.getCoverageCode(), aManufacturerCompanyCode).getCode(),EndorsementCodeEnum.P7C_SAV.getCode()) ||
					StringUtils.equalsIgnoreCase(EndorsementCodeEnum.valueOfCode(coverage.getCoverageRepositoryEntry()
							.getCoverageCode(), aManufacturerCompanyCode).getCode(),EndorsementCodeEnum.H1C_SAV.getCode()) ||
					StringUtils.equalsIgnoreCase(EndorsementCodeEnum.valueOfCode(coverage.getCoverageRepositoryEntry()
							.getCoverageCode(), aManufacturerCompanyCode).getCode(),EndorsementCodeEnum.H7C_SAV.getCode()) ||
					StringUtils.equalsIgnoreCase(EndorsementCodeEnum.valueOfCode(coverage.getCoverageRepositoryEntry()
							.getCoverageCode(), aManufacturerCompanyCode).getCode(),EndorsementCodeEnum.H1A_SAV.getCode()) ||
					StringUtils.equalsIgnoreCase(EndorsementCodeEnum.valueOfCode(coverage.getCoverageRepositoryEntry()
							.getCoverageCode(), aManufacturerCompanyCode).getCode(),EndorsementCodeEnum.H1B_SAV.getCode()) ) {
					aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum.PPA_SAV.getCode());
					aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
					aCoverageOfferBean.setCoverageSelectedIndicator(null);
					aCoverageOfferBean.setRank(2);
				}
			}
		}
		
	}


	/*
	 * (non-Javadoc)
	 * 
	 * @see com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#
	 * initEndorsement(com.intact.brokeroffice.controller
	 * .viewquote.offer.VehicleOfferBean,
	 * com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum)
	 */
	@Override
	protected void initEndorsement(VehicleOfferBeanAB aVehicleOfferBean, ManufacturerCompanyCodeEnum aManufacturerCompanyCode) {

		// ENDORSEMENT
				CoverageOfferBean aCoverageOfferBean = null;

				// DE1486 TRANSPORTATION REPLACEMENT INDICATOR - LOSS OF USE OF VEHICLE
				aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum._20_SAV.getCode());
				aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
				aCoverageOfferBean.setEndorsementEligibleInd(null);
				aCoverageOfferBean.setRank(1);

				// DE661 SEF 39 - ACCIDENT RATING WAIVER
				aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum._39_SAV.getCode());
				aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
				aCoverageOfferBean.setEndorsementEligibleInd(null);
				aCoverageOfferBean.setRank(3);

				// DE1596 MINOR CONVICTION RATING WAIVER
				aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum.CFE_SAV.getCode());
				aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
				aCoverageOfferBean.setEndorsementEligibleInd(null);
				aCoverageOfferBean.setRank(4);

				// DE1597 FULL GLASS COVERAGE - VERIFY IF NOT EXIST AS MENTIONNED BY F.
				// DEMERS
				aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum._13_SAV.getCode());
				aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
				aCoverageOfferBean.setEndorsementEligibleInd(null);
				aCoverageOfferBean.setRank(5);

				// DE663 SEF 13D - COMPREHENSIVE COVER - LIMITED GLASS ENDORSEMENT
				aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum._13D_SAV.getCode());
				aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
				aCoverageOfferBean.setEndorsementEligibleInd(null);
				aCoverageOfferBean.setRank(6);
				
				// DE3247 & DE3248  HAIL - H17 & H18
				aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum._H17_SAV.getCode());
				aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
				aCoverageOfferBean.setEndorsementEligibleInd(null);
				aCoverageOfferBean.setRank(7);
				
				aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum._H18_SAV.getCode());
				aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
				aCoverageOfferBean.setEndorsementEligibleInd(null);
				aCoverageOfferBean.setRank(8);

				// DE2972 CLAIMS ADVANTAGE - 49Y, 50Y, 49F and 50F
				aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum._49Y_SAV.getCode());
				aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
				aCoverageOfferBean.setEndorsementEligibleInd(null);
				aCoverageOfferBean.setRank(9);
				
				// UBI
				//aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum.UE05.getCode());
				//aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
				//aCoverageOfferBean.setEndorsementEligibleInd(null);
				//aCoverageOfferBean.setRank(8);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#
	 * obtainCoverageCode(com.ing.canada.plp.domain.
	 * insuranceriskoffer.CoverageOffer)
	 */
	@Override
	protected String obtainCoverageCode(CoverageOffer aCoverageOffer) {
		// important : to validate proper PLUS PAC information

		String theCoverageCode = aCoverageOffer.getCoverageRepositoryEntry().getCoverageCode();
		// Wrap PPB_SAV to PPA_SAV
		if (EndorsementCodeEnum.PPB_SAV.getCode().equals(aCoverageOffer.getCoverageRepositoryEntry().getCoverageCode())) {
			theCoverageCode = EndorsementCodeEnum.PPA_SAV.getCode();
		}
		// Wrap CFF_SAV to CFE_SAV
		else if (EndorsementCodeEnum.CFF_SAV.getCode().equals(aCoverageOffer.getCoverageRepositoryEntry().getCoverageCode())) {
			theCoverageCode = EndorsementCodeEnum.CFE_SAV.getCode();
		}

		// Wrap _39F_SAV to _39_SAV
		else if (EndorsementCodeEnum._39F_SAV.getCode().equals(aCoverageOffer.getCoverageRepositoryEntry().getCoverageCode())) {
			theCoverageCode = EndorsementCodeEnum._39_SAV.getCode();
		}
		
		// Wrap _50Y_SAV, _49F_SAV, _50F_SAV to _49Y_SAV
		else if (EndorsementCodeEnum._49F_SAV.getCode().equals(aCoverageOffer.getCoverageRepositoryEntry().getCoverageCode())
				|| EndorsementCodeEnum._50Y_SAV.getCode().equals(aCoverageOffer.getCoverageRepositoryEntry().getCoverageCode())
				|| EndorsementCodeEnum._50F_SAV.getCode().equals(aCoverageOffer.getCoverageRepositoryEntry().getCoverageCode())) {
			theCoverageCode = EndorsementCodeEnum._49Y_SAV.getCode();
		}
		return theCoverageCode;
	}

	@Override
	protected String mappingEndorsementKey(String anEndorsementKey) {

		if (EndorsementCodeEnum.PPB_SAV.getCode().equals(anEndorsementKey)) {
			return EndorsementCodeEnum.PPA_SAV.getCode();
		}

		else if (EndorsementCodeEnum.CFF_SAV.getCode().equals(anEndorsementKey)) {
			return EndorsementCodeEnum.CFE_SAV.getCode();
		}

		else if (EndorsementCodeEnum._39F_SAV.getCode().equals(anEndorsementKey)) {
			return EndorsementCodeEnum._39_SAV.getCode();
		}
		
		// Claims Advantage
		else if (EndorsementCodeEnum._49F_SAV.getCode().equals(anEndorsementKey) || EndorsementCodeEnum._50Y_SAV.getCode().equals(anEndorsementKey)
				|| EndorsementCodeEnum._50F_SAV.getCode().equals(anEndorsementKey)) {
			return EndorsementCodeEnum._49Y_SAV.getCode();
		}

		return anEndorsementKey;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#
	 * loadAllEndorsementsSelectedFromOffer(com.intact
	 * .brokeroffice.controller.viewquote.offer.VehicleOfferBean, java.util.Map)
	 */
	@Override
	protected void loadAllEndorsementsSelectedFromOffer(VehicleOfferBeanAB aVehicleOfferBean, Map<String, CoverageOfferBean> mapEndorsementOptions) {
		for (CoverageOfferBean bean : aVehicleOfferBean.getEndorsementSelectedList()) {
			String findKey = mappingEndorsementKey(bean.getCoverageCode());
			if (!mapEndorsementOptions.containsKey(findKey)) {
				mapEndorsementOptions.put(findKey, bean);
			}
		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#
	 * validateCoverageInformation(com.intact.brokeroffice
	 * .controller.viewquote.offer.CoverageOfferBean,
	 * com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer)
	 */
	@Override
	protected void loadCoverageInformation(CoverageOfferBean covOfferBean, CoverageOffer covOff) {
		// important : to validate proper PLUS PAC information
		if (covOfferBean.getCoverageSelectedIndicator() == null || !covOfferBean.getCoverageSelectedIndicator().booleanValue()) {
			covOfferBean.setCoverageSelectedIndicator(covOff.getCoverageSelectedIndicator());

			if (isPlusPac(covOff.getCoverageRepositoryEntry().getCoverageCode()) || isCse(covOff.getCoverageRepositoryEntry().getCoverageCode())
					|| isCfe(covOff.getCoverageRepositoryEntry().getCoverageCode()) || isClaimsAdvantage(covOff.getCoverageRepositoryEntry().getCoverageCode())) {
				if (covOfferBean.getCoverageSelectedIndicator() != null && covOfferBean.getCoverageSelectedIndicator().booleanValue()) {
					covOfferBean.setCoverageCode(covOff.getCoverageRepositoryEntry().getCoverageCode());
				}
			}
		}
	}

	/**
	 * Method uses to build the List of vehicle offer bean for ON
	 * 
	 * @param vehicleOfferBeanList
	 * @param policyVersion
	 * @param aLocale
	 */
	public void loadVehicleOfferBeanList(List<VehicleOfferBeanAB> vehicleOfferBeanList, PolicyVersion policyVersion, Locale aLocale,List<CoverageOffer> listSelectedOfferCoverage) {

		for (InsuranceRisk insuranceRisk : policyVersion.getInsuranceRisks()) {
			Boolean isPlusPackAlreadyTreated = false;

			Map<String, CoverageOfferBean> mapEndorsementCodes = new HashMap<String, CoverageOfferBean>();

			VehicleOfferBeanAB vehicleBean = new VehicleOfferBeanAB();
			
			loadBean(insuranceRisk, vehicleBean, aLocale, ProvinceCodeEnum.ALBERTA, policyVersion.getInsurancePolicy().getApplicationMode());
			
			loadCoverageOfferBeanFromInsuranceRiskOfferWithOffer(vehicleBean, VehicleOfferBeanAB.getCoveragesDisplayOrder(),
					insuranceRisk.getSelectedInsuranceRiskOffer(), ManufacturerCompanyCodeEnum.ING_WESTERN_REGION, listSelectedOfferCoverage);
			
			for (CoverageOffer covOff : listSelectedOfferCoverage) {
				// FOR QUEBEC ENDORSEMENTS (display only I90, NV1, NV2, 33E, NBD, NV3, _43V)
				// FOR ONTARIO ENDORSEMENTS (display only _20_SAV, PPC_SAV, PPD_SAV, RDG_SAV, ACHV_SAV)
				CoverageOfferBean covOfferBean = null;
				if (CoverageTypeCodeEnum.ENDORSEMENT.equals(covOff.getCoverageRepositoryEntry().getCoverageType())) {
					if (isValidEndorsements(EndorsementCodeEnum.valueOfCode(covOff.getCoverageRepositoryEntry()
							.getCoverageCode(), ManufacturerCompanyCodeEnum.ING_WESTERN_REGION))) {

						String coverageCode = obtainCoverageCode(covOff);
						
						for(CoverageOfferBean coverageOfferBean : vehicleBean.getEndorsementSelectedList()) {
							if(StringUtils.equalsIgnoreCase(coverageCode, coverageOfferBean.getCoverageCode()) || LIST_ENDORSEMENT_PLUS_PACK.contains(coverageCode) ) {
								covOfferBean = vehicleBean.getEndorsementsMappedWithValue(coverageCode, covOff);
								
								for(int i = 0; i < vehicleBean.getEndorsementSelectedList().size(); i++) {
									if(StringUtils.equalsIgnoreCase(coverageCode, vehicleBean.getEndorsementSelectedList().get(i).getCoverageCode())) {
										vehicleBean.getEndorsementSelectedList().get(i).setEndorsementDisplayInd(covOff.getCoverageSelectedIndicator());
										vehicleBean.getEndorsementSelectedList().get(i).setEndorsementIndicator(Boolean.TRUE);
										vehicleBean.getEndorsementSelectedList().get(i).setEndorsementEligibleInd(covOff.getCoverageEligibleIndicator());
										if (vehicleBean.getEndorsementSelectedList().get(i).getEndorsementEligibleInd() == null
												|| !vehicleBean.getEndorsementSelectedList().get(i).getEndorsementEligibleInd().booleanValue()) {
											vehicleBean.getEndorsementSelectedList().get(i).setEndorsementEligibleInd(covOff.getCoverageEligibleIndicator());
										}
										if(LIST_ENDORSEMENT_PLUS_PACK.contains(coverageCode) && BooleanUtils.isFalse(isPlusPackAlreadyTreated))
											isPlusPackAlreadyTreated = true;
									}

								}
							}
						}
					}
				}
			}
			
			this.loadAllEndorsementsSelectedFromOffer(vehicleBean, mapEndorsementCodes);
			
			//loadFullGlassCoverage(vehicleBean);
			
			updateLossOfUseCoverage(vehicleBean);
			
			vehicleOfferBeanList.add(vehicleBean);

			// retrieve endorsement mapped to each vehicle
			this.buildEndorsementsSelectedForEachVehicle(vehicleOfferBeanList, mapEndorsementCodes);
		}

	}
	
	
	public void loadCoverageOfferBeanFromInsuranceRiskOfferWithOffer(VehicleOfferBeanAB aVehicleOfferBean, BasicCoverageCodeEnum[] coverageCodeEnums,
			InsuranceRiskOffer insuranceRiskOffer, ManufacturerCompanyCodeEnum aManufacturerCompanyCode, List<CoverageOffer> listSelectedOfferCoverage) {
		if (this.isOfferSelected(aVehicleOfferBean, insuranceRiskOffer)) {
			this.initEndorsementWithList(aVehicleOfferBean, aManufacturerCompanyCode,listSelectedOfferCoverage);
			super.loadCoverageOfferBeanFromInsuranceRiskOffer(aVehicleOfferBean, coverageCodeEnums, insuranceRiskOffer, aManufacturerCompanyCode);
		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#
	 * loadCoverageOfferBeanFromInsuranceRiskOffer(com
	 * .intact.brokeroffice.controller.viewquote.offer.VehicleOfferBean,
	 * com.ing.canada.plp.domain.enums.BasicCoverageCodeEnum[],
	 * com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer,
	 * com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum)
	 */
	@Override
	public void loadCoverageOfferBeanFromInsuranceRiskOffer(VehicleOfferBeanAB aVehicleOfferBean, BasicCoverageCodeEnum[] coverageCodeEnums,
			InsuranceRiskOffer insuranceRiskOffer, ManufacturerCompanyCodeEnum aManufacturerCompanyCode) {
	}


	/**
	 * checks if an endorsement is a PLUS PAC (OPTION A or OPTION B)
	 * 
	 * @param aCode
	 * @return
	 */
	private boolean isPlusPac(String aCode) {
		return EndorsementCodeEnum.PPA_SAV.getCode().equals(aCode) || EndorsementCodeEnum.PPB_SAV.getCode().equals(aCode);
	}

	/**
	 * checks if an endorsement is a CSE (CSE or CSF)
	 * 
	 * @param aCode
	 * @return
	 */
	private boolean isCse(String aCode) {
		return EndorsementCodeEnum._39_SAV.getCode().equals(aCode) || EndorsementCodeEnum._39F_SAV.getCode().equals(aCode);
	}

	/**
	 * checks if an endorsement is a CFE (CFE or CFF)
	 * 
	 * @param aCode
	 * @return
	 */
	private boolean isCfe(String aCode) {
		return EndorsementCodeEnum.CFE_SAV.getCode().equals(aCode) || EndorsementCodeEnum.CFF_SAV.getCode().equals(aCode);
	}
	
	/**
	 * checks if an endorsement is a Claims Advantage (49Y or 50Y or 49F or 50F)
	 * 
	 * @param aCode
	 * @return
	 */
	private boolean isClaimsAdvantage(String aCode) {
		return EndorsementCodeEnum._49F_SAV.getCode().equals(aCode) || EndorsementCodeEnum._49Y_SAV.getCode().equals(aCode)
				|| EndorsementCodeEnum._50F_SAV.getCode().equals(aCode) || EndorsementCodeEnum._50Y_SAV.getCode().equals(aCode);
	}

	/**
	 * Verify if full of glass is not eligible or is included.
	 * 
	 * @param aVehicleOfferBean
	 */
	private void loadFullGlassCoverage(VehicleOfferBeanAB aVehicleOfferBean) {

		CoverageOfferBean aCoverageOfferBean = aVehicleOfferBean.getComprehensiveDeductible();

		CoverageOfferBean aFullGlassCoverage = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum._13_SAV.getCode());
		CoverageOfferBean aLimitedGassCoverage = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum._13D_SAV.getCode());

		if (aCoverageOfferBean != null && aCoverageOfferBean.isCovered()) {

				// comprehensive included <= 1000$ - limited glass selected
				if (aLimitedGassCoverage.getCoverageSelectedIndicator() != null && aLimitedGassCoverage.getCoverageSelectedIndicator().booleanValue()) {
					aFullGlassCoverage.setEndorsementDisplayInd(Boolean.TRUE);
					aFullGlassCoverage.setEndorsementEligibleInd(Boolean.FALSE);
				}
				// comprehensive included <= 1000$ - limited glass not selected
				else {
					aFullGlassCoverage.setEndorsementDisplayInd(Boolean.TRUE);
					aFullGlassCoverage.setEndorsementEligibleInd(Boolean.TRUE);
					aFullGlassCoverage.setCoverageSelectedIndicator(Boolean.TRUE);
				}
		}
		// comprehensive refused
		else {
			aFullGlassCoverage.setEndorsementDisplayInd(Boolean.TRUE);
			aFullGlassCoverage.setEndorsementEligibleInd(Boolean.FALSE);

			aLimitedGassCoverage.setEndorsementDisplayInd(Boolean.TRUE);
			aLimitedGassCoverage.setEndorsementEligibleInd(Boolean.FALSE);
		}

	}

	private void updateLossOfUseCoverage(VehicleOfferBeanAB aVehicleOfferBean) {

		// if plus pac is included ==> loss of use of vehicle MUST also be
		// included
		CoverageOfferBean aPlusPacCoverage = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum.PPA_SAV.getCode());
		if (aPlusPacCoverage.getCoverageSelectedIndicator() != null && aPlusPacCoverage.getCoverageSelectedIndicator().booleanValue()
				&& aPlusPacCoverage.getCoverageSelectedIndicator() != null && aPlusPacCoverage.getCoverageSelectedIndicator().booleanValue()) {
			CoverageOfferBean aLossOfUseCoverage = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum._20_SAV.getCode());
			aLossOfUseCoverage.setEndorsementDisplayInd(Boolean.TRUE);
			aLossOfUseCoverage.setEndorsementEligibleInd(Boolean.TRUE);
			aLossOfUseCoverage.setCoverageSelectedIndicator(Boolean.TRUE);

		}
	}

	/**
	 * Loads the annual or monthly premium with taxes and/or surcharges
	 * according to the province for an offerBean
	 * 
	 * @param anOfferBean
	 * @param aPolicyTermInMonths
	 * @param anInsuranceRiskOffer
	 * @param aProvinceCode
	 */
	public void loadOfferBean(OfferBean anOfferBean, InsuranceRiskOffer anInsuranceRiskOffer, ProvinceCodeEnum aProvinceCode) {

		PolicyVersion aPolicy = anInsuranceRiskOffer.getInsuranceRisk().getPolicyVersion();

		double surcharge = 0;

		if (br5883_4perc.validate(aPolicy) && validateBillingPlan(aPolicy, aProvinceCode)) {
			surcharge = SURCHARGE_4PERC_PERCENTAGE_AB;
		}
		if (br5883_2perc.validate(aPolicy) && validateBillingPlan(aPolicy, aProvinceCode)) {
			surcharge = SURCHARGE_2PERC_PERCENTAGE_AB;
		}

		QuoteCalculationDetails quoteCalculationDetails = this.quotationService.loadOfferBean(anOfferBean.getAnnualPremium(), anInsuranceRiskOffer,
				aProvinceCode, surcharge, true);

		anOfferBean.setAnnualPremiumWithTaxes(quoteCalculationDetails.getAnnualAmountWithTaxes());
		anOfferBean.setMonthlyPremiumWithTaxes(quoteCalculationDetails.getMonthlyPaymentWithTaxes());

	}

	@Override
	public void loadCVI(OfferBean anOfferBean, InsuranceRiskOffer anInsuranceRiskOffer) {
		if (anInsuranceRiskOffer != null && anInsuranceRiskOffer.getCustomerValueIndexPureWithCredit() != null) {
			anOfferBean.setCustomerValueIndex(anInsuranceRiskOffer.getCustomerValueIndexPureWithCredit().toString());
		}
	}
	
}
