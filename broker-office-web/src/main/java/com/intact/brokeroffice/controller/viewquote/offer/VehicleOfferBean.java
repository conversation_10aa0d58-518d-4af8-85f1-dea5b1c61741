package com.intact.brokeroffice.controller.viewquote.offer;

import static java.util.Arrays.asList;
import static java.util.Collections.unmodifiableList;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.BasicCoverageCodeEnum;
import com.ing.canada.plp.domain.enums.EndorsementCodeEnum;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer;

public class VehicleOfferBean {

	public static final List<String> LIST_ENDORSEMENT_PLUS_PACK = unmodifiableList( asList(EndorsementCodeEnum.PPC_SAV.getCode(), EndorsementCodeEnum.PPD_SAV.getCode()));
	
	public static final List<String> LIST_ENDORSEMENT_PLUS_PACK_AB = unmodifiableList( asList(EndorsementCodeEnum.P1C_SAV.getCode(),EndorsementCodeEnum.P7C_SAV.getCode(),
														EndorsementCodeEnum.H1C_SAV.getCode(),EndorsementCodeEnum.H7C_SAV.getCode(),EndorsementCodeEnum.H1A_SAV.getCode(),EndorsementCodeEnum.H1B_SAV.getCode()) );
		
	/** The sequence. */
	private Short sequence;

	/** The offers. */
	private Map<String, OfferBean> offers = new HashMap<String, OfferBean>(4);

	/** The offers. */
	private List<OfferBean> offerList = new ArrayList<OfferBean>();

	/** The coverage level offers. */
	private Map<String, CoverageOfferBean> coverageOffers = new HashMap<String, CoverageOfferBean>();

	/** The coverage level offers. */
	private Map<String, CoverageOfferBean> mapEndorsements = new HashMap<String, CoverageOfferBean>();

	/** The make. */
	private String make;

	/** The model. */
	private String model;

	/** The year. */
	private String year;

	/** The selected offer type. */
	private String selectedOfferType;

	/** The offer selected. */
	private Boolean offerSelected = Boolean.FALSE;
	
	/** The current application mode*/
	private ApplicationModeEnum appMode;

	private static final String PREFIX_PREMIUM_OFFER_TYPE = "payment.premium.offer.type.";

	private List<CoverageOfferBean> anEndorsementList = new ArrayList<CoverageOfferBean>();
	
	/**
	 * Gets the make BR5208 Display Year, Make & Model of vehicle(s) selected in the On-line Auto Quote application
	 * BR5193 Display client response entered in the On-line Auto Quote application. BR5209 Repeat for each vehicle
	 * listed.
	 * 
	 * @return String
	 */
	public String getMake() {
		return this.make;
	}

	/**
	 * Sets the make
	 * 
	 * @param aMake the make to set
	 */
	public void setMake(String aMake) {
		this.make = aMake;
	}

	/**
	 * Gets the model BR5208 Display Year, Make & Model of vehicle(s) selected in the On-line Auto Quote application
	 * BR5193 Display client response entered in the On-line Auto Quote application. BR5209 Repeat for each vehicle
	 * listed.
	 * 
	 * @return String
	 */
	public String getModel() {
		return this.model;
	}

	/**
	 * Sets the model
	 * 
	 * @param aModel the model to set
	 */
	public void setModel(String aModel) {
		this.model = aModel;
	}

	/**
	 * Gets the mapping between offer type and offer bean
	 * 
	 * @return Map
	 */
	public Map<String, OfferBean> getOffers() {
		return this.offers;
	}

	/**
	 * Sets the mapping between offer type and offer bean
	 * 
	 * @param theOffers the offers to set
	 */
	public void setOffers(Map<String, OfferBean> theOffers) {
		this.offers = theOffers;
	}

	/**
	 * Gets the selected offer type
	 * 
	 * @return
	 */
	public String getSelectedOfferType() {
		return this.selectedOfferType;
	}

	public void setSelectedOfferType(String aSelectedOfferType) {
		this.selectedOfferType = aSelectedOfferType;
	}

	/**
	 * Sets the selected offer index.
	 * 
	 * @param aSelectedOfferType the a selected offer type
	 */
	public void setSelectedOfferType(OfferTypeCodeEnum aSelectedOfferType) {
		this.setSelectedOfferType(aSelectedOfferType != null ? aSelectedOfferType.getCode() : null);
	}

	/**
	 * Gets the year BR5208 Display Year, Make & Model of vehicle(s) selected in the On-line Auto Quote application
	 * BR5193 Display client response entered in the On-line Auto Quote application. BR5209 Repeat for each vehicle
	 * listed.:
	 * 
	 * @return String
	 */
	public String getYear() {
		return this.year;
	}

	/**
	 * Sets the year
	 * 
	 * @param aYear the year to set
	 */
	public void setYear(String aYear) {
		this.year = aYear;
	}

	/**
	 * checks if an offer has been selecetd
	 * 
	 * @return Boolean.TRUE else Boolean.FALSE
	 */
	public Boolean getOfferSelected() {
		return this.offerSelected;
	}

	/**
	 * Sets the offer selected
	 * 
	 * @param anOfferSelected the offerSelected to set
	 */
	public void setOfferSelected(Boolean anOfferSelected) {
		this.offerSelected = anOfferSelected;
	}

	/**
	 * Gets the sequence
	 * 
	 * @return Short
	 */
	public Short getSequence() {
		return this.sequence;
	}

	/**
	 * Sets the sequence
	 * 
	 * @param aSequence the sequence to set
	 */
	public void setSequence(Short aSequence) {
		this.sequence = aSequence;
	}
	
	/**
	 * Gets the application mode
	 * 
	 * @return ApplicationModeEnum
	 */
	public ApplicationModeEnum getAppMode() {
		return appMode;
	}

	/**
	 * Sets the application mode
	 * 
	 * @param appMode the application mode to set
	 */
	public void setAppMode(ApplicationModeEnum appMode) {
		this.appMode = appMode;
	}
	
	/**
	 * Gets the offers in the order they should be displayed on screen. If you wish to hide an offer under certain
	 * circumstances, it is feasible by returning a dynamically constructed array. The front-end code should never
	 * assume a specific order for the offers, the goal is to be able to change the order easily without breaking the UI
	 * code.
	 * 
	 * @return the offers display order
	 */
	public final OfferTypeCodeEnum[] getOffersDisplayOrder() {
		return new OfferTypeCodeEnum[] { OfferTypeCodeEnum.ECONOMY, OfferTypeCodeEnum.RECOMMENDED,
				OfferTypeCodeEnum.PREMIUM, OfferTypeCodeEnum.CUSTOM, 
				OfferTypeCodeEnum.COVERAGEADVISOR_PEOPLELIKEYOU, OfferTypeCodeEnum.COVERAGEADVISOR_RECOMMENDED };
	}

	/**
	 * Gets the offers mapped.
	 * 
	 * @param offerType the offer type
	 * 
	 * @return the offers mapped
	 */
	public final OfferBean getOffersMapped(String offerType) {
		return getOffersMapped(OfferTypeCodeEnum.valueOfCode(offerType));
	}

	/**
	 * Gets the coverages mapped.
	 * 
	 * @param basicCoverageType the basic coverage type
	 * 
	 * @return the coverages mapped
	 */
	public final CoverageOfferBean getCoveragesMapped(String basicCoverageType) {
		return getCoveragesMappedFrom(basicCoverageType);
	}

	/**
	 * Gets the coverages mapped.
	 * 
	 * @param basicCoverage the basic coverage code enum
	 * 
	 * @return the coverages mapped
	 */
	public final CoverageOfferBean getCoveragesMapped(BasicCoverageCodeEnum basicCoverage) {
		CoverageOfferBean coverage = this.coverageOffers.get(basicCoverage.getCode());
		return coverage;
	}

	/**
	 * Gets the coverages mapped.
	 * 
	 * @param basicCoverage the basic coverage code enum
	 * 
	 * @return the coverages mapped
	 */
	public final CoverageOfferBean getCoveragesMapped(EndorsementCodeEnum basicCoverage) {
		CoverageOfferBean coverage = this.coverageOffers.get(basicCoverage.getCode());
		return coverage;
	}

	/**
	 * Gets the offers mapped.
	 * 
	 * @param offerType the offer type
	 * 
	 * @return the offers mapped
	 */

	private OfferBean getOffersMapped(OfferTypeCodeEnum offerType) {

		OfferBean offer = this.offers.get(offerType.getCode());
		// the maps uses the code, not the enum!

		if (offer == null) {
			offer = new OfferBean(offerType);
			this.offers.put(offerType.getCode(), offer);
		}

		return offer;
	}

	/**
	 * Gets the coverages mapped.
	 * 
	 * @param coverageTypeCode the basic coverage code
	 * 
	 * @return the coverages mapped
	 */

	public CoverageOfferBean getCoveragesMappedFrom(String coverageTypeCode) {

		CoverageOfferBean coverage = this.coverageOffers.get(coverageTypeCode);
		// the maps uses the code, not the enum!
		if (coverage == null) {
			coverage = new CoverageOfferBean(coverageTypeCode);
			this.coverageOffers.put(coverageTypeCode, coverage);
		}

		return coverage;
	}

	/**
	 * Gets the endorsement mapped .
	 * 
	 * @param coverageTypeCode the basic coverage code
	 * 
	 * @return the endorsement mapped
	 */

	public CoverageOfferBean getEndorsementsMapped(String coverageTypeCode) {

		CoverageOfferBean coverage = this.mapEndorsements.get(coverageTypeCode);
		
		// the maps uses the code, not the enum!
		if (coverage == null) {
			coverage = new CoverageOfferBean(coverageTypeCode);
			
			this.mapEndorsements.put(coverageTypeCode, coverage);
		}

		return coverage;
	}
	
	public CoverageOfferBean getEndorsementsMappedWithValue(String coverageTypeCode, CoverageOffer coverageOffer) {
		
		CoverageOfferBean covOfferBean = null;
		
		if(LIST_ENDORSEMENT_PLUS_PACK_AB.contains(coverageTypeCode) && coverageOffer.getCoverageSelectedIndicator() != null 
				&& BooleanUtils.isTrue(coverageOffer.getCoverageSelectedIndicator())) {
			
			if(this.mapEndorsements.get("PPA") != null ) {
				this.mapEndorsements.get("PPA").setEndorsementDisplayInd(coverageOffer.getCoverageSelectedIndicator());
				this.mapEndorsements.get("PPA").setEndorsementIndicator(Boolean.TRUE);
				this.mapEndorsements.get("PPA").setEndorsementEligibleInd(coverageOffer.getCoverageEligibleIndicator());
				if (this.mapEndorsements.get("PPA").getEndorsementEligibleInd() == null
						|| !this.mapEndorsements.get("PPA").getEndorsementEligibleInd().booleanValue()) {
					this.mapEndorsements.get("PPA").setEndorsementEligibleInd(coverageOffer.getCoverageEligibleIndicator());
				}
			}

		}else if (StringUtils.equalsIgnoreCase("PPD",coverageTypeCode) && LIST_ENDORSEMENT_PLUS_PACK.contains(coverageTypeCode)) {
			if(this.mapEndorsements.get("PPD") != null) {
				this.mapEndorsements.get("PPD").setEndorsementDisplayInd(coverageOffer.getCoverageSelectedIndicator());
				this.mapEndorsements.get("PPD").setEndorsementIndicator(Boolean.TRUE);
				this.mapEndorsements.get("PPD").setEndorsementEligibleInd(coverageOffer.getCoverageEligibleIndicator());
				if (this.mapEndorsements.get("PPD").getEndorsementEligibleInd() == null
						|| !this.mapEndorsements.get("PPD").getEndorsementEligibleInd().booleanValue()) {
					this.mapEndorsements.get("PPD").setEndorsementEligibleInd(coverageOffer.getCoverageEligibleIndicator());
				}
			}
		}
		else if (StringUtils.equalsIgnoreCase("PPC", coverageTypeCode) && this.mapEndorsements.get("PPD") != null  
						&& coverageOffer.getCoverageSelectedIndicator() != null && BooleanUtils.isTrue(coverageOffer.getCoverageSelectedIndicator()) ) {
							
				this.mapEndorsements.get("PPD").setEndorsementDisplayInd(coverageOffer.getCoverageSelectedIndicator());
				this.mapEndorsements.get("PPD").setEndorsementIndicator(Boolean.TRUE);
				this.mapEndorsements.get("PPD").setEndorsementEligibleInd(coverageOffer.getCoverageEligibleIndicator());
				if (this.mapEndorsements.get("PPD").getEndorsementEligibleInd() == null
						|| !this.mapEndorsements.get("PPD").getEndorsementEligibleInd().booleanValue()) {
					this.mapEndorsements.get("PPD").setEndorsementEligibleInd(coverageOffer.getCoverageEligibleIndicator());
				}
		}else if(this.mapEndorsements.get(coverageTypeCode) != null) {
				
				this.mapEndorsements.get(coverageTypeCode).setEndorsementDisplayInd(coverageOffer.getCoverageSelectedIndicator());
				this.mapEndorsements.get(coverageTypeCode).setEndorsementIndicator(Boolean.TRUE);
				this.mapEndorsements.get(coverageTypeCode).setEndorsementEligibleInd(coverageOffer.getCoverageEligibleIndicator());
				if (this.mapEndorsements.get(coverageTypeCode).getEndorsementEligibleInd() == null
						|| !this.mapEndorsements.get(coverageTypeCode).getEndorsementEligibleInd().booleanValue()) {
					this.mapEndorsements.get(coverageTypeCode).setEndorsementEligibleInd(coverageOffer.getCoverageEligibleIndicator());
				}
			}
		

		return covOfferBean;
	}

	/**
	 * Gets the offers mapped.
	 * 
	 * @param offerType the offer type
	 * 
	 * @return the offers mapped
	 */

	public void intiMapCoverageToType(String basicCoverageType) {

		CoverageOfferBean coverage = this.coverageOffers.get(basicCoverageType);
		// the maps uses the code, not the enum!
		if (coverage == null) {
			coverage = new CoverageOfferBean(basicCoverageType);

			coverage.setAnnualPremium(0.0);
			this.coverageOffers.put(basicCoverageType, coverage);
		}
	}

	/**
	 * Gets the Map of Coverage code type to coverage offer bean
	 * 
	 * @return Map
	 */
	public Map<String, CoverageOfferBean> getCoverageOffers() {
		return this.coverageOffers;
	}

	/**
	 * Sets the Map of Coverage code type to coverage offer bean
	 * 
	 * @param coverageOffers the coverageOffers to set
	 */
	public void setCoverageOffers(Map<String, CoverageOfferBean> theCoverageOffers) {
		this.coverageOffers = theCoverageOffers;
	}

	/**
	 * Gets the offer list
	 * 
	 * @return List<OfferBean>
	 */
	public List<OfferBean> getOfferList() {
		if (this.offerList == null) {
			this.offerList = new ArrayList<OfferBean>();
		}
		return this.offerList;
	}

	/**
	 * Sets the offer list
	 * 
	 * @param offerList the offerList to set
	 */
	public void setOfferList(List<OfferBean> someOfferList) {
		this.offerList = someOfferList;
	}

	/**
	 * Gets the premium offer bean (PLUS) BR5209 Repeat for each vehicle listed.:
	 * 
	 * @return OfferBean
	 */
	public OfferBean getPlusOffer() {
		return this.getOffersMapped(OfferTypeCodeEnum.PREMIUM);
	}

	/**
	 * Gets the economy offer bean (ECONOMY) BR5209 Repeat for each vehicle listed.:
	 * 
	 * @return OfferBean
	 */
	public OfferBean getEconomyOffer() {
		return this.getOffersMapped(OfferTypeCodeEnum.ECONOMY);
	}

	/**
	 * Gets the custom offer bean BR5209 Repeat for each vehicle listed.:
	 * 
	 * @return OfferBean
	 */
	public OfferBean getCustomOffer() {
		return this.getOffersMapped(OfferTypeCodeEnum.CUSTOM);
	}

	/**
	 * Gets the preferred offer bean BR5209 Repeat for each vehicle listed.:
	 * 
	 * @return OfferBean
	 */
	public OfferBean getPreferredOffer() {
		return this.getOffersMapped(OfferTypeCodeEnum.RECOMMENDED);
	}
	
	/**
	 * Gets the people like you offer bean for coverage advisor. Repeat for each vehicle listed.:
	 * 
	 * @return OfferBean
	 */
	public OfferBean getPeopleLikeYouOffer() {
		return this.getOffersMapped(OfferTypeCodeEnum.COVERAGEADVISOR_PEOPLELIKEYOU);
	}
	
	/**
	 * Gets the recommended offer bean for coverage advisor. Repeat for each vehicle listed.:
	 * 
	 * @return OfferBean
	 */
	public OfferBean getRecommendedOffer() {
		return this.getOffersMapped(OfferTypeCodeEnum.COVERAGEADVISOR_RECOMMENDED);
	}

	public Map<String, CoverageOfferBean> getMapEndorsements() {
		return this.mapEndorsements;
	}
	
	/**
	 * Sets the map endorsements
	 * 
	 * @param endorsements the mapEndorsements to set
	 */

	public void setMapEndorsements(Map<String, CoverageOfferBean> endorsements) {
		this.mapEndorsements = endorsements;
	}

	/**
	 * Gets the endorsements selected list
	 * 
	 * @return List<CoverageOfferBean>
	 */
	public List<CoverageOfferBean> getEndorsementSelectedList() {
		List<CoverageOfferBean> endorsementSelectedList = new ArrayList<CoverageOfferBean>();
		for (CoverageOfferBean bean : this.getMapEndorsements().values()) {
			CoverageOfferBean aCoverageOffer = new CoverageOfferBean(bean.getCoverageCode(),
					bean.getCoverageDescription());
			aCoverageOffer.setManufacturerCompanyCode(bean.getManufacturerCompanyCode());
			aCoverageOffer.setRank(bean.getRank());
			endorsementSelectedList.add(aCoverageOffer);
		}
		return endorsementSelectedList;
	}

	/**
	 * Gets the endorsements list
	 * 
	 * @return List<CoverageOfferBean>
	 */
	public List<CoverageOfferBean> getEndorsementsSorted() {
		anEndorsementList.addAll(this.getMapEndorsements().values());
		Collections.sort(anEndorsementList, new Comparator<CoverageOfferBean>() {
			public int compare(CoverageOfferBean o1, CoverageOfferBean o2) {
					if (o1.getRank() == null) {
						return -1;
					} else if (o2.getRank() == null) {
						return 1;
					} else {
						return o1.getRank().compareTo(o2.getRank());
					}
				}
		});

		return anEndorsementList;
	}

	/**
	 * Gets the selected offer bean
	 * 
	 * @return OfferBean
	 */
	public OfferBean getSelectedOfferBean() {

		if (this.getOfferSelected() != null && this.getOfferSelected().booleanValue()) {
			return this.getOffersMapped(OfferTypeCodeEnum.valueOfCode(this.getSelectedOfferType()));
		}
		return null;
	}

	/**
	 * Gets the selected offer type key
	 * 
	 * @return the selected offer type key
	 */
	public String getSelectedOfferTypeKey() {
		String offerTypeKey = "";
		
		if (this.appMode == ApplicationModeEnum.QUICK_QUOTE) {
			offerTypeKey = PREFIX_PREMIUM_OFFER_TYPE + "QQ." + this.getSelectedOfferType();
		} else {
			offerTypeKey = PREFIX_PREMIUM_OFFER_TYPE + this.getSelectedOfferType();
		}
		
		return offerTypeKey;
	}

	/**
	 * Checks if price has been proposed to client for specified vehicle BR5206 Display this text when the quote has
	 * been saved/submitted prior to the client making a choice of coverages.:
	 * 
	 * @return
	 */
	public boolean isRatingCalculated() {
		return this.getEconomyOffer() != null && this.getEconomyOffer().getAnnualPremium() != null;
	}
}
