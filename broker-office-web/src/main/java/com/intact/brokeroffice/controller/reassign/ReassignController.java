package com.intact.brokeroffice.controller.reassign;

import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import jakarta.faces.context.FacesContext;
import jakarta.inject.Inject;

import org.owasp.esapi.ESAPI;
import org.owasp.esapi.Logger;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.cif.service.ISubBrokersService;
import com.intact.brokeroffice.business.domain.Quote;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.brokeroffice.controller.authentification.AuthentificationController;
import com.intact.brokeroffice.controller.permission.PermissionController;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.brokeroffice.controller.search.SearchController;
import com.intact.brokeroffice.helper.FaceMessageHelper;
import com.intact.brokeroffice.helper.ResourceBundleHelper;
import com.intact.brokeroffice.service.IBrokerService;
import com.intact.brokeroffice.service.bloommqhandler.IBloomMqHandlerService;

/**
 * The Class ReassignController.
 */
@Component
@Scope("session")
public class ReassignController {

	/** The sub brokers business process. */
	@Inject
	private ISubBrokersService subBrokersService;

	/** The quote business process. */
	@Inject
	private IBrokerService quoteBusinessProcess;

	/** The quotes controller. */
	@Inject
	private SearchController searchController;

	/**** VD248 List of available Sub-brokers. ****/
	private Map<String, Long> brokers = new LinkedHashMap<String, Long>();

	/**** DE691 New Assigned Broker. ****/
	private Long subBrokerId;
	
	private int index = 0;

	/** The language controller. */
	@Inject
	private ProvinceController provinceController;

	@Inject
	private AuthentificationController authentificationController;
	
	@Inject
	private PermissionController permissionController;
	
	@Inject
	private IBloomMqHandlerService bloomMqHandlerService;
	
	public SearchController getSearchController() {
		return this.searchController;
	}

	public void setSearchController(SearchController searchController) {
		this.searchController = searchController;
	}

	public IBrokerService getQuoteBusinessProcess() {
		return quoteBusinessProcess;
	}

	public void setQuoteBusinessProcess(IBrokerService quoteBusinessProcess) {
		this.quoteBusinessProcess = quoteBusinessProcess;
	}

	/**
	 * Reset.
	 */
	public void reset() {
		this.subBrokerId = null;
		this.brokers.clear();
		this.searchController.getSearchState().setSearchResults(null);
		this.searchController.getSearchState().setNbQuotesSelected(0);
	}

	/**
	 * Gets the broker.
	 * 
	 * @return the broker
	 */
	public Long getBroker() {
		return this.subBrokerId;
	}

	/**
	 * Sets the broker.
	 * 
	 * @param aSubBrokerId
	 *            the new broker
	 */
	public void setBroker(Long aSubBrokerId) {
		this.subBrokerId = aSubBrokerId;
	}

	/**
	 * Gets the brokers.
	 * 
	 * @return the brokers
	 */
	public Map<String, Long> getBrokers() {
		if (this.searchController.getHasQuotesSelected()) {
			if (this.searchController.getAlternateCompanyConfirmed()) {
				this.brokers.clear();
				this.searchController.setAlternateProvinceConfirmed(Boolean.FALSE);
			}

			if (this.brokers.isEmpty()) {

				List<ISubBrokers> subBrokersList;
				
				// Get Auto Regular Quote access type (should change in the future)
				List<ApplicationIdEnum> apps = new ArrayList<ApplicationIdEnum>();
				apps.add(ApplicationIdEnum.AUTO_REGULAR_QUOTE);
				List<LineOfBusinessEnum> lobs = new ArrayList<LineOfBusinessEnum>();
				lobs.add(LineOfBusinessEnum.PERSONAL_LINE);

				// identify if it's the right group for diff list
				if (this.authentificationController.isReassignBroker()) {
					subBrokersList = this.subBrokersService.getWebSubBrokers(this.authentificationController.getAvailableMasters(),
							this.provinceController.getCompanyEnumCode().getSubBrokerCompanyNumber(), ApplicationIdEnum.asList(), 
							LineOfBusinessEnum.asList());
				} else {
					subBrokersList = this.subBrokersService.getWebSubBrokers(new ArrayList<String>(), 
											this.provinceController.getCompanyEnumCode().getSubBrokerCompanyNumber(), apps, lobs);
				}

				this.brokers.put(ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.reassign.reassign", this, "select.default"), null);
				for (ISubBrokers subBrokers : subBrokersList) {
					this.brokers.put(subBrokers.getSubBrokerNumber() + " " + subBrokers.getNameLine1() + ", " + subBrokers.getCity(),
							subBrokers.getSubBrokerId());
				}
			}
		}
		return this.brokers;
	}

	public final int getIndex() {
		return this.index;
	}

	public final void setIndex(int index) {
		this.index = index;
	}

	/**
	 * Reassign.
	 * @throws BrokerServiceException 
	 * @throws com.intact.brokeroffice.business.exception.BrokerServiceException 
	 */
	public void reassign() throws BrokerServiceException {
		if (!validate()) {
			return;
		}

		
		try {
			this.quoteBusinessProcess.reassignQuotes(this.permissionController.getUserContext(), this.searchController.getSelectedQuotes(), "" + this.subBrokerId);
			this.sendQuotesToBloom(searchController.getSelectedQuotes());
			
		} catch (Exception ex) {
			ex.printStackTrace();
			this.getSearchController().getSearchState().setFailed("REASS", true);
		}

		reset();
		this.searchController.setReassignConfirmation(Boolean.TRUE);
	}

	private void sendQuotesToBloom(List<Quote> quotes) {
		for(Quote quote : quotes) {
			bloomMqHandlerService.sendQuoteToBloom(quote);
		}
	}

	/**
	 * Validate.
	 * 
	 * @return true, if successful
	 */
	private boolean validate() {

		boolean valid = true;

		if (this.subBrokerId == null || this.subBrokerId.doubleValue() == 0) {
			FaceMessageHelper.addErrorMessage("reassignForm:broker", "select.broker.error.msg", this);
			valid = false;
		}

		return valid;
	}

	/**
	 * Gets the oncomplete.
	 * 
	 * @return the oncomplete
	 */
	public String getOncomplete() {
		if (FacesContext.getCurrentInstance().getMaximumSeverity() == null) {
			return "Primefaces.hideModalPanel(\'mainTab:modalPanelReassign\');";
		}
		return "Primefaces.showModalPanel(\'mainTab:modalPanelReassign\');";
	}
}
