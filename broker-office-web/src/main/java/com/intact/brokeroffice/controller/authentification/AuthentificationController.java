package com.intact.brokeroffice.controller.authentification;

import com.ing.canada.common.web.api.auth.TamTokensEnum;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;
import com.intact.brokeroffice.helper.ResourceBundleHelper;
import jakarta.faces.context.FacesContext;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.servlet.http.HttpSession;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.StringTokenizer;

@Component
@Scope("session")
public class AuthentificationController {

	/**
	 * The ldap group broker.
	 */
	private final String ldapGroupBroker;

	private final String ldapGroupBrokerReassign;

	private final String b2bLogoutUrl;

	private final String b2eLogoutUrl;

	private final String spoeMode;

	private List<String> blockedSubbrokers;

	@Inject
	public AuthentificationController(@Named("ldap-group-broker") String ldapGroupBroker,
									  @Named("ldap-group-broker-reassign") String ldapGroupBrokerReassign,
									  @Named("logout-url-b2b") String b2bLogoutUrl,
									  @Named("logout-url-b2e") String b2eLogoutUrl,
									  @Named("spoe-mode") String spoeMode) {
		this.ldapGroupBroker = ldapGroupBroker;
		this.ldapGroupBrokerReassign = ldapGroupBrokerReassign;
		this.b2bLogoutUrl = b2bLogoutUrl;
		this.b2eLogoutUrl = b2eLogoutUrl;
		this.spoeMode = spoeMode;
	}


	/**
	 * Invalidates current session (if exists) and redirects to "logout.jsf" page.
   * <p>
   * This method is only used when {@link #spoeMode} is true. Otherwise, logout behavior is to redirect to
   * {@link #getLogoutUrl()} which handles logging out the user (removing webseal session cookie).
	 */
	public String logout() {
		FacesContext context = FacesContext.getCurrentInstance();
		HttpSession session = (HttpSession) context.getExternalContext().getSession(false);

		if (session != null) {
			session.invalidate();
		}

		return "logout";
	}

	/**
	 * Gets the url to exit
	 *
	 * @return the url to exit
	 */
	public String getLogoutUrl() {
		return isMasterRole() ? this.b2eLogoutUrl : this.b2bLogoutUrl;
	}

	/**
	 * Gets the current account uid.
	 *
	 * @return the current account uid
	 */
	public String getCurrentAccountUId() {
		FacesContext context = FacesContext.getCurrentInstance();
		HttpSession session = (HttpSession) context.getExternalContext().getSession(false);
		return (String) session.getAttribute(TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue());
	}

	/**
	 * Gets the available masters.
	 *
	 * @return the available masters
	 */
	@SuppressWarnings("unchecked")
	public List<String> getAvailableMasters() {
		FacesContext context = FacesContext.getCurrentInstance();
		HttpSession session = (HttpSession) context.getExternalContext().getSession(false);
		return (List<String>) session.getAttribute(SessionConstantsEnum.AVAILABLE_MASTERS.getSessionConstant());
	}

	/**
	 * Gets the current access level.
	 *
	 * @return the current access level
	 */
	public String getCurrentAccessLevel() {
		FacesContext context = FacesContext.getCurrentInstance();
		HttpSession session = (HttpSession) context.getExternalContext().getSession(false);
		return (String) session.getAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue());
	}

	/**
	 * Checks if is master role.
	 *
	 * @return true, if is master role
	 */
	public boolean isMasterRole() {
		// return !this.ldapGroupBroker.equals(getCurrentAccessLevel());
		return getCurrentAccessLevel() != null && !getCurrentAccessLevel().startsWith(
				this.ldapGroupBroker) && !getCurrentAccessLevel().startsWith(this.ldapGroupBrokerReassign);
	}

	public boolean isReassignBroker() {
		return getCurrentAccessLevel() != null && getCurrentAccessLevel().startsWith(this.ldapGroupBrokerReassign);
	}

	/**
	 * Checks if login user has subbrokers assigned to him
	 *
	 * @return true if user has subbrokers assigned to him else false
	 */
	public boolean isUserWithoutSubbrokers() {
		List<String> theAvailableMasters = this.getAvailableMasters();
		return !this.isMasterRole() && (theAvailableMasters == null || theAvailableMasters.isEmpty());
	}


	/**
	 * Gets the list of subbroker(s) not allowed to see the quote view
	 * <p>
	 * BR5572 Accounts with Quote View forbidden
	 *
	 * @return the list subbroker(s) not allowed to see the quote view
	 */
	public List<String> getBlockedSubbrokers() {

		if (this.blockedSubbrokers == null) {

			String LIST_BROKER_NOTALLOWED = "list-subbroker-notallowed";
			String subbrokers = ResourceBundleHelper.getMessage("brokerOffice-web", this, LIST_BROKER_NOTALLOWED);

			StringTokenizer st = new StringTokenizer(subbrokers, ";");

			this.blockedSubbrokers = new ArrayList<String>();
			while (st.hasMoreTokens()) {
				this.blockedSubbrokers.add((st.nextToken()).trim().toUpperCase());
			}
		}
		return this.blockedSubbrokers;
	}

	/**
	 * Check if user can view quote BR5572 Accounts with Quote View forbidden
	 *
	 * @return true if user can view quote else return false
	 */
	public boolean isCanViewQuote() {

		if (this.getBlockedSubbrokers() != null && this.getCurrentAccountUId() != null && this.getBlockedSubbrokers().contains(
				this.getCurrentAccountUId().toUpperCase())) {
			return false;
		}
		return true;

	}

	public String getLoginPageName() {
		return Boolean.parseBoolean(spoeMode) ? "spoe.jsf" : "index.jsf";
	}
}
