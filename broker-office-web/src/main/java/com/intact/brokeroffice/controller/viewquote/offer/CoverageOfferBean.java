package com.intact.brokeroffice.controller.viewquote.offer;

import java.math.BigDecimal;

import org.apache.commons.lang3.StringUtils;

import com.ing.canada.plp.domain.enums.EndorsementCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;

/**
 * <AUTHOR>
 * 
 */
public class CoverageOfferBean {

	private String coverageCode;

	private String coverageDescription;

	private BigDecimal coverageAmount;

	private Double annualPremium;

	private Double fullTermPremium;

	private Double annualPremiumCAA;

	private Double fullTermPremiumCAA;

	private Boolean coverageSelectedIndicator;

	private Boolean endorsementIndicator;

	private Boolean endorsementEligibleInd;

	private Boolean endorsementDisplayInd = Boolean.TRUE;

	private Integer rank;

	private static final String ENDORSEMENT_INCLUDED = "offer.endorsement.included";

	private static final String ENDORSEMENT_NOT_INCLUDED = "offer.endorsement.not.included";

	private static final String ENDORSEMENT_NOT_ELIGIBLE = "offer.endorsement.not.eligible";

	private static final String ENDORSEMENT_DECLINED = "offer.endorsement.declined";

	private static final String ENDORSEMENT_DESCRIPTION_KEY = "endorsement.description.";

	private static final String ENDORSEMENT_NOT_DISPLAY = "offer.not.selected";

	private static final String REGISTERED = "offer.endorsement.registered";

	private static final String PLUS_PAC_OPTION = "plus.pac.option";

	private ManufacturerCompanyCodeEnum manufacturerCompanyCode;

	/**
	 * first CONSTRUCTOR
	 */
	public CoverageOfferBean() {
		// default constructor
	}

	/**
	 * 2nd CONSTRUCTOR
	 * 
	 * @param aCode
	 */
	public CoverageOfferBean(String aCode) {
		this.coverageCode = aCode;
	}

	/**
	 * 3rd CONSTRUCTOR
	 * 
	 * @param aCode
	 * @param aDesc
	 */
	public CoverageOfferBean(String aCode, String aDesc) {
		this.coverageCode = aCode;
		this.coverageDescription = aDesc;
	}

	/**
	 * Gets the annual premium
	 * 
	 * @return Double
	 */
	public Double getAnnualPremium() {
		return this.annualPremium;
	}

	/**
	 * Sets the annual premium
	 * 
	 * @return anAnnualPremium the annualPremium to set
	 */
	public void setAnnualPremium(Double anAnnualPremium) {
		this.annualPremium = anAnnualPremium;
	}

	/**
	 * Gets the caa annual premium
	 * 
	 * @return Double
	 */
	public Double getAnnualPremiumCAA() {
		return this.annualPremiumCAA;
	}

	/**
	 * Sets the caa annual premium
	 * 
	 * @param anAnnualPremiumCAA the annualPremiumCAA to set
	 */
	public void setAnnualPremiumCAA(Double anAnnualPremiumCAA) {
		this.annualPremiumCAA = anAnnualPremiumCAA;
	}

	/**
	 * Gets the coverage code
	 * 
	 * @return String
	 */
	public String getCoverageCode() {
		return this.coverageCode;
	}

	/**
	 * Sets the coverage code
	 * 
	 * @param aCoverageCode the coverageCode to set
	 */
	public void setCoverageCode(String aCoverageCode) {
		this.coverageCode = aCoverageCode;
	}

	/**
	 * Gets the coverage description
	 * 
	 * @return String
	 */
	public String getCoverageDescription() {
		return this.coverageDescription;
	}

	/**
	 * Sets the coverage description
	 * 
	 * @return aCoverageDescription the coverageDescription to set
	 */
	public void setCoverageDescription(String aCoverageDescription) {
		this.coverageDescription = aCoverageDescription;
	}

	/**
	 * Checks if coverage has been selected
	 * 
	 * @return Boolean.TRUE else Boolean.FALSE
	 */
	public Boolean getCoverageSelectedIndicator() {
		return this.coverageSelectedIndicator;
	}

	/**
	 * Sets the coverage selected indicator
	 * 
	 * @param aCoverageSelectedIndicator the coverageSelectedIndicator to set
	 */
	public void setCoverageSelectedIndicator(Boolean aCoverageSelectedIndicator) {
		this.coverageSelectedIndicator = aCoverageSelectedIndicator;
	}

	/**
	 * Gets the full term premium
	 * 
	 * @return Double
	 */
	public Double getFullTermPremium() {
		return this.fullTermPremium;
	}

	/**
	 * Sets the full term premium
	 * 
	 * @param aFullTermPremium the fullTermPremium to set
	 */
	public void setFullTermPremium(Double aFullTermPremium) {
		this.fullTermPremium = aFullTermPremium;
	}

	/**
	 * Gets the full term premium CAA
	 * 
	 * @return Double
	 */
	public Double getFullTermPremiumCAA() {
		return this.fullTermPremiumCAA;
	}

	/**
	 * Sets the full term premium CAA
	 * 
	 * @param aFullTermPremiumCAA the fullTermPremiumCAA to set
	 */
	public void setFullTermPremiumCAA(Double aFullTermPremiumCAA) {
		this.fullTermPremiumCAA = aFullTermPremiumCAA;
	}

	/**
	 * Gets the coverage amount
	 * 
	 * @return BigDecimal
	 */
	public BigDecimal getCoverageAmount() {
		return this.coverageAmount;
	}

	/**
	 * Sets the coverage amount
	 * 
	 * @param aCoverageAmount the coverageAmount to set
	 */
	public void setCoverageAmount(BigDecimal aCoverageAmount) {
		this.coverageAmount = aCoverageAmount;
	}

	/**
	 * Gets the endoresment ind
	 * 
	 * @return Boolean.TRUE else Boolean.FALSE
	 */
	public Boolean getEndorsementIndicator() {
		return this.endorsementIndicator;
	}

	/**
	 * Sets the endorsement Indicator
	 * 
	 * @param anEndorsementIndicator the endorsementIndicator to set
	 */
	public void setEndorsementIndicator(Boolean anEndorsementIndicator) {
		this.endorsementIndicator = anEndorsementIndicator;
	}

	/**
	 * Gets the endorsement code enum value
	 * 
	 * @return EndorsementCodeEnum
	 */
	public EndorsementCodeEnum getEndorsementCodeEnum() {
		return EndorsementCodeEnum.valueOfCode(this.coverageCode, this.manufacturerCompanyCode);
	}

	/**
	 * Gets the endorsement eligible Indicator
	 * 
	 * @return
	 */
	public Boolean getEndorsementEligibleInd() {
		return this.endorsementEligibleInd;
	}

	/**
	 * Sets the endorsement eligible Indicator
	 * 
	 * @param endorsementEligibleInd
	 */
	public void setEndorsementEligibleInd(Boolean endorsementEligibleInd) {
		this.endorsementEligibleInd = endorsementEligibleInd;
	}
	
	public String getEndorsementStatus() {
		if(StringUtils.equalsIgnoreCase("PPA",this.getCoverageCode()) && this.endorsementDisplayInd != null && this.endorsementDisplayInd && this.endorsementEligibleInd != null && this.endorsementEligibleInd)
			return ENDORSEMENT_INCLUDED;
		else if(StringUtils.equalsIgnoreCase("PPD",this.getCoverageCode()) && this.endorsementDisplayInd != null && this.endorsementDisplayInd && this.endorsementEligibleInd != null && this.endorsementEligibleInd)
			return ENDORSEMENT_INCLUDED;
		else if(StringUtils.equalsIgnoreCase("PPD",this.getCoverageCode()) && this.endorsementDisplayInd != null && !this.endorsementDisplayInd && this.endorsementEligibleInd != null && !this.endorsementEligibleInd)
			return ENDORSEMENT_NOT_INCLUDED;
		else if(StringUtils.equalsIgnoreCase("PPA",this.getCoverageCode()) && this.endorsementDisplayInd != null && !this.endorsementDisplayInd && this.endorsementEligibleInd != null && !this.endorsementEligibleInd)
			return ENDORSEMENT_NOT_INCLUDED;
		else if(StringUtils.equalsIgnoreCase("13",this.getCoverageCode()) && this.endorsementDisplayInd != null && this.endorsementEligibleInd == null)
			return ENDORSEMENT_NOT_ELIGIBLE;
		else if(this.endorsementDisplayInd != null && this.endorsementDisplayInd && this.endorsementEligibleInd != null && this.endorsementEligibleInd)
			return ENDORSEMENT_INCLUDED;
		else if(this.endorsementDisplayInd != null && !this.endorsementDisplayInd && this.endorsementEligibleInd != null && !this.endorsementEligibleInd)
		 	return ENDORSEMENT_NOT_ELIGIBLE;
		else
			return ENDORSEMENT_NOT_INCLUDED;
	}
	
	/**
	 * Gets the endorsement description key from code enum
	 * 
	 * @return
	 */
	public String getEndorsementDescriptionKey() {

		if (this.coverageCode != null) {
			// I90 INOV endorsement
			if (EndorsementCodeEnum.I90.equals(this.getEndorsementCodeEnum())
					// NV1 - My Auto and Me endorsement
					|| EndorsementCodeEnum.NV1.equals(this.getEndorsementCodeEnum())
					// NV2 - My Auto and Me+ endorsement
					|| EndorsementCodeEnum.NV2.equals(this.getEndorsementCodeEnum())
					// 33E - Roadside assistance endorsement
					|| EndorsementCodeEnum._33E.equals(this.getEndorsementCodeEnum())
					// NBD - New Crashproof endorsement
					|| EndorsementCodeEnum.NBD.equals(this.getEndorsementCodeEnum())
					// NV3 - New Crashproof Open Road endorsement
					|| EndorsementCodeEnum.NV3.equals(this.getEndorsementCodeEnum())
					// 43V - Replacement Cost endorsement
					|| EndorsementCodeEnum._43V.equals(this.getEndorsementCodeEnum())
					// OPCF 20
					|| EndorsementCodeEnum._20_SAV.equals(this.getEndorsementCodeEnum())
					// PPC_SAV - Plus Pac endorsement
					|| EndorsementCodeEnum.PPC_SAV.equals(this.getEndorsementCodeEnum())
					// EPCE_SAV - Plus Pac endorsement
					|| EndorsementCodeEnum.EPCE_SAV.equals(this.getEndorsementCodeEnum())
					// PPD_SAV - Plus Pac endorsement
					|| EndorsementCodeEnum.PPD_SAV.equals(this.getEndorsementCodeEnum())
					// RDP_SAV - Responsible Driver Guarantee
					|| EndorsementCodeEnum.RDP_SAV.equals(this.getEndorsementCodeEnum())
					// ACHV_SAV - My Achiever
					|| EndorsementCodeEnum.ACHV_SAV.equals(this.getEndorsementCodeEnum())
					// DE14 INOV AUTOCOMFORT INDICATOR - PLUS PAC OPTION
					|| EndorsementCodeEnum.PPA_SAV.equals(this.getEndorsementCodeEnum())
					|| EndorsementCodeEnum.PPB_SAV.equals(this.getEndorsementCodeEnum())
					// DE661 SEF 39 - ACCIDENT RATING WAIVER
					|| EndorsementCodeEnum._39_SAV.equals(this.getEndorsementCodeEnum())
					|| EndorsementCodeEnum._39F_SAV.equals(this.getEndorsementCodeEnum())
					// DE1596 MINOR CONVICTION RATING WAIVER
					|| EndorsementCodeEnum.CFE_SAV.equals(this.getEndorsementCodeEnum())
					|| EndorsementCodeEnum.CFF_SAV.equals(this.getEndorsementCodeEnum())
					// DE1597 FULL GLASS COVERAGE
					|| EndorsementCodeEnum._13_SAV.equals(this.getEndorsementCodeEnum())
					// DE663 SEF 13D - COMPREHENSIVE COVER - LIMITED GLASS ENDORSEMENT
					|| EndorsementCodeEnum._13D_SAV.equals(this.getEndorsementCodeEnum())
					// DE2354 RELY Network ***POSTPONED***
					// || EndorsementCodeEnum.RELY.equals(this.getEndorsementCodeEnum())
					// UBI
					|| EndorsementCodeEnum.UBI.equals(this.getEndorsementCodeEnum())
					|| EndorsementCodeEnum.UE05.equals(this.getEndorsementCodeEnum())
					// DE2972 - CLAIMS ADVANTAGE
					|| EndorsementCodeEnum._49Y_SAV.equals(this.getEndorsementCodeEnum())
					|| EndorsementCodeEnum._50Y_SAV.equals(this.getEndorsementCodeEnum())
					|| EndorsementCodeEnum._49F_SAV.equals(this.getEndorsementCodeEnum())
					|| EndorsementCodeEnum._50F_SAV.equals(this.getEndorsementCodeEnum())
					// DE3247 : CEA 17
					|| EndorsementCodeEnum._H17_SAV.equals(this.getEndorsementCodeEnum())
					// DE3248 : CEA 18
					|| EndorsementCodeEnum._H18_SAV.equals(this.getEndorsementCodeEnum())
			) {
				return ENDORSEMENT_DESCRIPTION_KEY + getEndorsementCodeEnum().getCode();
			}
		}
		return ENDORSEMENT_DESCRIPTION_KEY + "default";
	}

	/**
	 * Gets the endorsement registered code for the description key from code enum
	 * 
	 * @return
	 */
	public String getEndorsementRegisteredCode() {
		if (this.coverageCode != null) {
			// RDG - Responsible Driver Guarantee
			if (EndorsementCodeEnum.RDP_SAV.equals(this.getEndorsementCodeEnum())
					|| EndorsementCodeEnum.ACHV_SAV.equals(this.getEndorsementCodeEnum())) {
				return REGISTERED;
			}
		}
		return ENDORSEMENT_DESCRIPTION_KEY + "default";
	}

	/**
	 * Gets the Plus Pac Option Code
	 * 
	 * @return
	 */
	public String getPlusPacOption() {
		if (this.coverageCode != null && this.isPlusPac() && this.endorsementEligibleInd != null
				&& this.endorsementDisplayInd.booleanValue() && this.coverageSelectedIndicator != null
				&& this.coverageSelectedIndicator.booleanValue()) {

			return PLUS_PAC_OPTION + "." + this.coverageCode;
		}

		return ENDORSEMENT_DESCRIPTION_KEY + "default";
	}

	/**
	 * Gets the rank
	 * 
	 * @return the rank
	 */
	public Integer getRank() {
		return this.rank;
	}

	/**
	 * Sets the rank
	 * 
	 * @param aRank the rank to set
	 */
	public void setRank(Integer aRank) {
		this.rank = aRank;
	}

	/**
	 * Gets the endorsement display ind
	 * 
	 * @return
	 */
	public Boolean getEndorsementDisplayInd() {
		return this.endorsementDisplayInd;
	}

	/**
	 * Set the endorsement display indicator
	 * 
	 * @param aEndorsementDisplayInd
	 */
	public void setEndorsementDisplayInd(Boolean aEndorsementDisplayInd) {
		this.endorsementDisplayInd = aEndorsementDisplayInd;
	}

	/**
	 * @return
	 */
	public ManufacturerCompanyCodeEnum getManufacturerCompanyCode() {
		return this.manufacturerCompanyCode;
	}

	/**
	 * @param aManufacturerCompanyCode
	 */
	public void setManufacturerCompanyCode(ManufacturerCompanyCodeEnum aManufacturerCompanyCode) {
		this.manufacturerCompanyCode = aManufacturerCompanyCode;
	}

	/**
	 * Gets covered indicator BR5216 If the client did not choose this coverage, display Not Covered / Non-couvert:
	 * 
	 * @return covered boolean true else false
	 */
	public boolean isCovered() {
		return this.coverageAmount != null;

	}

	public boolean isPlusPac() {
		return EndorsementCodeEnum.PPC_SAV.getCode().equals(this.coverageCode)
				|| EndorsementCodeEnum.EPCE_SAV.getCode().equals(this.coverageCode)
				|| EndorsementCodeEnum.PPD_SAV.getCode().equals(this.coverageCode)
				|| EndorsementCodeEnum.PPA_SAV.getCode().equals(this.coverageCode)
				|| EndorsementCodeEnum.PPB_SAV.getCode().equals(this.coverageCode);
	}

	public boolean isCse() {
		return EndorsementCodeEnum._39_SAV.getCode().equals(this.coverageCode)
				|| EndorsementCodeEnum._39F_SAV.getCode().equals(this.coverageCode);
	}

	public boolean isCfe() {
		return EndorsementCodeEnum.CFE_SAV.getCode().equals(this.coverageCode)
				|| EndorsementCodeEnum.CFF_SAV.getCode().equals(this.coverageCode);
	}

	// TODO check if needed
	public boolean isClaimsAdvantage() {
		return EndorsementCodeEnum._49Y_SAV.getCode().equals(this.coverageCode)
				|| EndorsementCodeEnum._50Y_SAV.getCode().equals(this.coverageCode)
				|| EndorsementCodeEnum._49F_SAV.getCode().equals(this.coverageCode)
				|| EndorsementCodeEnum._50F_SAV.getCode().equals(this.coverageCode);
	}

}
