package com.intact.brokeroffice.controller.viewquote.offer;

import static java.util.Arrays.asList;
import static java.util.Collections.unmodifiableList;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.ing.canada.common.services.api.Language;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.BasicCoverageCodeEnum;
import com.ing.canada.plp.domain.enums.BillingPlanCodeEnum;
import com.ing.canada.plp.domain.enums.CoverageTypeCodeEnum;
import com.ing.canada.plp.domain.enums.EndorsementCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.vehicle.VehicleDetailSpecificationRepositoryEntry;
import com.ing.canada.plp.domain.vehicle.VehicleRepositoryEntry;
import com.intact.brokeroffice.controller.viewquote.payment.PremiumBean;
@Component
public abstract class OfferAdapter<E extends VehicleOfferBean> extends OfferBaseAdapter implements IOfferAdapter<E> {


	public static final List<String> LIST_ENDORSEMENT_PLUS_PACK = unmodifiableList( asList(EndorsementCodeEnum.PPC_SAV.getCode(), EndorsementCodeEnum.PPD_SAV.getCode(),EndorsementCodeEnum.P1C_SAV.getCode(),
			EndorsementCodeEnum.P7C_SAV.getCode(), EndorsementCodeEnum.H1C_SAV.getCode(),EndorsementCodeEnum.H7C_SAV.getCode(),EndorsementCodeEnum.H1A_SAV.getCode(),EndorsementCodeEnum.H1B_SAV.getCode()) );
	
	
	private List<CoverageOfferBean> endorsementItems = new ArrayList<CoverageOfferBean>();
	public Boolean getConsent(PolicyVersion policyVersion) {
		return null;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.intact.brokeroffice.controller.viewquote.offer.IOfferAdapter#loadBean(com.ing.canada.plp.domain.policyversion
	 * .PolicyVersion, com.ing.canada.plp.domain.insurancerisk.InsuranceRisk,
	 * com.intact.brokeroffice.controller.viewquote.offer.VehicleOfferBean, java.util.Locale,
	 * com.ing.canada.plp.domain.enums.ProvinceCodeEnum)
	 */
	public void loadBean(InsuranceRisk anInsuranceRisk, E vehicleOffer, Locale locale, ProvinceCodeEnum aProvinceCode, ApplicationModeEnum appMode) {

		// Get the language
		Language language = Language.fromLocale(locale);

		vehicleOffer.setSequence(anInsuranceRisk.getInsuranceRiskSequence());
		setVehicleDescription(vehicleOffer, anInsuranceRisk, language);
		vehicleOffer.setAppMode(appMode);

		// retrieves selected offer
		InsuranceRiskOffer selectedOffer = anInsuranceRisk.getSelectedInsuranceRiskOffer();
		if (selectedOffer != null) {
			vehicleOffer.setOfferSelected(!anInsuranceRisk.getInsuranceRiskOfferSystemSelectedIndicator()
					&& selectedOffer.getAnnualPremium() != null && !selectedOffer.getAnnualPremium().equals(0));

			vehicleOffer.setSelectedOfferType(anInsuranceRisk.getInsuranceRiskOfferSystemSelectedIndicator() ? null
					: selectedOffer.getOfferType());
		}

		// Populate the offers
		for (OfferTypeCodeEnum offerType : vehicleOffer.getOffersDisplayOrder()) {
			OfferBean offer = vehicleOffer.getOffersMapped(offerType.getCode()); // form creates object if necessary
			InsuranceRiskOffer insuranceRiskOffer = this.insuranceRiskOfferHelper.getInsuranceRiskOfferForOfferType(
					anInsuranceRisk, offerType);
			offer.setHasCorrespondingInsuranceRiskOffer(insuranceRiskOffer != null);

			offer.validateOfferSelection(
					vehicleOffer.getOfferSelected(),
					vehicleOffer.getSelectedOfferType() != null ? OfferTypeCodeEnum.valueOfCode(vehicleOffer
							.getSelectedOfferType()) : null);

			// Only the custom offer can have an annual premium value of null, other offers
			// always have annual premiums
			NumberFormat nf = NumberFormat.getCurrencyInstance(locale);
			nf.setMaximumFractionDigits(2);

			offer.setAnnualPremium(insuranceRiskOffer != null && insuranceRiskOffer.getAnnualPremium() != null
					&& insuranceRiskOffer.getAnnualPremium() != 0 ? Double.valueOf(insuranceRiskOffer
					.getAnnualPremium()) : null);

			offer.setMonthlyPremium(insuranceRiskOffer != null && insuranceRiskOffer.getAnnualPremium() != null
					&& insuranceRiskOffer.getAnnualPremium() != 0 ? Double.valueOf(insuranceRiskOffer
					.getAnnualPremium().doubleValue() / 12) : null);

			if (offer.getSelected()) {
				loadOfferBean(offer, insuranceRiskOffer, aProvinceCode);
			}

			loadCVI(offer, insuranceRiskOffer);

			vehicleOffer.getOfferList().add(offer);
		}
	}

	public void loadCVI(OfferBean anOfferBean, InsuranceRiskOffer anInsuranceRiskOffer) {
		// implement in child classes only
	}

	/**
	 * Method to initialize the vehicle description
	 * 
	 * @param vehicle
	 * @param insuranceRisk
	 * @param language
	 */
	private void setVehicleDescription(VehicleOfferBean vehicle, InsuranceRisk insuranceRisk, Language language) {

		VehicleDetailSpecificationRepositoryEntry vEntry = insuranceRisk.getVehicle()
				.getVehicleDetailSpecificationRepositoryEntry();

		if (vEntry != null) {

			VehicleRepositoryEntry vehicleDesc = vEntry.getVehicleRepositoryEntry();

			if (vehicleDesc != null) {
				vehicle.setYear(vEntry.getVehicleYear().toString());
				if (Language.FRENCH.equals(language)) {
					vehicle.setMake(vehicleDesc.getVehicleMakeFrench());
					vehicle.setModel(vehicleDesc.getVehicleModelFrench());
				} else if (Language.ENGLISH.equals(language)) {
					vehicle.setMake(vehicleDesc.getVehicleMakeEnglish());
					vehicle.setModel(vehicleDesc.getVehicleModelEnglish());
				}
			}
		}
	}

	/**
	 * init coverage offer list
	 * 
	 * @param aVehicleOfferBean
	 * @param coverageCodeEnums
	 */
	public void initCoverageOffer(E aVehicleOfferBean, BasicCoverageCodeEnum[] coverageCodeEnums) {
		// init the coverages Map
		for (BasicCoverageCodeEnum coverageType : coverageCodeEnums) {
			aVehicleOfferBean.intiMapCoverageToType(coverageType.getCode());
		}
	}

	/**
	 * Method to load the coverage offer bean from the insurance risk offer object
	 * 
	 * @param offerType
	 * @param insuranceRiskOffer
	 * @param insuranceRisk
	 * 
	 * @return List<CoverageOfferBean>
	 */
	public void loadCoverageOfferBeanFromInsuranceRiskOffer(E aVehicleOfferBean,
			BasicCoverageCodeEnum[] coverageCodeEnums, InsuranceRiskOffer insuranceRiskOffer,
			ManufacturerCompanyCodeEnum aManufacturerCompanyCode) {

		// There are multiple Code for the PlusPack Endorsement. We only need one. So for example if we receive twice the code for the PlusPack 
		// we will treat it once and diregard the others.
		Boolean isPlusPackAlreadyTreated = false;
		
		initCoverageOffer(aVehicleOfferBean, coverageCodeEnums);

		for (CoverageOffer covOff : insuranceRiskOffer.getCoverageOffers()) {

			// FOR QUEBEC ENDORSEMENTS (display only I90, NV1, NV2, 33E, NBD, NV3, _43V)
			// FOR ONTARIO ENDORSEMENTS (display only _20_SAV, PPC_SAV, PPD_SAV, RDG_SAV, ACHV_SAV)
			CoverageOfferBean covOfferBean = null;
			if (CoverageTypeCodeEnum.ENDORSEMENT.equals(covOff.getCoverageRepositoryEntry().getCoverageType())) {
				if (isValidEndorsements(EndorsementCodeEnum.valueOfCode(covOff.getCoverageRepositoryEntry()
						.getCoverageCode(), aManufacturerCompanyCode))) {

					String coverageCode = obtainCoverageCode(covOff);
					
					for(CoverageOfferBean coverageOfferBean : aVehicleOfferBean.getEndorsementSelectedList()) {
						if(StringUtils.equalsIgnoreCase(coverageCode, coverageOfferBean.getCoverageCode()) || LIST_ENDORSEMENT_PLUS_PACK.contains(coverageCode) ) {
							covOfferBean = aVehicleOfferBean.getEndorsementsMappedWithValue(coverageCode, covOff);
							
							for(int i = 0; i < aVehicleOfferBean.getEndorsementSelectedList().size(); i++) {
								if(StringUtils.equalsIgnoreCase(coverageCode, aVehicleOfferBean.getEndorsementSelectedList().get(i).getCoverageCode())) {
									aVehicleOfferBean.getEndorsementSelectedList().get(i).setEndorsementDisplayInd(covOff.getCoverageSelectedIndicator());
									aVehicleOfferBean.getEndorsementSelectedList().get(i).setEndorsementIndicator(Boolean.TRUE);
									aVehicleOfferBean.getEndorsementSelectedList().get(i).setEndorsementEligibleInd(covOff.getCoverageEligibleIndicator());
									if (aVehicleOfferBean.getEndorsementSelectedList().get(i).getEndorsementEligibleInd() == null
											|| !aVehicleOfferBean.getEndorsementSelectedList().get(i).getEndorsementEligibleInd().booleanValue()) {
										aVehicleOfferBean.getEndorsementSelectedList().get(i).setEndorsementEligibleInd(covOff.getCoverageEligibleIndicator());
									}
									if(LIST_ENDORSEMENT_PLUS_PACK.contains(coverageCode) && BooleanUtils.isFalse(isPlusPackAlreadyTreated))
										isPlusPackAlreadyTreated = true;
								}

							}
						}
					}
				}
			}
			// FOR QUEBEC COVERAGE LEVELS (Civil liability, Collision deductible, Comprehensive deductible, Accidents
			// benefits, Family protection)
			// FOR ONTARIO COVERAGE LEVELS (Liability, Collision deductible, Comprehensive deductible, Accidents
			// benefits, Family protection)
			else {
				// RETRIEVE ONLY SELECTED COVERAGE
				covOfferBean = initSelectedCoverage(covOff, aVehicleOfferBean);
			}
			if (covOfferBean != null) {
				covOfferBean.setCoverageDescription(covOff.getCoverageDescription());
				// important : to validate proper PPAC information
				loadCoverageInformation(covOfferBean, covOff);
			}
		}
	}

	/**
	 * Loads the coverage offer bean for a vehicle
	 * 
	 * @param covOff
	 * @param aVehicleOfferBean
	 * @return CoverageOfferBean
	 */
	public CoverageOfferBean initSelectedCoverage(CoverageOffer covOff, E aVehicleOfferBean) {

		// RETRIEVE ONLY SELECTED COVERAGE
		CoverageOfferBean covOfferBean = null;
		if (covOff != null && covOff.getCoverageSelectedIndicator() != null && covOff.getCoverageSelectedIndicator()) {
			covOfferBean = aVehicleOfferBean.getCoveragesMapped(covOff.getCoverageRepositoryEntry().getCoverageCode());
			covOfferBean.setEndorsementIndicator(Boolean.FALSE);

			BigDecimal amount = null;
			if (covOff.getDeductibleAmount() != null) {
				amount = BigDecimal.valueOf(covOff.getDeductibleAmount()).setScale(2);
			}
			if (covOff.getCoverageRepositoryEntry() != null
					&& (BasicCoverageCodeEnum.LIABILITY_A.getCode().equals(
							covOff.getCoverageRepositoryEntry().getCoverageCode()) || BasicCoverageCodeEnum.LIABILITY_LIAB
							.getCode().equals(covOff.getCoverageRepositoryEntry().getCoverageCode()))) {
				if (covOff.getLimitOfInsurance() != null) {
					amount = BigDecimal.valueOf(covOff.getLimitOfInsurance()).setScale(2);
				}
			}
			covOfferBean.setCoverageAmount(amount);
		}
		return covOfferBean;

	}

	/**
	 * verifies if the client has selected an offer
	 * 
	 * @param aVehicleOfferBean
	 * @param insuranceRiskOffer
	 * @return
	 */
	public boolean isOfferSelected(E aVehicleOfferBean, InsuranceRiskOffer insuranceRiskOffer) {
		return (insuranceRiskOffer != null && aVehicleOfferBean.getOfferSelected() != null && aVehicleOfferBean
				.getOfferSelected().booleanValue());
	}

	/**
	 * Method uses to identify the selected endorsements
	 * 
	 * @param vehicleOfferBeanList
	 * @param aMapEndorsementOptions
	 */
	/**
	 * Method uses to identify the selected endorsements
	 * 
	 * @param vehicleOfferBeanList
	 * @param aMapEndorsementOptions
	 */
	public void buildEndorsementsSelectedForEachVehicle(List<E> vehicleOfferBeanList,
			Map<String, CoverageOfferBean> aMapEndorsementOptions) {
		if (aMapEndorsementOptions != null && aMapEndorsementOptions.keySet() != null && CollectionUtils.isNotEmpty(aMapEndorsementOptions.values())) {
			this.endorsementItems = new ArrayList<CoverageOfferBean>();
			this.endorsementItems.addAll(aMapEndorsementOptions.values());
			
			Collections.sort(this.endorsementItems, new Comparator<CoverageOfferBean>() {
				public int compare(CoverageOfferBean e1, CoverageOfferBean e2) {
						return e1.getRank().compareTo(e2.getRank());
				}
			});

			for (String endorsementKey : aMapEndorsementOptions.keySet()) {
				for (E vehicule : vehicleOfferBeanList) {
					String theKey = this.mappingEndorsementKey(endorsementKey);
					CoverageOfferBean endorsement = vehicule.getEndorsementsMapped(theKey);
					if (!vehicule.getOfferSelected()) {
						endorsement.setEndorsementIndicator(Boolean.TRUE);
						resetEndorsementRank(theKey, endorsement);
						endorsement.setEndorsementDisplayInd(Boolean.FALSE);

						endorsement.setCoverageSelectedIndicator(Boolean.FALSE);
					}
				}
			}
		}
		if (aMapEndorsementOptions != null) {
			aMapEndorsementOptions.clear();
		}
	}
	

	/**
	 * Gets the endorsement items
	 * 
	 * @return List<CoverageOfferBean>
	 */
	public List<CoverageOfferBean> getEndorsementItems() {
		return this.endorsementItems;
	}

	/**
	 * Set the endorsement items
	 * 
	 * @param endorsementElements the endorsementItems to set
	 */
	public void setEndorsementItems(List<CoverageOfferBean> endorsementElements) {
		this.endorsementItems = endorsementElements;
	}

	/**
	 * Methods to build the premium bean information
	 * 
	 * @param vehicleOfferBeanList
	 * @param premiumBean
	 * @param policyVersion
	 */
	@SuppressWarnings("unchecked")
	public void loadPremiumBean(List<E> vehicleOfferBeanList, PremiumBean premiumBean) {
		// verify if Premium Exist
		premiumBean.setPremiumExist(this.verifyPremiumExistence(vehicleOfferBeanList));
		premiumBean.getVehicleOfferBeanList().addAll(vehicleOfferBeanList);
	}

	/**
	 * Checks if premium exists
	 * 
	 * @param vehicleOfferBeanList
	 * @param premiumBean
	 * @return Boolean.TRUE if exist else return Boolean.FALSE
	 */
	private Boolean verifyPremiumExistence(List<E> vehicleOfferBeanList) {
		for (E vehOfferBean : vehicleOfferBeanList) {
			if (BooleanUtils.isTrue(vehOfferBean.getOfferSelected())) {
				return Boolean.TRUE;
			}
		}
		return Boolean.FALSE;
	}

	/**
	 * Checks if it is a valid endorsement
	 * 
	 * @param codeEnum
	 * @return
	 */

	protected abstract boolean isValidEndorsements(EndorsementCodeEnum codeEnum);

	/**
	 * Checks if it is a valid endorsement
	 * 
	 * @param codeEnum
	 * @return
	 */
	protected abstract void resetEndorsementRank(String codeValueEnum, CoverageOfferBean aCoverageOfferBean);

	/**
	 * Initialize the ENDORSEMENTS
	 * 
	 * @param aVehicleOfferBean (non-Javadoc)
	 * @see com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#initEndorsement(com.intact.brokeroffice.controller.viewquote.offer.VehicleOfferBean,
	 *      com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum)
	 */

	protected abstract void initEndorsement(E aVehicleOfferBean, ManufacturerCompanyCodeEnum aManufacturerCompanyCode);

	/**
	 * Reset the coverage code only for PLUS PAC
	 * 
	 * @param aCoverageOffer
	 * @return
	 */
	protected abstract String obtainCoverageCode(CoverageOffer aCoverageOffer);

	/**
	 * RESET THE ENDORSEMENT KEY ONLY FOR PLUS PAC
	 * 
	 * @param anEndosementKey
	 * @return
	 */
	protected abstract String mappingEndorsementKey(String anEndosementKey);

	/**
	 * Method to load all the endorsements selected from offer
	 * 
	 * @param aVehicleOfferBean
	 * @param mapEndorsementOptions
	 */
	protected abstract void loadAllEndorsementsSelectedFromOffer(E aVehicleOfferBean,
			Map<String, CoverageOfferBean> mapEndorsementOptions);

	/**
	 * Loads the annual or monthly premium with taxes and/or surcharges according to the province for an offerBean
	 * 
	 * @param anOfferBean
	 * @param aPolicyTermInMonths
	 * @param anInsuranceRiskOffer
	 * @param aProvinceCode
	 */
	public abstract void loadOfferBean(OfferBean anOfferBean, InsuranceRiskOffer anInsuranceRiskOffer,
			ProvinceCodeEnum aProvinceCode);

	/**
	 * Methods to load the coverage information
	 * 
	 * @param covOfferBean
	 * @param covOff
	 */
	protected abstract void loadCoverageInformation(CoverageOfferBean covOfferBean, CoverageOffer covOff);

	/**
	 * 
	 * @param aPolicyVersion
	 * @param aProvinceCode
	 * @return
	 */
	protected boolean validateBillingPlan(PolicyVersion aPolicyVersion, ProvinceCodeEnum aProvinceCode) {
		if (aProvinceCode != null
				&& !ProvinceCodeEnum.QUEBEC.equals(aProvinceCode)
				&& aPolicyVersion.getBilling() != null
				&& (BillingPlanCodeEnum.PRE_AUTHORIZED_CHEQUE_PLAN.equals(aPolicyVersion.getBilling().getBillingPlan()) || BillingPlanCodeEnum.CREDIT_CARD_MONTHLY
						.equals(aPolicyVersion.getBilling().getBillingPlan()))) {
			return true;
		}
		return false;
	}

}
