package com.intact.brokeroffice.controller.quotes;

import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfInsuranceCodeEnum;
import com.intact.brokeroffice.business.quote.QuotesBean;
import com.intact.brokeroffice.controller.language.LanguageController;
import com.intact.brokeroffice.helper.ResourceBundleHelper;
import com.intact.brokeroffice.service.util.Configuration;
import com.intact.plt.information.service.InformationApplicationService;
import com.intact.plt.information.service.client.domain.InputLocaleAdapter;
import com.intact.plt.information.service.client.util.InformationPieceTO;
import com.intact.plt.information.service.client.util.InformationSearchTO;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import java.util.ArrayList;

@Component
@Scope("request")
public class QuoteToolTipController {

	@Inject
	private LanguageController languageController = null;

	@Inject
	private InformationApplicationService informationService = null;

	@Inject
	@Named("plt.information.keys")
	private Configuration informationKeys = null;

	private QuotesBean quote = null;

	public QuoteToolTipController() {
		super();
	}

	public LanguageController getLanguageController() {
		return this.languageController;
	}

	public void setLanguageController(LanguageController languageController) {
		this.languageController = languageController;
	}


	public InformationApplicationService getInformationService() {
		return this.informationService;
	}

	public void setInformationService(InformationApplicationService informationService) {
		this.informationService = informationService;
	}

	public Configuration getInformationKeys() {
		return this.informationKeys;
	}

	public void setInformationKeys(Configuration informationKeys) {
		this.informationKeys = informationKeys;
	}

	public QuotesBean getQuote() {
		return this.quote;
	}

	public void resetQuote() {
		this.quote = null;
	}

	public void setQuote(String quoteId, String lineOfBusiness, String lineOfInsurance,
						 String agreementNumber) {
		this.quote = new QuotesBean(quoteId, LineOfBusinessCodeEnum.valueOfCode(lineOfBusiness),
									LineOfInsuranceCodeEnum.valueOfCode(lineOfInsurance));
		this.quote.setAgreementNumber(agreementNumber);
	}

	public InformationPieceTO getTooltipInfo() throws Exception {

		InformationPieceTO piece = null;

		try {
			if (this.getQuote() != null) {
				piece = this.getInformationService()
						.getInformation((String) this.getInformationKeys()
												.getValue("tooltip-" + this.getQuote().getApplicationMode()),
										this.buildSearch(this.getQuote().getId()));
			}
		} catch (Exception ex) {
			piece = this.buildErrorPiece();
			ex.printStackTrace();
		}

		this.resetQuote();

		return piece;
	}

	protected InformationPieceTO buildErrorPiece() {
		InformationPieceTO piece = new InformationPieceTO();
		piece.setValue("error");
		piece.getChildrens().addAll(new ArrayList<InformationPieceTO>());

		InformationPieceTO childPiece = new InformationPieceTO();
		childPiece.getPieces().addAll(new ArrayList<InformationPieceTO>());

		InformationPieceTO pieceClient = new InformationPieceTO();
		pieceClient.setValue(
				ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client",
												this, "message.error.plt"));
		childPiece.getPieces().add(pieceClient);
		piece.getChildrens().add(childPiece);

		return piece;
	}

	protected InformationSearchTO buildSearch(String referenceNumber) throws Exception {

		InformationSearchTO search = new InformationSearchTO();
		search.setAgreementNumber(referenceNumber);
		search.setLocale(new InputLocaleAdapter(this.getLanguageController().getLocale()));

		return search;
	}

	public String toString() {
		return "Quote Tooltip Controller";
	}

}
