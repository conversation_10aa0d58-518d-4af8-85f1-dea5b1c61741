package com.intact.brokeroffice.controller.viewquote;

import java.util.Calendar;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;

import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.ing.canada.plp.domain.enums.ConsentTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.party.Consent;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.brokeroffice.controller.viewquote.offer.CoverageOfferBean;
import com.intact.brokeroffice.controller.viewquote.offer.OfferAdapterAB;
import com.intact.brokeroffice.controller.viewquote.offer.VehicleOfferBeanAB;
import com.intact.brokeroffice.controller.viewquote.payment.PaymentAdapterAB;
import com.intact.brokeroffice.controller.viewquote.payment.PaymentBean;

/**
 * The Class ViewQuoteController. This is a Request scope controller to view
 * multiple quotes in separate web page.
 */
@Component
@Scope("request")
public class ViewQuoteControllerAB extends
		AbstractViewQuoteController<VehicleOfferBeanAB> implements
		IViewQuoteController {

	/** The offer adapter AB. */
	@Autowired
	private OfferAdapterAB offerAdapterAB;

	/** The payment adapter AB. */
	@Autowired
	private PaymentAdapterAB paymentAdapterAB;

	/** DE630 Risk Report Ordering Consent - GC */
	private Boolean riskReportOrderingConsentInd = Boolean.TRUE;

	/** public api for loading a quote **/
	public void viewQuote(String referenceNumber)
			throws Exception {
	
		viewQuote(true);
	}
	
	public void viewQuote(String referenceNumber, boolean newPageMode)
			throws Exception {
	
		viewQuote(newPageMode);
	}

	/**
	 * Gets the payment bean.
	 * 
	 * @return the payment bean
	 */
	public PaymentBean getPaymentBean() {
		return this.paymentBean;
	}

	/**
	 * Gets the endorsement sorted items.
	 * 
	 * @return the endorsement sorted items
	 */
	public List<CoverageOfferBean> getEndorsementSortedItems() {
		return this.offerAdapterAB.getEndorsementItems();
	}

	/**
	 * View quote.
	 */
	public void viewQuote(boolean isNewPage) throws Exception {
		List<CoverageOffer> listSelectedOfferCoverage = new LinkedList<CoverageOffer>();

		PolicyVersion policyVersion = this
				.buildViewQuoteCommon(this.getQuote().getId());		
		
		if (policyVersion != null) {
			for (InsuranceRisk insuranceRisk : policyVersion.getInsuranceRisks()) {
				
				for (InsuranceRiskOffer insuranceRiskOffer : insuranceRisk.getInsuranceRiskOffers()) {
						if(insuranceRisk != null 
							&& insuranceRisk.getSelectedInsuranceRiskOffer() != null 
							&& insuranceRisk.getSelectedInsuranceRiskOffer().getId() != null 
							&& insuranceRiskOffer != null
							&& insuranceRiskOffer.getId() != null 
							&& StringUtils.equalsIgnoreCase(insuranceRisk.getSelectedInsuranceRiskOffer().getId().toString(), insuranceRiskOffer.getId().toString())) {
							
							listSelectedOfferCoverage.addAll(insuranceRiskOffer.getCoverageOffers());
					}
				}
			}
			
			this.updateRiskReportOrderingConsentInd(policyVersion);
	
			// LOAD COVERAGE INFORMATION
			this.offerAdapterAB.loadVehicleOfferBeanList(
					this.getVehicleOfferBeanList(), policyVersion,
					this.languageController.getLocale(),listSelectedOfferCoverage);
	
			// LOAD PREMIUM INFORMATION
			this.offerAdapterAB.loadPremiumBean(this.getVehicleOfferBeanList(),
					this.getPremiumBean());
	
			// LOAD PAYMENT INFORMATION
			if (BooleanUtils.isTrue(this.getPremiumBean().getPremiumExist())) {
				this.paymentAdapterAB.loadPaymentBean(this.paymentBean,
						policyVersion);
				this.paymentBean.setTotalMonthlyPremium(this
						.loadTotalMonthlyPremium());
			}
			super.updateHasOffers(policyVersion);
		}
		
		this.view(this.getQuote());
	}

	/**
	 * Gets the current date for access denied page
	 * 
	 * @return the current date
	 */
	public Date getCurrentDate() {
		return Calendar.getInstance().getTime();
	}

	/**
	 * Check if quote to open is from same company as the one selected
	 * 
	 * @param quoteId The current quote Id
	 * @return true if same company else return false
	 */
	public Boolean getSameCompany(String quoteId) {
		return (ManufacturerCompanyCodeEnum.ING_WESTERN_REGION.equals(retrieveManufacturerCompany(quoteId)));
	}

	/**
	 * update read confirmation indicator based on policyVersion DB
	 * 
	 * @param aPolicyVersion
	 */
	public void updateRiskReportOrderingConsentInd(PolicyVersion aPolicyVersion) {
		Consent currentConsent = retrieveProperConsentFromPolicyHolder(
				ConsentTypeCodeEnum.GENERAL_CONSENT, aPolicyVersion);
		if (currentConsent != null) {
			this.setRiskReportOrderingConsentInd(currentConsent
					.getConsentIndicator());
		} else {
			this.setRiskReportOrderingConsentInd(Boolean.TRUE);
		}
	}

	public Boolean getRiskReportOrderingConsentInd() {
		return riskReportOrderingConsentInd;
	}

	public void setRiskReportOrderingConsentInd(
			Boolean riskReportOrderingConsentInd) {
		this.riskReportOrderingConsentInd = riskReportOrderingConsentInd;
	}
}
