package com.intact.brokeroffice.controller.province;

import java.util.HashMap;
import java.util.Map;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
 
public enum SubscriptionCompanyEnum {
	
	GC(CifCompanyEnum.INTACT_QC, "A - INTACT Quebec"),
	HAL(CifCompanyEnum.INTACT_HALIFAX, "6 - HALIFAX (daily halcion)"),
	WU(CifCompanyEnum.INTACT_AB, "3 - Western Union (daily halcion)");
	
	private CifCompanyEnum companyEnum;
	private ManufacturerCompanyCodeEnum manufacturerCode;
	private String label;

	private static Map<String, SubscriptionCompanyEnum> subscriptionCies = null;
	
	private SubscriptionCompanyEnum (CifCompanyEnum companyEnum, String label) {
		this.companyEnum = companyEnum;
		this.manufacturerCode = ManufacturerCompanyCodeEnum.valueOfCode(companyEnum.getSubBrokerCompanyNumber());
		this.label = label;		
	}

	public CifCompanyEnum getCifCompany() {
		return this.companyEnum;
	}

	public ManufacturerCompanyCodeEnum getManufacturerCode() {
		return manufacturerCode;
	}

	public String getLabel() {
		return label;
	}

	public void setLabel(String label) {
		this.label = label;
	}

	public static SubscriptionCompanyEnum fromCode(String value) {
		
		if (SubscriptionCompanyEnum.subscriptionCies == null) {
			Map<String, SubscriptionCompanyEnum> newCies = new HashMap<String, SubscriptionCompanyEnum>();
			
			for (SubscriptionCompanyEnum val : values()) {
				newCies.put(val.getManufacturerCode().getCode(), val);
			}
			
			SubscriptionCompanyEnum.subscriptionCies = newCies;
		}
		
		return SubscriptionCompanyEnum.subscriptionCies.get(value);
	}
}
