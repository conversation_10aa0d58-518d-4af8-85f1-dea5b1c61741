package com.intact.brokeroffice.controller.viewquote;

import java.util.Calendar;
import java.util.GregorianCalendar;
import java.util.List;
import java.util.Set;

import jakarta.inject.Inject;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.WordUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ing.canada.cif.domain.ISubBrokers;
import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.common.services.api.policy.IPolicyVersionInfoService;
import com.ing.canada.plp.domain.ManufacturingContext;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransaction;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransactionActivity;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransactionSubActivity;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransactionSubActivityComplementInfo;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.enums.BusinessTransactionSubActivityCodeEnum;
import com.ing.canada.plp.domain.enums.ConsentTypeCodeEnum;
import com.ing.canada.plp.domain.enums.LineOfBusinessCodeEnum;
import com.ing.canada.plp.domain.enums.PhoneTypeCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicyNote;
import com.ing.canada.plp.domain.party.Consent;
import com.ing.canada.plp.domain.party.Party;
import com.ing.canada.plp.domain.party.Phone;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.domain.subbrokerassignment.SubBrokerAssignment;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.helper.IPolicyVersionHelper;
import com.ing.canada.plp.helper.enums.PaymentPlanEnum;
import com.ing.canada.plp.report.enums.QuoteStatusCodeEnum;
import com.ing.canada.plp.service.IBusinessTransactionSubActivityService;
import com.ing.canada.plp.service.IInsurancePolicyService;
import com.ing.canada.plp.service.IPolicyHolderService;
import com.intact.brokeroffice.business.quote.NoteBean;
import com.intact.brokeroffice.helper.ResourceBundleHelper;
import com.intact.brokeroffice.service.IBrokerService;

/**
 * The Class ViewQuoteAdapter.
 * 
 */
@Component
public class ViewQuoteAdapter {

	/** The party helper. */
	@Autowired
	private IPartyHelper partyHelper;

	/** The insurance policy service. */
	@Autowired
	private IInsurancePolicyService insurancePolicyService;

	/** The quote business process. */
	@Inject
	private IBrokerService quoteBusinessProcess;

	/** The Sub brokers service cif. */
	@Autowired
	private ISubBrokersService subBrokersService;

	@Autowired
	private IPolicyVersionHelper policyVersionHelper;

	@Autowired
	private IBusinessTransactionSubActivityService businessTransactionSubActivityService;

	@Autowired
	private IPolicyHolderService policyHolderService;

	@Autowired
	private IPolicyVersionInfoService policyVersionInfoService;

	private static final int THRESHOLD_MAX_WORD_WRAP = 50;


	/**
	 * Load client contact bean.
	 *
	 * @param clientContactBean the client contact bean
	 * @param aPolicyVersion the a policy version
	 */
	public void loadClientContactBean(ClientContactBean clientContactBean, PolicyVersion aPolicyVersion, boolean admin){

		InsurancePolicy insurancePolicy = aPolicyVersion.getInsurancePolicy();
		clientContactBean.getNotes().clear();

		//BR5198  Displays the date and time of any activity recorded on the quote being viewed.:
		//BR5199  Displays the User Name of the individual who performed the recorded activity.:
		//BR5709  Activity initiated by an internal user:
		//BR5200  Displays the activity that was performed by the user as follows::
		//BR5369  Displays the activity that was performed by the user as follows::
		NoteBean note = null;
		for(InsurancePolicyNote insurancePolicyNote : insurancePolicy.getInsurancePolicyNotes()){
			note = new NoteBean();
			note.setAccount(insurancePolicyNote.getAuthorUID());
			note.setActivity(insurancePolicyNote.getUserActivityType());
			note.setNote(findNoteDesc(insurancePolicyNote.getInsurancePolicyNote()));
			note.setDate(insurancePolicyNote.getAuditTrail().getCreateTimestamp());
			clientContactBean.addNote(note);
		}

		clientContactBean.setFollowupStatus(insurancePolicy.getAgreementFollowUpStatus());
		clientContactBean.setOldFollowupStatus(insurancePolicy.getAgreementFollowUpStatus());
		clientContactBean.setNoteText("");

		PolicyHolder thePolicyHolder = this.policyVersionHelper.getPrincipalInsuredPolicyHolder(aPolicyVersion);

		Party policyHolder = thePolicyHolder.getParty();

		clientContactBean.setPolicyHolder(policyHolder.getFirstName()+" "+policyHolder.getLastName());

		if (policyHolder.getFirstName() != null && policyHolder.getFirstName().contains("null")) {
			for (Party party : aPolicyVersion.getParties()) {
				if (party != null && party.getFirstName() != null  && !party.getFirstName().contains("null")) {
				clientContactBean.setPolicyHolder(party.getFirstName()+" "+party.getLastName());
				break;
				}
			}
		}

		clientContactBean.setBusinessName(policyHolder.getUnstructuredName()); // Set the business name (for commercial lines only)

		BusinessTransaction businessTransaction = aPolicyVersion.getBusinessTransaction();
		clientContactBean.setStatus(QuoteStatusCodeEnum.getQuoteStatus(insurancePolicy.getQuotationValidityExpiryDate(),
				insurancePolicy.getAgreementLegacyNumber(), businessTransaction.getTransactionStatus()));

		clientContactBean.setLanguage(aPolicyVersion.getLanguageOfCommunication().getCodeISO());
		clientContactBean.setPhoneCell(getFormattedPhone(this.partyHelper.getCurrentPhone(policyHolder, PhoneTypeCodeEnum.CELLULAR_PHONE)));
		clientContactBean.setPhoneHome(getFormattedPhone(this.partyHelper.getCurrentPhone(policyHolder, PhoneTypeCodeEnum.HOME_PHONE)));
		clientContactBean.setPhoneWork(getFormattedPhone(this.partyHelper.getCurrentPhone(policyHolder, PhoneTypeCodeEnum.BUSINESS_PHONE)));

		if (clientContactBean.getPhoneHome() != null && !clientContactBean.getPhoneHome().trim().isEmpty()) {
			clientContactBean.setUnformattedPhone(this.partyHelper.getCurrentPhone(policyHolder, PhoneTypeCodeEnum.HOME_PHONE));
		}
		else if (clientContactBean.getPhoneCell() != null && !clientContactBean.getPhoneCell().trim().isEmpty()) {
			clientContactBean.setUnformattedPhone(this.partyHelper.getCurrentPhone(policyHolder, PhoneTypeCodeEnum.CELLULAR_PHONE));
		}
		else if (clientContactBean.getPhoneWork() != null && !clientContactBean.getPhoneWork().trim().isEmpty()) {
			clientContactBean.setUnformattedPhone(this.partyHelper.getCurrentPhone(policyHolder, PhoneTypeCodeEnum.BUSINESS_PHONE));
		}


		SubBrokerAssignment sba = insurancePolicy.getLatestSubBrokerAssignment();
		ISubBrokers subBroker = this.subBrokersService.getSubBrokerById(sba.getCifSubBrokerId());

		String brokerAdvisor = subBroker.getNameLine1();
		if(subBroker.getNameLine2() != null){
			brokerAdvisor += subBroker.getNameLine2();
		}
		clientContactBean.setBrokerAdvisor(brokerAdvisor);
		BusinessTransactionActivity lastBusinessTransactionActivity = businessTransaction.getLastBusinessTransactionActivity();
		clientContactBean.setLastUpdate(lastBusinessTransactionActivity.getAuditTrail().getCreateTimestamp());
		clientContactBean.setCreationDate(insurancePolicy.getAuditTrail().getCreateTimestamp());

		if(insurancePolicy.getExternalSystemOrigin() == null){
			clientContactBean.setQuoteSource("INT");
		}
		else{
			clientContactBean.setQuoteSource(insurancePolicy.getExternalSystemOrigin().getCode());
		}

		clientContactBean.setApplicationMode(insurancePolicy.getApplicationMode());


		clientContactBean.setBrokerNbr(String.valueOf(subBroker.getSubBrokerNumber()));

		generateMessages(clientContactBean, aPolicyVersion, admin);
	}
	/**
	 * Gets the formatted phone.
	 *
	 * @param aPhone the a phone
	 *
	 * @return the formatted phone
	 */
	private String getFormattedPhone(Phone aPhone){
		if (aPhone != null) {
			StringBuilder phoneFormat = new StringBuilder();
			phoneFormat.append("(").append(aPhone.getPhoneAreaCode()).append(")");
			phoneFormat.append(aPhone.getPhoneNumber().substring(0, 3)).append("-").append(aPhone.getPhoneNumber().substring(3));
			if(StringUtils.isNotEmpty(aPhone.getPhoneExtension())){
				phoneFormat.append(" ext ").append(aPhone.getPhoneExtension());
			}
			return phoneFormat.toString();
		}
		return null;
	}

	/**
	 * Generate the section important messages of the quote view.
	 *
	 * @param clientContactBean the client contact bean
	 * @param aPolicyVersion the a policy version
	 */
	private void generateMessages(ClientContactBean clientContactBean, PolicyVersion aPolicyVersion, boolean admin) {
		generateRoadblockMessage(clientContactBean, aPolicyVersion);
		generateRecentActivityMessage(clientContactBean, aPolicyVersion);
		List<InsurancePolicy> unexpiredInsurancePolicies = null;

		PolicyHolder aPolicyHolder = this.policyHolderService.findPrincipalPolicyHolder(aPolicyVersion);

		String emailParam = aPolicyHolder.getParty().getEmailAddress();

		if(admin){
			unexpiredInsurancePolicies = this.insurancePolicyService.findAllUnexpiredInsurancePolicyForEmail(emailParam);
		}
		else{
			unexpiredInsurancePolicies = this.insurancePolicyService.findAllUnexpiredInsurancePolicy(emailParam, aPolicyVersion.getInsurancePolicy().getId());
		}

		generateRecentQuoteMessage(clientContactBean, unexpiredInsurancePolicies);
		generateUploadedQuoteMessage(clientContactBean, aPolicyVersion, unexpiredInsurancePolicies);

		// IRCA-specific messages
		if (aPolicyVersion.getInsurancePolicy().getLineOfBusiness() == LineOfBusinessCodeEnum.COMMERCIAL_LINES) {
			// Credit consent (explicit) not given
			generateCreditConsentMessage(clientContactBean, aPolicyVersion);
		} else {
			generateEligibleWithdrawalsMessage(clientContactBean,aPolicyVersion);
		}
	}

	/**
	 * Generate roadblock message.
	 *
	 * @param clientContactBean the client contact bean
	 * @param aPolicyVersion the a policy version
	 */
	private void generateRoadblockMessage(ClientContactBean clientContactBean, PolicyVersion aPolicyVersion) {
		clientContactBean.getRedMessages().clear();

		BusinessTransaction aBusinessTransaction =  aPolicyVersion.getBusinessTransaction();
		if (aBusinessTransaction!=null) {
			BusinessTransactionSubActivity b = this.businessTransactionSubActivityService.findLastSubActivity(aBusinessTransaction.getId());
			if (b!=null && BusinessTransactionSubActivityCodeEnum.ROADBLOCK.equals(b.getSubActivityCode())) {
				String msg = null;
				for (BusinessTransactionSubActivityComplementInfo ci : b.getBusinessTransactionSubActivityComplementInfos()) {

					String ircaText = aPolicyVersion.getInsurancePolicy().getLineOfBusiness() == LineOfBusinessCodeEnum.COMMERCIAL_LINES ? ".irca" : "";

					msg = ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client", this, "client.message.roadblock" + ircaText,
							ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client", this, b.getBusinessTransactionActivity().getBusinessTransaction().getCurrentApplicationFlowState().name()),
							getRoadBlockText(ci));
					clientContactBean.addRedMessages(msg);
					break;
				}
			}
		}
	}

	/**
	 * Gets the road block text.
	 * WZ081 Display this message text in Red (RGB: 192,12,48)
	 * @param ci the ci
	 *
	 * @return the road block text
	 */
	private String getRoadBlockText(BusinessTransactionSubActivityComplementInfo ci) {
		String rbMsg = ResourceBundleHelper.validateKeyExistence("autoquote_roadblock", this, ci.getAttributeValue())? ResourceBundleHelper.getMessage("autoquote_roadblock", this, ci.getAttributeValue()): null;

		if (rbMsg == null) {
			String title = ResourceBundleHelper.validateKeyExistence("autoquote_roadblock", this, ci.getAttributeValue()+ ".title")? ResourceBundleHelper.getMessage("autoquote_roadblock", this, ci.getAttributeValue() + ".title"):null;
			String intro = ResourceBundleHelper.validateKeyExistence("autoquote_roadblock", this, ci.getAttributeValue()+ ".intro")?ResourceBundleHelper.getMessage("autoquote_roadblock", this, ci.getAttributeValue() + ".intro"):null;
			String content = ResourceBundleHelper.validateKeyExistence("autoquote_roadblock", this, ci.getAttributeValue()+ ".content")?ResourceBundleHelper.getMessage("autoquote_roadblock", this, ci.getAttributeValue() + ".content"):null;

			StringBuilder sb = new StringBuilder();
			if(StringUtils.isNotEmpty(title)){
				sb.append(title).append(" ");
			}
			if(StringUtils.isNotEmpty(intro)){
				sb.append(intro).append(" ");
			}
			if(StringUtils.isNotEmpty(content)){
				sb.append(content);
			}
			rbMsg = sb.toString().trim();
		}

		if(rbMsg == null){
			rbMsg = ci.getAttributeValue();
		}
		return rbMsg;
	}


	/**
	 * Generate recent activity message.
	 * BR5184  This message is displayed whenever the quote being viewed has
	 * had any activity registered in the Activity Log within the previous 15 minutes.
	 *
	 * @param clientContactBean the client contact bean
	 * @param aPolicyVersion the a policy version
	 */
	private void generateRecentActivityMessage(ClientContactBean clientContactBean, PolicyVersion aPolicyVersion) {
		InsurancePolicyNote note = aPolicyVersion.getInsurancePolicy().getLatestInsurancePolicyNote();
		clientContactBean.getMessages().clear();
		if (note != null) {
			GregorianCalendar now = new GregorianCalendar();
			GregorianCalendar lastActivity = new GregorianCalendar();
			lastActivity.setTimeInMillis(note.getAuditTrail().getCreateTimestamp().getTime());
			lastActivity.roll(Calendar.MINUTE, 15);
			if (now.getTimeInMillis() <= lastActivity.getTimeInMillis()) {
				String msg = ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client", this, "client.message.recentActivity");
				clientContactBean.addMessages(msg);
			}
		}
	}

	/**
	 * Generate recent quote message.
	 * BR5185  This message is displayed whenever the client has more than one unexpired quote in the Web Zone.
	 * @param clientContactBean the client contact bean
	 * @param aPolicyVersion the a policy version
	 * @param unexpiredInsurancePolicies the unexpired insurance policies
	 */
	private void generateRecentQuoteMessage(ClientContactBean clientContactBean, List<InsurancePolicy> unexpiredInsurancePolicies) {
		if (unexpiredInsurancePolicies.size() > 1) {
			String msg = ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client", this, "client.message.recentQuote");
			clientContactBean.addMessages(msg);
		}
	}

	/**
	 * Generate uploaded quote message.
	 * BR5186  This message is displayed whenever the client has more than one unexpired quote in the Web Zone and
	 * one of these quotes other than the quote being viewed has already been uploaded to goBRIO.
	 * BR5366  This message is displayed whenever the client has more than one unexpired quote in the Web Zone and
	 * one of these quotes other than the quote being viewed has already been uploaded to Savers
	 * @param clientContactBean the client contact bean
	 * @param aPolicyVersion the a policy version
	 * @param unexpiredInsurancePolicies the unexpired insurance policies
	 */
	private void generateUploadedQuoteMessage(ClientContactBean clientContactBean, PolicyVersion aPolicyVersion, List<InsurancePolicy> unexpiredInsurancePolicies) {
		String nbr = aPolicyVersion.getInsurancePolicy().getAgreementNumber();
		String msg = null;
		for(InsurancePolicy ip : unexpiredInsurancePolicies){
			if(!ip.getAgreementNumber().equals(nbr) && ip.getAgreementLegacyNumber() != null){
				msg = ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client", this, "client.message.uploadQuote");
				clientContactBean.addMessages(msg);
				break;
			}
		}
	}

	/**
	 * Generate eligible withdrawals message (intact quebec, auto only).
	 * BR5181 Display this message text in Red (RGB:192.12.48)
	 * BR7083 Display message when nor eligible to plan A and E
	 * @param clientContactBean the client contact bean
	 * @param aPolicyVersion the a policy version
	 */
	private void generateEligibleWithdrawalsMessage(ClientContactBean clientContactBean, PolicyVersion aPolicyVersion) {
		Set<PaymentPlanEnum> setPaymentPlanEnum = null;
		ManufacturingContext manufacturingContext = aPolicyVersion.getInsurancePolicy().getManufacturingContext();
		ProvinceCodeEnum province = manufacturingContext.getProvince();

		if (province.equals(ProvinceCodeEnum.QUEBEC)) {
			setPaymentPlanEnum = this.policyVersionInfoService.getPaymentPlans(aPolicyVersion);
			if(!setPaymentPlanEnum.contains(PaymentPlanEnum.PLAN_A)&& !setPaymentPlanEnum.contains(PaymentPlanEnum.PLAN_E)){
				String msg = ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client", this, "client.message.eligible.withdrawals");
				clientContactBean.addRedMessages(msg);
			}
		}
	}

	/**
	 * Generate credit consent message (IRCA only).
	 * BR15114 Display this message for Residential and IRCA Quick Quotes when the client
	 * has not provided consent to obtain the credit score.
	 *
	 * @param clientContactBean the client contact bean
	 * @param aPolicyVersion the a policy version
	 */
	private void generateCreditConsentMessage(ClientContactBean clientContactBean, PolicyVersion aPolicyVersion) {
		Consent creditConsent = null;
		PolicyHolder policyHolder = this.policyHolderService.findPrincipalPolicyHolder(aPolicyVersion);
		if (policyHolder != null) {
			for (Consent consent : policyHolder.getParty().getConsents()) {
				if (consent.getConsentType() == ConsentTypeCodeEnum.CREDIT_SCORE) {
					creditConsent = consent;
				}
			}
		}

		if (creditConsent != null && !creditConsent.getConsentIndicator()) {
			String msg = ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client", this, "client.message.consent.credit");
			clientContactBean.addMessages(msg);
		}
	}

	/**
	 * find note description
	 * @param aCode
	 * @return
	 */
	private String findNoteDesc(String aCode){
		if("NOT_CONTACTED".equals(aCode) || "CONTACTED_FOLLOWUP_REQUIRED".equals(aCode) || "CONTACTED_NO_FOLLOWUP_REQUIRED".equals(aCode) || "NOT_CONTACTED_NO_FOLLOWUP_REQUIRED".equals(aCode)){
			return ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client", this, aCode );
		}

    return aCode;
	}

	/**
	 * load consent info for quote contact consent and future communication
	 * @param aClientContactBean
	 * @param aQuoteContactConsentInd
	 * @param aFutureCommmunicationConsentInd
	 */
	public void loadConsentInfo(ClientContactBean aClientContactBean, Boolean antispamConsent){
		generateClientRemoveFutureCommmunicationConsent(aClientContactBean, antispamConsent);
	}

	/**
	 * generate client remove future communication consent message
	 * BR5181 Display this message text in Red (RGB: 192,12,48)
	 * BR5182  Display this message when the client has removed their consent to be contacted with respect to this quote
	 * @param clientContactBean
	 * @param aFutureCommmunicationConsentInd
	 */
	private void generateClientRemoveFutureCommmunicationConsent(ClientContactBean clientContactBean, Boolean aFutureCommmunicationConsentInd){
		// 65442 Do not display for QQ
		if(Boolean.FALSE.equals(aFutureCommmunicationConsentInd) && ApplicationModeEnum.QUICK_QUOTE != clientContactBean.getApplicationMode()){
			String msg = ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client", this, "client.message.consent.marketing");
			clientContactBean.addRedMessages(msg);
		}
	}
}
