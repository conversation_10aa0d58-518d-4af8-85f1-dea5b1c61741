package com.intact.brokeroffice.controller.viewquote.offer;

import com.ing.canada.plp.domain.enums.BasicCoverageCodeEnum;



public class VehicleOfferBeanON extends VehicleOfferBean {
	
	
	/**
	 * Gets the coverage levels in the order they should be displayed on screen. If you wish to hide an offer under certain
	 * circumstances, it is feasible by returning a dynamically constructed array. The front-end code should never
	 * assume a specific order for the offers, the goal is to be able to change the order easily without breaking the UI
	 * code.
	 * 
	 * @return the offers display order
	 */
	public static final BasicCoverageCodeEnum[] getCoveragesDisplayOrder() {
		return new BasicCoverageCodeEnum[] { BasicCoverageCodeEnum.LIABILITY_LIAB, BasicCoverageCodeEnum.COLLISION_COLL,
				BasicCoverageCodeEnum.COMPREHENSIVE_COMP, BasicCoverageCodeEnum.ACCIDENT_BENEFITS_ACCB, BasicCoverageCodeEnum.DIRECT_COMP_PROPERTY_DAM_DCPD};
	}	
	
	/**
	 * Gets the liability ON CoverageOfferBean
	 * BR5209  Repeat for each vehicle listed.: 
	 * @return the liability ON 
	 */
	public CoverageOfferBean getLiability(){		
		if(this.getOfferSelected()!=null && getOfferSelected().booleanValue()){
			return this.getCoveragesMapped(BasicCoverageCodeEnum.LIABILITY_LIAB);
		}
		return null;
	}
	
	/**
	 * Gets the Collision Deductible ON
	 * BR5209  Repeat for each vehicle listed.: 
	 * BR5215  Display the default value for the selected package or the value selected by the client if this is a custom package
	 * @return the Collision Deductible ON 
	 */
	public CoverageOfferBean getCollisionDeductible(){		
		if(this.getOfferSelected()!=null && getOfferSelected().booleanValue()){
			return this.getCoveragesMapped(BasicCoverageCodeEnum.COLLISION_COLL);
		}
		return null;
	}
	
	/**
	 * Gets the Comprehensive Deductible ON
	 * BR5209  Repeat for each vehicle listed.: 
	 * BR5215  Display the default value for the selected package or the value selected by the client if this is a custom package
	 * @return the Comprehensive Deductible ON 
	 */
	public CoverageOfferBean getComprehensiveDeductible(){	
		if(this.getOfferSelected()!=null && getOfferSelected().booleanValue()){
			return this.getCoveragesMapped(BasicCoverageCodeEnum.COMPREHENSIVE_COMP);
		}
		return null;
	}
	
	/**
	 * Gets the direct compensation property Damage ON
	 * BR5209  Repeat for each vehicle listed.: 
	 * BR5215  Display the default value for the selected package or the value selected by the client if this is a custom package
	 * @return the  direct compensation property Damage ON 
	 */

	public CoverageOfferBean getDirectCompensationPropertyDamage(){	
		if(this.getOfferSelected()!=null && getOfferSelected().booleanValue()){
			return this.getCoveragesMapped(BasicCoverageCodeEnum.DIRECT_COMP_PROPERTY_DAM_DCPD);
		}		
		return null;
	}	
	
	/**Gets the accident benefit ON
	 * BR5217  Display the default.: 
	 * BR5209  Repeat for each vehicle listed.: 
	 * @return the accident benefit ON 
	 */
	public CoverageOfferBean getAccidentBenefit(){	
		if(this.getOfferSelected()!=null && getOfferSelected().booleanValue()){
			return this.getCoveragesMapped(BasicCoverageCodeEnum.ACCIDENT_BENEFITS_ACCB);
		}		
		return null;
	}	
	
	
	/**Gets the FAMILY PROTECTION ON
	 * TODO CHANGE FOR REAL VALUE CODE ENUM
	 * @return the family protection ON 
	 */
	//public CoverageOfferBean getFamilyProtection(){	
	//	if(this.getOfferSelected()!=null && getOfferSelected().booleanValue()){
	//		return this.getCoveragesMapped(EndorsementCodeEnum._44_SAV);
	//	}		
	//	return null;
	//}	
	

}
