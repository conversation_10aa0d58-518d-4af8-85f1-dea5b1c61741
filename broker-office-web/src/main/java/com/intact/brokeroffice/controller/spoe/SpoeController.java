/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2010, Intact Insurance, All rights reserved.<br>
 */
package com.intact.brokeroffice.controller.spoe;

import com.ing.canada.cif.domain.enums.ApplicationIdEnum;
import com.ing.canada.cif.domain.enums.LineOfBusinessEnum;
import com.ing.canada.common.web.api.auth.TamTokensEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.business.accounts.IAccountsBusinessProcess;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.brokeroffice.controller.accounts.MasterBrokerBean;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;
import com.intact.brokeroffice.controller.helper.ProvinceHelper;
import com.intact.brokeroffice.controller.province.ProvinceClusterEnum;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.brokeroffice.controller.province.SubscriptionCompanyEnum;
import com.intact.brokeroffice.controller.search.SearchController;
import com.intact.brokeroffice.util.ProvinceCompanyConverter;
import com.intact.canada.brm.domain.broker.BrokerWebOfficeAccess;
import com.intact.canada.brm.domain.user.UserAccount;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import jakarta.faces.context.FacesContext;
import jakarta.faces.model.SelectItem;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.servlet.http.Cookie;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpSession;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Component
@Scope("session")
public class SpoeController {

	private List<SelectItem> accessLevel = new ArrayList<SelectItem>();

	@Inject @Named("ldap-group-broker")
	private String ldapGroupBroker;

	@Inject @Named("ldap-group-broker-reassign")
	private String ldapGroupBrokerReassign;

	@Inject @Named("ldap-group-underwritter")
	private String ldapGroupUnderwritter;

	@Inject @Named("ldap-group-quote-admins")
	private String ldapGroupQuoteAdmins;

	@Inject @Named("ldap-group-program-admins")
	private String ldapGroupProgramAdmins;

	@Inject @Named("ldap-group-admins")
	private String ldapGroupAdmins;

	@Inject @Named("spoe-mode")
	private String spoeMode;

	@Autowired
	private IAccountsBusinessProcess accountsBusinessProcess;

	private List<MasterBrokerBean> masterBrokers;

	private SpoeBean spoeBean = new SpoeBean();

	private List<ProvinceCodeEnum> provinces;

	/** The province controller. */
	@Autowired
	private ProvinceController provinceController;

	@Autowired
	private SearchController searchController;

	/**
	 * Gets the spoe bean.
	 * 
	 * @return the spoeBean
	 */
	public SpoeBean getSpoeBean() {
		return this.spoeBean;
	}

	/**
	 * Gets the list of access level available for the application (to be displayed
	 * in a select list)
	 * 
	 * @return List<SelectItem>
	 */
	public List<SelectItem> getAccessLevel() {

		this.initAccessLevel();
		return this.accessLevel;

	}
	
	public void initialize() {
		
		// Reset the session
		HttpSession session = (HttpSession) FacesContext.getCurrentInstance().getExternalContext().getSession(false);
		
		if (session != null) {
			session.invalidate();
		}
	}

	/**
	 * init the access level available for the application
	 */
	private void initAccessLevel() {

		this.accessLevel.clear();

			this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupBroker));
			
			this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupBroker, ProvinceCodeEnum.ONTARIO));
			
			this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupBroker, ProvinceCodeEnum.ONTARIO,
					ProvinceCodeEnum.QUEBEC));
			
			this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupBroker, ProvinceCodeEnum.ONTARIO,
					ProvinceCodeEnum.QUEBEC, ProvinceCodeEnum.ALBERTA));

			this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupBrokerReassign));
			
			this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupBrokerReassign, ProvinceCodeEnum.ONTARIO));
			
			this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupBrokerReassign, ProvinceCodeEnum.ONTARIO,
					ProvinceCodeEnum.QUEBEC, ProvinceCodeEnum.ALBERTA));

			// WebZone-Intact-Underwriters
			for (ProvinceCodeEnum province : ProvinceHelper.activeProvinces) {
				this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(ldapGroupUnderwritter, province));
			}

			// WebZone-Intact-Quote-Admins
			for (ProvinceCodeEnum province : ProvinceHelper.activeProvinces) {
				this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupQuoteAdmins, province));
			}

			// WebZone-Program-Admins-Intact
			for (ProvinceCodeEnum province : ProvinceHelper.activeProvinces) {
				this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupProgramAdmins, province));
			}
			// WebZone-Intact-Admins-activeProvinces
			for (ProvinceCodeEnum province : ProvinceHelper.activeProvinces) {
				this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupAdmins, province));
			}

			// WebZone-Intact-Underwriters-ON + WebZone-Intact-Underwriters-QC
			this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupUnderwritter, ProvinceCodeEnum.ONTARIO,
					ProvinceCodeEnum.QUEBEC));

			// WebZone-Intact-Quote-Admins-ON + WebZone-Intact-Quote-Admins-QC
			this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupQuoteAdmins, ProvinceCodeEnum.ONTARIO,
					ProvinceCodeEnum.QUEBEC));

			// WebZone-Intact-Admins-ON + WebZone-Intact-Admins-QC
			this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupAdmins, ProvinceCodeEnum.ONTARIO,
					ProvinceCodeEnum.QUEBEC));

			// WebZone-Intact-Admins-ON + WebZone-Intact-Admins-QC +
			// WebZone-Intact-Admins-AB
			this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupAdmins, ProvinceCodeEnum.ONTARIO,
					ProvinceCodeEnum.QUEBEC, ProvinceCodeEnum.ALBERTA));

			// WebZone-Program-Admins-Intact ON-QC-AB
			this.accessLevel.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupProgramAdmins, ProvinceCodeEnum.ONTARIO,
					ProvinceCodeEnum.QUEBEC, ProvinceCodeEnum.ALBERTA));

			// TODO adjust aligning with desired access levels
			// WebZone-Program-Admins-Intact ROC
			this.accessLevel
					.add(ProvinceHelper.AddItemAccesLevel(this.ldapGroupProgramAdmins, ProvinceClusterEnum.ALL));
	}

	/**
	 * This is the SPOE version of the login
	 * 
	 * @return the string
	 * @throws BrokerServiceException
	 */
	public String login() throws Exception {

		FacesContext context = FacesContext.getCurrentInstance();

		HttpSession session = (HttpSession) context.getExternalContext().getSession(true);

		// Reset user context
		session.removeAttribute("user.context");
		
		session.setAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(),
				this.spoeBean.getSelectedAccessLevel());
		
		System.out.println("GROUP : " + this.spoeBean.getSelectedAccessLevel());
		
		// CLASSIC USER ID in session
		session.setAttribute(TamTokensEnum.TAM_USER_CLASSIC_USER_ID.getTokenValue(), "DA7143");

		List<String> selectedMasters = new ArrayList<String>();
		// List<String> selectedProvinces = new ArrayList<String>();
  
		// ONE PROVINCE BY BROKER AT LOGIN TIME 
		if (this.spoeBean.getSelectedAccessLevel().startsWith(this.ldapGroupBroker)
				|| this.spoeBean.getSelectedAccessLevel().startsWith(this.ldapGroupBrokerReassign)) {

			String defaultProvince = this.accountsBusinessProcess
					.getDefaultProvince(!StringUtils.isBlank(this.spoeBean.getUserId()) ? this.spoeBean.getUserId() : "spoeUId");

			session.setAttribute(
					TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(),
					!StringUtils.isBlank(this.spoeBean.getUserId()) ? this.spoeBean.getUserId() : "spoeUId"
			);

			session.setAttribute(
					SessionConstantsEnum.COMPANY.getSessionConstant(),
					defaultProvince != null ? ProvinceCompanyConverter.convertProvinceToSubBrokerCompany(defaultProvince) : this.spoeBean.getSelectedCompany()
			);

			for (MasterBrokerBean masterBrokerBean : this.masterBrokers) {
				if (BooleanUtils.isTrue(masterBrokerBean.getSelected())) {
					selectedMasters.add(masterBrokerBean.getNumber());
				}
			}

			if (StringUtils.isNotBlank(this.spoeBean.getUserId()) && selectedMasters.isEmpty()) {
				UserAccount anUserAccount = this.accountsBusinessProcess.findByUId(this.spoeBean.getUserId());
				for (BrokerWebOfficeAccess aBrokerWebOfficeAccess : anUserAccount.getBrokerWebOfficeAccesses()) {
					selectedMasters.add(aBrokerWebOfficeAccess.getMasterOwnerCode());
				}
			}
		}

		this.provinceController.setProvinces(null);
		this.provinceController.setCompanies(null);
		this.manageAccessLevel(session);

		session.setAttribute(
				TamTokensEnum.TAM_USER_NAME_CREDENTIAL.getTokenValue(),
				!StringUtils.isBlank(this.spoeBean.getUserId()) ? this.spoeBean.getUserId() : "spoeUId"
		);

		session.setAttribute(SessionConstantsEnum.AVAILABLE_MASTERS.getSessionConstant(), selectedMasters);

		return "index";
	}

	/**
	 * manage province list
	 * 
	 * @param aSession
	 * @param provinceList
	 */
	protected void manageAccessLevel(HttpSession aSession) throws Exception {

		String userRole = this.spoeBean.getSelectedAccessLevel();

		if (userRole.contains(this.ldapGroupAdmins)) {
			aSession.setAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), this.ldapGroupAdmins);
			manageCompanies(aSession, userRole, this.ldapGroupAdmins);

		} else if (userRole.contains(this.ldapGroupProgramAdmins)) {
			aSession.setAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), this.ldapGroupProgramAdmins);
			manageCompanies(aSession, userRole, this.ldapGroupProgramAdmins);

		} else if (userRole.contains(this.ldapGroupQuoteAdmins)) {
			aSession.setAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), this.ldapGroupQuoteAdmins);
			manageCompanies(aSession, userRole, this.ldapGroupQuoteAdmins);

		} else if (userRole.contains(this.ldapGroupUnderwritter)) {
			aSession.setAttribute(TamTokensEnum.TAM_SECURITY_ROLES_ATTRIB.getTokenValue(), this.ldapGroupUnderwritter);
			manageCompanies(aSession, userRole, this.ldapGroupUnderwritter);
		} else {
			manageCompanies(aSession, userRole, null);
		}
	}

	/**
	 * Method to manage the company based on the ldap security settings applicable
	 * for only ldapGroupProgramAdmins
	 * 
	 * @param httpServletRequest
	 * @param userRole
	 * @param ldapSecurityGroup
	 */
	public void manageCompanies(HttpSession aSession, String userRole, String ldapSecurityGroup) throws Exception {
		String oldCompany = this.provinceController.getCompany();
		
		int nbCompanies = ProvinceHelper.manageCompanies(aSession, userRole, ldapSecurityGroup, provinceController);
		if (nbCompanies > 1) {
			retrieveDefaultCompany();
		}
		if (oldCompany.compareTo(this.provinceController.getCompany()) != 0) {
			if (this.searchController.getQuotesBeans() != null) {
				this.searchController.getQuotesBeans().clear();
			}
		}
	}

	/**
	 * retrieves the cookie for the default company
	 */
	private void retrieveDefaultCompany() {
		FacesContext context = FacesContext.getCurrentInstance();
		HttpServletRequest httpServletRequest = (HttpServletRequest) context.getExternalContext().getRequest();
		HttpSession aSession = (HttpSession) context.getExternalContext().getSession(true);
		String defaultCompany = null;
		if (ProvinceController.searchForBrokerDefaultCompany(httpServletRequest) != null) {
			Cookie aCookie = ProvinceController.searchForBrokerDefaultCompany(httpServletRequest);
			defaultCompany = aCookie.getValue();
		}

		// use ST parameter to set the province parameter
		else {
			defaultCompany = ProvinceCompanyConverter.convertProvinceToSubBrokerCompany(this.accountsBusinessProcess.getDefaultProvince(this.spoeBean.getUserId()));
			// manage scenario WHERE ST is NULL
			if (defaultCompany == null) {
				List<String> provinceAvailables = (List<String>) aSession
						.getAttribute(SessionConstantsEnum.AVAILABLE_PROVINCES.getSessionConstant());
				defaultCompany = provinceAvailables.get(0);
			}
		}

		this.provinceController.setSelectedCompany(SubscriptionCompanyEnum.fromCode(defaultCompany));
		aSession.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), defaultCompany);
		this.provinceController.reset();
	}

	/**
	 * Gets the list of masters (use for masters checkboxes to select).
	 * 
	 * @return the master brokers
	 */
	public List<MasterBrokerBean> getMasterBrokers() {
		// by default the dropdown points to ldap broker, in any scenario where
		// we have ldap broker, we want to display
		// the list of masters

		if (this.spoeBean.getSelectedAccessLevel() == null
				|| this.spoeBean.getSelectedAccessLevel().contains(this.ldapGroupBroker)
				|| this.spoeBean.getSelectedAccessLevel().contains(this.ldapGroupBrokerReassign)) {

			Map<String, String> masterBrokerMap = this.accountsBusinessProcess.getAssignedMasterBrokers(
					this.provinceController.getCompanyEnumCode(), ApplicationIdEnum.asList(), LineOfBusinessEnum.asList());
			List<MasterBrokerBean> masterBrokerBeans = new ArrayList<MasterBrokerBean>();

			for (String key : masterBrokerMap.keySet()) {
				String value = masterBrokerMap.get(key);
				MasterBrokerBean masterBrokerBean = new MasterBrokerBean(key, value);
				masterBrokerBeans.add(masterBrokerBean);
			}

			this.masterBrokers = masterBrokerBeans;
		} else {
			this.masterBrokers = new ArrayList<MasterBrokerBean>();
		}

		return this.masterBrokers;
	}

	/**
	 * Gets the checks if is spoe mode.
	 * 
	 * @return the checks if is spoe mode
	 */
	public String getIsSpoeMode() {
		return this.spoeMode;
	}

	/**
	 * Gets the province list
	 * 
	 * @return the province list
	 */
	public List<ProvinceCodeEnum> getProvinces() {

		if (this.provinces == null) {
			this.provinces = new ArrayList<>();
			for (ProvinceCodeEnum provinceCodeEnum : ProvinceHelper.activeProvinces) {
				this.provinces.add(provinceCodeEnum);
			}
		}
		return this.provinces;
	}

	/**
	 * initialize the province value
	 */
	public void onProvinceChanged() {

		FacesContext context = FacesContext.getCurrentInstance();
		HttpSession session = (HttpSession) context.getExternalContext().getSession(true);

		session.setAttribute(SessionConstantsEnum.PROVINCE.getSessionConstant(), this.spoeBean.getSelectedProvince());
		this.provinceController.reset();
	}

	public void onAccessLevelChanged() {
		this.spoeBean.setUserId("");
		String selectedAccessLevel = this.spoeBean.getSelectedAccessLevel();
		// Legacy accounts
		if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupUnderwritter,
				ProvinceCodeEnum.ONTARIO)) {
			this.spoeBean.setUserId("m_webuwon");
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupUnderwritter,
				ProvinceCodeEnum.ALBERTA)) {
			this.spoeBean.setUserId("m_webuwab");
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupUnderwritter,
				ProvinceCodeEnum.ONTARIO, ProvinceCodeEnum.QUEBEC)) {
			this.spoeBean.setUserId("m_webuwon2");
		
		// Broker Advisor
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupBroker,
				ProvinceCodeEnum.ONTARIO, ProvinceCodeEnum.QUEBEC)) {
			this.spoeBean.setUserId("WZ_PBK");
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupBroker,
				ProvinceCodeEnum.ONTARIO, ProvinceCodeEnum.QUEBEC, ProvinceCodeEnum.ALBERTA)) {
			this.spoeBean.setUserId("WZ_PBK");
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupBroker,
				ProvinceCodeEnum.QUEBEC)) {
			this.spoeBean.setUserId("WZ_PBK_QC");
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupBroker,
				ProvinceCodeEnum.ONTARIO)) {
			this.spoeBean.setUserId("WZ_PBK_ON");
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupBroker,
				ProvinceCodeEnum.ALBERTA)) {
			this.spoeBean.setUserId("WZ_PBK_AB");
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupBroker)) {
			this.spoeBean.setUserId("WZ_PBK_QC");
			
		// Admins
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupAdmins,
				ProvinceCodeEnum.ONTARIO, ProvinceCodeEnum.QUEBEC, ProvinceCodeEnum.ALBERTA)) {
			this.spoeBean.setUserId("WZ_ADMN");
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupProgramAdmins,
				ProvinceCodeEnum.ONTARIO, ProvinceCodeEnum.QUEBEC, ProvinceCodeEnum.ALBERTA)) {
			this.spoeBean.setUserId("WZ_ADMN");
		} 
		else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupAdmins,
				ProvinceCodeEnum.ONTARIO)) {
			this.spoeBean.setUserId("WZ_ADMN");
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupAdmins,
				ProvinceCodeEnum.QUEBEC)) {
			this.spoeBean.setUserId("WZ_ADMN");
		} else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupAdmins,
				ProvinceCodeEnum.ALBERTA)) {
			this.spoeBean.setUserId("WZ_ADMN");
		} 
		else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupProgramAdmins,
				ProvinceCodeEnum.ONTARIO)) {
			this.spoeBean.setUserId("WZ_ADMN");
		}  else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupProgramAdmins,
				ProvinceCodeEnum.QUEBEC)) {
			this.spoeBean.setUserId("WZ_ADMN");
		}  else if (ProvinceHelper.containAccessLevelAndProvinces(selectedAccessLevel, this.ldapGroupProgramAdmins,
				ProvinceCodeEnum.ALBERTA)) {
			this.spoeBean.setUserId("WZ_ADMN");
		} 
	}
}
