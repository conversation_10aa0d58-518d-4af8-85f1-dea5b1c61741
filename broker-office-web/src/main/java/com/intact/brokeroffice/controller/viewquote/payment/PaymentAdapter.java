/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */

package com.intact.brokeroffice.controller.viewquote.payment;

import java.util.Iterator;
import java.util.Set;

import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ing.canada.common.domain.QuoteCalculationDetails;
import com.ing.canada.common.services.api.rating.IQuotationService;
import com.ing.canada.plp.crypto.IIngCipher;
import com.ing.canada.plp.domain.billing.Billing;
import com.ing.canada.plp.domain.enums.BillingPlanCodeEnum;
import com.ing.canada.plp.domain.enums.CombinedPolicyCodeEnum;
import com.ing.canada.plp.domain.enums.MethodOfPaymentCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;

/**
 * Adapter for a PaymentBean
 * 
 * <AUTHOR>
 */
@Component
public abstract class PaymentAdapter<E extends PaymentBean> implements IPaymentAdapter<E> {

	private static final Log log = LogFactory.getLog(PaymentAdapter.class);
	
	/** The quotation service. */
	@Autowired
	protected IQuotationService quotationService;

	@Autowired
	private IIngCipher cipher;

	/***
	 * (non-Javadoc)
	 * 
	 * @see com.intact.brokeroffice.controller.viewquote.payment.IPaymentAdapter#loadPaymentBean(com.intact.brokeroffice.controller.viewquote.payment.PaymentBean,
	 *      com.ing.canada.plp.domain.policyversion.PolicyVersion, com.ing.canada.plp.domain.enums.ProvinceCodeEnum)
	 */
	public void loadPaymentBean(E aPaymentBean, PolicyVersion aPolicyVersion, ProvinceCodeEnum aProvinceCode) {
		if (isSelectedOfferRatingFound(aPolicyVersion)) {
			QuoteCalculationDetails quoteCalculationDetails = this.quotationService.getQuoteCalculationDetails(
					aPolicyVersion, aProvinceCode);	
			
			applySpecificCalculationRules(quoteCalculationDetails, aPolicyVersion);

			this.quotationService.roundUpTheQuoteNumber(quoteCalculationDetails);
			
			this.loadPremiumInformation(aPaymentBean, quoteCalculationDetails);

			aPaymentBean.setAnnualAmountTaxes(quoteCalculationDetails.getAnnualAmountTaxes());
			aPaymentBean.setMonthlyFirstPayment(quoteCalculationDetails.getFirstMonthlyPaymentWithTaxesAndSurcharge());

			if (quoteCalculationDetails.getPolicyTermInMonths() != null) {
				aPaymentBean.setNbYears(quoteCalculationDetails.getPolicyTermInMonths().getCode() / 12);
			}

			aPaymentBean.setMonthlyPaymentsEligible(quoteCalculationDetails.isMonthlyPaymentsEligible());
			aPaymentBean.setAnyPaymentsEligible(quoteCalculationDetails.isAnyPaymentsEligible());
			
			if (aPolicyVersion.getCombinedPolicyCode() != null) {
				if (aPolicyVersion.getCombinedPolicyCode().equals(CombinedPolicyCodeEnum.COMBO_POLICY)) {
					aPaymentBean.setInsuranceHomeAndAutoInd(true);
				} else if (aPolicyVersion.getCombinedPolicyCode().equals(CombinedPolicyCodeEnum.MONOLINE_POLICY)) {
					aPaymentBean.setInsuranceHomeAndAutoInd(false);
				}
			}
			if (aPolicyVersion.getPreferredPaymentMethod() != null) {
				aPaymentBean.setPreferredPaymentMethod(aPolicyVersion.getPreferredPaymentMethod().getCode());			
			}
		}
	}

	protected abstract void applySpecificCalculationRules(QuoteCalculationDetails quotationDetails, PolicyVersion policyVersion);
	
	/**
	 * Load monthly and annually premium
	 */
	protected abstract void loadPremiumInformation(E aPaymentBean, QuoteCalculationDetails quoteCalculationDetails);
	

	/**
	 * checks if selected offer rating is found
	 * 
	 * @param aPolicy
	 * @return true if exists else false
	 */
	private boolean isSelectedOfferRatingFound(PolicyVersion aPolicy) {
		Set<InsuranceRisk> aSetOfRisk = aPolicy.getInsuranceRisks();
		if (aSetOfRisk != null) {
			Iterator<InsuranceRisk> aIteRisk = aPolicy.getInsuranceRisks().iterator();
			if (aIteRisk.hasNext()) {
				InsuranceRisk risk = aIteRisk.next();
				InsuranceRiskOffer riskOffer = risk.getSelectedInsuranceRiskOffer();
				if (riskOffer != null && riskOffer.getPolicyOfferRating().getAnnualPremium() != null) {
					return true;
				}
			}
		}
		return false;
	}

	/**
	 * Loads payment info for bank payment
	 * 
	 * @param aPaymentBean
	 * @param aPolicy
	 */
	public void loadPaymentBank(E aPaymentBean, PolicyVersion aPolicy) {

		Billing theBilling = aPolicy.getBilling();
		PaymentBankBean paymentBank = new PaymentBankBean();
		paymentBank.setBankAccountHolderName(theBilling.getAccount().getBankAccountHolderName());
		try {
			paymentBank.setAccountNumber(this.cipher.decryptToString(theBilling.getAccount().getAccountNumber()));
			paymentBank.setInstitution(this.cipher.decryptToString(theBilling.getAccount()
					.getFinancialInstitutionNumber()));
			paymentBank.setTransit(this.cipher.decryptToString(theBilling.getAccount().getRoutingNumber()));
			aPaymentBean.setPaymentMonthlyBank(paymentBank);
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
		}
	}

	/**
	 * Loads payment info for credit card payment
	 * 
	 * @param aPaymentBean
	 * @param aPolicy
	 */
	public void loadPaymentCC(E aPaymentBean, PolicyVersion aPolicy) {

		Billing theBilling = aPolicy.getBilling();
		PaymentCCBean paymentCC = new PaymentCCBean();
		paymentCC.setCardToken(theBilling.getAccount().getCreditCardToken());

		try {		
			
			paymentCC.setCardMaskedNumber(theBilling.getAccount().getCreditCardAccountNumberMask());
			
			if(theBilling.getAccount().getCreditCardExpiryDate()!=null){
				String dateMMYY = this.cipher.decryptToString(theBilling.getAccount().getCreditCardExpiryDate());
				if (dateMMYY != null) {
					paymentCC.setCardExpiryMonth(dateMMYY.substring(0, 2));
					paymentCC.setCardExpiryYear(dateMMYY.substring(2, 4));				
				}
			}
			
			paymentCC.setCreditCardCompany(theBilling.getAccount().getCreditCardCompanyCode());
			if(theBilling.getAccount().getCreditCardHolderName()!=null){
				paymentCC.setCardHolderName(this.cipher.decryptToString(theBilling.getAccount().getCreditCardHolderName()));
			}
			if (BillingPlanCodeEnum.CREDIT_CARD_MONTHLY.equals(theBilling.getBillingPlan())) {
				aPaymentBean.setPaymentMonthlyCC(paymentCC);
			} else {
				aPaymentBean.setPaymentFullCC(paymentCC);
			}
		} catch (Exception e) {
			log.error(ExceptionUtils.getStackTrace(e));
		}
	}

	/**
	 * Checks if payment bank monthly
	 * 
	 * @param theBilling
	 * @return
	 */
	public boolean isBankPayment(Billing theBilling) {
		if (theBilling != null && theBilling.getAccount() != null
				&& BillingPlanCodeEnum.PRE_AUTHORIZED_CHEQUE_PLAN.equals(theBilling.getBillingPlan())
				&& MethodOfPaymentCodeEnum.ELECTRONIC_FUNDS_TRANSFER.equals(theBilling.getMethodOfPayment())) {
			return true;
		}
		return false;

	}

	/**
	 * Checks if payment used Credit Card
	 * 
	 * @param theBilling
	 * @return
	 */
	public boolean isCCPayment(Billing theBilling) {

		if (theBilling != null
				&& theBilling.getAccount() != null
				&& MethodOfPaymentCodeEnum.CREDIT_CARD.equals(theBilling.getMethodOfPayment())
				&& (BillingPlanCodeEnum.CREDIT_CARD_MONTHLY.equals(theBilling.getBillingPlan()) || BillingPlanCodeEnum.CASH_AND_INSTRUMENTS
						.equals(theBilling.getBillingPlan()))) {
			return true;
		}
		return false;
	}

	/**
	 * Load payment info for bank or credit card method
	 * 
	 * @param aPaymentBean
	 * @param aPolicyVersion
	 */
	public void loadMethodPaymentBean(E aPaymentBean, PolicyVersion aPolicyVersion) {
		Billing theBilling = aPolicyVersion.getBilling();

		if (this.isBankPayment(theBilling)) {
			aPaymentBean.setPaymentBank(true);
			this.loadPaymentBank(aPaymentBean, aPolicyVersion);
		} else if (this.isCCPayment(theBilling)) {
			aPaymentBean.setPaymentCC(true);
			this.loadPaymentCC(aPaymentBean, aPolicyVersion);
		}
	}
	
	/**
	 * 
	 * @param aPolicyVersion
	 * @param aProvinceCode
	 * @return
	 */
	protected boolean validateBillingPlan(PolicyVersion aPolicyVersion, ProvinceCodeEnum aProvinceCode){
		if (aProvinceCode !=null &&  !ProvinceCodeEnum.QUEBEC.equals(aProvinceCode) && 
				aPolicyVersion.getBilling() != null &&
				(BillingPlanCodeEnum.PRE_AUTHORIZED_CHEQUE_PLAN.equals(aPolicyVersion.getBilling().getBillingPlan()) || 
				 BillingPlanCodeEnum.CREDIT_CARD_MONTHLY.equals(aPolicyVersion.getBilling().getBillingPlan()))){
			return true;
		}
		return false;
	}

}
