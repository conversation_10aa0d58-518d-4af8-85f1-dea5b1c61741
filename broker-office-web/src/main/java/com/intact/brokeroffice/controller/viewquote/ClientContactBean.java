package com.intact.brokeroffice.controller.viewquote;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import com.ing.canada.plp.domain.enums.AgreementFollowUpStatusEnum;
import com.ing.canada.plp.domain.enums.ApplicationModeEnum;
import com.ing.canada.plp.domain.party.Phone;
import com.ing.canada.plp.report.enums.QuoteStatusCodeEnum;
import com.intact.brokeroffice.business.quote.NoteBean;

/**
 * The Class ClientContactBean.
 */
public class ClientContactBean {

	/** The policy holder : concatenates DE22 Last Name + DE21  First Name  ***/
	private String policyHolder;
	
	/** The business name (for commercial lines) */
	private String businessName;
	
	/** The language. */
	private String language;
	
	/** The status. */
	private QuoteStatusCodeEnum status;
	
	/** The broker advisor. */
	private String brokerAdvisor;
	
	/** The phone home. */
	/** DE193  Phone Line Number       **/
	/** DE192  Phone Exchange Number   **/
	/** DE191  Phone Area Code         **/ 
	private String phoneHome;
	
	/** The phone cell. */
	/** DE193  Phone Line Number       **/
	/** DE192  Phone Exchange Number   **/
	/** DE191  Phone Area Code         **/ 
	private String phoneCell;
	
	/** The phone work. */
	/** DE193  Phone Line Number       **/
	/** DE192  Phone Exchange Number   **/
	/** DE191  Phone Area Code         **/ 
	private String phoneWork;
	
	// for dialer purpose
	private String unformattedPhone= null;
	
	/** The last update. */
	private Date lastUpdate;
	
	/** The creation date. */
	private Date creationDate;
		
	/** DE695  Web Zone Follow-up Status . */
	private AgreementFollowUpStatusEnum followupStatus;
	
	private AgreementFollowUpStatusEnum oldFollowupStatus;
	
	/** The notes. */
	private List<NoteBean> notes = new ArrayList<NoteBean>();
	
	/** The note text. */
	private String noteText;
	
	/** The messages. */
	private List<String> messages = new ArrayList<String>();
	
	/** The messages. */
	private List<String> redMessages = new ArrayList<String>();
	
	private String quoteSource;
	
	private String brokerNbr;
	
	public String getBrokerNbr() {
		return brokerNbr;
	}

	public void setBrokerNbr(String brokerNbr) {
		this.brokerNbr = brokerNbr;
	}

	private ApplicationModeEnum applicationMode;

	
	public AgreementFollowUpStatusEnum getOldFollowupStatus() {
		return this.oldFollowupStatus;
	}

	public void setOldFollowupStatus(AgreementFollowUpStatusEnum oldFollowupStatus) {
		this.oldFollowupStatus = oldFollowupStatus;
	}

	/**
	 * Gets the followup status.
	 * 
	 * @return the followup status
	 */
	public AgreementFollowUpStatusEnum getFollowupStatus() {
		return this.followupStatus;
	}

	/**
	 * Sets the followup status.
	 * 
	 * @param followupStatus the new followup status
	 */
	public void setFollowupStatus(AgreementFollowUpStatusEnum aFollowupStatus) {
		this.followupStatus = aFollowupStatus;
	}

	/**
	 * Gets the note text.
	 * 
	 * @return the note text
	 */
	public String getNoteText() {
		return this.noteText;
	}

	/**
	 * Sets the note text.
	 * 
	 * @param noteText the new note text
	 */
	public void setNoteText(String aNoteText) {
		this.noteText = aNoteText;
	}

	/**
	 * Gets the notes.
	 * 
	 * @return the notes
	 */
	public List<NoteBean> getNotes() {
		return this.notes;
	}
	
	/**
	 * Adds the note.
	 * 
	 * @param note the note
	 */
	public void addNote(NoteBean note){
		this.notes.add(note);
	}

	/**
	 * Gets the broker advisor.
	 * BR5192  Displays the Online Name of the Point of Sale assigned to the client on the quote
	 * @return the broker advisor
	 */
	public String getBrokerAdvisor() {
		return this.brokerAdvisor;
	}

	/**
	 * Sets the broker advisor.
	 * 
	 * @param brokerAdvisor the new broker advisor
	 */
	public void setBrokerAdvisor(String aBrokerAdvisor) {
		this.brokerAdvisor = aBrokerAdvisor;
	}

	/**
	 * Gets the language.
	 * BR5189  Language in which the quote was completed by the client.
	 * @return the language
	 */
	public String getLanguage() {
		return this.language;
	}

	/**
	 * Sets the language.
	 * 
	 * @param language the new language
	 */
	public void setLanguage(String aLanguage) {
		this.language = aLanguage;
	}

	/**
	 * Gets the phone cell.
	 * BR5193  Display client response entered in the On-line Auto Quote application
	 * @return the phone cell
	 */
	public String getPhoneCell() {
		return this.phoneCell;
	}

	/**
	 * Sets the phone cell.
	 * 
	 * @param phoneCell the new phone cell
	 */
	public void setPhoneCell(String aPhoneCell) {
		this.phoneCell = aPhoneCell;
	}

	/**
	 * Gets the phone home.
	 * BR5193  Display client response entered in the On-line Auto Quote application
	 * @return the phone home
	 */
	public String getPhoneHome() {
		return this.phoneHome;
	}

	/**
	 * Sets the phone home.
	 * 
	 * @param phoneHome the new phone home
	 */
	public void setPhoneHome(String aPhoneHome) {
		this.phoneHome = aPhoneHome;
	}

	/**
	 * Gets the phone work.
	 * BR5193  Display client response entered in the On-line Auto Quote application
	 * @return the phone work
	 */
	public String getPhoneWork() {
		return this.phoneWork;
	}

	/**
	 * Sets the phone work.
	 * 
	 * @param phoneWork the new phone work
	 */
	public void setPhoneWork(String aPhoneWork) {
		this.phoneWork = aPhoneWork;
	}

	/**
	 * Gets the policy holder.
	 * BR5188  Concatenate the Policyholders First Name and Last Name.: 
	 * @return the policy holder
	 */
	public String getPolicyHolder() {
		return this.policyHolder;
	}

	/**
	 * Sets the policy holder.
	 * 
	 * @param policyHolder the new policy holder
	 */
	public void setPolicyHolder(String aPolicyHolder) {
		this.policyHolder = aPolicyHolder;
	}
	
	/**
	 * Gets the business name (for commercial lines)
	 * @return the policy holder
	 */
	public String getBusinessName() {
		return this.businessName;
	}
	
	/**
	 * Sets the business name
	 * 
	 * @param aBusinessName the new business name
	 */
	public void setBusinessName(String aBusinessName) {
		this.businessName = aBusinessName;
	}

	/**
	 * Gets the status.
	 * BR5134  In Quebec quote can have one of the following states::
	 * BR5368  In Ontario a quote can have one of the following states::  
	 * @return the status
	 */
	public QuoteStatusCodeEnum getStatus() {
		return this.status;
	}

	/**
	 * Sets the status.
	 * 
	 * @param status the new status
	 */
	public void setStatus(QuoteStatusCodeEnum aStatus) {
		this.status = aStatus;
	}

	/**
	 * Gets the creation date.
	 * BR5195  Date the quote was first submitted by the client.: 
	 * @return the creation date
	 */
	public Date getCreationDate() {
		return this.creationDate;
	}

	/**
	 * Sets the creation date.
	 * 
	 * @param creationDate the new creation date
	 */
	public void setCreationDate(Date aCreationDate) {
		this.creationDate = aCreationDate;
	}

	/**
	 * Gets the last update.
	 * BR5196  Date of the last update made to the quote by the client, if any
	 * @return the last update
	 */
	public Date getLastUpdate() {
		return this.lastUpdate;
	}

	/**
	 * Sets the last update.
	 * 
	 * @param lastUpdate the new last update
	 */
	public void setLastUpdate(Date aLastUpdate) {
		this.lastUpdate = aLastUpdate;
	}

	/**
	 * Gets the messages.
	 * BR5231  Section Title appears only when there is at least 1 message to be 
	 * @return the messages
	 */
	public Boolean getHasMessages() {
		return (this.messages.size() > 0 || this.redMessages.size() > 0);
	}

	/**
	 * Gets the messages.
	 * 
	 * @return the messages
	 */
	public List<String> getMessages() {
		return this.messages;
	}
	
	/**
	 * Adds the messages.
	 * 
	 * @param msg the msg
	 */
	public void addMessages(String msg){
		this.messages.add(msg);
	}

	
	/**
	 * Gets the red messages
	 * BR5181  Display this message text in Red (RGB: 192,12,48): 
	 * @return red messages list 
	 */
	public List<String> getRedMessages() {
		return this.redMessages;
	}

	/**
	 * Sets the red messages
	 * @param redMessages the red messages to set
	 */
	public void setRedMessages(List<String> redMessages) {
		this.redMessages = redMessages;
	}
	
	/**
	 * Adds the red messages.
	 * 
	 * @param msg the msg
	 */
	public void addRedMessages(String msg){
		this.redMessages.add(msg);
	}

	/**
	 * Gets the quote source
	 * @return the quote source
	 */
	public String getQuoteSource() {
		return this.quoteSource;
	}

	/**
	 * Sets the quote source
	 * @param aQuoteSource the quote source to set
	 */
	public void setQuoteSource(String aQuoteSource) {
		this.quoteSource = aQuoteSource;
	}	
	
	public ApplicationModeEnum getApplicationMode() {
		return applicationMode;
	}

	public void setApplicationMode(ApplicationModeEnum applicationMode) {
		this.applicationMode = applicationMode;
	}	
	
	public String getUnformattedPhone() {
		return this.unformattedPhone;
	}
	
	public void setUnformattedPhone(Phone phone) {
		this.unformattedPhone = "";
		if (phone.getPhoneAreaCode() != null) {
			this.unformattedPhone = phone.getPhoneAreaCode().trim();
		}
		
		if (phone.getPhoneNumber() != null) {
			this.unformattedPhone = this.unformattedPhone + phone.getPhoneNumber().trim();	
		}
		
	}
}
