/*
 * 
 */
package com.intact.brokeroffice.controller.tabs;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

/**
 * The Class GeneralTabController manages the global tabs.
 */
@Component
@Scope("session")
public class GeneralTabController {	
		
	/**
	 * The Enum Page.
	 */
	private static enum Page { QUOTES }
	
	/** The current page. */
	private Page page = Page.QUOTES; 

	
	/**
	 * Gets the current page.
	 * 
	 * @return the page
	 */
	public Page getPage() {
		return this.page;
	}

	/**
	 * Sets the current page.
	 * 
	 * @param aPage the new page
	 */
	public void setPage(Page aPage) {
		this.page = aPage;
	}
		
	/**
	 * Quotes.
	 */
	public void quotes(){
		this.page = Page.QUOTES;
	}

	/**
	 * Checks if is quotes.
	 * 
	 * @return the boolean
	 */
	public Boolean isQuotes(){
		return this.page == Page.QUOTES;
	}
}
