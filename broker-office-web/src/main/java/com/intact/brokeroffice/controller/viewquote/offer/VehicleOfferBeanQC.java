package com.intact.brokeroffice.controller.viewquote.offer;

import com.ing.canada.plp.domain.enums.BasicCoverageCodeEnum;



public class VehicleOfferBeanQC extends VehicleOfferBean {
	
	
	/**
	 * Gets the coverage levels in the order they should be displayed on screen. If you wish to hide an offer under certain
	 * circumstances, it is feasible by returning a dynamically constructed array. The front-end code should never
	 * assume a specific order for the offers, the goal is to be able to change the order easily without breaking the UI
	 * code.
	 * 
	 * @return the offers display order
	 */
	public static final BasicCoverageCodeEnum[] getCoveragesDisplayOrder() {
		return new BasicCoverageCodeEnum[] { BasicCoverageCodeEnum.LIABILITY_A, BasicCoverageCodeEnum.COLLISION_B2,
				BasicCoverageCodeEnum.COMPREHENSIVE_B3, BasicCoverageCodeEnum.ACCIDENT_BENEFITS_P1};
	}	
	
	/**
	 * Gets the liability QC CoverageOfferBean
	 * BR5214  If no custom package has been calculated, indicate N/A.: 
	 * BR5209  Repeat for each vehicle listed
	 * @return the liability QC 
	 */
	public CoverageOfferBean getLiability(){		
		if(this.getOfferSelected()!=null && getOfferSelected().booleanValue()){
			return this.getCoveragesMapped(BasicCoverageCodeEnum.LIABILITY_A);
		}
		return null;
	}
	
	/**
	 * Gets the Collision Deductible QC
	 * BR5214  If no custom package has been calculated, indicate N/A.:
	 * BR5209  Repeat for each vehicle listed 
	 * @return the Collision Deductible QC 
	 */
	public CoverageOfferBean getCollisionDeductible(){		
		if(this.getOfferSelected()!=null && getOfferSelected().booleanValue()){
			return this.getCoveragesMapped(BasicCoverageCodeEnum.COLLISION_B2);
		}
		return null;
	}
	
	/**
	 * Gets the Comprehensive Deductible QC
	 * BR5214  If no custom package has been calculated, indicate N/A.: 
	 * BR5209  Repeat for each vehicle listed
	 * @return the Comprehensive Deductible QC 
	 */
	public CoverageOfferBean getComprehensiveDeductible(){	
		if(this.getOfferSelected()!=null && getOfferSelected().booleanValue()){
			return this.getCoveragesMapped(BasicCoverageCodeEnum.COMPREHENSIVE_B3);
		}
		return null;
	}
	
	/**Gets the accident benefit ON
	 * BR5214  If no custom package has been calculated, indicate N/A.: 
	 * BR5209  Repeat for each vehicle listed
	 * BR5217  Display the default.: 
	 * @return the accident benefit ON 
	 */
	public CoverageOfferBean getAccidentBenefit(){	
		if(this.getOfferSelected()!=null && getOfferSelected().booleanValue()){
			return this.getCoveragesMapped(BasicCoverageCodeEnum.ACCIDENT_BENEFITS_P1);
		}		
		return null;
	}	
	
	/**Gets the family protection  QC
	 * BR5214  If no custom package has been calculated, indicate N/A.: 
	 * BR5209  Repeat for each vehicle listed
	 * @return the accident benefit QC 
	 */
	//public CoverageOfferBean getFamilyProtection(){	
	//	if(this.getOfferSelected()!=null && getOfferSelected().booleanValue()){
	//		return this.getCoveragesMapped(EndorsementCodeEnum._44);
	//	}		
	//	return null;
	//}	

}
