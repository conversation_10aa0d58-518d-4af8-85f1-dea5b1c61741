package com.intact.brokeroffice.controller.viewquote;

import java.util.Date;
import java.util.List;

import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.brokeroffice.controller.viewquote.offer.CoverageOfferBean;
import com.intact.brokeroffice.controller.viewquote.payment.PaymentBean;

public interface IViewQuoteController {

	/**
	 * Update the quote.
	 * @throws Exception 
	 */
	public void update(String referenceNumber, String lineOfBusiness, String lineOfInsurance, String applicationMode) throws Exception;

	/**
	 * Upload the quote.
	 */
	public void upload(String quoteId);

	/**
	 * Gets the payment bean.
	 * 
	 * @return the payment bean
	 */
	public PaymentBean getPaymentBean();
	
	/**
	 * Loads quote information
	 * @param referenceNumber
	 * @throws BrokerServiceException
	 * @throws UsageServiceException
	 * @throws I18nServiceException
	 */
	void viewQuote(String referenceNumber) throws Exception;
	
	/**
	 * This method has the flexibility to invoke the controller
	 * in a new page or in process mode. The difference is the stats that
	 * is not updated when it is invoked with newPageMode set to false
	 * @param referenceNumber
	 * @param newPageMode
	 * @throws Exception
	 */
	void viewQuote(String referenceNumber, boolean newPageMode) throws Exception;

	/**
	 * Gets the endorsement sorted items.
	 * 
	 * @return the endorsement sorted items
	 */
	public List<CoverageOfferBean> getEndorsementSortedItems();

	// void viewQuote(boolean isNewPage);

	/**
	 * Gets the current date for access denied page
	 * 
	 * @return the current date
	 */
	public Date getCurrentDate();
}
