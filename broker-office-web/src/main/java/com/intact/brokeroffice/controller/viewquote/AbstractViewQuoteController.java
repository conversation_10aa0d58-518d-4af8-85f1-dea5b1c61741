package com.intact.brokeroffice.controller.viewquote;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.cif.service.ISubBrokersService;
import com.ing.canada.plp.domain.businesstransaction.BusinessTransaction;
import com.ing.canada.plp.domain.enums.*;
import com.ing.canada.plp.domain.insurancepolicy.InsurancePolicy;
import com.ing.canada.plp.domain.party.Consent;
import com.ing.canada.plp.domain.policyversion.PolicyHolder;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IPartyHelper;
import com.ing.canada.plp.report.enums.QuoteStatusCodeEnum;
import com.ing.canada.plp.service.IPolicyHolderService;
import com.intact.brokeroffice.business.common.ICommandBusinessProcess;
import com.intact.brokeroffice.business.domain.Quote;
import com.intact.brokeroffice.business.domain.bloommq.BloomQNameEnum;
import com.intact.brokeroffice.business.domain.dialer.PartyDTO;
import com.intact.brokeroffice.business.domain.dialer.QuoteDialerDTO;
import com.intact.brokeroffice.business.exception.BrokerServiceException;
import com.intact.brokeroffice.business.exception.DialerServiceException;
import com.intact.brokeroffice.business.helper.IQuoteDialerDTOHelper;
import com.intact.brokeroffice.business.quote.QuotesBean;
import com.intact.brokeroffice.business.quote.util.PDFDocumentInfo;
import com.intact.brokeroffice.business.quote.util.PolicyInfo;
import com.intact.brokeroffice.controller.authentification.AuthentificationController;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;
import com.intact.brokeroffice.controller.language.LanguageController;
import com.intact.brokeroffice.controller.permission.PermissionController;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.brokeroffice.controller.search.SearchController;
import com.intact.brokeroffice.controller.viewquote.offer.VehicleOfferBean;
import com.intact.brokeroffice.controller.viewquote.payment.PaymentBean;
import com.intact.brokeroffice.controller.viewquote.payment.PremiumBean;
import com.intact.brokeroffice.dao.SystemDAO;
import com.intact.brokeroffice.helper.ResourceBundleHelper;
import com.intact.brokeroffice.service.IBrokerService;
import com.intact.brokeroffice.service.IDialerService;
import com.intact.brokeroffice.service.IQuoteService;
import com.intact.brokeroffice.service.bloommqhandler.IBloomMqHandlerService;
import com.intact.brokeroffice.service.util.Configuration;
import com.intact.brokeroffice.util.Constantes;
import com.intact.brokeroffice.util.ProvinceCompanyConverter;
import com.intact.plt.domain.broker.FollowUpStatusEnum;
import com.intact.plt.information.service.InformationApplicationService;
import com.intact.plt.information.service.client.domain.InputLocaleAdapter;
import com.intact.plt.information.service.client.util.InformationPieceTO;
import com.intact.plt.information.service.client.util.InformationSearchTO;
import com.intact.tools.logging.exception.LoggingServiceException;
import com.intact.tools.logging.service.LoggingApplicationService;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;

import jakarta.faces.context.ExternalContext;
import jakarta.faces.context.FacesContext;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.*;

import static java.util.Arrays.asList;
import static java.util.Collections.unmodifiableList;

/**
 * The Class ViewQuoteController. This is a Request scope controller to view multiple quotes in
 * separate web page.
 * <p>
 * BR5112 When clicked, opens the Quote View for the selected record in a new window. see:
 * <h:outputLink value="pages/quotes/viewQuote/viewQuote.jsf" target="_blank" > in quotes.xhtml
 * BR5234 Displayed to the left of the Terms & Conditions statements: see quoteTerms.xhtml BR5223
 * Place image before the Field label.: see quoteTerms.xhtml
 */
@SuppressWarnings("rawtypes")
public abstract class AbstractViewQuoteController<V extends VehicleOfferBean> {

  /**************** CONSTANTS *****************/

  private final Log log = LogFactory.getLog(AbstractViewQuoteController.class);
  public static final List<String> LIST_INVALID_STATUS_FOR_DIALER = unmodifiableList(asList(
      AgreementFollowUpStatusEnum.CONTACTED_NO_FOLLOWUP_REQUIRED.getCode(),
      AgreementFollowUpStatusEnum.DUPLICATE_FAKE.getCode()));
  // List of dialer admin users contains users provided by Marc-Antoine Landry (SME) as well as WZ_ADMN and echevali (for testing)
  public static final List<String> LIST_DIALER_ADMIN_USERS = unmodifiableList(
      asList("wz_admn", "echevali", "marlandr", "matberub", "mjeanson", "algrigno", "cractlif",
          "jmarsot"));
  public static final int DIALER_BROKER_NAME_LENGTH = 50;
  public String tabTitle = null;
  /**
   * The language controller.
   */
  @Inject
  protected LanguageController languageController;
  /**
   * The search controller.
   */
  @Inject
  protected SearchController searchController;
  @Inject
  protected IPartyHelper partyhelper;
  /**
   * The payment bean.
   */
  protected PaymentBean paymentBean = new PaymentBean();
  /**
   * The command business process.
   */
  @Inject
  private ICommandBusinessProcess commandBusinessProcess;
  /**
   * The quote business process.
   */
  @Inject
  private IBrokerService quoteBusinessProcess;
  /**
   * The permission controller.
   */
  @Inject
  private PermissionController permissionController;
  /**
   * The quote adapter.
   */
  @Inject
  private ViewQuoteAdapter quoteAdapter;
  /**
   * The authentification controller.
   */
  @Inject
  private AuthentificationController authentificationController;
  @Inject
  @Named("plt.information.keys")
  private Configuration informationKeys = null;
  @Inject
  private IPolicyHolderService policyHolderService;
  @Inject
  private IQuoteService quoteService;
  @Inject
  private IDialerService dialerService;
  @Inject
  private SystemDAO systemDAO;
  @Inject
  @Named("subBrokersServiceOPC")
  private ISubBrokersService subBrokerService = null;
  private boolean showUbiConsent;
  private Boolean ubiConsentInd = Boolean.FALSE;
  @Inject
  @Named("uploadStatuses")
  private Configuration uploadStatuses;
  /**
   * The followup status list.
   */
  private Map<String, AgreementFollowUpStatusEnum> followupStatusList = null;
  /**
   * The vehicle offer bean qc list.
   */
  private List<V> vehicleOfferBeanList = new ArrayList<V>();
  /**
   * The premium bean.
   */
  private PremiumBean<?> premiumBean = new PremiumBean();
  /**
   * The client contact bean.
   */
  private ClientContactBean clientContactBean = new ClientContactBean();
  /**
   * The quote access.
   */
  private Boolean quoteAccess = null;
  private String uploadMessage;
  private Boolean uploadSucceed;
  private Boolean dialerSucceed;
  private String dialerOutput;
  private boolean quoteUploaded = false;
  private String remoteSystemUrl;
  /**
   * The reference Legacy no.
   */
  private String referenceLegacyNo;
  /**
   * future communication consent - MK
   */
  private Boolean antispamConsent = Boolean.TRUE;
  /**
   * DE639 create profile consent - PR
   */
  private Boolean createProfileConsentInd = Boolean.TRUE;
  /**
   * credit checks consent defaulted to true - CS
   */
  private Boolean creditConsentInd = Boolean.TRUE;
  /**
   * The language controller.
   */
  @Autowired
  private ProvinceController provinceController;
  @Autowired
//  @Qualifier("webzoneClientInformationService")
  private InformationApplicationService infoService;
  private boolean newPage;
  private Boolean hasEconomyOffer = Boolean.FALSE;
  private Boolean hasValuePackOffer = Boolean.FALSE;
  private Boolean hasPlusOffer = Boolean.FALSE;
  private Boolean hasCustomOffer = Boolean.FALSE;
  private Boolean hasPreferredOffer = Boolean.FALSE;
  private Boolean hasPeopleLikeYouOffer = Boolean.FALSE;
  private Boolean hasRecommendedOffer = Boolean.FALSE;
  private InformationPieceTO generalInfoPiece = null;
  private InformationPieceTO driverInfoPiece = null;
  private InformationPieceTO vehicleInfoPiece = null;
  private QuotesBean quote = null;
  @Inject
  private IQuoteDialerDTOHelper quoteDialerDTOHelper;
  @Autowired
  private IBloomMqHandlerService bloomMqHandlerService;
  @Inject
  private LoggingApplicationService logService = null;
  private Boolean assignSucceed;
  private boolean quoteAssigned = false;

  public AbstractViewQuoteController() {
  }

  public InformationApplicationService getInfoService() {
    return this.infoService;
  }

  public void setInfoService(InformationApplicationService infoService) {
    this.infoService = infoService;
  }

  public Configuration getInformationKeys() {
    return informationKeys;
  }

  public void setInformationKeys(Configuration informationKeys) {
    this.informationKeys = informationKeys;
  }

  public void changeLanguage(String language) throws Exception {

    if ("en".equals(language)) {
      this.languageController.english();
    } else {
      this.languageController.french();
    }

    this.setGeneralInfoPiece(null);
  }

  public void initialize() throws Exception {

    String referenceNo = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap()
            .get("referenceNo");
    String lineOfBusiness = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap()
            .get("lineOfBusiness");
    String lineOfInsurance = FacesContext.getCurrentInstance().getExternalContext().getRequestParameterMap()
            .get("lineOfInsurance");

    if (log.isDebugEnabled()) {
      log.debug("Initializing quote with [referenceNo=" + referenceNo + ", lineOfBusiness=" + lineOfBusiness + ", lineOfInsurance=" + lineOfInsurance + "]");
    }

    this.setQuote(new QuotesBean(
        referenceNo,
        lineOfBusiness,
        lineOfInsurance));

    if (!(LineOfBusinessCodeEnum.PERSONAL_LINES.equals(this.getQuote().getLineOfBusiness())
        && LineOfInsuranceCodeEnum.RESIDENTIAL.equals(this.getQuote().getLineOfInsurance()))) {
      PolicyVersion policyVersion = this.getCommandBusinessProcess()
          .findLatestPolicyVersion(this.getQuote().getId());
      this.updateCreateProfileConsentInd(policyVersion);
    }

    if (Boolean.TRUE.equals(this.getCreateProfileConsentInd())) {
      this.viewQuote(this.isNewPage());
    }

  }

  public abstract void viewQuote(boolean newPage) throws Exception;

  public QuotesBean getQuote() {
    return this.quote;
  }

  public void setQuote(QuotesBean quote) {
    this.quote = quote;
  }

  public String getApplicationMode() {
    return this.getQuote().getAgreementNumber().substring(0, 2);
  }

  /**
   * BR5336 On the Quote View page: Update the quote.
   *
   * @throws Exception
   */
  public void update(String aReferenceNumber, String lineOfBusiness, String lineOfInsurance,
      String applicationMode)
      throws Exception {

    String activityNote = null;
    String statusNote = null;

    if (!StringUtils.isEmpty(clientContactBean.getNoteText())) {
      activityNote = clientContactBean.getNoteText();
      // BR5204 User can enter freeform text up to 150 characters at a time to
      // describe
      // attempts to contact client, notes, etc. Entries will be displayed in the
      // Notes
      // column of the Activity Log.:
      if (clientContactBean.getNoteText().length() > 150) {
        activityNote = clientContactBean.getNoteText().substring(0, 150);
      }
    }

    if (clientContactBean.getFollowupStatus() != null
        && !clientContactBean.getFollowupStatus()
        .equals(clientContactBean.getOldFollowupStatus())) {

      if (clientContactBean.getOldFollowupStatus() == null) {
        clientContactBean.setOldFollowupStatus(clientContactBean.getFollowupStatus());
      }

      statusNote = ResourceBundleHelper
          .getMessage("com.intact.brokeroffice.controller.viewquote.client", this,
              "client.followup.note",
              ResourceBundleHelper
                  .getMessage("com.intact.brokeroffice.controller.viewquote.client", this,
                      clientContactBean.getOldFollowupStatus().name()),
              ResourceBundleHelper
                  .getMessage("com.intact.brokeroffice.controller.viewquote.client", this,
                      clientContactBean.getFollowupStatus().name()));
    }

    this.getQuoteBusinessProcess().updateQuote(
        this.permissionController.getUserContext(),
        this.buildQuote(aReferenceNumber, lineOfBusiness, lineOfInsurance,
            clientContactBean.getFollowupStatus(), statusNote, activityNote, applicationMode));
    sendMessageToBloomMqHandlerService();
    this.viewQuote(false);

    if (LineOfInsuranceCodeEnum.RESIDENTIAL.getCode().equals(lineOfInsurance)) {
      this.setGeneralInfoPiece(null);
    }
  }

  public AgreementFollowUpStatusEnum getFollowUpStatus() throws Exception {
    AgreementFollowUpStatusEnum status = null;

    if (this.getGeneralInfoPiece() != null) {
      try {
        status = AgreementFollowUpStatusEnum.valueOfCode(
            this.getGeneralInfoPiece().getChildren("follow_up_status").getPieces().get(1)
                .getValue());
      } catch (Exception ignored) {
      }
    } else if (this.getClientContactBean().getFollowupStatus() != null) {
      status = this.getClientContactBean().getFollowupStatus();
    }

    return status;

  }

  public void setFollowUpStatus(AgreementFollowUpStatusEnum status) throws Exception {
    this.getClientContactBean().setOldFollowupStatus(getFollowUpStatus());
    this.getClientContactBean().setFollowupStatus(status);
  }

  protected Quote buildQuote(QuotesBean quote) {
    Quote newQuote = new Quote();

    newQuote.setId(quote.getId());
    newQuote.setLineOfBusiness(quote.getLineOfBusiness());
    newQuote.setLineOfInsurance(quote.getLineOfInsurance());
    newQuote.setViewNote("");
    newQuote.setApplicationMode(quote.getApplicationMode());

    return newQuote;
  }

  protected Quote buildQuote(String quoteId, String lineOfBusiness, String lineOfInsurance,
      AgreementFollowUpStatusEnum status, String statusNote, String activityNote,
      String applicationMode) {

    Quote newQuote = new Quote();
    newQuote.setId(quoteId);
    newQuote.setLineOfBusiness(LineOfBusinessCodeEnum.valueOfCode(lineOfBusiness));
    newQuote.setLineOfInsurance(LineOfInsuranceCodeEnum.valueOfCode(lineOfInsurance));
    newQuote.setStatus(status);
    newQuote.setStatusNote(statusNote);
    newQuote.setActivityNote(activityNote);
    newQuote.setApplicationMode(applicationMode);

    return newQuote;

  }

  public Configuration getUploadStatuses() {
    return this.uploadStatuses;
  }

  public void setUploadStatuses(Configuration uploadStatuses) {
    this.uploadStatuses = uploadStatuses;
  }

  /**
   * Gets the follow up status list.
   *
   * @return the follow up status list
   */
  public Map<String, AgreementFollowUpStatusEnum> getFollowupStatusList() {
    if (this.followupStatusList == null) {
      this.followupStatusList = getFollowpStatus();
    }
    return this.followupStatusList;
  }

  /**
   * Gets the client contact bean.
   *
   * @return the client contact bean
   */
  public ClientContactBean getClientContactBean() {
    return this.clientContactBean;
  }

  /**
   * Sets the client contact bean.
   *
   * @param ClientContactBean the new client contact bean
   */
  public void setClientContactBean(ClientContactBean clientContactBean) {
    this.clientContactBean = clientContactBean;
  }

  /**
   * Gets the premium bean.
   *
   * @return the premium bean
   */
  public PremiumBean<?> getPremiumBean() {
    return this.premiumBean;
  }

  /**
   * Gets the vehicle offer QC/ON bean list .
   *
   * @return the vehicle offer bean QC/ON list
   */
  public List<V> getVehicleOfferBeanList() {
    return this.vehicleOfferBeanList;
  }

  /**
   * Sets the vehicle offer bean QC/ON list.
   *
   * @param someVehicleOfferBeanList the new vehicle offer bean QC/ON list
   */
  public void setVehicleOfferBeanList(List<V> someVehicleOfferBeanList) {
    this.vehicleOfferBeanList = someVehicleOfferBeanList;
  }

  /**
   * Gets the vehicle offer size.
   *
   * @return the vehicle offer size
   */
  public Integer getVehicleOfferSize() {
    return this.vehicleOfferBeanList.size();
  }

  /**
   * Gets the quote access.
   * <p>
   * BR5113 If the current user is a participating point of sale and the selected quote has been
   * reassigned to another point of sale since the List of Quotes/Search Results were first
   * displayed, a message indicating the selected quote is no longer available w: BR5226 If the
   * quote is uploaded by one user while being viewed by another user, the hyperlink will be removed
   * from display if clicked by the user that did not perform the upload. The Uploaded Quote error
   * message will be displayed BR5228 Quote access when reassigned: BR5197 If the current user is a
   * participating point of sale and the selected quote has been reassigned to another point of sale
   * since the Quote View was first displayed, if they attempt to change the Follow-up status, the
   * Quote View will be closed and a :
   *
   * @return the quote access
   * @throws BrokerServiceException
   */
  public Boolean getQuoteAccess() throws BrokerServiceException {
    if (this.quoteAccess == null) {
      this.quoteAccess = this.permissionController.getCheckQuoteAccess();
    }
    return this.quoteAccess;
  }

  /**
   * Sets the quote access
   *
   * @param aQuoteAccess
   */
  public void setQuoteAccess(Boolean aQuoteAccess) {
    this.quoteAccess = aQuoteAccess;
  }

  /**
   * BUILD QUOTE INFORMATION based on referenceNo
   *
   * @param aReferenceNo
   * @return
   */
  public PolicyVersion buildViewQuoteCommon(String aReferenceNo) {
    removeEmailReferenceNo();

    PolicyVersion policyVersion = this.commandBusinessProcess.findLatestPolicyVersion(aReferenceNo);

    // If there is a policy version, it is an Auto quote
    if (policyVersion != null) {
      checkAutoQuoteUploaded(policyVersion);

      this.updateShowUbiConsent(policyVersion);

      this.updateUbiConsent(policyVersion);

      updateAntispamConsent(policyVersion);

      // LOAD TERMS AND POLICY
      updateCreditConsentInd(policyVersion);
      updateCreateProfileConsentInd(policyVersion);

      // LOAD CLIENT CONTACT INFORMATION
      this.quoteAdapter.loadClientContactBean(this.clientContactBean, policyVersion,
          this.authentificationController.isMasterRole());

      this.quoteAdapter.loadConsentInfo(this.clientContactBean, this.antispamConsent);
    } else if (aReferenceNo
        != null) { // If there is no policy version, but there is a reference nbr, it is a home
      // quote
      // Obtain the quote details from the search results
      this.setQuote(this.searchController.getSearchState().getQuotesMap().get(aReferenceNo));

      checkHomeQuoteUploaded();
    }

    return policyVersion;

  }

  protected void updateCreateProfileConsentInd(PolicyVersion policyVersion) {
    Consent currentConsent = retrieveProperConsentFromPolicyHolder(
        ConsentTypeCodeEnum.CLIENT_PROFILE_CREATION,
        policyVersion);
    if (currentConsent != null) {
      this.setCreateProfileConsentInd(currentConsent.getConsentIndicator());
    } else {
      this.setCreateProfileConsentInd(Boolean.TRUE);
    }

  }

  /**
   * Gets the follow up status.
   *
   * @return the follow up status
   */
  private Map<String, AgreementFollowUpStatusEnum> getFollowpStatus() {
    Map<String, AgreementFollowUpStatusEnum> map = new LinkedHashMap<String, AgreementFollowUpStatusEnum>();
    map.put(
        ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client", this,
            "client.followup.notContacted"), AgreementFollowUpStatusEnum.NOT_CONTACTED);
    map.put(
        ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client", this,
            "client.followup.required"), AgreementFollowUpStatusEnum.CONTACTED_FOLLOWUP_REQUIRED);
    map.put(
        ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client", this,
            "client.followup.notRequired"),
        AgreementFollowUpStatusEnum.CONTACTED_NO_FOLLOWUP_REQUIRED);
    map.put(
        ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client", this,
            "client.followup.noneRequired"),
        AgreementFollowUpStatusEnum.NOT_CONTACTED_NO_FOLLOWUP_REQUIRED);
    map.put(
        ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client", this,
            "client.followup.duplicate"), AgreementFollowUpStatusEnum.DUPLICATE_FAKE);
    return map;
  }

  /**
   * Gets the upload message to display
   *
   * @return the upload message
   */
  public String getUploadMessage() {
    return this.uploadMessage;
  }

  /**
   * Sets the upload message to display
   *
   * @param aUploadMessage the upload message to set
   */
  public void setUploadMessage(String aUploadMessage) {
    this.uploadMessage = aUploadMessage;
  }

  /**
   * gets the upload succeed indicator return true if upload succeed else false
   */
  public Boolean getUploadSucceed() throws Exception {
    return this.uploadSucceed;
  }

  /**
   * Sets the upload succeed indicator BR5226 If the quote is uploaded by one user while being
   * viewed by another user, the Hyperlink will be removed from display if clicked by the user that
   * did not perform the upload. The Uploaded Quote error message will be displayed
   *
   * @param aUploadSucceed
   */
  public void setUploadSucceed(Boolean aUploadSucceed) {
    this.uploadSucceed = aUploadSucceed;
  }

  /**
   * Verify if the auto quote has already been uploaded.
   *
   * @param aPolicyVersion
   */
  protected void checkAutoQuoteUploaded(PolicyVersion aPolicyVersion) {
    InsurancePolicy currentInsurancePolicy = aPolicyVersion.getInsurancePolicy();
    setReferenceLegacyNo(currentInsurancePolicy.getAgreementLegacyNumber());
    this.setQuoteUploaded(this.getAutoQuoteUploadStatus(aPolicyVersion));
  }

  /**
   * Verify if the home quote has already been uploaded.
   */
  protected void checkHomeQuoteUploaded() {
    if (this.getQuote() != null) {
      setReferenceLegacyNo(this.getQuote().getAgreementLegacyNumber());
    }
    this.setQuoteUploaded(this.getHomeQuoteUploadStatus());
  }

  protected QuoteStatusCodeEnum getQuoteStatus(PolicyVersion aPolicyVersion) {

    InsurancePolicy currentInsurancePolicy = aPolicyVersion.getInsurancePolicy();
    BusinessTransaction aBusinessTransaction = aPolicyVersion.getBusinessTransaction();

    return QuoteStatusCodeEnum.getQuoteStatus(currentInsurancePolicy.getOriginalInceptionDate(),
        currentInsurancePolicy.getAgreementLegacyNumber(),
        aBusinessTransaction.getTransactionStatus());
  }

  /**
   * Gets the quote uploaded indicator BR5371 Once the quote has been uploaded successfully
   *
   * @return the quote uploaded indicator
   */
  public boolean isQuoteUploaded() {
    return this.quoteUploaded;
  }

  /**
   * Sets the quote uploaded indicator
   *
   * @param aQuoteUploaded the quote uploaded indicator to set
   */
  public void setQuoteUploaded(boolean aQuoteUploaded) {
    this.quoteUploaded = aQuoteUploaded;
  }

  public boolean isCheckUpload() {

    boolean isPC = (this.getQuote() != null && "PC"
        .equalsIgnoreCase(this.getQuote().getApplicationMode()));

    return !isPC && !(this.getQuote() != null &&
        LineOfBusinessCodeEnum.COMMERCIAL_LINES.equals(this.getQuote().getLineOfBusiness())
            && (CifCompanyEnum.INTACT_HALIFAX.getSubBrokerCompanyNumber()
            .equals(this.systemDAO.getCompany())
            || CifCompanyEnum.INTACT_AB.getSubBrokerCompanyNumber()
            .equals(this.systemDAO.getCompany())));
  }

  /**
   * Gets the reference legacy No
   *
   * @return the reference legacy No
   */
  public String getReferenceLegacyNo() {
    return this.referenceLegacyNo;
  }

  /**
   * Sets the reference legacy No
   *
   * @param referenceLegacyNo the reference legacy No to set
   */
  public void setReferenceLegacyNo(String aReferenceLegacyNo) {
    this.referenceLegacyNo = aReferenceLegacyNo;
  }

  public final void setTabTitle(String tabTitle) {
    this.tabTitle = tabTitle;
  }

  /**
   * Gets the url to goBRIO
   *
   * @return the url to goBRIO
   */
  public String getRemoteSystemUrl() {

    if (this.remoteSystemUrl == null) {
      this.remoteSystemUrl = this.systemDAO.getRemoteSystemUrl();
    }

    return this.remoteSystemUrl;
  }

  /**
   * Sets the url to gobrio
   *
   * @param urlToGoBrio the url to gobrio to set
   */
  public void setRemoteSystemUrl(String remoteSystemUrl) {
    this.remoteSystemUrl = remoteSystemUrl;
  }

  public boolean isShowRemoteLink() {

    boolean show = false;

    boolean isRqqON = this.getQuote() != null && StringUtils
        .equalsIgnoreCase("QH", this.getQuote().getApplicationMode())
        && StringUtils.equalsIgnoreCase(CifCompanyEnum.INTACT_HALIFAX.getSubBrokerCompanyNumber(),
        this.systemDAO.getCompany());

    if (this.getReferenceLegacyNo() != null && !isRqqON) {
      show = true;
    } else if (this.systemDAO.getCompany()
        .equals(CifCompanyEnum.INTACT_HALIFAX.getSubBrokerCompanyNumber())
        || this.systemDAO.getCompany()
        .equals(CifCompanyEnum.INTACT_AB.getSubBrokerCompanyNumber())) {
      show = this.getRemoteSystemUrl() != null;
    }

    return show;
  }

  public boolean isShowDownloadLink() {

    boolean show = false;

    if (StringUtils.isNotBlank(this.getQuote().getId()) && this.getQuote().getApplicationMode()
        .equals("PC")) {
      show = true;
    }

    return show;
  }

  public boolean isShowDialerLink() {
    String username = this.permissionController.getUserContext().getUser();

    // If username is null, don't display link. Otherwise, set username to lowercase
    if (username == null) {
      return false;
    } else {
      username = username.toLowerCase();
    }

    /*
     * Rules for displaying create dialer file link :
     * - Following users can see the link (dialer admin users) : "wz_admn", "echevali", "marlandr", "matberub", "mjeanson", "algrigno", "cractlif", "jmarsot"
     * - Users starting with "klientel_" in their username can see the link
     * 		--> Only if they don't have "Contacted No Follow-up Needed" or "Duplicate/Fake" as follow-up status (no status is also ok)
     */
    boolean validStatus = this.clientContactBean == null
        || this.clientContactBean.getFollowupStatus() == null
        || !LIST_INVALID_STATUS_FOR_DIALER
        .contains(this.clientContactBean.getFollowupStatus().getCode());

    return StringUtils.isNotBlank(this.getQuote().getId())
        && (
        LIST_DIALER_ADMIN_USERS.contains(username)
            || (username.startsWith("klientel_") && validStatus));
  }

  @SuppressWarnings("unchecked")
  public InformationPieceTO getGeneralInfoPiece(String quoteId) throws Exception {
    // Obtain the quote details from the search results
    this.setQuote(this.searchController.getSearchState().getQuotesMap().get(quoteId));

    // Generate the general info section
    if (this.generalInfoPiece == null && this.getQuote() != null) {

      try {
        this.generalInfoPiece = this.getInfoService()
            .getInformation((String)
                    this.getInformationKeys()
                        .getValue("client-" + this.getQuote().getApplicationMode()),
                this.getInformationSearch());
      } catch (Exception ex) {
        this.generalInfoPiece = this.buildErrorPiece();
        ex.printStackTrace();
      }
    }

    return this.generalInfoPiece;
  }

  public InformationPieceTO getGeneralInfoPiece() throws Exception {
    return this.generalInfoPiece;
  }

  public void setGeneralInfoPiece(InformationPieceTO piece) throws Exception {
    this.generalInfoPiece = piece;
  }

  @SuppressWarnings("unchecked")
  public final InformationPieceTO getDriverInfoPiece() throws Exception {
    if (this.driverInfoPiece == null && this.getQuote() != null) {
      try {
        this.driverInfoPiece = this.getInfoService()
            .getInformation((String)
                    this.getInformationKeys()
                        .getValue("driver-" + this.getQuote().getLineOfInsurance().getCode() + "-"
                            + this.getQuote().getLineOfBusiness().getCode()),
                this.getInformationSearch());
      } catch (Exception ex) {
        log.error("An error occurred while getting driver info", ex);
        this.driverInfoPiece = this.buildErrorPiece();
      }
    }

    return this.driverInfoPiece;
  }

  public final InformationPieceTO getVehicleInfoPiece() throws Exception {
    if (this.vehicleInfoPiece == null && this.getQuote() != null) {
      try {
        this.vehicleInfoPiece = this.getInfoService()
            .getInformation(
                (this.getQuote().getLineOfBusiness() == LineOfBusinessCodeEnum.COMMERCIAL_LINES
                    ? "webzone.vehicle.info.cl"
                    : "webzone.vehicle.info"), this.getInformationSearch());
      } catch (Exception ex) {
        log.error("An error occurred while getting vehicle info", ex);
        this.vehicleInfoPiece = this.buildErrorPiece();
      }
    }

    return this.vehicleInfoPiece;
  }

  protected InformationPieceTO buildErrorPiece() {
    InformationPieceTO piece = new InformationPieceTO();
    piece.setValue("error");
    piece.getChildrens().addAll(new ArrayList<InformationPieceTO>());

    InformationPieceTO childPiece = new InformationPieceTO();
    childPiece.getPieces().addAll(new ArrayList<InformationPieceTO>());

    InformationPieceTO pieceClient = new InformationPieceTO();
    pieceClient.setValue(
        ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client",
            this, "message.error.plt"));
    childPiece.getPieces().add(pieceClient);
    piece.getChildrens().add(childPiece);
    return piece;
  }

  protected InformationSearchTO getInformationSearch() throws Exception {
    InformationSearchTO search = new InformationSearchTO();
    search.setAgreementNumber(this.getQuote().getId());
    search.setLocale(new InputLocaleAdapter(this.languageController.getLocale()));

    return search;
  }

  /**
   * retrieve proper consent from policy holder
   *
   * @param consentType
   * @param aPolicyVersion
   * @return
   */
  protected Consent retrieveProperConsentFromPolicyHolder(ConsentTypeCodeEnum consentType,
      PolicyVersion aPolicyVersion) {
    PolicyHolder policyHolder = this.policyHolderService.findPrincipalPolicyHolder(aPolicyVersion);
    if (policyHolder != null) {
      for (Consent consent : policyHolder.getParty().getConsents()) {
        if (consent.getConsentType().equals(consentType)) {
          return consent;
        }
      }
    }
    return null;
  }

  /**
   * Gets the antispam consent indicator
   *
   * @return the antispamn consent indicator
   */
  public Boolean getAntispamConsent() {
    return this.antispamConsent;
  }

  /**
   * Sets the future communication consent indicator
   *
   * @param futureCommunicationConsentInd the future communication consent indicator to set
   */
  public void setAntispamConsent(Boolean antispamConsent) {
    this.antispamConsent = antispamConsent;
  }

  /**
   * update future communication consent indicator based on policyVersion DB
   *
   * @param aPolicyVersion
   */
  public void updateAntispamConsent(PolicyVersion aPolicyVersion) {
    Consent currentConsent = retrieveProperConsentFromPolicyHolder(
        ConsentTypeCodeEnum.MARKETING_CONSENT,
        aPolicyVersion);
    if (currentConsent != null) {
      this.setAntispamConsent(currentConsent.getConsentIndicator());
    } else {
      this.setAntispamConsent(Boolean.TRUE);
    }
  }

  /**
   * Gets the current date for access denied page
   *
   * @return the current date
   */
  public Date getCurrentDate() {
    return Calendar.getInstance().getTime();
  }

  /**
   * Method use to display image flash when upload is selected
   *
   * @param out
   * @param data
   * @throws IOException
   */

  public void paintFlash(OutputStream out, Object data) throws IOException {
    ClassLoader loader = Thread.currentThread().getContextClassLoader();
    if (loader == null) {
      loader = getClass().getClassLoader();
    }

    if (loader != null) {
      InputStream stream = loader.getResourceAsStream("image/waiting.swf");
      if (stream != null) {
          try (stream) {
              copy(stream, out);
          }
      }
    }

  }

  /**
   * COPY IMAGES DATA
   *
   * @param in
   * @param out
   * @throws IOException
   */
  private void copy(InputStream in, OutputStream out) throws IOException {
    byte[] buffer = new byte[2048];
    int read;

    while ((read = in.read(buffer)) != -1) {
      out.write(buffer, 0, read);
    }
  }

  /**
   * remove email reference Number from session attribtute
   */
  private void removeEmailReferenceNo() {
    FacesContext context = FacesContext.getCurrentInstance();
    HttpSession session = (HttpSession) context.getExternalContext().getSession(true);
    if (session.getAttribute(SessionConstantsEnum.EMAIL_REFERENCE_NUMBER.getSessionConstant())
        != null) {
      session.removeAttribute(SessionConstantsEnum.EMAIL_REFERENCE_NUMBER.getSessionConstant());
    }
  }

  /**
   * Gets the create Profile Consent Ind BR5193 Display client response entered in the On-line Auto
   * Quote application.:
   *
   * @return the create Profile Consent Ind
   */
  public Boolean getCreateProfileConsentInd() {
    return this.createProfileConsentInd;
  }

  /**
   * Sets the create Profile Consent Ind
   *
   * @param the create Profile Consent Ind to set
   */
  public void setCreateProfileConsentInd(Boolean aCreateProfileConsentInd) {
    this.createProfileConsentInd = aCreateProfileConsentInd;
  }

  /**
   * Gets the credit consent Ind BR5193 Display client response entered in the On-line Auto Quote
   * application.:
   *
   * @return the credit consent Ind
   */
  public Boolean getCreditConsentInd() {
    return this.creditConsentInd;
  }

  /**
   * Sets the credit consent Ind
   *
   * @param creditConsentInd the credit consent Ind to set
   */
  public void setCreditConsentInd(Boolean aCreditConsentInd) {
    this.creditConsentInd = aCreditConsentInd;
  }

  /**
   * update credit consent indicator based on policyVersion DB
   *
   * @param aPolicyVersion
   */
  public void updateCreditConsentInd(PolicyVersion aPolicyVersion) {
    Consent currentConsent = retrieveProperConsentFromPolicyHolder(ConsentTypeCodeEnum.CREDIT_SCORE,
        aPolicyVersion);
    if (currentConsent != null) {
      this.setCreditConsentInd(currentConsent.getConsentIndicator());
    } else {
      this.setCreditConsentInd(Boolean.TRUE);
    }
  }

  /**
   * check if quote has been upload to savers
   *
   * @return
   */
  public boolean isRecordUploadedToSavers() {
    return this.provinceController.isCompany6() && this.quoteUploaded;
  }

  /**
   * Gets the authentificationController
   *
   * @return the authentificationController
   */
  public AuthentificationController getAuthentificationController() {
    return this.authentificationController;
  }

  /**
   * View quote activity.
   *
   * @param insurancePolicy the insurance policy
   * @throws com.intact.business.service.exception.BrokerServiceException
   */
  protected void view(QuotesBean quote) throws BrokerServiceException {

    boolean newPage = true;
    String aReferenceNo = (String) FacesContext.getCurrentInstance().getExternalContext()
        .getRequestParameterMap()
        .get("referenceNo");

    if (FacesContext.getCurrentInstance().getViewRoot().getAttributes().get("referenceNo")
        == null) {
      FacesContext.getCurrentInstance().getViewRoot().getAttributes()
          .put("referenceNo", aReferenceNo);
    } else {
      newPage = false;
    }

    if (newPage) {
      Quote newQuote = this.buildQuote(quote);
      this.getQuoteBusinessProcess()
          .viewQuote(this.permissionController.getUserContext(), newQuote);
    }
  }

  /**
   * Gets the command business process
   *
   * @return
   */
  public ICommandBusinessProcess getCommandBusinessProcess() {
    return this.commandBusinessProcess;
  }

  /**
   * The quote business process.
   */

  public IBrokerService getQuoteBusinessProcess() {
    return this.quoteBusinessProcess;
  }

  /**
   * Checks if new page
   *
   * @return the new page boolean status
   */
  public boolean isNewPage() {
    return this.newPage;
  }

  /**
   * Sets the new page boolean condition
   *
   * @param aNewPage the new page to set
   */
  public void setNewPage(boolean aNewPage) {
    this.newPage = aNewPage;
  }

  /**
   * Gets the Manufacturer Company Code
   *
   * @return the Manufacturer Company Code
   */
  protected ManufacturerCompanyCodeEnum retrieveManufacturerCompany(String quoteId) {

    ManufacturerCompanyCodeEnum manufacturer = null;

    PolicyVersion policy = this.getCommandBusinessProcess().findLatestPolicyVersion(quoteId);

    if (policy != null) {
      manufacturer = policy.getInsurancePolicy().getManufacturerCompany();
    } else {
      manufacturer = ManufacturerCompanyCodeEnum.valueOfCode(this.systemDAO.getCompany());
    }

    return manufacturer;
  }

  public Double loadTotalMonthlyPremium() {
    Double totalMonthly = null;
    for (V vehicleBean : this.getVehicleOfferBeanList()) {
      if (vehicleBean.getSelectedOfferBean() != null
          && vehicleBean.getSelectedOfferBean().getMonthlyPremiumWithTaxes() != null) {
        if (totalMonthly == null) {
          totalMonthly = Double.valueOf(0);
        }
        totalMonthly += vehicleBean.getSelectedOfferBean().getMonthlyPremiumWithTaxes();
      }
    }
    return totalMonthly;
  }

  public Boolean getHasEconomyOffer() {

    return this.hasEconomyOffer;
  }

  public Boolean getHasValuePackOffer() {

    return this.hasValuePackOffer;
  }

  public Boolean getHasPlusOffer() {

    return this.hasPlusOffer;
  }

  public Boolean getHasCustomOffer() {

    return this.hasCustomOffer;
  }

  public Boolean getHasPreferredOffer() {

    return this.hasPreferredOffer;
  }

  public Boolean getHasPeopleLikeYouOffer() {

    return this.hasPeopleLikeYouOffer;
  }

  public Boolean getHasRecommendedOffer() {

    return this.hasRecommendedOffer;
  }

  protected void updateHasOffers(PolicyVersion policyVersion) {
    for (VehicleOfferBean vehicleOffer : this.getVehicleOfferBeanList()) {

      if (policyVersion.getInsurancePolicy().getApplicationMode()
          == ApplicationModeEnum.QUICK_QUOTE) {
        // Value Pack, People Like You and Recommended offers are only for Quick Quotes
        if (vehicleOffer.getEconomyOffer().getHasCorrespondingInsuranceRiskOffer()) {
          this.hasValuePackOffer = true;
        }
        if (vehicleOffer.getPeopleLikeYouOffer().getHasCorrespondingInsuranceRiskOffer()) {
          this.hasPeopleLikeYouOffer = true;
        }
        if (vehicleOffer.getRecommendedOffer().getHasCorrespondingInsuranceRiskOffer()) {
          this.hasRecommendedOffer = true;
        }
      } else {
        // Economy, Plus,Custom and Preferred offers are only for Regular Quotes
        if (vehicleOffer.getEconomyOffer().getHasCorrespondingInsuranceRiskOffer()) {
          this.hasEconomyOffer = true;
        }
        if (vehicleOffer.getPlusOffer().getHasCorrespondingInsuranceRiskOffer()) {
          this.hasPlusOffer = true;
        }
        if (vehicleOffer.getCustomOffer().getHasCorrespondingInsuranceRiskOffer()) {
          this.hasCustomOffer = true;
        }
        if (vehicleOffer.getPreferredOffer().getHasCorrespondingInsuranceRiskOffer()) {
          this.hasPreferredOffer = true;
        }
      }

    }
  }

  public void resetBeanList() {
    this.vehicleOfferBeanList = new ArrayList<V>();
    this.paymentBean = new PaymentBean();
  }

  @SuppressWarnings("unchecked")
  protected boolean getAutoQuoteUploadStatus(PolicyVersion policy) {

    Boolean uploaded = false;

    QuoteStatusCodeEnum status = this.getQuoteStatus(policy);
    uploaded = (Boolean) this.getUploadStatuses()
        .getValue((policy.getInsurancePolicy().getApplicationMode() != null
            ? policy.getInsurancePolicy().getApplicationMode().getCode()
            : ApplicationModeEnum.REGULAR_QUOTE.getCode()) + "-" + status.getCode());

    if (uploaded == null) {
      uploaded = (Boolean) this.getUploadStatuses().getValue("default");
    }

    return uploaded;

  }

  protected boolean getHomeQuoteUploadStatus() {

    boolean uploaded = false;
    String quoteStatus = "";

    if (this.getQuote() != null && StringUtils.isNotBlank(this.getQuote().getId())) {
      quoteStatus =
          this.getQuote().getStatus() != null ? this.getQuote().getStatus().getCode() : "";

      if (CifCompanyEnum.INTACT_HALIFAX.getSubBrokerCompanyNumber().equals(this.systemDAO.getCompany())
          || CifCompanyEnum.INTACT_AB.getSubBrokerCompanyNumber().equals(this.systemDAO.getCompany())) {
        uploaded =
                this.getQuote().getLastActivityNote() != null && UserActivityTypeCodeEnum.REASSIGNED_QUOTE == this.getQuote().getLastActivityNote().getActivity();
      } else {
        uploaded = StringUtils
            .equalsIgnoreCase(quoteStatus, QuoteStatusCodeEnum.UPLOADED_ACCEPTED.getCode()) ||
            StringUtils
                .equalsIgnoreCase(quoteStatus, QuoteStatusCodeEnum.UPLOADED_REFUSED.getCode());
      }
    }

    return uploaded;

  }

  /**
   * Upload the quote.
   *
   * @throws IllegalStateException
   * @throws BrokerServiceException
   */
  public void upload(String quoteId) {
    // Common steps for both Home and Auto
    this.setUploadSucceed(null);

    this.setQuote(this.searchController.getSearchState().getQuotesMap().get(quoteId));

    if (this.getQuote().getLineOfInsurance() == LineOfInsuranceCodeEnum.AUTOMOBILE) {
      this.uploadAutoQuote();
    } else if (this.getQuote().getLineOfInsurance() == LineOfInsuranceCodeEnum.RESIDENTIAL) {
      if (LineOfBusinessCodeEnum.PERSONAL_LINES == this.getQuote().getLineOfBusiness()) {
        this.uploadHomeQuote();
      } else {
        this.uploadCommercialResidentialQuote();
      }
    }
  }

  /**
   * At the moment, "uploading" a CL residential quote results in simply download a PDF that the broker will use to
   * upload the quote manually in another system.
   */
  private void uploadCommercialResidentialQuote() {

    try {
      Quote quote = new Quote(this.getQuote().getId(), this.getQuote().getLineOfBusiness(),
                              this.getQuote().getLineOfInsurance(), this.getQuote().getApplicationMode());
      quote.setUploadNote(FollowUpStatusEnum.PDF_DOWNLOADED.getCode());
      String fileNamePDF = ResourceBundleHelper.getMessage("com.intact.brokeroffice.controller.viewquote.client",
                                                           this,
                                                           "client.filenamePDF",
                                                           this.getQuote().getAgreementNumber());

      quote.setLanguage(this.languageController.getLocale().getLanguage());

      PDFDocumentInfo pdfData = this.getQuoteBusinessProcess().downloadDocument(
              this.permissionController.getUserContext(), quote);

      if (pdfData == null || pdfData.getDocument() == null) {
        String pdfExceptionMessage = pdfData == null ? "Null object returned" : "PDF object : " + pdfData;
        throw new BrokerServiceException("Problem returning the PDF. " + pdfExceptionMessage);
      }

      FacesContext facesContext = FacesContext.getCurrentInstance();
      ExternalContext externalContext = facesContext.getExternalContext();
      HttpServletResponse response = (HttpServletResponse) externalContext.getResponse();

      // Initialize response.
      response.reset();
      response.setContentType("application/pdf");
      response.setHeader("Content-disposition", "attachment; filename=" + fileNamePDF);
      response.setContentLength(pdfData.getDocument().length);
      response.setHeader("Cache-Control", "no-cache");

      // Write file to response.
      ServletOutputStream output = response.getOutputStream();
      output.write(pdfData.getDocument());
      output.close();
      // Inform JSF to not take the response in hands.
      facesContext.responseComplete();


      this.generalInfoPiece = null;

      bloomMqHandlerService.sendMessage(BloomQNameEnum.Q_BLOOM_CL.name(), this.getQuote().getId());

    } catch (Exception e) {
      if (e instanceof BrokerServiceException) {
        this.setUploadSucceed(false);
      }
      log.error("An error occurred while downloading the PDF for commercial CL quote %s".formatted(
					this.getQuote().getAgreementNumber()), e);
    }
  }

  /**
   * Upload an Auto Quote.
   *
   * @throws IllegalStateException
   * @throws BrokerServiceException
   */
  protected void uploadAutoQuote() {
    PolicyVersion policyVersion = this.getCommandBusinessProcess()
        .findLatestPolicyVersion(this.getQuote().getAgreementNumber());

    checkAutoQuoteUploaded(policyVersion);
    this.setRemoteSystemUrl(null);
    updateCreateProfileConsentInd(policyVersion);
    // CHECKS CLIENT PROFILE CREATION CONSENT FOR PERSONAL INFORMATION

    // rechecks quote access for the current user as a participating
    // broker account
    this.setQuoteAccess(null);

    try {
      if (this.getQuoteAccess()) {

        // String language = (String)
        // session.getAttribute(SessionConstantsEnum.BROKER_LANGUAGE.getSessionConstant());

        Quote newQuote = new Quote(this.getQuote().getAgreementNumber(),
            this.getQuote().getLineOfBusiness(),
            this.getQuote().getLineOfInsurance(),
            this.getApplicationMode());
        newQuote.setUploadNote("");
        // BR5371 Once the quote has been uploaded successfully
        PolicyInfo policy = this.getQuoteBusinessProcess().uploadQuote(
            this.permissionController.getUserContext(),
            newQuote, "");
        this.setUploadSucceed(true);
        this.setRemoteSystemUrl(policy.getUri());
        this.setReferenceLegacyNo(policy.getNumber());

        // MSG506 Upload to goBRIO Confirmation Message
        this.viewQuote(false);
        this.generalInfoPiece = null;

        this.setUploadSucceed(true);
        this.setRemoteSystemUrl(policy.getUri());
        this.setReferenceLegacyNo(policy.getNumber());

        // send uuid to bloom mq service to synchronise with bloom project
        bloomMqHandlerService.sendMessage(BloomQNameEnum.Q_BLOOM_AUTO.name(),
            policyVersion.getInsurancePolicy().getUuId());
      }
    } catch (Exception bse) {
      bse.printStackTrace();
      this.setUploadSucceed(false);
    }
  }

  /**
   * Upload an Home Quote.
   *
   * @throws IllegalStateException
   * @throws BrokerServiceException
   */
  protected void uploadHomeQuote() {
    String policyNumber = null;

    checkHomeQuoteUploaded();
    this.setRemoteSystemUrl(null);
    // CHECKS CLIENT PROFILE CREATION CONSENT FOR PERSONAL INFORMATION

    // rechecks quote access for the current user as a participating
    // broker account
    this.setQuoteAccess(null);

    try {
      if (this.getQuoteAccess()) {

        Quote newQuote = new Quote(this.getQuote().getId(), this.getQuote().getLineOfBusiness(),
            this.getQuote().getLineOfInsurance(), this.getQuote().getApplicationMode());
        newQuote.setUploadNote("");

        // BR5371 Once the quote has been uploaded successfully
        PolicyInfo uploadInfo = this.getQuoteBusinessProcess()
            .uploadQuote(this.permissionController.getUserContext(), newQuote,
                this.getQuote().getBrokerNumber());

        this.setRemoteSystemUrl(uploadInfo.getUri());
        this.setReferenceLegacyNo(uploadInfo.getNumber());
        policyNumber = uploadInfo.getNumber();

        QuotesBean quote = this.searchController.getSearchState().getQuotesMap()
            .get(this.getQuote().getAgreementNumber());
        if (quote != null) {
          quote.setAgreementLegacyNumber(policyNumber);
        }

        this.setUploadSucceed(true);
        this.viewQuote(false);
        this.generalInfoPiece = null;

        // MSG506 Upload to goBRIO Confirmation Message
        this.setUploadSucceed(true);

        // send uuid to bloom mq service to synchronise with bloom project
        bloomMqHandlerService
            .sendMessage(BloomQNameEnum.Q_BLOOM_HOME.name(), this.getQuote().getId());
      }
    } catch (Exception e) {
      this.setUploadSucceed(false);
    }
  }

  public final boolean isShowUbiConsent() {
    return showUbiConsent;
  }

  public final void setShowUbiConsent(boolean showUbiConsent) {
    this.showUbiConsent = showUbiConsent;
  }

  public final Boolean getUbiConsentInd() {
    return ubiConsentInd;
  }

  public final void setUbiConsentInd(Boolean ubiConsentInd) {
    this.ubiConsentInd = ubiConsentInd;
  }

  /**
   * update ubi consent indicator based on policyVersion DB
   *
   * @param aPolicyVersion
   */
  public final void updateUbiConsent(PolicyVersion aPolicyVersion) {
    Consent currentConsent = retrieveProperConsentFromPolicyHolder(
        ConsentTypeCodeEnum.UBI_PROGRAM_CONSENT,
        aPolicyVersion);
    if (currentConsent != null) {
      this.setUbiConsentInd(currentConsent.getConsentIndicator());
    } else {
      this.setUbiConsentInd(Boolean.FALSE);
    }
  }

  public final void updateShowUbiConsent(PolicyVersion policyVersion) {
    this.setShowUbiConsent(this.partyhelper.isUbiIncluded(policyVersion.getInsuranceRisks()));

  }

  public String getTabTitle(String lineOfBusiness) {
    if (lineOfBusiness != null && lineOfBusiness
        .equals(LineOfBusinessCodeEnum.COMMERCIAL_LINES.getCode())) {
      this.tabTitle = " " + this.getClientContactBean().getBusinessName();
    } else {
      this.tabTitle = " " + this.getClientContactBean().getPolicyHolder();
    }

    return this.tabTitle;
  }

  public abstract Boolean getSameCompany(String quoteId);

  public boolean isAllowed(String quoteId) throws Exception {
    return this.getSameCompany(quoteId) && this.getQuoteAccess();
  }

  public void setPartyHelper(IPartyHelper partyHelper) {
    this.partyhelper = partyHelper;
  }

  /**
   * Function used to log an error message from the dialer file creation process as well as set the
   * desired error message to be displayed in the UI.
   *
   * @param errorMessage : The error message to set to be displayed in the UI
   * @param logMessage   : The message to log
   */
  protected void manageCreateDialerFileFailure(String errorMessage, String logMessage) {
    this.setDialerSucceed(false);
    this.setDialerOutput(errorMessage);
    log.error(logMessage);

    if (clientContactBean != null) {
      clientContactBean.setNoteText("Failed Dialer creation file.");

      // Update to set the "failed" note
      try {
        if (this.getQuote() != null
            && this.getQuote().getLineOfBusiness() != null
            && this.getQuote().getLineOfInsurance() != null
            && this.getQuote().getApplicationMode() != null) {
          update(this.getQuote().getId(), this.getQuote().getLineOfBusiness().getCode(),
              this.getQuote().getLineOfInsurance().getCode(), this.getQuote().getApplicationMode());
        }
      } catch (Exception e) {
        log.error("Could not update quote with error message.", e);
      }
    }
  }

  public void createDialerFile() throws LoggingServiceException {
    String newConfirmationNumber = null;

    this.setDialerSucceed(null);

    // Validate if critical data objects are present (avoid NullPointerExceptions)
    if (this.getClientContactBean() == null || this.getQuote() == null
        || this.getQuote().getAgreementNumber() == null
        || this.languageController.getLocale() == null
        || this.permissionController.getUserContext() == null) {
      this.manageCreateDialerFileFailure(ResourceBundleHelper
              .getMessage(Constantes.CLIENT_PROPERTY_FILE_PATH, this,
                  Constantes.DIALER_FAILED_GENERAL_MESSAGE),
          String.format("Issue with dialer file creation - missing data. "
                  + "ClientContactBean : %s;%n"
                  + "Quote : %s;%n"
                  + "AgreementNumber : %s;%n"
                  + "Locale : %s;%n"
                  + "UserContext : %s;%n",
              this.getClientContactBean(), this.getQuote(),
              this.getQuote() == null ? "null quote" : this.getQuote().getAgreementNumber(),
              this.languageController.getLocale(), this.permissionController.getUserContext()));
      return;
    }

    // Obtain necessary informations to build DTOs to send to the broker dialer service
    String language = this.languageController.getLocale().getLanguage();

    String brokerNumber = getBrokerNumber();

    String provinceFromCompany = ProvinceCompanyConverter
        .convertSubBrokerCompanyToProvince(this.permissionController.getUserContext().getCompany());
    String policyHolderName = this.getClientContactBean().getPolicyHolder();

    // If the auto policy holder name is null or contains null, get the name from the quote
    if (policyHolderName == null || policyHolderName.contains("null")) {
      policyHolderName = "";
      if (quote.getFirstName() != null && !quote.getFirstName().trim().isEmpty()) {
        policyHolderName += quote.getFirstName();
      }
      if (quote.getLastName() != null && !quote.getLastName().trim().isEmpty()) {
        policyHolderName += " " + quote.getLastName();
      }
    }

    // Build broker dialer service DTOs
    QuoteDialerDTO quoteDialerInformation = this.quoteDialerDTOHelper
        .buildQuoteDialerDTO(quote, this.permissionController.getUserContext(), brokerNumber,
            this.getApplicationMode(), generalInfoPiece);
    PartyDTO dialerParty = this.quoteDialerDTOHelper
        .buildDialerPartyDTO(driverInfoPiece, generalInfoPiece, quote, policyHolderName,
            this.getApplicationMode(), provinceFromCompany);

    if (quoteDialerInformation == null || dialerParty == null) {

      this.manageCreateDialerFileFailure(ResourceBundleHelper
              .getMessage(Constantes.CLIENT_PROPERTY_FILE_PATH, this,
                  Constantes.DIALER_FAILED_GENERAL_MESSAGE),
          quoteDialerInformation == null
              ? "Issue with dialer file creation - Resulting QuoteDialerDTO object is null." :
              "Issue with dialer file creation - Resulting dialer PartyDTO object is null.");
      return;

    } else if (StringUtils.equalsIgnoreCase("PC", this.getApplicationMode()) && StringUtils
        .isEmpty(dialerParty.getProvince())) {

      this.manageCreateDialerFileFailure(ResourceBundleHelper
              .getMessage(Constantes.CLIENT_PROPERTY_FILE_PATH, this,
                  Constantes.DIALER_FAILED_PROVINCE_ADDRESS_MESSAGE),
          "Issue with dialer file creation - Resulting P&C party doesn't have a province.");
      return;

    } else if (quoteDialerInformation.getPhone() == null || StringUtils
        .isEmpty(quoteDialerInformation.getPhone().getPhoneNumber())) {

      this.manageCreateDialerFileFailure(ResourceBundleHelper
              .getMessage(Constantes.CLIENT_PROPERTY_FILE_PATH, this,
                  Constantes.DIALER_FAILED_GENERAL_MESSAGE),
          "Issue with dialer file creation - Resulting dialer quote doesn't have a phone number.");
      return;

    } else {

      quoteDialerInformation.setParty(dialerParty);

      // Only obtain the vehicles list if its an auto quote
      if (LineOfInsuranceCodeEnum.AUTOMOBILE.equals(quote.getLineOfInsurance())) {
        quoteDialerInformation.setListVehiclesDTO(
            this.quoteDialerDTOHelper.buildDialerVehicleDTOList(vehicleInfoPiece, language));
      }
    }

    // Call the broker dialer service to create the dialer file
    try {
      if (log.isDebugEnabled()) {
        log.debug("Trying to upload to dialer: " + quoteDialerInformation);
      }
      newConfirmationNumber = this.dialerService.sendToDialer(quoteDialerInformation);

      log.info("Uploaded to dialer [ConfirmationNumber=" + newConfirmationNumber + "]");

      // Build linked quotes text for note if there are linked quotes present
      String linkedQuotes = "";
      if (quoteDialerInformation != null && quoteDialerInformation.getListRelatedQuotes() != null
          && !quoteDialerInformation.getListRelatedQuotes().isEmpty()) {
        for (String relatedQuote : quoteDialerInformation.getListRelatedQuotes()) {
          if (linkedQuotes.isEmpty()) {
            linkedQuotes =
                " linked quote" + (this.getQuote().getChildrens().size() > 1 ? "s" : "") + ":"
                    + relatedQuote;
          } else {
            linkedQuotes += ", " + relatedQuote;
          }
        }
      }

      // Set status and note of the linked quotes and the confirmation number
      clientContactBean
          .setFollowupStatus(AgreementFollowUpStatusEnum.CONTACTED_NO_FOLLOWUP_REQUIRED);
      clientContactBean.setNoteText("Dialer: " + newConfirmationNumber + linkedQuotes);

      this.setDialerSucceed(true);
      this.setDialerOutput(ResourceBundleHelper
          .getMessage(Constantes.CLIENT_PROPERTY_FILE_PATH, this,
              Constantes.DIALER_SUCCESS_GENERAL_MESSAGE) + " " + newConfirmationNumber + ".");

      update(this.getQuote().getId(), this.getQuote().getLineOfBusiness().getCode(),
          this.getQuote().getLineOfInsurance().getCode(), this.getQuote().getApplicationMode());

    } catch (DialerServiceException dialerException) {

      this.getLogService().logError("error", "Error during the dialer file creation process. %nQuoteDialerDTO : %s"
					.formatted(
							quoteDialerInformation.toString()));

      // There is a specific error message for an unmatch broker error
      if (!dialerException.getCodes().isEmpty() && dialerException.getCodes()
          .contains(DialerServiceException.UNMATCH_BROKER_CODE)) {

        this.manageCreateDialerFileFailure(ResourceBundleHelper
                .getMessage(Constantes.CLIENT_PROPERTY_FILE_PATH, this,
                    Constantes.DIALER_FAILED_MATCH_BROKER_MESSAGE)
                + " [" + brokerNumber + "]",
            "Issue with dialer file creation - Unmatch broker for broker: " + brokerNumber);

      } else {

        this.manageCreateDialerFileFailure(ResourceBundleHelper
                .getMessage(Constantes.CLIENT_PROPERTY_FILE_PATH, this,
                    Constantes.DIALER_FAILED_GENERAL_MESSAGE),
            "Issue with dialer file creation - Http Status Code Exception");

      }
      dialerException.printStackTrace();

    } catch (Exception e) {
      log.error("Error during the dialer file creation process. %nQuoteDialerDTO : %s"
					.formatted(quoteDialerInformation), e);
      this.manageCreateDialerFileFailure(ResourceBundleHelper
              .getMessage(Constantes.CLIENT_PROPERTY_FILE_PATH, this,
                  Constantes.DIALER_FAILED_GENERAL_MESSAGE),
          "Issue with dialer file creation - General Exception");
    }
  }

  private void sendMessageToBloomMqHandlerService() {
    bloomMqHandlerService.sendQuoteToBloom(this.getQuote());
  }

  /**
   * gets the dialer succeed indicator return true if dialer succeed else false
   */
  public Boolean getDialerSucceed() throws Exception {
    return this.dialerSucceed;
  }

  /**
   * @param aDialerSucceed
   */
  public void setDialerSucceed(Boolean aDialerSucceed) {
    this.dialerSucceed = aDialerSucceed;
  }

  public String getDialerOutput() {
    return dialerOutput;
  }

  public void setDialerOutput(String dialerOutput) {
    this.dialerOutput = dialerOutput;
  }

  public LoggingApplicationService getLogService() {
    return logService;
  }

  public void setLogService(LoggingApplicationService logService) {
    this.logService = logService;
  }

  /**
   * Assign to me. Replaces upload for TARGET Quotes.
   */
  public void assign(String quoteId) {

    this.setQuote(this.searchController.getSearchState().getQuotesMap().get(quoteId));
    this.setRemoteSystemUrl(null);
    this.setQuoteAccess(null);

    try {
      if (this.getQuoteAccess()) {
        Quote newQuote = new Quote(this.getQuote().getId(), this.getQuote().getLineOfBusiness(),
            this.getQuote().getLineOfInsurance(), this.getApplicationMode());
        newQuote.setUploadNote("");

        this.quoteBusinessProcess
            .reassignQuote(this.permissionController.getUserContext(), newQuote);

        this.setAssignSucceed(true);
        this.viewQuote(false);
        this.generalInfoPiece = null;

        this.sendMessageToBloomMqHandlerService();

      }
    } catch (Exception e) {
      this.setAssignSucceed(false);
    }

  }

  public Boolean getAssignSucceed() throws Exception {
    return this.assignSucceed;
  }

  public void setAssignSucceed(Boolean aAssignSucceed) {
    this.assignSucceed = aAssignSucceed;
  }

  /**
   * Quote assignation - act as upload for contact since upload is now automatic
   */
  public boolean isQuoteAssigned() {
    return this.quoteAssigned;
  }

  public void setQuoteAssigned(boolean aQuoteAssigned) {
    this.quoteAssigned = aQuoteAssigned;
  }

  public boolean isCheckAssign() {

    boolean isPC = (this.getQuote() != null && "PC"
        .equalsIgnoreCase(this.getQuote().getApplicationMode()));

    return !isPC && !(
        LineOfBusinessCodeEnum.COMMERCIAL_LINES.equals(this.getQuote().getLineOfBusiness())
            && (CifCompanyEnum.INTACT_HALIFAX.getSubBrokerCompanyNumber()
            .equals(this.systemDAO.getCompany())
            || CifCompanyEnum.INTACT_AB.getSubBrokerCompanyNumber()
            .equals(this.systemDAO.getCompany())));
  }

  private String getBrokerNumber() {

    String [] brokerFromContactBean = {"QF", "QA", "IR"};
    if (ArrayUtils.contains(brokerFromContactBean, this.getQuote().getApplicationMode())) {
      return StringUtils.defaultIfEmpty(this.getClientContactBean().getBrokerNbr(), "");
    }
    else {
      return StringUtils.defaultIfEmpty(this.getQuote().getBrokerNumber(), "");
    }
  }

}
