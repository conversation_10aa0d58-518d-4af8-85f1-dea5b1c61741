package com.intact.brokeroffice.controller.viewquote;

import com.intact.brokeroffice.controller.province.ProvinceController;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

@Component
@Scope("request")
public class FactoryViewQuoteController {
	@Autowired
	@Qualifier("viewQuoteControllerQC")
	private ViewQuoteControllerQC viewQuoteControllerA;
	
	@Autowired
	@Qualifier("viewQuoteControllerON")
	private ViewQuoteControllerON viewQuoteController6;
	
	@Autowired
	@Qualifier("viewQuoteControllerAB")
	private ViewQuoteControllerAB viewQuoteController3;
	
	@Autowired
	private ProvinceController provinceController;
	
	private IViewQuoteController viewQuoteController;
	
	//Creation de factory
	public IViewQuoteController getViewQuoteController() throws Exception{
		
		AbstractViewQuoteController controller = null;
		
		 if(this.viewQuoteController ==null)
		 {
			 	if(this.provinceController.isCompanyA()) {
			 		controller = this.viewQuoteControllerA;
			 	} else if(this.provinceController.isCompany6()) {			 		
			 		controller = this.viewQuoteController6;
			 	}else if(this.provinceController.isCompany3()) { 		
			 		controller = this.viewQuoteController3;
			 	}
	
			 	controller.initialize();
			 	this.viewQuoteController = (IViewQuoteController) controller;	
		 }
		 return this.viewQuoteController;		
	}
}
