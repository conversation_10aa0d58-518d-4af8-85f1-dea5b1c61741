

/*
 *
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */

package com.intact.brokeroffice.controller.viewquote.payment;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import com.ing.canada.common.domain.QuoteCalculationDetails;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.business.rules.quote.BR5883_HasThreePercentageSurcharge;
import com.intact.business.rules.quote.BR5883_HasTwoPercentageSurcharge;

/**
 * Adapter for a PaymentBeanON
 *
 * <AUTHOR>
 */
@Component
public class PaymentAdapterON extends PaymentAdapter<PaymentBean> {

	private static final double SURCHARGE_3PERCENTAGE_ON = 0.03;
	private static final double SURCHARGE_2PERCENTAGE_ON = 0.02;
	private static final double SURCHARGE_ONE_POINT_THREE_PERCENTAGE_ON = 0.013;

	@Autowired
	private BR5883_HasThreePercentageSurcharge br5883_3perc;

	@Autowired
	private BR5883_HasTwoPercentageSurcharge  br5883_2perc;

	@Autowired
//	@Qualifier("ontario-interest-change-date")
	private LocalDate ontarioInterestChangeDate;

	/**
	 * @see com.intact.brokeroffice.controller.viewquote.payment.IPaymentAdapter#loadPaymentBean(
	 * 		com.ing.canada.common.domain.QuoteCalculationDetails,
	 *      com.intact.brokeroffice.controller.payment.PaymentBean,
	 *      com.ing.canada.plp.domain.policyversion.PolicyVersion,
	 *      com.ing.canada.plp.domain.enums.ProvinceCodeEnum
	 *      )
	 */
	public void loadPaymentBean(PaymentBean aPaymentBean, PolicyVersion aPolicyVersion)
	{
		super.loadPaymentBean(aPaymentBean, aPolicyVersion, ProvinceCodeEnum.ONTARIO);
		this.loadMethodPaymentBean(aPaymentBean,  aPolicyVersion);

		// EVO 51576 Ontario reform new interest charge needs to be applied
		// Based on the transaction rating date
		// set in the bean for display purposes
		LocalDate refDate;
		try {
			refDate = aPolicyVersion.getPolicyInceptionDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		} catch (Exception e) {
			refDate = LocalDate.parse(aPolicyVersion.getPolicyInceptionDate().toString());
		}

		if (!refDate.isBefore(ontarioInterestChangeDate)) {
			aPaymentBean.setNewInterest(true);
		}
	}

	/* (non-Javadoc)
	 * @see com.intact.brokeroffice.controller.viewquote.payment.PaymentAdapter#loadPremiumInformation(com.intact.brokeroffice.controller.viewquote.payment.PaymentBean, com.ing.canada.common.domain.QuoteCalculationDetails)
	 */
	@Override
	protected void loadPremiumInformation(PaymentBean aPaymentBean, QuoteCalculationDetails quoteCalculationDetails){
		aPaymentBean.setAnnualAmountWithTaxes(quoteCalculationDetails.getFullTermPremiumWithTaxesAndSurcharge());
		aPaymentBean.setMonthlyPremium(quoteCalculationDetails.getMonthlyPaymentWithTaxesAndSurcharge());
	}

	/* (non-Javadoc)
	 * @see com.intact.brokeroffice.controller.viewquote.payment.PaymentAdapter#applySpecificCalculationRules(com.ing.canada.common.domain.QuoteCalculationDetails, com.ing.canada.plp.domain.policyversion.PolicyVersion)
	 */
	@Override
	protected void applySpecificCalculationRules(QuoteCalculationDetails quotationDetails,PolicyVersion policyVersion) {

		// EVO 51576 Ontario reform new interest charge needs to be applied
		// Based on the transaction rating date
		LocalDate refDate;
		try {
			refDate = policyVersion.getPolicyInceptionDate().toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
		} catch (Exception e) {
			refDate = LocalDate.parse(policyVersion.getPolicyInceptionDate().toString());
		}
		boolean newInterest = !refDate.isBefore(ontarioInterestChangeDate);

		if (br5883_3perc.validate(policyVersion) && validateBillingPlan(policyVersion, ProvinceCodeEnum.ONTARIO) && !newInterest) {
			this.quotationService.calculateFirstMonthlyPayment(quotationDetails, 1);
			this.quotationService.addSurchargeToQuoteDetails(quotationDetails, SURCHARGE_3PERCENTAGE_ON, true);
		} else if (br5883_2perc.validate(policyVersion)&& validateBillingPlan(policyVersion, ProvinceCodeEnum.ONTARIO)  && !newInterest) {
			this.quotationService.calculateFirstMonthlyPayment(quotationDetails, 1);
			this.quotationService.addSurchargeToQuoteDetails(quotationDetails, SURCHARGE_2PERCENTAGE_ON, true);
		} else if ((br5883_3perc.validate(policyVersion)||br5883_2perc.validate(policyVersion)) && validateBillingPlan(policyVersion, ProvinceCodeEnum.ONTARIO)  && newInterest) {
			this.quotationService.calculateFirstMonthlyPayment(quotationDetails, 1);
			this.quotationService.addSurchargeToQuoteDetails(quotationDetails, SURCHARGE_ONE_POINT_THREE_PERCENTAGE_ON, true);
		} else {
			this.quotationService.addSurchargeToQuoteDetails(quotationDetails, 0.0, false);
		}

		quotationDetails.setMonthlyPaymentWithTaxes(quotationDetails.getMonthlyPaymentWithTaxesAndSurcharge());
		quotationDetails.setAnnualAmountWithTaxes(quotationDetails.getFullTermPremiumWithTaxesAndSurcharge());
	}

}
