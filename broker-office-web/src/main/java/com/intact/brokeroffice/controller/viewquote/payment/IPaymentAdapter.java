package com.intact.brokeroffice.controller.viewquote.payment;

import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;


/**
 * The Interface IPaymentAdapter.
 */
public interface IPaymentAdapter <E extends PaymentBean>{

	/**
	 * Load the premium information
	 * 
	 * @param aQuoteCalculationDetails
	 * @param aPaymentBean
	 */
	void loadPaymentBean(E aPaymentBean, PolicyVersion aPolicyVersion, ProvinceCodeEnum aProvinceCode);
			
}
