package com.intact.brokeroffice.controller.viewquote;

import java.util.List;

import org.apache.commons.lang.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.ing.canada.plp.helper.IPartyHelper;
import com.intact.brokeroffice.controller.viewquote.offer.CoverageOfferBean;
import com.intact.brokeroffice.controller.viewquote.offer.OfferAdapterQC;
import com.intact.brokeroffice.controller.viewquote.offer.VehicleOfferBeanQC;
import com.intact.brokeroffice.controller.viewquote.payment.PaymentAdapterQC;
import com.intact.brokeroffice.controller.viewquote.payment.PaymentBean;

/**
 * The Class ViewQuoteController. This is a Request scope controller to view
 * multiple quotes in separate web page.
 */
@Component
@Scope("request")
public class ViewQuoteControllerQC extends AbstractViewQuoteController<VehicleOfferBeanQC>
		implements IViewQuoteController {

	/** The offer adapter QC. */
	@Autowired
	private OfferAdapterQC offerAdapterQC;

	/** The payment adapter QC. */
	@Autowired
	private PaymentAdapterQC paymentAdapterQC;

	@Autowired
	protected IPartyHelper partyhelper;

	/** public api for loading a quote **/
	public void viewQuote(String referenceNumber) throws Exception {
		viewQuote(true);
	}

	public void viewQuote(String referenceNumber, boolean newPageMode) throws Exception {
		viewQuote(newPageMode);
	}

	/**
	 * Gets the payment bean.
	 * 
	 * @return the payment bean
	 */
	public PaymentBean getPaymentBean() {
		return this.paymentBean;
	}

	/**
	 * Gets the endorsement sorted items.
	 * 
	 * @return the endorsement sorted items
	 */
	public List<CoverageOfferBean> getEndorsementSortedItems() {
		return this.offerAdapterQC.getEndorsementItems();
	}

	/**
	 * View quote.
	 * 
	 * @throws Exception
	 */
	public void viewQuote(boolean isNewPage) throws Exception {

		PolicyVersion policyVersion = this.buildViewQuoteCommon(this.getQuote().getId());

		if (policyVersion != null) {
			// LOAD COVERAGE INFORMATION
			this.offerAdapterQC.loadVehicleOfferBeanList(this.getVehicleOfferBeanList(), policyVersion,
					this.languageController.getLocale());

			// LOAD PREMIUM INFORMATION
			this.offerAdapterQC.loadPremiumBean(this.getVehicleOfferBeanList(), this.getPremiumBean());

			// LOAD PAYMENT INFORMATION
			if (BooleanUtils.isTrue(this.getPremiumBean().getPremiumExist())) {
				this.paymentAdapterQC.loadPaymentBean(this.paymentBean, policyVersion);
				this.paymentBean.setTotalMonthlyPremium(this.loadTotalMonthlyPremium());
			}
			super.updateHasOffers(policyVersion);
		}
		  
		this.view(this.getQuote());
	}

	/**
	 * Check if quote to open is from same company as the one selected
	 * 
	 * @param quoteId The current quote Id
	 * @return true if same company else return false
	 */
	public Boolean getSameCompany(String quoteId) {
		return (ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION.equals(retrieveManufacturerCompany(quoteId)));
	}

	@Override
	public void setVehicleOfferBeanList(List someVehicleOfferBeanList) {
		super.setVehicleOfferBeanList(someVehicleOfferBeanList);
		
	}
}
