package com.intact.brokeroffice.controller.viewquote.payment;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang.BooleanUtils;

import com.intact.brokeroffice.controller.viewquote.offer.VehicleOfferBean;

public class PremiumBean<E extends VehicleOfferBean> {	
	
	//BR5209  Repeat for each vehicle listed.: 
	private List<E> vehicleOfferBeanList = new ArrayList<E>();
	
	private Boolean premiumExist;	
		
	/**
	 * PremiumBean CONSTRUCTOR
	 */
	public PremiumBean(){
		//default constructor
	}

	
	/**
	 * method for reset
	 * 
	 */
	public void reset(){
		this.vehicleOfferBeanList = null;
		this.premiumExist = null;
	}
	
	
	/**
	 * Checks if premium exists
	 * BR5207  Hide the subsequent fields in this section.: 
	 * 
	 * @return Boolean.TRUE if exist else Boolean.FALSE (or null)  
	 */
	public Boolean getPremiumExist() {
		return this.premiumExist;
	}

	/**
	 * Sets if premium exist
	 * @param aPremiumExist the premiumExist to set
	 */
	public void setPremiumExist(Boolean aPremiumExist) {
		this.premiumExist = aPremiumExist;
	}

	
	/**
	 * Gets the <E VehicleOfferBean> objects List
	 * @return the List<E>
	 */
	public List<E> getVehicleOfferBeanList() {
		return this.vehicleOfferBeanList;
	}

	/**
	 * Sets the <E extends VehicleOfferBean> objects List 
	 * @param someVehicleOfferBeanList the vehicleOfferBeanList to set
	 */
	
	public void setVehicleOfferBeanList(List<E> someVehicleOfferBeanList) {
		this.vehicleOfferBeanList = someVehicleOfferBeanList;
	}		
	
	
	/**
	 * BR5206  Display this text when the quote has been saved/submitted prior to the client making a choice of coverages. 
	 * @return the rating calculated boolean
	 */
	public boolean isRatingCalculated(){		
		for(E aVehicleOfferBean : this.vehicleOfferBeanList){			
			if(aVehicleOfferBean.isRatingCalculated()){
				return true;
			}
		}
		return false;
	}
	
	/**
	 * Checks if premium exists
	 * 
	 * BR5206  Display this text when the quote has been saved/submitted prior to the client making a choice of coverages.: 
	 * BR5207  Hide the subsequent fields in this section.: 
	 * @param vehicleOfferBeanList
	 * @param premiumBean
	 * @return Boolean.TRUE if exist else return Boolean.FALSE
	 */
	public boolean isAllOfferedSelected()
	{
		for(E vehOfferBean : this.vehicleOfferBeanList)
		{			
			if(BooleanUtils.isFalse(vehOfferBean.getOfferSelected()))
			{
				return false;
			}
		}
		return true;
	}
}
