package com.intact.brokeroffice.controller.viewquote.offer;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;


import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import com.ing.canada.common.domain.QuoteCalculationDetails;
import com.ing.canada.common.services.api.rating.IQuotationService;
import com.ing.canada.plp.domain.enums.BasicCoverageCodeEnum;
import com.ing.canada.plp.domain.enums.CoverageTypeCodeEnum;
import com.ing.canada.plp.domain.enums.EndorsementCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;
import com.intact.business.rules.quote.BR5883_HasThreePercentageSurcharge;
import com.intact.business.rules.quote.BR5883_HasTwoPercentageSurcharge;

@Component
public class OfferAdapterON extends OfferAdapter<VehicleOfferBeanON> {


	@Autowired
	private IQuotationService quotationService;

	private static final double SURCHARGE_3PERC_PERCENTAGE_ON = 0.03;

	private static final double SURCHARGE_2PERC_PERCENTAGE_ON = 0.02;

	private static final double SURCHARGE_ONE_POINT_THREE_PERCENTAGE_ON = 0.013;

	@Autowired
	private BR5883_HasThreePercentageSurcharge br5883_3perc;

	@Autowired
	private BR5883_HasTwoPercentageSurcharge br5883_2perc;


	@Autowired
//	@Qualifier("ontario-interest-change-date")
	private LocalDate ontarioInterestChangeDate;


	/**
	 * Method uses to build the List of vehicle offer bean for ON
	 *
	 * @param vehicleOfferBeanList
	 * @param policyVersion
	 * @param aLocale
	 */
	public void loadVehicleOfferBeanList(List<VehicleOfferBeanON> vehicleOfferBeanList, PolicyVersion policyVersion,
																			 Locale aLocale,List<CoverageOffer> listSelectedOfferCoverage) {

		for (InsuranceRisk insuranceRisk : policyVersion.getInsuranceRisks()) {
			Boolean isPlusPackAlreadyTreated = false;

			Map<String, CoverageOfferBean> mapEndorsementCodes = new HashMap<String, CoverageOfferBean>();

			VehicleOfferBeanON vehicleBean = new VehicleOfferBeanON();

			loadBean(insuranceRisk, vehicleBean, aLocale, ProvinceCodeEnum.ONTARIO, policyVersion.getInsurancePolicy().getApplicationMode());

			loadCoverageOfferBeanFromInsuranceRiskOfferWithOffer(vehicleBean, VehicleOfferBeanON.getCoveragesDisplayOrder(),
																													 insuranceRisk.getSelectedInsuranceRiskOffer(), ManufacturerCompanyCodeEnum.ING_CENTRAL_ATLANTIC_REGION, listSelectedOfferCoverage);

			for (CoverageOffer covOff : listSelectedOfferCoverage) {
				// FOR QUEBEC ENDORSEMENTS (display only I90, NV1, NV2, 33E, NBD, NV3, _43V)
				// FOR ONTARIO ENDORSEMENTS (display only _20_SAV, PPC_SAV, PPD_SAV, RDG_SAV, ACHV_SAV)
				CoverageOfferBean covOfferBean = null;
				if (CoverageTypeCodeEnum.ENDORSEMENT.equals(covOff.getCoverageRepositoryEntry().getCoverageType())) {
					if (isValidEndorsements(EndorsementCodeEnum.valueOfCode(covOff.getCoverageRepositoryEntry()
																																				.getCoverageCode(), ManufacturerCompanyCodeEnum.ING_WESTERN_REGION))) {

						String coverageCode = obtainCoverageCode(covOff);

						for(CoverageOfferBean coverageOfferBean : vehicleBean.getEndorsementSelectedList()) {
							if(StringUtils.equalsIgnoreCase(coverageCode, coverageOfferBean.getCoverageCode()) || LIST_ENDORSEMENT_PLUS_PACK.contains(coverageCode) ) {
								covOfferBean = vehicleBean.getEndorsementsMappedWithValue(coverageCode, covOff);

								for(int i = 0; i < vehicleBean.getEndorsementSelectedList().size(); i++) {
									if(StringUtils.equalsIgnoreCase(coverageCode, vehicleBean.getEndorsementSelectedList().get(i).getCoverageCode())) {
										vehicleBean.getEndorsementSelectedList().get(i).setEndorsementDisplayInd(covOff.getCoverageSelectedIndicator());
										vehicleBean.getEndorsementSelectedList().get(i).setEndorsementIndicator(Boolean.TRUE);
										vehicleBean.getEndorsementSelectedList().get(i).setEndorsementEligibleInd(covOff.getCoverageEligibleIndicator());
										if (vehicleBean.getEndorsementSelectedList().get(i).getEndorsementEligibleInd() == null
												|| !vehicleBean.getEndorsementSelectedList().get(i).getEndorsementEligibleInd().booleanValue()) {
											vehicleBean.getEndorsementSelectedList().get(i).setEndorsementEligibleInd(covOff.getCoverageEligibleIndicator());
										}
										if(LIST_ENDORSEMENT_PLUS_PACK.contains(coverageCode) && BooleanUtils.isFalse(isPlusPackAlreadyTreated))
											isPlusPackAlreadyTreated = true;
									}

								}
							}
						}
					}
				}
			}

			this.loadAllEndorsementsSelectedFromOffer(vehicleBean, mapEndorsementCodes);

			//loadFullGlassCoverage(vehicleBean);

			vehicleOfferBeanList.add(vehicleBean);

			// retrieve endorsement mapped to each vehicle
			this.buildEndorsementsSelectedForEachVehicle(vehicleOfferBeanList, mapEndorsementCodes);
		}

	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#loadCoverageOfferBeanFromInsuranceRiskOffer(com
	 * .intact.brokeroffice.controller.viewquote.offer.VehicleOfferBean,
	 * com.ing.canada.plp.domain.enums.BasicCoverageCodeEnum[],
	 * com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer,
	 * com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum)
	 */
	@Override
	public void loadCoverageOfferBeanFromInsuranceRiskOffer(VehicleOfferBeanON aVehicleOfferBean,
																													BasicCoverageCodeEnum[] coverageCodeEnums, InsuranceRiskOffer insuranceRiskOffer,
																													ManufacturerCompanyCodeEnum aManufacturerCompanyCode) {

		if (this.isOfferSelected(aVehicleOfferBean, insuranceRiskOffer)) {
			this.initEndorsement(aVehicleOfferBean, aManufacturerCompanyCode);
			super.loadCoverageOfferBeanFromInsuranceRiskOffer(aVehicleOfferBean, coverageCodeEnums, insuranceRiskOffer,
																												aManufacturerCompanyCode);
		}
	}

	public void loadCoverageOfferBeanFromInsuranceRiskOfferWithOffer(VehicleOfferBeanON aVehicleOfferBean, BasicCoverageCodeEnum[] coverageCodeEnums,
																																	 InsuranceRiskOffer insuranceRiskOffer, ManufacturerCompanyCodeEnum aManufacturerCompanyCode, List<CoverageOffer> listSelectedOfferCoverage) {
		if (this.isOfferSelected(aVehicleOfferBean, insuranceRiskOffer)) {
			this.initEndorsementWithList(aVehicleOfferBean, aManufacturerCompanyCode,listSelectedOfferCoverage);
			super.loadCoverageOfferBeanFromInsuranceRiskOffer(aVehicleOfferBean, coverageCodeEnums, insuranceRiskOffer, aManufacturerCompanyCode);
		}
	}

	/**
	 * Initialize the ON ENDORSEMENTS FOR ONTARIO ENDORSEMENTS (display only _20_SAV, PPC_SAV, PPD_SAV, RDP_SAV,
	 * ACHV_SAV)
	 *
	 * @param aVehicleOfferBean (non-Javadoc)
	 * @see com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#initEndorsement(com.intact.brokeroffice.controller.viewquote.offer.VehicleOfferBean,
	 *      com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum)
	 */
	@Override
	protected void initEndorsement(VehicleOfferBeanON aVehicleOfferBean,
																 ManufacturerCompanyCodeEnum aManufacturerCompanyCode) {

		// ENDORSEMENT
		CoverageOfferBean aCoverageOfferBean = null;

		// OPCF 20
		aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum._20_SAV.getCode());
		aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
		aCoverageOfferBean.setEndorsementEligibleInd(null);
		aCoverageOfferBean.setRank(1);

		// RESPONSIBLE DRIVER GUARANTEE
		aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum.RDP_SAV.getCode());
		aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
		aCoverageOfferBean.setEndorsementEligibleInd(null);
		aCoverageOfferBean.setRank(3);

		// MY ACHIEVER
		aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum.ACHV_SAV.getCode());
		aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
		aCoverageOfferBean.setEndorsementEligibleInd(null);
		aCoverageOfferBean.setRank(4);

		// UBI
		aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum.UE05.getCode());
		aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
		aCoverageOfferBean.setEndorsementEligibleInd(Boolean.FALSE);
		aCoverageOfferBean.setRank(5);

		// DE2972 CLAIMS ADVANTAGE - 49F, 49Y, 50F and 50Y
		aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum._49Y_SAV.getCode());
		aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
		aCoverageOfferBean.setEndorsementEligibleInd(null);
		aCoverageOfferBean.setRank(6);
	}

	public void initEndorsementWithList(VehicleOfferBeanON aVehicleOfferBean, ManufacturerCompanyCodeEnum aManufacturerCompanyCode, List<CoverageOffer> listSelectedOfferCoverage) {
		initEndorsement(aVehicleOfferBean, aManufacturerCompanyCode);

		CoverageOfferBean aCoverageOfferBean = null;

		for(CoverageOffer coverage : listSelectedOfferCoverage) {

			if (CoverageTypeCodeEnum.ENDORSEMENT.equals(coverage.getCoverageRepositoryEntry().getCoverageType())
					&& EndorsementCodeEnum.valueOfCode(coverage.getCoverageRepositoryEntry().getCoverageCode(), aManufacturerCompanyCode) != null) {

				if(StringUtils.equalsIgnoreCase(EndorsementCodeEnum.valueOfCode(coverage.getCoverageRepositoryEntry()
																																								.getCoverageCode(), aManufacturerCompanyCode).getCode(), EndorsementCodeEnum.PPC_SAV.getCode()) ||
						StringUtils.equalsIgnoreCase(EndorsementCodeEnum.valueOfCode(coverage.getCoverageRepositoryEntry()
																																								 .getCoverageCode(), aManufacturerCompanyCode).getCode(),EndorsementCodeEnum.PPD_SAV.getCode())  ) {
					aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum.PPD_SAV.getCode());
					aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
					aCoverageOfferBean.setCoverageSelectedIndicator(null);
					aCoverageOfferBean.setRank(2);
				}
			}
		}

	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#resetEndorsementRank(java.lang.String,
	 * com.intact.brokeroffice.controller.viewquote.offer.CoverageOfferBean)
	 */
	@Override
	protected void resetEndorsementRank(String codeValue, CoverageOfferBean aCoverageOfferBean) {

		EndorsementCodeEnum codeEnum = EndorsementCodeEnum.valueOfCode(codeValue,
																																	 ManufacturerCompanyCodeEnum.ING_CENTRAL_ATLANTIC_REGION);

		if (EndorsementCodeEnum._20_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(1);
		} else if (EndorsementCodeEnum.PPC_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(2);
		} else if (EndorsementCodeEnum.EPCE_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(2);
		} else if (EndorsementCodeEnum.PPD_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(2);
		} else if (EndorsementCodeEnum.RDP_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(3);
		} else if (EndorsementCodeEnum.ACHV_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(4);
		} else if (EndorsementCodeEnum.UE05.equals(codeEnum)) {
			aCoverageOfferBean.setRank(5);

			// CLAIMS ADVANTAGE: _49Y_SAV = _50Y_SAV = _49F_SAV = _50F_SAV
		} else if (EndorsementCodeEnum._49Y_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(6);
		} else if (EndorsementCodeEnum._50Y_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(6);
		} else if (EndorsementCodeEnum._49F_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(6);
		} else if (EndorsementCodeEnum._50F_SAV.equals(codeEnum)) {
			aCoverageOfferBean.setRank(6);
		}
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#isValidEndorsements(com.ing.canada.plp.domain
	 * .enums.EndorsementCodeEnum)
	 */
	@Override
	protected boolean isValidEndorsements(EndorsementCodeEnum codeEnum) {

		if (EndorsementCodeEnum._20_SAV.equals(codeEnum)
				|| EndorsementCodeEnum.PPC_SAV.equals(codeEnum)
				|| EndorsementCodeEnum.EPCE_SAV.equals(codeEnum)
				|| EndorsementCodeEnum.PPD_SAV.equals(codeEnum)
				|| EndorsementCodeEnum.RDP_SAV.equals(codeEnum)
				|| EndorsementCodeEnum.ACHV_SAV.equals(codeEnum)
				|| EndorsementCodeEnum.UE05.equals(codeEnum)
				|| EndorsementCodeEnum._49Y_SAV.equals(codeEnum)
				|| EndorsementCodeEnum._50Y_SAV.equals(codeEnum)
				|| EndorsementCodeEnum._49F_SAV.equals(codeEnum)
				|| EndorsementCodeEnum._50F_SAV.equals(codeEnum)
		) {
			return true;
		}
		return false;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#obtainCoverageCode(com.ing.canada.plp.domain.
	 * insuranceriskoffer.CoverageOffer)
	 */
	@Override
	protected String obtainCoverageCode(CoverageOffer aCoverageOffer) {
		// important : to validate proper PPAC information
		String theCoverageCode = aCoverageOffer.getCoverageRepositoryEntry().getCoverageCode();
		// Wrap PPD_SAV to PPC_SAV
		if (EndorsementCodeEnum.PPD_SAV.getCode().equals(aCoverageOffer.getCoverageRepositoryEntry().getCoverageCode())) {
			theCoverageCode = EndorsementCodeEnum.PPC_SAV.getCode();
		}
		// Wrap EPCE_SAV to PPC_SAV
		if (EndorsementCodeEnum.EPCE_SAV.getCode()
																		.equals(aCoverageOffer.getCoverageRepositoryEntry().getCoverageCode())) {
			theCoverageCode = EndorsementCodeEnum.PPC_SAV.getCode();
		}

		// Wrap _50Y_SAV, _49F_SAV, _50F_SAV to _49Y_SAV
		else if (EndorsementCodeEnum._50Y_SAV.getCode().equals(
				aCoverageOffer.getCoverageRepositoryEntry().getCoverageCode())
				|| EndorsementCodeEnum._49F_SAV.getCode().equals(
				aCoverageOffer.getCoverageRepositoryEntry().getCoverageCode())
				|| EndorsementCodeEnum._50F_SAV.getCode().equals(
				aCoverageOffer.getCoverageRepositoryEntry().getCoverageCode())) {
			theCoverageCode = EndorsementCodeEnum._49Y_SAV.getCode();
		}
		return theCoverageCode;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#mappingEndorsementKey(java.lang.String)
	 */
	@Override
	protected String mappingEndorsementKey(String anEndorsementKey) {

		if (EndorsementCodeEnum.PPD_SAV.getCode().equals(anEndorsementKey)
				|| EndorsementCodeEnum.PPC_SAV.getCode().equals(anEndorsementKey)
				|| EndorsementCodeEnum.EPCE_SAV.getCode().equals(anEndorsementKey)) {
			return EndorsementCodeEnum.PPD_SAV.getCode();
		}

		// Claims Advantage
		else if (EndorsementCodeEnum._50Y_SAV.getCode().equals(anEndorsementKey)
				|| EndorsementCodeEnum._49F_SAV.getCode().equals(anEndorsementKey)
				|| EndorsementCodeEnum._50F_SAV.getCode().equals(anEndorsementKey)) {
			return EndorsementCodeEnum._49Y_SAV.getCode();
		}

		return anEndorsementKey;
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#loadAllEndorsementsSelectedFromOffer(com.intact
	 * .brokeroffice.controller.viewquote.offer.VehicleOfferBean, java.util.Map)
	 */
	@Override
	protected void loadAllEndorsementsSelectedFromOffer(VehicleOfferBeanON aVehicleOfferBean,
																											Map<String, CoverageOfferBean> mapEndorsementOptions) {
		for (CoverageOfferBean bean : aVehicleOfferBean.getEndorsementSelectedList()) {
			String findKey = mappingEndorsementKey(bean.getCoverageCode());
			if (!mapEndorsementOptions.containsKey(findKey)) {
				mapEndorsementOptions.put(findKey, bean);
			}
		}
	}

	/*
	 * (non-Javadoc)
	 *
	 * @see
	 * com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#validateCoverageInformation(com.intact.brokeroffice
	 * .controller.viewquote.offer.CoverageOfferBean, com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer)
	 */
	@Override
	protected void loadCoverageInformation(CoverageOfferBean covOfferBean, CoverageOffer covOff) {
		// important : to validate proper PPAC information
		if (covOfferBean.getCoverageSelectedIndicator() == null
				|| !covOfferBean.getCoverageSelectedIndicator().booleanValue()) {
			covOfferBean.setCoverageSelectedIndicator(covOff.getCoverageSelectedIndicator());

			if (isPlusPac(covOff.getCoverageRepositoryEntry().getCoverageCode())
					|| isClaimsAdvantage(covOff.getCoverageRepositoryEntry().getCoverageCode())) {
				if (covOfferBean.getCoverageSelectedIndicator() != null
						&& covOfferBean.getCoverageSelectedIndicator().booleanValue()) {
					covOfferBean.setCoverageCode(covOff.getCoverageRepositoryEntry().getCoverageCode());
				}
			}
		}
	}

	/**
	 * checks if an endorsement is a Plus Pac (PPC or PPD)
	 *
	 * @param aCode
	 * @return
	 */
	private boolean isPlusPac(String aCode) {
		return EndorsementCodeEnum.PPC_SAV.getCode().equals(aCode)
				|| EndorsementCodeEnum.EPCE_SAV.getCode().equals(aCode)
				|| EndorsementCodeEnum.PPD_SAV.getCode().equals(aCode);
	}

	/**
	 * checks if an endorsement is a Claims Advantage (49Y or 50Y or 49F or 50F)
	 *
	 * @param aCode
	 * @return
	 */
	private boolean isClaimsAdvantage(String aCode) {
		return EndorsementCodeEnum._49F_SAV.getCode().equals(aCode)
				|| EndorsementCodeEnum._49Y_SAV.getCode().equals(aCode)
				|| EndorsementCodeEnum._50F_SAV.getCode().equals(aCode)
				|| EndorsementCodeEnum._50Y_SAV.getCode().equals(aCode);
	}

	/**
	 * Loads the annual or monthly premium with taxes and/or surcharges according to the province for an offerBean
	 *
	 * @param anOfferBean
	 * @param aPolicyTermInMonths
	 * @param anInsuranceRiskOffer
	 * @param aProvinceCode
	 */
	public void loadOfferBean(OfferBean anOfferBean, InsuranceRiskOffer anInsuranceRiskOffer,
														ProvinceCodeEnum aProvinceCode) {

		PolicyVersion policyVersion = anInsuranceRiskOffer.getInsuranceRisk().getPolicyVersion();

		QuoteCalculationDetails quoteCalculationDetails;

		// EVO 51576 Ontario reform new interest charge needs to be applied
		// Based on the transaction rating date
		LocalDate refDate = LocalDate.parse(policyVersion.getPolicyInceptionDate().toString());
		boolean newInterest = !refDate.isBefore(ontarioInterestChangeDate);

		if (br5883_3perc.validate(policyVersion) && validateBillingPlan(policyVersion, aProvinceCode) && !newInterest) {
			quoteCalculationDetails = this.quotationService.loadOfferBean(anOfferBean.getAnnualPremium(),
																																		anInsuranceRiskOffer, aProvinceCode, SURCHARGE_3PERC_PERCENTAGE_ON, true);
		}
		else if (br5883_2perc.validate(policyVersion) && validateBillingPlan(policyVersion, aProvinceCode)  && !newInterest) {
			quoteCalculationDetails = this.quotationService.loadOfferBean(anOfferBean.getAnnualPremium(),
																																		anInsuranceRiskOffer, aProvinceCode, SURCHARGE_2PERC_PERCENTAGE_ON, true);
		}
		else if ((br5883_2perc.validate(policyVersion) || br5883_3perc.validate(policyVersion)) && validateBillingPlan(policyVersion, aProvinceCode)  && newInterest) {
			quoteCalculationDetails = this.quotationService.loadOfferBean(anOfferBean.getAnnualPremium(),
																																		anInsuranceRiskOffer, aProvinceCode, SURCHARGE_ONE_POINT_THREE_PERCENTAGE_ON, true);
		}
		else {
			quoteCalculationDetails = this.quotationService.loadOfferBean(anOfferBean.getAnnualPremium(),
																																		anInsuranceRiskOffer, aProvinceCode, 0, false);
		}


		anOfferBean.setAnnualPremiumWithTaxes(quoteCalculationDetails.getAnnualAmountWithTaxes());
		anOfferBean.setMonthlyPremiumWithTaxes(quoteCalculationDetails.getMonthlyPaymentWithTaxes());

	}

}
