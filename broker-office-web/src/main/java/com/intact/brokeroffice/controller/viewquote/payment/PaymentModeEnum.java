

/**
 * Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
 * without the written permission of Intact Insurance 
 * Copyright (c) 2009, Intact Insurance, All rights reserved.<br>
 */

package com.intact.brokeroffice.controller.viewquote.payment;
import static com.ing.canada.plp.domain.enums.CreditCardCompanyCodeEnum.MASTERCARD;
import static com.ing.canada.plp.domain.enums.CreditCardCompanyCodeEnum.VISA;
import static com.ing.canada.plp.domain.enums.PaymentPlanCodeEnum.ONE_PAY_FOR_ANNUAL_POLICY;
import static com.ing.canada.plp.domain.enums.PaymentPlanCodeEnum.TWO_PAYMENTS;

import java.util.HashMap;
import java.util.Map;

import com.ing.canada.plp.domain.enums.CreditCardCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.PaymentPlanCodeEnum;

/**
 * Enum for the payment mode
 * 
 * <AUTHOR>
 * 
 */
public enum PaymentModeEnum {

	PAYMENT_MONTHLY_CC_VISA(0, VISA, true, null),
	PAYMENT_MONTHLY_CC_MC(1, MASTERCARD, true, null),
	PAYMENT_FULL_CC_VISA(2, VISA, false, null),
	PAYMENT_FULL_CC_MC(3, MASTERCARD, false, null),
	PAYMENT_MONTHLY_BANK(4, null, true, null),
	PAYMENT_AT_SOURCE(5, null, false, null),
	PAYMENT_ONE_CKECK(6, null, false, ONE_PAY_FOR_ANNUAL_POLICY),
	PAYMENT_TWO_CKECKS(7, null, false, TWO_PAYMENTS),	
	;

	private static Map<Integer, PaymentModeEnum> paymentModeMap;

	static {
		paymentModeMap = new HashMap<Integer, PaymentModeEnum>();
		for (PaymentModeEnum paymentMode : PaymentModeEnum.values()) {	
			paymentModeMap.put(paymentMode.getCode(), paymentMode);
		}
	}
	
	private final int code;
	
	private final CreditCardCompanyCodeEnum creditCardCode;

	private final boolean monthly;
	private final PaymentPlanCodeEnum paymentPlanCode;

	/**
	 * Instantiates a new payment mode enum.
	 * 
	 * @param aCode the a code
	 * @param aCreditCardCode the a credit card code
	 * @param aMontly the a montly
	 */
	private PaymentModeEnum(int aCode, CreditCardCompanyCodeEnum aCreditCardCode, boolean aMontly, PaymentPlanCodeEnum aPaymentPlanCode) {
		this.code = aCode;
		this.creditCardCode = aCreditCardCode;
		this.monthly = aMontly;
		this.paymentPlanCode = aPaymentPlanCode;
	}

	/**
	 * Gets the code.
	 * 
	 * @return the code
	 */
	public int getCode() {
		return this.code;
	}

	/**
	 * Gets the credit card code.
	 * 
	 * @return the credit card code
	 */
	public CreditCardCompanyCodeEnum getCreditCardCode() {
		return this.creditCardCode;
	}

	/**
	 * Get a PaymentModeEnum with the code value.
	 * 
	 * @param paymentTypeIndex the payment type index
	 * 
	 * @return the PaymentModeEnum
	 */
	public static PaymentModeEnum valueOf(int paymentTypeIndex) {
		return paymentModeMap.get(paymentTypeIndex);
	}

	/**
	 *  Checks if monthly payment
	 * @return
	 */
	public boolean isMonthly() {
		return this.monthly;
	}
	
	/**
	 * Gets the credit card code.
	 * 
	 * @return the credit card code
	 */
	public PaymentPlanCodeEnum getPaymentPlanCode() {
		return this.paymentPlanCode;
	}
	
}
