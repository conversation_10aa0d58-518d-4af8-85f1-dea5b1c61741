package com.intact.brokeroffice.controller.viewquote.payment;

public class PaymentBankBean implements IPaymentBean {

	/** bank account holder name */
	private String bankAccountHolderName = "";

	/** transit */
	private String transit = "";

	/** institution */
	private String institution = "";

	/** account number */
	private String accountNumber = "";

	/**
	 * Gets the account number
	 * BR5376  Display client response entered in the On-line Auto Quote application when monthly payments by bank withdrawal are selected.: 
	 * BR5222  This information will not be displayed after the record has been uploaded to Savers.
	 * BR5758  Display Only When Monthly Withdrawal Selected
	 * @return String
	 */
	public String getAccountNumber() {
		return this.accountNumber;
	}

	/**
	 * Sets the account number 
	 * @param anAccountNumber the accountNumber to set
	 */
	public void setAccountNumber(String anAccountNumber) {
		this.accountNumber = anAccountNumber;
	}

	/**
	 * Gets the bank account holder name
	 * BR5193  Display client response entered in the On-line Auto Quote application.
	 * BR5222  This information will not be displayed after the record has been uploaded to Savers.
	 * BR5758  Display Only When Monthly Withdrawal Selected
	 * @return String
	 */
	public String getBankAccountHolderName() {
		return this.bankAccountHolderName;
	}

	/**
	 * Sets the bank account holder name
	 * @param aBankAccountHolderName the bankAccountHolderName to set
	 */
	public void setBankAccountHolderName(String aBankAccountHolderName) {
		this.bankAccountHolderName = aBankAccountHolderName;
	}

	/**
	 * Gets the institution
	 * BR5376  Display client response entered in the On-line Auto Quote application when monthly payments by bank withdrawal are selected.: 
	 * BR5222  This information will not be displayed after the record has been uploaded to Savers.
	 * BR5758  Display Only When Monthly Withdrawal Selected
	 * @return String
	 */
	public String getInstitution() {
		return this.institution;
	}
	
	/**
	 * Sets the institution
	 * @param anInstitution the institution to set
	 */
	public void setInstitution(String anInstitution) {
		this.institution = anInstitution;
	}
	
	/**
	 * Gets the transit
	 * BR5376  Display client response entered in the On-line 
	 * Auto Quote application when monthly payments by bank withdrawal are selected.
	 * BR5222  This information will not be displayed after the record has been uploaded to Savers.
	 * BR5758  Display Only When Monthly Withdrawal Selected
	 * @return String
	 */
	public String getTransit() {
		return this.transit;
	}

	/**
	 * Sets the transit
	 * @param aTransit the transit to set
	 */
	public void setTransit(String aTransit) {
		this.transit = aTransit;
	}
	
	public boolean isAccountHolderExist(){		
		return getBankAccountHolderName()!=null && !"".equals(getBankAccountHolderName());
	}
	
	public boolean isInstitutionExist(){		
		return getInstitution()!=null && !"".equals(getInstitution());
	}
	
	public boolean isTransitExist(){		
		return getTransit()!=null && !"".equals(getTransit());
	}
	
	public boolean isAccountNoExist(){		
		return getAccountNumber()!=null & !"".equals(getAccountNumber());
	}
	
}
