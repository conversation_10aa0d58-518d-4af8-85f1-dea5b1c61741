package com.intact.brokeroffice.controller.tabs;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.intact.brokeroffice.util.SearchState;

/**
 * The Class QuotesTabController manages the Quotes tab.
 */
@Component
@Scope("session")
public class QuotesTabController {
	
	private int firstAccess = 0;

	/** The general tab controller. */
	@Autowired
	private GeneralTabController generalTabController;

	private Map<Integer, SearchState> searchStates = null;

	@Autowired
	private int maxSearchRequests = 0;

	public QuotesTabController() {
		this.setSearchStates(new ConcurrentHashMap<Integer, SearchState>());
	}

	/**
	 * Quotes. BR5137 List of Quotes is displayed when
	 */
	public void quotes() {
		this.generalTabController.quotes();

	}

	public final SearchState getSearchState(int index) {
		SearchState state = this.getSearchStates().get(index);
 
		if (state == null) {  
			state = new SearchState();
			state.setType(index == 0 ? "VIEW" : "SEARCH");
			state.setTimestamp(new Date());
			this.getSearchStates().put(index, state);
		}

		return state;
	}

	public final Map<Integer, SearchState> getSearchStates() {
		return searchStates;
	}

	public final void setSearchStates(Map<Integer, SearchState> searchStates) {
		this.searchStates = searchStates;
	}

	public final int getMaxSearchRequests() {
		return this.maxSearchRequests;
	}

	public final void setMaxSearchRequests(int maxSearchRequests) {
		this.maxSearchRequests = maxSearchRequests;
	}

	/**
	 * Return to list. BR5140 Redirects the user to the default List of Quotes
	 * page from which the Quote Search page was called.
	 */
	public void returnToList() {

		this.quotes();
		this.generalTabController.quotes();
	}
	
	
}
