package com.intact.brokeroffice.controller.viewquote.offer;

import com.ing.canada.plp.domain.enums.OfferTypeCodeEnum;

public class OfferBean {

	/** The offer type. */
	private OfferTypeCodeEnum offerType;

	/** The annual premium. */
	private Double annualPremium;

	/** The monthly premium. */
	private Double monthlyPremium;

	/** The annual premium. */
	private Double annualPremiumWithTaxes;

	/** The monthly premium. */
	private Double monthlyPremiumWithTaxes;

	/** boolean for a selected offer **/
	private Boolean selected;

	private String customerValueIndex;

	private static final String CUSTOMER_VALUE_INDEX_BAND = "cvi.band.";

	/**
	 * inform us if this bean was created inside the managed bean with no corsponding insuranceRiskOffer in the DB
	 */
	private Boolean hasCorrespondingInsuranceRiskOffer = Boolean.TRUE;

	/**
	 * The Constructor.
	 * 
	 * @param aOfferType the a offer type
	 */

	public OfferBean(OfferTypeCodeEnum aOfferType) {
		this.offerType = aOfferType;
	}

	/**
	 * Gets the annual premium BR5210 Displays the Total Annual Premium for the indicated vehicle for this package
	 * BR5214 If no custom package has been calculated, indicate N/A.: * BR5221 Display calculated value.: BR5761
	 * Rounding of dollar amounts including tax:
	 * 
	 * @return Double
	 */
	public Double getAnnualPremium() {
		return this.annualPremium;
	}

	/**
	 * Sets the annual premium
	 * 
	 * @param anAnnualPremium the annualPremium to set
	 */
	public void setAnnualPremium(Double anAnnualPremium) {
		this.annualPremium = anAnnualPremium;
	}

	public OfferTypeCodeEnum getOfferType() {
		return this.offerType;
	}

	/**
	 * Sets the offer type
	 * 
	 * @param anOfferType the offerType to set
	 */
	public void setOfferType(OfferTypeCodeEnum anOfferType) {
		this.offerType = anOfferType;
	}

	/**
	 * Checks if offer has been selected
	 * 
	 * @return Boolean.TRUE if selected else Boolean.FALSE
	 * 
	 */
	public Boolean getSelected() {
		return this.selected;
	}

	/**
	 * Sets selected BR5211 Display a checkmark ( ) next to the premium if this is the selected package for the
	 * indicated vehicle
	 * 
	 * @param aSelected the selected to set
	 */
	public void setSelected(Boolean aSelected) {
		this.selected = aSelected;
	}

	/**
	 * Gets the monthly premium BR5221 Display calculated value. BR5761 Rounding of dollar amounts including tax
	 * 
	 * @return Double
	 */
	public Double getMonthlyPremium() {
		return this.monthlyPremium;
	}

	/**
	 * Sets the monthly premium
	 * 
	 * @param aMonthlyPremium the monthlyPremium to set
	 */
	public void setMonthlyPremium(Double aMonthlyPremium) {
		this.monthlyPremium = aMonthlyPremium;
	}

	/**
	 * Methods to validate if an offer has been selected or not
	 * 
	 * @param selected
	 * @param anOfferType
	 */
	public void validateOfferSelection(Boolean aSelected, OfferTypeCodeEnum anOfferType) {
		if (aSelected != null && aSelected.booleanValue() && anOfferType != null && anOfferType.equals(this.offerType)) {
			this.setSelected(Boolean.TRUE);
		} else {
			this.setSelected(Boolean.FALSE);
		}
	}

	/**
	 * Gets the annual premium with taxes
	 * 
	 * @return the annual premium with taxes
	 */
	public Double getAnnualPremiumWithTaxes() {
		return this.annualPremiumWithTaxes;
	}

	/**
	 * Sets the annual premium with taxes
	 * 
	 * @param annualPremiumWithTaxes the the annual premium with taxes to set
	 */
	public void setAnnualPremiumWithTaxes(Double anAnnualPremiumWithTaxes) {
		this.annualPremiumWithTaxes = anAnnualPremiumWithTaxes;
	}

	/**
	 * Gets the monthly premium with taxes
	 * 
	 * @return the monthly premium with taxes
	 */
	public Double getMonthlyPremiumWithTaxes() {
		return this.monthlyPremiumWithTaxes;
	}

	/**
	 * Sets the monthly premium with taxes
	 * 
	 * @param monthlyPremiumWithTaxes the monthly premium with taxes to set
	 */
	public void setMonthlyPremiumWithTaxes(Double aMonthlyPremiumWithTaxes) {
		this.monthlyPremiumWithTaxes = aMonthlyPremiumWithTaxes;
	}

	/**
	 * @return
	 */
	public String getCustomerValueIndex() {
		return customerValueIndex;
	}

	/**
	 * @param customerValueIndex
	 */
	public void setCustomerValueIndex(String customerValueIndex) {
		this.customerValueIndex = customerValueIndex;
	}

	public String getCustomerValueIndexBand() {

		if (this.customerValueIndex != null) {

			int aCustomerValueInt = Integer.valueOf(this.customerValueIndex);

			if (-999 < aCustomerValueInt && aCustomerValueInt < -30) {
				return CUSTOMER_VALUE_INDEX_BAND + "1";
			} else if (-29 < aCustomerValueInt && aCustomerValueInt < -15) {
				return CUSTOMER_VALUE_INDEX_BAND + "2";
			} else if (-14 < aCustomerValueInt && aCustomerValueInt < 8) {
				return CUSTOMER_VALUE_INDEX_BAND + "3";
			} else if (9 < aCustomerValueInt && aCustomerValueInt < 28) {
				return CUSTOMER_VALUE_INDEX_BAND + "4";
			} else if (29 < aCustomerValueInt && aCustomerValueInt < 999) {
				return CUSTOMER_VALUE_INDEX_BAND + "5";
			}
		}
		return CUSTOMER_VALUE_INDEX_BAND + "DEFAULT";

	}

	/**
	 * 
	 * @return Boolean
	 */
	public Boolean getHasCorrespondingInsuranceRiskOffer() {
		return this.hasCorrespondingInsuranceRiskOffer;
	}

	/**
	 * 
	 * @param addedByTheApp
	 */
	public void setHasCorrespondingInsuranceRiskOffer(Boolean noCorrespondingInsuranceRiskOffer) {
		this.hasCorrespondingInsuranceRiskOffer = noCorrespondingInsuranceRiskOffer;
	}

}
