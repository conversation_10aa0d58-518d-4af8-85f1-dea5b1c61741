package com.intact.brokeroffice.controller.viewquote.offer;

import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ing.canada.common.domain.QuoteCalculationDetails;
import com.ing.canada.common.services.api.rating.IQuotationService;
import com.ing.canada.plp.domain.enums.BasicCoverageCodeEnum;
import com.ing.canada.plp.domain.enums.CoverageTypeCodeEnum;
import com.ing.canada.plp.domain.enums.EndorsementCodeEnum;
import com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.ing.canada.plp.domain.insurancerisk.InsuranceRisk;
import com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer;
import com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer;
import com.ing.canada.plp.domain.policyversion.PolicyVersion;

@Component
public class OfferAdapterQC extends OfferAdapter<VehicleOfferBeanQC> {

	@Autowired
	private IQuotationService quotationService;

	private static final double SURCHARGE_PERCENTAGE_QC = 0.0175;
	
	/**
	 * Method uses to build the List of vehicle offer bean for QC
	 * 
	 * @param vehicleOfferBeanList
	 * @param policyVersion
	 * @param aLocale
	 */
	public void loadVehicleOfferBeanList(List<VehicleOfferBeanQC> vehicleOfferBeanList, PolicyVersion policyVersion,
			Locale aLocale) {

		Map<String, CoverageOfferBean> mapEndorsementCodes = new HashMap<String, CoverageOfferBean>();
		for (InsuranceRisk insuranceRisk : policyVersion.getInsuranceRisks()) {
			VehicleOfferBeanQC vehicleBean = new VehicleOfferBeanQC();
			vehicleOfferBeanList.add(vehicleBean);
			loadBean(insuranceRisk, vehicleBean, aLocale, ProvinceCodeEnum.QUEBEC, policyVersion.getInsurancePolicy().getApplicationMode());
			loadCoverageOfferBeanFromInsuranceRiskOffer(vehicleBean, VehicleOfferBeanQC.getCoveragesDisplayOrder(),
					insuranceRisk.getSelectedInsuranceRiskOffer(), ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION);
			this.loadAllEndorsementsSelectedFromOffer(vehicleBean, mapEndorsementCodes);
		}

		// retrieve endorsement mapped to each vehicle
		this.buildEndorsementsSelectedForEachVehicle(vehicleOfferBeanList, mapEndorsementCodes);
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#loadCoverageOfferBeanFromInsuranceRiskOffer(com
	 * .intact.brokeroffice.controller.viewquote.offer.VehicleOfferBean,
	 * com.ing.canada.plp.domain.enums.BasicCoverageCodeEnum[],
	 * com.ing.canada.plp.domain.insuranceriskoffer.InsuranceRiskOffer,
	 * com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum)
	 */
	@Override
	public void loadCoverageOfferBeanFromInsuranceRiskOffer(VehicleOfferBeanQC aVehicleOfferBean,
			BasicCoverageCodeEnum[] coverageCodeEnums, InsuranceRiskOffer insuranceRiskOffer,
			ManufacturerCompanyCodeEnum aManufacturerCompanyCode) {

		// Verify offer has been selected by customer
		if (this.isOfferSelected(aVehicleOfferBean, insuranceRiskOffer)) {
			this.initEndorsement(aVehicleOfferBean, aManufacturerCompanyCode);
			super.loadCoverageOfferBeanFromInsuranceRiskOffer(aVehicleOfferBean, coverageCodeEnums, insuranceRiskOffer,
					aManufacturerCompanyCode);

			// Yvan Laberge
			// NV3 is a special case.
			// If both 33E and NBD were selected by user
			// AQ creates NV3 instead of 33E and NBD.
			for (CoverageOffer covOff : insuranceRiskOffer.getCoverageOffers()) {

				CoverageOfferBean covOfferBean33E = null;
				CoverageOfferBean covOfferBeanNBD = null;
				if (CoverageTypeCodeEnum.ENDORSEMENT.equals(covOff.getCoverageRepositoryEntry().getCoverageType())) {
					String coverageCode = obtainCoverageCode(covOff);
					if(EndorsementCodeEnum.NV3.getCode().equals(coverageCode)) {
						covOfferBean33E = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum._33E.getCode());
						covOfferBean33E.setEndorsementIndicator(Boolean.TRUE);
						if (covOfferBean33E.getEndorsementEligibleInd() == null
								|| !covOfferBean33E.getEndorsementEligibleInd().booleanValue()) {
							covOfferBean33E.setEndorsementEligibleInd(covOff.getCoverageEligibleIndicator());
						}
						covOfferBeanNBD = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum.NBD.getCode());
						covOfferBeanNBD.setEndorsementIndicator(Boolean.TRUE);
						if (covOfferBeanNBD.getEndorsementEligibleInd() == null
								|| !covOfferBeanNBD.getEndorsementEligibleInd().booleanValue()) {
							covOfferBeanNBD.setEndorsementEligibleInd(covOff.getCoverageEligibleIndicator());
						}
					}
				}
				if (covOfferBean33E != null) {
					covOfferBean33E.setCoverageDescription(covOff.getCoverageDescription());
					loadCoverageInformation(covOfferBean33E, covOff);
				}
				if (covOfferBeanNBD != null) {
					covOfferBeanNBD.setCoverageDescription(covOff.getCoverageDescription());
					loadCoverageInformation(covOfferBeanNBD, covOff);
				}
			}
		}
	}

	/**
	 * init the QC ENDORSEMENTS FOR QUEBEC ENDORSEMENTS (display only I90, NV1, NV2, 33E, NBD, NV3, _43V) (non-Javadoc)
	 * 
	 * @see com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#initEndorsement(com.intact.brokeroffice.controller.viewquote.offer.VehicleOfferBean,
	 *      com.ing.canada.plp.domain.enums.ManufacturerCompanyCodeEnum)
	 */
	@Override
	protected void initEndorsement(VehicleOfferBeanQC aVehicleOfferBean,
			ManufacturerCompanyCodeEnum aManufacturerCompanyCode) {

		// ENDORSEMENT
		CoverageOfferBean aCoverageOfferBean = null;

		// INOV
		aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum.I90.getCode());
		aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
		aCoverageOfferBean.setEndorsementEligibleInd(null);
		aCoverageOfferBean.setRank(1);

		// MY AUTO AND ME
		aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum.NV1.getCode());
		aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
		aCoverageOfferBean.setEndorsementEligibleInd(null);
		aCoverageOfferBean.setRank(2);
		// MY AUTO AND ME +
		aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum.NV2.getCode());
		aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
		aCoverageOfferBean.setEndorsementEligibleInd(null);
		aCoverageOfferBean.setRank(3);

		// ROADSIDE ASSISTANCE
		aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum._33E.getCode());
		aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
		aCoverageOfferBean.setEndorsementEligibleInd(null);
		aCoverageOfferBean.setRank(4);

		// NEW CRASHPROOF
		aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum.NBD.getCode());
		aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
		aCoverageOfferBean.setEndorsementEligibleInd(null);
		aCoverageOfferBean.setRank(5);

		// REPLACEMENT COST
		aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum._43V.getCode());
		aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
		aCoverageOfferBean.setEndorsementEligibleInd(null);
		aCoverageOfferBean.setRank(6);
		
		// UBI
		aCoverageOfferBean = aVehicleOfferBean.getEndorsementsMapped(EndorsementCodeEnum.UBI.getCode());
		aCoverageOfferBean.setManufacturerCompanyCode(aManufacturerCompanyCode);
		aCoverageOfferBean.setEndorsementEligibleInd(null);
		aCoverageOfferBean.setRank(7);

	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#resetEndorsementRank(java.lang.String,
	 * com.intact.brokeroffice.controller.viewquote.offer.CoverageOfferBean)
	 */
	@Override
	protected void resetEndorsementRank(String codeValue, CoverageOfferBean aCoverageOfferBean) {

		EndorsementCodeEnum codeEnum = EndorsementCodeEnum.valueOfCode(codeValue,
				ManufacturerCompanyCodeEnum.INTACT_QUEBEC_REGION);
		// QC ENDORSEMENTS
		if (EndorsementCodeEnum.I90.equals(codeEnum)) {
			aCoverageOfferBean.setRank(1);
		} else if (EndorsementCodeEnum.NV1.equals(codeEnum)) {
			aCoverageOfferBean.setRank(2);
		} else if (EndorsementCodeEnum.NV2.equals(codeEnum)) {
			aCoverageOfferBean.setRank(3);
		} else if (EndorsementCodeEnum._33E.equals(codeEnum)) {
			aCoverageOfferBean.setRank(4);
		} else if (EndorsementCodeEnum.NBD.equals(codeEnum)) {
			aCoverageOfferBean.setRank(5);
		} else if (EndorsementCodeEnum._43V.equals(codeEnum)) {
			aCoverageOfferBean.setRank(6);
		} else if (EndorsementCodeEnum.UBI.equals(codeEnum)) {
			aCoverageOfferBean.setRank(7);
		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#isValidEndorsements(com.ing.canada.plp.domain
	 * .enums.EndorsementCodeEnum)
	 */
	@Override
	protected boolean isValidEndorsements(EndorsementCodeEnum codeEnum) {
		if (EndorsementCodeEnum.I90.equals(codeEnum)
				|| EndorsementCodeEnum.NV1.equals(codeEnum)
				|| EndorsementCodeEnum.NV2.equals(codeEnum)
				|| EndorsementCodeEnum._33E.equals(codeEnum)
				|| EndorsementCodeEnum.NBD.equals(codeEnum)
				|| EndorsementCodeEnum._43V.equals(codeEnum)
				|| EndorsementCodeEnum.UBI.equals(codeEnum)) {
			return true;
		}
		return false;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#obtainCoverageCode(com.ing.canada.plp.domain.
	 * insuranceriskoffer.CoverageOffer)
	 */
	@Override
	protected String obtainCoverageCode(CoverageOffer aCoverageOffer) {
		String theCoverageCode = aCoverageOffer.getCoverageRepositoryEntry().getCoverageCode();
		return theCoverageCode;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#mappingEndorsementKey(java.lang.String)
	 */
	@Override
	protected String mappingEndorsementKey(String anEndorsementKey) {
		return anEndorsementKey;
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#loadAllEndorsementsSelectedFromOffer(com.intact
	 * .brokeroffice.controller.viewquote.offer.VehicleOfferBean, java.util.Map)
	 */
	@Override
	protected void loadAllEndorsementsSelectedFromOffer(VehicleOfferBeanQC aVehicleOfferBean,
			Map<String, CoverageOfferBean> mapEndorsementOptions) {
		for (CoverageOfferBean bean : aVehicleOfferBean.getEndorsementSelectedList()) {
			if (!mapEndorsementOptions.containsKey(bean.getCoverageCode())) {
				mapEndorsementOptions.put(bean.getCoverageCode(), bean);
			}
		}
	}

	/*
	 * (non-Javadoc)
	 * 
	 * @see
	 * com.intact.brokeroffice.controller.viewquote.offer.OfferAdapter#validateCoverageInformation(com.intact.brokeroffice
	 * .controller.viewquote.offer.CoverageOfferBean, com.ing.canada.plp.domain.insuranceriskoffer.CoverageOffer)
	 */
	@Override
	protected void loadCoverageInformation(CoverageOfferBean covOfferBean, CoverageOffer covOff) {
		if (covOfferBean.getCoverageSelectedIndicator() == null
				|| !covOfferBean.getCoverageSelectedIndicator().booleanValue()) {
			covOfferBean.setCoverageSelectedIndicator(covOff.getCoverageSelectedIndicator());
		}
	}

	/**
	 * Loads the annual or monthly premium with taxes and/or surcharges according to the province for an offerBean
	 * 
	 * @param anOfferBean
	 * @param aPolicyTermInMonths
	 * @param anInsuranceRiskOffer
	 * @param aProvinceCode
	 */
	public void loadOfferBean(OfferBean anOfferBean, InsuranceRiskOffer anInsuranceRiskOffer,
			ProvinceCodeEnum aProvinceCode) {
		
		if (anOfferBean.getAnnualPremium() != null) {
			QuoteCalculationDetails quoteCalculationDetails = this.quotationService
					.loadOfferBean(anOfferBean.getAnnualPremium(), anInsuranceRiskOffer, aProvinceCode, 0, false);

			this.quotationService.addSurchargeToOfferBeforeTaxes(quoteCalculationDetails, SURCHARGE_PERCENTAGE_QC);

			anOfferBean.setAnnualPremiumWithTaxes(quoteCalculationDetails.getAnnualAmountWithTaxes());
			anOfferBean.setMonthlyPremiumWithTaxes(quoteCalculationDetails.getMonthlyPaymentWithTaxes());
		}
	}
}
