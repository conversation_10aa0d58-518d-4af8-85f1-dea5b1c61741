package com.intact.brokeroffice.controller.helper;

import java.util.ArrayList;
import java.util.List;

import jakarta.faces.model.SelectItem;
import jakarta.servlet.http.HttpSession;

import org.apache.commons.lang.StringUtils;

import com.ing.canada.cif.domain.enums.CifCompanyEnum;
import com.ing.canada.plp.domain.enums.ProvinceCodeEnum;
import com.intact.brokeroffice.controller.enums.SessionConstantsEnum;
import com.intact.brokeroffice.controller.province.ProvinceClusterEnum;
import com.intact.brokeroffice.controller.province.ProvinceController;
import com.intact.brokeroffice.util.ProvinceCompanyConverter;

public final class ProvinceHelper {

	public static ProvinceCodeEnum[] activeProvinces = ProvinceClusterEnum.ALL.getProvinces();
	public static ProvinceClusterEnum[] activeProvinceClusters = {ProvinceClusterEnum.ALL};
	
	public static SelectItem AddItemAccesLevel(String group){
		SelectItem item = new SelectItem(group, group);		
		return item;
	}
	public static SelectItem AddItemAccesLevel(String group,ProvinceCodeEnum ... provincesEnum){
		String concatenateString = AddProvinceSuffix(group,provincesEnum);		
		SelectItem item = new SelectItem(concatenateString,concatenateString);		
		return item;
	}
	
	public static SelectItem AddItemAccesLevel(String group, ProvinceClusterEnum provinceCluster, ProvinceCodeEnum ... provincesEnum) {
		String concatenateString = AddProvinceSuffix(group,provincesEnum);
		if ("".equals(concatenateString))
			concatenateString = group + "-" + provinceCluster.getCode();
		else
			concatenateString = concatenateString + " + " + group + "-" + provinceCluster.getCode();
					
		SelectItem item = new SelectItem(concatenateString,concatenateString);		
		return item;
	}
	
	public static String AddProvinceSuffix(String prefix,ProvinceCodeEnum ... provincesEnum){
		String concatenateString = "";
		for (ProvinceCodeEnum provinceCodeEnum : provincesEnum) {
			if(concatenateString.equals("")){
				concatenateString = prefix + "-" + provinceCodeEnum.getCode();	
			} else {
				concatenateString = concatenateString + " + " + prefix + "-" + provinceCodeEnum.getCode();
			}				
		}
		return concatenateString;
	}

	
	public static int manageCompanies(HttpSession aSession, String userRole, String ldapSecurityGroup){
		return manageCompanies(aSession, userRole, ldapSecurityGroup,null);
	}
	
	/**
	 * Method to manage the companies based on the provinces from the ldap security settings
	 * applicable for only ldapGroupProgramAdmins
	 * 
	 * BR5792  Only List Regions to which User has Access
	 * BR5791  Display Select Region List
	 * 
	 * @param httpServletRequest
	 * @param userRole
	 * @param ldapSecurityGroup
	 */
	public static int manageCompanies(HttpSession aSession, String userRole, String ldapSecurityGroup,ProvinceController provinceController){
		List<String> availableCompanies = new ArrayList<String>();
		String currentCompany = null;
		
		for (ProvinceCodeEnum provinceCodeEnum : ProvinceHelper.activeProvinces) {
			if(StringUtils.contains(userRole, "-" + provinceCodeEnum.getCode() )){
				currentCompany = ProvinceCompanyConverter.convertProvinceToSubBrokerCompany(provinceCodeEnum.getCode());
				if (!availableCompanies.contains(currentCompany))
					availableCompanies.add(currentCompany);
			}
		}
		
		for(ProvinceClusterEnum provinceClusterEnum : ProvinceHelper.activeProvinceClusters) {
			if(StringUtils.contains(userRole, "-" + provinceClusterEnum.getCode())) {
				for (ProvinceCodeEnum provinceCodeEnum : provinceClusterEnum.getProvinces()) {
					currentCompany = ProvinceCompanyConverter.convertProvinceToSubBrokerCompany(provinceCodeEnum.getCode());
					if (!availableCompanies.contains(currentCompany))
						availableCompanies.add(currentCompany);				
				}
			}
		}
		
		// Display Select Companies List	
		// Only List Companies to which User has Access
		if(availableCompanies.size() > 1){				
			aSession.setAttribute(SessionConstantsEnum.AVAILABLE_COMPANIES.getSessionConstant(), availableCompanies);
			//Retrieve COOKIE of the default province			
		}
		else if(availableCompanies.size() == 1){	
			aSession.removeAttribute(SessionConstantsEnum.AVAILABLE_COMPANIES.getSessionConstant());
			aSession.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), availableCompanies.get(0));
			if(provinceController != null){
				provinceController.reset();
			}
		}else{
			aSession.removeAttribute(SessionConstantsEnum.AVAILABLE_COMPANIES.getSessionConstant());
			aSession.setAttribute(SessionConstantsEnum.COMPANY.getSessionConstant(), CifCompanyEnum.INTACT_QC.getSubBrokerCompanyNumber());
			if(provinceController != null){
				provinceController.reset();
			}
		}
		return availableCompanies.size();
		
	}
	
	public static boolean containAccessLevelAndProvinces(String SelectedAccessLevel,String AccessLevel,ProvinceCodeEnum ... provincesEnum){		
		if(SelectedAccessLevel.contains(AccessLevel)){
			for (ProvinceCodeEnum provinceCodeEnum : provincesEnum) {
				if(!SelectedAccessLevel.contains("-" + provinceCodeEnum.getCode())){
					return false;
				}
			}
		}else{
			return false;
		}
		return true;
		
	}

}
