package com.intact.brokeroffice.service;

import com.intact.plt.information.service.client.ClientInformationApplicationService;
import com.intact.plt.information.service.client.util.WSPLTInformationEndpoint;

import java.net.MalformedURLException;

public class WebzoneClientInformationService extends ClientInformationApplicationService {

    private WSPLTInformationEndpoint wspltInformationEndpoint;
    public WebzoneClientInformationService() {
        super();
    }

    @Override
    public synchronized  WSPLTInformationEndpoint getWSPLTInformation() throws MalformedURLException {
        if(wspltInformationEndpoint == null){
            wspltInformationEndpoint = super.getWSPLTInformation();
        }
        return wspltInformationEndpoint;
    }
}
