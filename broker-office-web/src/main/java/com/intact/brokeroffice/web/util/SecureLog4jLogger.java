package com.intact.brokeroffice.web.util;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.apache.log4j.Priority;

public class SecureLog4jLogger extends org.apache.log4j.Logger {
	
	private String [] IMMUNE_LOG_KEY = new String[] {"\"", "&", "<", ">", "'", ";", "{", "}", "\r", "\n" };
	private String [] IMMUNE_LOG_VALUE = new String[] {"&#34;", "&#38;", "&#60;", "&#62;", "&#39;", "&#59;", "&#123;", "&#125;", "_", "_" };

	static String FQCN = new StringBuilder(SecureLog4jLogger.class.getName()).append(".").toString();

	private static SecureLog4jLogFactory factory = new SecureLog4jLogFactory();
	
	@Override
	public void trace(Object message, Throwable t) {
		super.log(FQCN, Level.TRACE, sanitize(message), t);
	}

	@Override
	public void trace(Object message) {
		super.log(FQCN, Level.TRACE, sanitize(message), null);
	}

	@Override
	public void debug(Object message, Throwable t) {
		super.log(FQCN, Level.DEBUG, sanitize(message), t);
	}

	@Override
	public void debug(Object message) {
		super.log(FQCN, Level.DEBUG, sanitize(message), null);
	}

	@Override
	public void error(Object message, Throwable t) {
		super.log(FQCN, Level.ERROR, sanitize(message), t);
	}

	@Override
	public void error(Object message) {
		super.log(FQCN, Level.ERROR, sanitize(message), null);
	}

	@Override
	public void fatal(Object message, Throwable t) {
		super.log(FQCN, Level.FATAL, sanitize(message), t);
	}

	@Override
	public void fatal(Object message) {
		super.log(FQCN, Level.FATAL, sanitize(message), null);
	}

	@Override
	public void info(Object message, Throwable t) {
		super.log(FQCN, Level.INFO, sanitize(message), t);
	}

	@Override
	public void info(Object message) {
		super.log(FQCN, Level.INFO, sanitize(message), null);
	}

	@Override
	public void warn(Object message, Throwable t) {
		super.log(FQCN, Level.WARN, sanitize(message), t);
	}

	@Override
	public void warn(Object message) {
		super.log(FQCN, Level.WARN, sanitize(message), null);
	}

	protected Object sanitize(Object logEntry) {
		return this.sanitizeLogEntry("" + logEntry);
	}
	
	public String sanitizeLogEntry(String logEntry) {

		String cleanLogEntry = null;

		if (logEntry != null) {
			cleanLogEntry = StringUtils.replaceEach(logEntry, IMMUNE_LOG_KEY, IMMUNE_LOG_VALUE).trim();
		}
		
		return cleanLogEntry;
	}

	protected SecureLog4jLogger(String name) {
		super(name);
	}

	public static SecureLog4jLogger getLogger(String name) {
		return (SecureLog4jLogger) Logger.getLogger(name, factory);
	}

	@SuppressWarnings("rawtypes")
	// because eclipse requires T type
	public static SecureLog4jLogger getLogger(Class clazz) {
		return (SecureLog4jLogger) Logger.getLogger(clazz.getName(), factory);
	}

	@Override
	public void log(String callerFQCN, Priority level, Object message, Throwable t) {
		super.log(callerFQCN, level, this.sanitize(message), t);
	}

}
