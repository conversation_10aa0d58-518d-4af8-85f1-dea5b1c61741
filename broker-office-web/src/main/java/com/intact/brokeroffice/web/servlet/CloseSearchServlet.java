package com.intact.brokeroffice.web.servlet;

import java.io.IOException;
import java.io.Serial;

import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServlet;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.servlet.http.HttpSession;

import com.intact.brokeroffice.controller.tabs.QuotesTabController;

public class CloseSearchServlet extends HttpServlet {

	@Serial
	private static final long serialVersionUID = 5581932965028204390L;

	public void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {

		HttpSession session = req.getSession(false);

		if (session != null) {

			try {
				QuotesTabController controller = (QuotesTabController) session.getAttribute("quotesTabController");

				if (controller != null) {
					controller.getSearchStates().remove(Integer.parseInt(req.getParameter("index")));
				}
			} catch (Exception ignored) {
			}
		}
	}

}
