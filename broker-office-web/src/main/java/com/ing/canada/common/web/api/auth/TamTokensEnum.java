/*
 * Important notice: This software is the sole property of Intact insurance Inc. and
 * cannot be distributed and/or copied without the written permission of Intact insurance
 * Inc.
 *
 * Copyright (c) 2009, Intact insurance Inc., All rights reserved.
 */
package com.ing.canada.common.web.api.auth;

import java.util.EnumSet;
import java.util.HashMap;
import java.util.Map;

/**
 * Tokens provided by TAM for authentication. They are found in HTTP request headers.
 * Copied from the Common API Project since it was archived, and we only need this enum.
 * 
 * <AUTHOR> <PERSON>u
 * 
 */
public enum TamTokensEnum {
	TAM_CIF_ID("ingcifclientid"), TAM_SECURITY_ROLES_ATTRIB("iv-groups"), TAM_USER_NAME_CREDENTIAL("iv-user"), TAM_USER_CLASSIC_USER_ID(
			"classicid"), /*TAM_USER_HALCION_USER_ID("halcionid"),*/ TAM_UNIQUE_ID("uniqueidentifier"), TAM_SERVER_NAME("iv_server_name");

	private static final Map<String, TamTokensEnum> lookup = new HashMap<String, TamTokensEnum>();

	static {
		for (TamTokensEnum t : EnumSet.allOf(TamTokensEnum.class)) {
			lookup.put(t.getTokenValue(), t);
		}
	}

	/** The token value. */
	private String tokenValue;

	private TamTokensEnum(String tokenValueToSet) {
		this.tokenValue = tokenValueToSet;
	}

	public String getTokenValue() {
		return tokenValue;
	}

	public static TamTokensEnum get(String tokenValue) {
		return lookup.get(tokenValue);
	}
}
