<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:aop="http://www.springframework.org/schema/aop" xmlns:jee="http://www.springframework.org/schema/jee"
	   xmlns:tx="http://www.springframework.org/schema/tx"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans
                           http://www.springframework.org/schema/beans/spring-beans.xsd
                           http://www.springframework.org/schema/aop
                           http://www.springframework.org/schema/aop/spring-aop.xsd
                           http://www.springframework.org/schema/context
                           http://www.springframework.org/schema/context/spring-context.xsd
                           http://www.springframework.org/schema/jee
                           http://www.springframework.org/schema/jee/spring-jee.xsd
                           http://www.springframework.org/schema/tx
                           http://www.springframework.org/schema/tx/spring-tx.xsd">
	<aop:config>
		<aop:aspect id="aspect.logging.application" ref="logging.aspect">
			<aop:pointcut id="pointcut.application" expression="execution(* com.intact.brokeroffice.service.IBrokerService.*(..))" />
			<aop:before method="logApplication" pointcut-ref="pointcut.application" />
		</aop:aspect>
	</aop:config>

	<aop:config>
		<aop:aspect id="aspect.logging.error" ref="logging.aspect">
			<aop:pointcut id="pointcut.error" expression="execution(* com.intact.brokeroffice.service.IBrokerService.*(..))" />
			<aop:around method="logError" pointcut-ref="pointcut.error" />
		</aop:aspect>
	</aop:config>

	<aop:config>
		<aop:aspect id="aspect.logging.time" ref="logging.aspect">
			<aop:pointcut id="pointcut.time" expression="execution(* com.intact.brokeroffice.service.IBrokerService.*(..))" />
			<aop:around method="logTime" pointcut-ref="pointcut.time" />
		</aop:aspect>
	</aop:config>

	<aop:config>
		<aop:aspect id="aspect.caching.domain" ref="caching.aspect">
			<aop:pointcut id="pointcut.cache.common" expression="execution(* com.ing.canada.cif.service.IBrokerService.retrieve*(..))" />
			<aop:around method="cacheDomain"  pointcut-ref="pointcut.cache.common" />
		</aop:aspect>
	</aop:config>

	<!-- Atomikos Transaction Manager -->
	<bean id="atomikosTransactionManager" class="com.atomikos.icatch.jta.UserTransactionManager" init-method="init" destroy-method="close">
		<property name="forceShutdown" value="true"/>
		<!--        <property name="startupTimeout" value="60"/>-->
	</bean>

	<!-- Atomikos User Transaction -->
	<bean id="atomikosUserTransaction" class="com.atomikos.icatch.jta.UserTransactionImp">
		<property name="transactionTimeout" value="300"/>
	</bean>

	<!-- JTA Transaction Manager -->
	<bean id="transactionManager" class="org.springframework.transaction.jta.JtaTransactionManager">
		<property name="transactionManager" ref="atomikosTransactionManager"/>
		<property name="userTransaction" ref="atomikosUserTransaction"/>
	</bean>

	<!-- Enable Transaction Annotations -->
	<tx:annotation-driven transaction-manager="transactionManager"/>

	<tx:advice id="advice.transaction.plp">
		<tx:attributes>
			<tx:method name="retrieve*" read-only="true" />
			<tx:method name="*" propagation="REQUIRES_NEW" />
		</tx:attributes>
	</tx:advice>

	<aop:config>
		<aop:pointcut id="pointcut.transaction.plp" expression="execution(* com.intact.business.service.broker.common.BaseBrokerService.reassignQuotes(..))" />
		<aop:advisor pointcut-ref="pointcut.transaction.plp" advice-ref="advice.transaction.plp" />
	</aop:config>

	<bean id="caching.aspect" class="com.intact.brokeroffice.service.util.CachingAspect" />
	<bean id="logging.aspect" class="com.intact.brokeroffice.service.util.LoggingAspect" />

</beans>
