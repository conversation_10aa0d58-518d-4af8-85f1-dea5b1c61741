<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
  ~ without the written permission of Intact Insurance
  ~
  ~ Copyright (c) 2013 Intact Insurance, All rights reserved.
  -->
<beans xmlns="http://www.springframework.org/schema/beans"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xmlns:context="http://www.springframework.org/schema/context"
  xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans.xsd
						http://www.springframework.org/schema/context
						http://www.springframework.org/schema/context/spring-context.xsd">

  <context:annotation-config/>
  <context:component-scan base-package="com.intact.brokeroffice"/>

  <!-- property placeholder post-processor -->
  <bean id="plp-placeholderConfig"
    class="org.springframework.context.support.PropertySourcesPlaceholderConfigurer">
    <property name="locations">
      <list>
        <value>classpath:plp-services.properties</value>
      </list>
    </property>
  </bean>

  <!-- property placeholder post-processor -->
  <bean id="brokeroffice-placeholderConfig"
    class="org.springframework.context.support.PropertySourcesPlaceholderConfigurer">
    <property name="location" value="classpath:brokerOffice-web.properties"/>
    <property name="placeholderPrefix" value="$("/>
    <property name="placeholderSuffix" value=")"/>
    <property name="properties">
      <props>
        <prop key="quote-uploader-enabled">false</prop>
      </props>
    </property>
  </bean>

  <bean id="pdf.url" class="java.lang.String">
    <constructor-arg value="$(pdf.url)"/>
  </bean>

  <bean id="homeUploadRestURL" class="java.lang.String">
    <constructor-arg value="$(homeUploadRestURL)"/>
  </bean>

  <bean id="homeUploadRestAPIKey" class="java.lang.String">
    <constructor-arg value="$(homeUploadRestAPIKey)"/>
  </bean>

  <bean id="dialerRestURL" class="java.lang.String">
    <constructor-arg value="$(dialerRestURL)"/>
  </bean>

  <bean id="dialerRestAPIKey" class="java.lang.String">
    <constructor-arg value="$(dialerRestAPIKey)"/>
  </bean>

  <bean id="bloomMqServiceUrl" class="java.lang.String">
    <constructor-arg value="$(bloomMqServiceUrl)"/>
  </bean>

  <bean id="plp-groupLockManager" class="com.ing.canada.plp.lock.InsurancePolicyLockManager"
    scope="prototype">
    <property name="enabled" value="${optimistic.group.lock.enabled}"/>
  </bean>

  <bean id="ldap-group-broker" class="java.lang.String">
    <constructor-arg value="$(ldap-group-broker)"/>
  </bean>

  <bean id="ldap-group-underwritter" class="java.lang.String">
    <constructor-arg value="$(ldap-group-underwritter)"/>
  </bean>

  <bean id="ldap-group-quote-admins" class="java.lang.String">
    <constructor-arg value="$(ldap-group-quote-admins)"/>
  </bean>

  <bean id="ldap-group-program-admins" class="java.lang.String">
    <constructor-arg value="$(ldap-group-program-admins)"/>
  </bean>

  <bean id="ldap-group-admins" class="java.lang.String">
    <constructor-arg value="$(ldap-group-admins)"/>
  </bean>

  <bean id="spoe-mode" class="java.lang.String">
    <constructor-arg value="$(spoe-mode)"/>
  </bean>

  <bean id="logout-url-b2b" class="java.lang.String">
    <constructor-arg value="$(logout-url-b2b)"/>
  </bean>

  <bean id="logout-url-b2e" class="java.lang.String">
    <constructor-arg value="$(logout-url-b2e)"/>
  </bean>

  <bean id="nfs-url-dir" class="java.lang.String">
    <constructor-arg value="$(nfs-url-dir)"/>
  </bean>

  <bean id="nfs-server-mode" class="java.lang.String">
    <constructor-arg value="$(nfs-server-mode)"/>
  </bean>

  <bean id="ldap-group-broker-reassign" class="java.lang.String">
    <constructor-arg value="$(ldap-group-broker-reassign)"/>
  </bean>

  <bean id="quoteUploaderEnabled" class="java.lang.Boolean">
    <constructor-arg value="$(quote-uploader-enabled)"></constructor-arg>
  </bean>

  <bean id="maxSearchRequests" class="java.lang.Integer">
    <constructor-arg value="5"/>
  </bean>

  <bean id="autoSearchRefreshInterval" class="java.lang.Integer">
    <constructor-arg value="300000"/>
  </bean>

  <bean id="accesses.map" class="com.intact.brokeroffice.service.util.Configuration">
    <property name="configs">
      <!-- 	        <map key-type="java.lang.String" value-type="java.lang.String"> -->
      <!-- 	            <entry key="search.extended.ON" value="true" /> -->
      <!-- 	            <entry key="search.extended.AB" value="true" /> -->
      <!-- 	     	</map> -->
      <map key-type="java.lang.String" value-type="java.lang.String">
        <entry key="search.extended.6" value="true"/>
        <entry key="search.extended.3" value="true"/>
      </map>
    </property>
  </bean>

  <bean id="autoRoadBlocksMap" class="com.intact.brokeroffice.service.util.Configuration">
    <property name="configs">
      <map key-type="java.lang.String" value-type="java.lang.String">
        <entry key="MSG7326" value="not_canadian.png"/>
        <entry key="MSG202" value="intact-client.png"/>
        <entry key="MSG203" value="intact-client.png"/>
        <entry key="MSG528" value="intact-client.png"/>
        <entry key="MSG139" value="intact-client.png"/>
        <entry key="MSG531" value="intact-client.png"/>
        <entry key="MSG532" value="intact-client.png"/>
        <entry key="default" value="wrong-way.png"/>
      </map>
    </property>
  </bean>

  <bean id="homeRoadBlocksMap" class="com.intact.brokeroffice.service.util.Configuration">
    <property name="configs">
      <map>
        <entry key="WQ00180" value="intact-client.png"/>
        <entry key="WQ00201" value="intact-client.png"/>
        <entry key="WQ00203" value="intact-client.png"/>
        <entry key="default" value="wrong-way.png"/>
      </map>
    </property>
  </bean>

  <bean id="max.row.company" class="com.intact.brokeroffice.service.util.Configuration">
    <property name="configs">
      <map key-type="java.lang.String" value-type="java.lang.Integer">
        <entry key="A" value="500"/>
        <entry key="6" value="500"/>
        <entry key="3" value="200"/>
      </map>
    </property>
  </bean>

  <bean id="pages.view.quote" class="com.intact.brokeroffice.service.util.Configuration">
    <property name="configs">
      <map key-type="java.lang.String" value-type="java.lang.String">
        <entry key="PC" value="pages/quotes/viewQuote/viewQuote.jsf"/>
        <entry key="BA" value="pages/quotes/viewQuote/viewQuote.jsf"/>
        <entry key="BH" value="pages/quotes/viewQuote/viewQuote.jsf"/>
        <entry key="BC" value="pages/quotes/viewQuote/viewQuote.jsf"/>
        <entry key="BT" value="pages/quotes/viewQuote/viewQuote.jsf"/>
        <entry key="QH" value="pages/quotes/viewQuote/viewQuote.jsf"/>
        <entry key="QT" value="pages/quotes/viewQuote/viewQuote.jsf"/>
        <entry key="QC" value="pages/quotes/viewQuote/viewQuote.jsf"/>
        <entry key="QA" value="pages/quotes/viewQuote/viewQuoteFromPLT.jsf"/>
        <entry key="QF" value="pages/quotes/viewQuote/viewQuoteFromPLT.jsf"/>
        <entry key="IR" value="pages/quotes/viewQuote/viewQuoteFromPLT.jsf"/>
      </map>
    </property>
  </bean>


  <bean id="uploadStatuses" class="com.intact.brokeroffice.service.util.Configuration">
    <property name="configs">
      <map key-type="java.lang.String" value-type="java.lang.Boolean">
        <entry key="Q-ACC" value="true"/>
        <entry key="Q-REF" value="true"/>
        <entry key="R-UPL" value="true"/>
        <entry key="R-ACC" value="true"/>
        <entry key="R-REF" value="true"/>
        <entry key="default" value="false"/>
      </map>
    </property>
  </bean>

  <bean id="time.flag.config" class="com.intact.brokeroffice.service.util.Configuration">
    <property name="configs">
      <map key-type="java.lang.String" value-type="java.lang.Long">
        <entry key="A-SMALL" value="1800000"/>
        <entry key="A-MEDIUM" value="7200000"/>
        <entry key="A-LONG" value="-1"/>
        <entry key="6-SMALL" value="300000"/>
        <entry key="6-MEDIUM" value="600000"/>
        <entry key="6-LONG" value="-1"/>
        <entry key="3-SMALL" value="300000"/>
        <entry key="3-MEDIUM" value="600000"/>
        <entry key="3-LONG" value="-1"/>
      </map>
    </property>
  </bean>

  <bean id="plt.information.keys" class="com.intact.brokeroffice.service.util.Configuration">
    <property name="configs">
      <map key-type="java.lang.String" value-type="java.lang.String">
        <entry key="tooltip-QF" value="webzone.tooltip.info.personal.auto"/>
        <entry key="tooltip-QA" value="webzone.tooltip.info.personal.auto"/>
        <entry key="tooltip-QH" value="webzone.tooltip.info.personal.home"/>
        <entry key="tooltip-QT" value="webzone.tooltip.info.personal.home"/>
        <entry key="tooltip-QC" value="webzone.tooltip.info.personal.home"/>
        <entry key="tooltip-BA" value="webzone.tooltip.info.personal.car"/>
        <entry key="tooltip-IR" value="webzone.tooltip.info.commercial.auto"/>
        <entry key="tooltip-BH" value="webzone.tooltip.info.personal.home"/>
        <entry key="tooltip-BT" value="webzone.tooltip.info.personal.home"/>
        <entry key="tooltip-BC" value="webzone.tooltip.info.personal.home"/>
        <entry key="tooltip-PC" value="webzone.tooltip.info.commercial.home"/>

        <entry key="client-QF" value="webzone.client.info.personal.auto"/>
        <entry key="client-QA" value="webzone.client.info.personal.auto"/>
        <entry key="client-QH" value="webzone.client.info.personal.home"/>
        <entry key="client-QT" value="webzone.client.info.personal.home"/>
        <entry key="client-QC" value="webzone.client.info.personal.home"/>
        <entry key="client-BA" value="webzone.client.info.personal.car"/>
        <entry key="client-IR" value="webzone.client.info.commercial.auto"/>
        <entry key="client-BH" value="webzone.client.info.personal.home"/>
        <entry key="client-BC" value="webzone.client.info.personal.home"/>
        <entry key="client-BT" value="webzone.client.info.personal.home"/>
        <entry key="client-PC" value="webzone.client.info.commercial.home"/>


        <entry key="driver-AU-P" value="webzone.driver.info"/>
        <entry key="driver-AU-C" value="webzone.driver.info.cl"/>
      </map>
    </property>
  </bean>

<!--  <bean id="interestChangeDate" class="org.apache.commons.lang.time.DateUtils"-->
<!--    factory-method="parseDate">-->
<!--    <constructor-arg type="java.lang.String" value="$(interest_date_on)"/>-->
<!--    <constructor-arg>-->
<!--      <list>-->
<!--        <value>yyyy-MM-dd</value>-->
<!--      </list>-->
<!--    </constructor-arg>-->
<!--  </bean>-->

<!--  <bean id="ontario-interest-change-date" class="java.util.Date">-->
<!--    <constructor-arg>-->
<!--      <bean factory-bean="interestChangeDate" factory-method="getTime"/>-->
<!--    </constructor-arg>-->
<!--  </bean>-->


</beans>

