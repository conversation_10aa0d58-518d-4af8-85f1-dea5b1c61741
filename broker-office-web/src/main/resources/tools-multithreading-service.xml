<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xmlns:context="http://www.springframework.org/schema/context"
	   xmlns:aop="http://www.springframework.org/schema/aop"
		xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd
							http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
							http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
		
		<bean class="com.intact.tools.multithreading.service.standard.StandardMultiThreadingApplicationService">
			<property name="configurations">
				<map>
					<entry key="search">
						<bean class="com.intact.tools.multithreading.service.standard.util.Configuration">
							<property name="numberOfThreads" value="7" />
							<property name="maxThreadTimeout" value="30" />
						</bean>
					</entry>
				</map>
			</property>
		</bean>					
</beans>						