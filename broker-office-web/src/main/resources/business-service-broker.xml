<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	   xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

	<bean id="cifBrokersService" class="com.ing.canada.cif.service.impl.BrokerService"/>
	<bean id="brmUserAccountService" class="com.intact.canada.brm.service.impl.UserAccountService"/>
	<bean id="brmAccessProfileService" class="com.intact.canada.brm.service.impl.AccessProfileService"/>

	<bean id="brokeroffice-placeholderConfig"
		  class="org.springframework.context.support.PropertySourcesPlaceholderConfigurer">
		<property name="locations">
			<list>
				<value>classpath:brokerOffice-web.properties</value>
				<value>classpath:plt-service.properties</value>
			</list>
		</property>
	</bean>

	<bean id="zzz" class="com.intact.brokeroffice.service.search.MultiThreadBrokerService">
		<property name="services">
			<bean class="com.intact.business.service.broker.common.Configuration">
				<property name="configs">
					<map>
						<!-- base list for service calls for list and search -->
						<entry key="webzone.P.RE.A" value-ref="business.service.broker.plt"/>
						<entry key="webzone.P.RE.6" value-ref="business.service.broker.plt"/>
						<entry key="webzone.P.RE.3" value-ref="business.service.broker.plt"/>
						<entry key="webzone.P.AU.6.LEGACY" value-ref="business.service.broker.plp"/>
						<entry key="webzone.P.AU.3.LEGACY" value-ref="business.service.broker.plp"/>
						<entry key="webzone.P.AU.3" value-ref="business.service.broker.plt"/>
						<entry key="webzone.P.AU.6" value-ref="business.service.broker.plt"/>
						<entry key="webzone.P.AU.A" value-ref="business.service.broker.plp"/>
						<entry key="webzone.C.AU.3" value-ref="business.service.broker.plp"/>
						<entry key="webzone.C.AU.6" value-ref="business.service.broker.plp"/>
						<entry key="webzone.C.AU.A" value-ref="business.service.broker.plp"/>
						<entry key="webzone.C.RE.3" value-ref="business.service.broker.plt"/>
						<entry key="webzone.C.RE.6" value-ref="business.service.broker.plt"/>
						<entry key="webzone.C.RE.A" value-ref="business.service.broker.plt"/>

						<!-- base list for service calls for view quote depending on application mode -->
						<entry key="webzone.PC" value-ref="business.service.broker.plt"/>
						<entry key="webzone.BH" value-ref="business.service.broker.plt"/>
						<entry key="webzone.BT" value-ref="business.service.broker.plt"/>
						<entry key="webzone.BC" value-ref="business.service.broker.plt"/>
						<entry key="webzone.QA" value-ref="business.service.broker.plp"/>
						<entry key="webzone.QF" value-ref="business.service.broker.plp"/>
						<entry key="webzone.QH" value-ref="business.service.broker.plt"/>
						<entry key="webzone.QT" value-ref="business.service.broker.plt"/>
						<entry key="webzone.QC" value-ref="business.service.broker.plt"/>
						<entry key="webzone.IR" value-ref="business.service.broker.plp"/>
						<entry key="webzone.BA" value-ref="business.service.broker.plt"/>

						<!-- base list for service calls for upload quote  -->
						<entry key="upload.P.RE" value-ref="business.service.broker.plt"/>
						<entry key="upload.P.AU" value-ref="business.service.broker.plt"/>
						<entry key="upload.C.AU" value-ref="business.service.broker.plt"/>
					</map>
				</property>
			</bean>
		</property>
		<property name="threadTimeout" value="${thread.max.timeout}"/>
	</bean>

	<bean id="business.service.broker.plp"
		  class="com.intact.business.service.broker.plp.PLPBrokerService"/>

	<bean id="business.service.broker.plt"
		  class="com.intact.business.service.broker.plt.PLTBrokerService">
		<property name="url" value="${plt.broker.ws.url}"/>
	</bean>

</beans>				
