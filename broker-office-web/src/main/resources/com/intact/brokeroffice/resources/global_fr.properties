#
# ----------------------------------------------------------
# This file will contains labels common to both provinces.
# ----------------------------------------------------------

# Common buttons
# ----------------------------------------------------------
button.add               = Ajouter
button.back              = Précédent
button.cancel            = Annuler
button.close             = Fermer
button.confirm           = Confirmer
button.continue          = Continuer &gt;
button.signout           = Quitter
button.modify            = Modifier
button.no                = Non
button.ok                = Ok
button.print			 = Imprimer
button.previous          = &lt; Précédent 
button.remove            = Supprimer
button.reset             = Reset
button.return            = Retour
button.save				 = Sauvegarder
button.submit            = Soumettre
button.yes               = Oui
button.quit				 = Quitter
button.delete			 = Supprimer

common.windowTitle    = Broker Office
common.tab.quotes	  = SOUMISSIONS
common.tab.brokers	  = GESTION D'ACCÈS
common.tab.reports	  = RAPPORTS
common.tab.subbroker  = GESTION DES POINTS DE VENTE (PDV)
common.tab.account    = GESTION UTILISATEURS
common.tab.fsa		  = GESTION DES RTA

global.close          = Fermer
global.default.select = Sélectionnez ...
global.no             = Non
global.yes            = Oui
global.true			  = Oui
global.false		  = Non
global.female         = Femme
global.male           = Homme

logout=Vous avez été déconnecté avec succès.
logout.link=Cliquez ici pour vous connecter à nouveau

# Error page
# ----------------------------------------------------------
global.copyright.footer = Intact Compagnie d'assurance - Tous droits réservés.

# Icones alt text
# ---------------------------------------------------------- 
icons.help = Aide

# Validation error
# ----------------------------------------------------------
validation.error.title=*Veuillez vérifier le(s) champs(s) avec la note explicative en rouge.

# Error page (error.jsp)
# ---------------------------------------------------------- 
error.title=Problème technique
error.line.1=Un problème technique nous empêche de continuer. Nous allons y remédier dans les plus brefs délais. Nous vous invitons à réessayer plus tard.
error.line.2=Merci de votre compréhension.

# Session expired page (sessionExpired.jsp)
# ----------------------------------------------------------
sessionExpired.title=Session expirée
sessionExpired.message=Votre session est expirée.

# Language
# ---------------------------------------------------------- 
suffix = _fr
language_full = français
language.english = EN
language.french = FR
language.switch = English
en=Anglais
fr=Français
 
# Data scroller
# ---------------------------------------------------------- 
ds.header.display=Afficher
ds.header.perPage=par page
ds.footer.pageOf=Page {0} de {1}
ds.footer.next=Suivant >>
ds.footer.previous=<< Précédent
ds.footer.first=<<
ds.footer.last=>>


# Quote status
# ---------------------------------------------------------- 
QUOTE_INCOMPLETE=Soumission incomplète
QUOTE_COMPLETE=Soumission effectuée
BIND_INCOMPLETE=Achat incomplet
BOUND=Achat effectué
EXPIRED=Expirée
UPLOADED=Téléchargée
UPLOADED_ACCEPTED=Acceptée
UPLOADED_REFUSED=Refusée
PDF_DOWNLOADED=PDF Téléchargé

global.image.relative=/image/fr
global.image=/image/
form.button.search=Rechercher
form.button.update=Mettre à jour
form.button.updateAll=Mettre à jour tous les points de ventes
form.button.cancel=Annuler
form.button.back=Retour
form.button.reassign=Réassigner les soumissions


ONTARIO	= Ontario
QUEBEC 	= Québec
ALBERTA = Alberta
BRITISH_COLUMBIA = Colombie Britannique
MANITOBA = Manitoba
NEW_BRUNSWICK = Nouveau Brunswick
NEWFOUNDLAND_AND_LABRADOR = Terre-Neuve Et Labrador 
NOVA_SCOTIA = Nouvelle Écosse
PRINCE_EDWARD_ISLAND = Ile Du Prince Édouard
SASKATCHEWAN = Saskatchewan
NORTHWEST_TERRITORIES = Territoires Du Nord-Ouest
YUKON = Yukon
NUNAVUT = Nunavut
region	= Région

month.0=Janvier
month.1=Février
month.2=Mars
month.3=Avril
month.4=Mai
month.5=Juin
month.6=Juillet
month.7=Août
month.8=Septembre
month.9=Octobre
month.10=Novembre
month.11=Décembre

driver.type.residence.HO=Maison
driver.type.residence.TN=Locataire
driver.type.residence.CO=Condo

global.alternate.waiting=Veuilliez attendre SVP.
quotes.window.title.search=Recherche #{0}
quotes.window.title.webzone=WebZone

subscription.company=Compagnie de souscription