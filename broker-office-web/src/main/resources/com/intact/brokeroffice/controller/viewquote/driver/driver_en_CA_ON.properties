#POLICY HOLDER SECTION 
#LBL5200
driver.aboutyou.permissioninfos=I give Intact Insurance and my assigned broker permission to collect, use and disclose my personal information as permitted by law, for the purpose of creating a profile in my name and providing me with a quote for automobile insurance.
driver.aboutyou.savewhenvehicleandhomeareinsuredwithus=Insure your home and automobile with us and SAVE 15% on your automobile insurance! Would you like a quote as if you had your home insurance policy with Intact Insurance?

#GENERAL INFORMATION SECTION

#DRIVING RECORD SECTION
driver.aboutyou.licencetype=Class of valid driver's licence
driver.aboutyou.agewhenlicenseobtained=When did you obtain your first licence in Canada or United States?

driver.aboutyou.ontario.G2.obtained.when= When did you obtain your Ontario G2 driver's licence?	
driver.aboutyou.ontario.G.obtained.when=When did you obtain your Ontario G driver's licence?	

driver.aboutyou.trainingcourse=Have you successfully completed a driver training course in a recognized school in the last 3 years?

driver.aboutyou.claimsorlosses=Have you had any accidents or made any claims arising from the ownership or operation of any automobile during the last 9 years (whether you were responsible or not)?
driver.aboutyou.how.many.claimsorlosses=How many accidents or accident-related claims have you had in the last 6 years ?
driver.aboutyou.howlongagolossoccur=How long ago was the claim made or accident occur?
driver.aboutyou.typeofloss=Type of accident or claim
 
driver.aboutyou.commitedinfractions=Have you had any convictions in the past 3 years? (Do not consider parking tickets.)
driver.aboutyou.conviction.date= Conviction date
driver.aboutyou.nature.conviction=Type of conviction

driver.aboutyou.class6.licence=Do you also have a Class 6 licence ?

driver.aboutyou.retired.receiving.a.pension=Are you retired?
driver.aboutyou.fulltime.student.ontario=Are you currently a full-time student in Ontario?
driver.aboutyou.good.student.ontario=Are you maintaining a grade average of 80% or higher (Grade "A" or better)?
driver.aboutyou.student.type.of.school.ontario=Are you a student in
driver.aboutyou.school.more.than.100km.from.parentshome=Is your school more than 100 kilometers from your permanent residence?

driver.aboutyou.student.type.of.school.ontario.0=College or University
driver.aboutyou.student.type.of.school.ontario.1=High School

#INSURANCE HISTORY
#  for policyHolder
driver.aboutyou.ontario.start.this.insurance.coverage=As of which date would you like your insurance coverage to begin?
driver.aboutyou.ontario.licencesuspended=In the last 6 years, have you, or any driver listed on your policy, or any other person living in your household, had their driver's licence suspended or revoked? (Do not consider suspensions due to unpaid fines.)
driver.aboutyou.monthswithoutparents=Number of months per year not living with your parents
driver.aboutyou.universitydegree=Do you have an undergraduate or graduate degree (Bachelor, Master or PhD) from a university in Canada?
driver.aboutyou.start.this.insurance.coverage
driver.aboutyou.currentlyinsured=Do you currently have auto insurance?
driver.aboutyou.insuredhowlong=How many years have you been continuously insured? 
driver.aboutyou.last.5years.with.same.insurer=Have the last 3 years been with the same insurer?
driver.aboutyou.currentinsurer=Who is your most recent insurer?

driver.aboutyou.insurancecoverage.northamerica=Have you ever had insurance coverage in North America
driver.aboutyou.howholdwhengetinsured=How old where you when you first obtained insurance coverage
driver.aboutyou.interruptioncoverage=Has there been an interruption (gap or lapse) in your insurance coverage of more than 2 years in the last 6 years ?
driver.aboutyou.outstanding.premiums.to.any.insurer=Do you currently owe outstanding premium to any insurer ?
driver.aboutyou.insurancerefusal=In the last 4 years, has your insurance policy, or that of any driver listed on your policy, or any other person living in your household, been cancelled, declined or a renewal refused by another insurance company?
driver.aboutyou.policyrefusalreason=Why was the policy cancelled?
driver.aboutyou.policyrefusal.because.of.nonpayment=Was it because of non-payment?	
driver.aboutyou.policyrefusal.how.many.times=How many times were you cancelled?	
driver.aboutyou.agreeToUseAutoRelyNetwork=Do you want to include this discount in your car quote?


#OTHER DRIVERS

driver.aboutothers.universitydegree=Does this person have an undergraduate or graduate degree(Bachelor, Master or PhD) from a university in Canada?
driver.aboutothers.retired.receiving.a.pension=Is this person retired?
driver.aboutothers.fulltime.student.ontario=Is this person currently a full-time student in Ontario?
driver.aboutothers.school.more.than.100km.from.parentshome=Is the school more than 100 kilometres from their permanent residence?
driver.aboutothers.good.student.ontario=Does this person maintain a grade average of at least an "A" (80%)?

#SECTION: DRIVING RECORD
driver.aboutothers.licencetype=Class of valid driver's licence 
driver.aboutothers.agewhenlicenseobtained=When did this person obtain their first driver's licence in Canada or United States?
driver.aboutothers.ontario.G2.obtained.when= When did this person obtain their Ontario G2 driver's licence?	
driver.aboutothers.ontario.G.obtained.when=When did this person obtain their Ontario G driver's licence?
driver.aboutothers.trainingcourse=Has this person successfully completed a driver's training course from a recognized school in the last 3 years?

# CLAIMS OR LOSSES
driver.aboutothers.claimsorlosses=Has this person had any accidents or made any claims arising from the ownership or operation of any automobile during the last 9 years (whether you were responsible or not)? 
driver.aboutothers.howlongagolossoccur=How long ago did the loss occur?
driver.aboutothers.claimamount=Claim total amount

driver.aboutothers.commitedinfractions=Has this person had any convictions in the past 3 years? (Do not consider parking tickets.) 
driver.aboutothers.conviction.date=Conviction date
driver.aboutothers.nature.conviction=Type of Conviction

driver.aboutothers.another.licencetype=Does this driver hold another driver's licence type?

driver.aboutothers.licencesuspended= Has this driver ever had their drivers licence under suspension or revocation at any time during the previous 6 years ?	
driver.aboutothers.accident.description.and.date= Please choose the option that best describes the accident and indicate in which month and year it occurred.
driver.aboutothers.driving.infractions= How many driving convictions has this driver had in the last 3 years? (excluding parking tickets)	
driver.aboutothers.driving.conviction.description.and.datePlease= choose the option that best describes the driving conviction and indicate in which month and year it occurred.	
driver.aboutothers.how.many.nonaccident.claims= How many non-accident insurance claims (like theft or windshield claims) has this driver made in the past 6 years?	
driver.aboutothers.option.that.best.describes.claim= Please choose the option that best describes the claim and indicate in which month and year it occurred.	


#Insurance History
driver.aboutothers.currentlyinsured=Does this person currently have auto insurance?
driver.aboutothers.insuredhowlong=How many years has this person been continuously insured?
driver.aboutothers.continuousyear.insurance.coverage=How many years of continuous insurance coverage has this driver had?	
driver.aboutothers.interruptioncoverage=Has there been an interruption (gap or lapse) in this driver's insurance coverage of more than 2 years in the last 6 years?	
driver.aboutothers.outstanding.premiums.to.any.insurer=Does this driver currently owe outstanding premium to any insurer?	
driver.aboutothers.insurancerefusal=Does this driver ever had their policy cancelled in the last three years?	
driver.aboutothers.policyrefusal.because.of.nonpayment=Was it because of non-payment?	
driver.aboutothers.policyrefusal.how.many.times=How many times were they cancelled?	

driver.aboutothers.firstname=First name
driver.aboutothers.lastname=Last name

driver.aboutothers.monthswithoutparents=Number of months per year not living with parents

driver.postalcode=Postal code
driver.civicnumber=Street number
driver.streetname=Street name
driver.apartmentnumber=Apartment number (if applicable)
driver.city=City
driver.province=Province
driver.country=Country

driver.address.type.POBOX=PO BOX
driver.address.type.RR=RR
driver.address.type.GD=GENERAL DELIVERY
driver.address.type.other=

driver.last.move.000=Less than 6 months
driver.last.move.006=Between 6 months and 2 years
driver.last.move.024=More than 2 years

driver.last.move.I00=Less than 1 year
driver.last.move.I12=1 year, but less than 2
driver.last.move.I24=2 years, but less than 3
driver.last.move.I36=3 years, but less than 4
driver.last.move.I48=4 years, but less than 5
driver.last.move.I60=5 years or more

driver.validation.groupcode.valid.start=As a member of 
driver.validation.groupcode.valid.end=, you benefit from belairdirect's 'Your Group Program'!

# ENUM TYPE DESCRIPTION
driver.years.insured.continuously.0=Less than 1 year
driver.years.insured.continuously.1=1 year, but less than 2
driver.years.insured.continuously.2=2 years, but less than 3
driver.years.insured.continuously.3=3 years, but less than 4
driver.years.insured.continuously.4=4 years, but less than 5
driver.years.insured.continuously.5=5 years, but less than 6
driver.years.insured.continuously.6=6 years, but less than 7
driver.years.insured.continuously.7=7 years, but less than 8
driver.years.insured.continuously.8=8 years, but less than 9
driver.years.insured.continuously.9=9 years, but less than 10
driver.years.insured.continuously.10=10 years or more

driver.license.P=Regular or probationary permit
driver.license.R=Regular or probationary permit
driver.license.L=Learner's permit
driver.license.U=Not licensed
driver.license.A=From another province/country

driver.license.class.G=Ontario G driver's licence
driver.license.class.G1=Ontario G1 driver's licence
driver.license.class.G2=Ontario G2 driver's licence

driver.license.class.R=Ontario G driver's licence
driver.license.class.L=Ontario G1 driver's licence
driver.license.class.P=Ontario G2 driver's licence
driver.license.class.U=Not licensed
driver.license.class.A=From another province/country


driver.claim.year.00=Less than 1 year
driver.claim.year.01=1 year, but less than 2 
driver.claim.year.02=2 years, but less than 3
driver.claim.year.03=3 years, but less than 4
driver.claim.year.04=4 years, but less than 5
driver.claim.year.05= 5 years, but less than 6
driver.claim.year.06=6 years, but less than 7
driver.claim.year.07=7 years, but less than 10

driver.claim.nature.AA	=	At-fault accident
driver.claim.nature.FIR	=	Fire 
driver.claim.nature.VAN	=	Vandalism
driver.claim.nature.TH	=	Theft
driver.claim.nature.GW	=	Glass replacement
driver.claim.nature.GR	=	Glass repair
driver.claim.nature.HR	=	Hit and run
driver.claim.nature.NA	=	Not-at-fault accident
driver.claim.nature.WN	=	Windstorm or hail storm
driver.claim.nature.IA	=	Impact with animal

driver.marital.status.M=Couple
driver.marital.status.C=Single

driver.month.0=January
driver.month.1=February
driver.month.2=March
driver.month.3=April
driver.month.4=May
driver.month.5=June
driver.month.6=July
driver.month.7=August
driver.month.8=September
driver.month.9=October
driver.month.10=November
driver.month.11=December

driver.conviction.type.CD=Careless driving
driver.conviction.type.CN=Criminal negligence
driver.conviction.type.DD=Dangerous driving
driver.conviction.type.DUS=Driving with suspended licence
driver.conviction.type.OMVNI=Driving without insurance
driver.conviction.type.IMP=Drug/alcohol-related offences
driver.conviction.type.FCIC=Failure to carry/produce proof of insurance
driver.conviction.type.TS=Failure to obey traffic sign/signal
driver.conviction.type.FTSSA=Failure to remain at an accident
driver.conviction.type.FRA=Failure to report an accident/damage
driver.conviction.type.FTSPO=Failure to stop for police
driver.conviction.type.SB=Failure to wear seat belt
driver.conviction.type.TFTC=Following too closely (tailgating)
driver.conviction.type.GNA=G1/G2 licence infractions - non alcohol
driver.conviction.type.GAL=G1/G2 licence infractions - alcohol
driver.conviction.type.PSB=Improper passing - school bus
driver.conviction.type.RAC=Racing
driver.conviction.type.PSG=Improper passing/speeding - schools or playgrounds
driver.conviction.type.SP1=Speeding less than 50 km/h over limit
driver.conviction.type.SP2=Speeding 50 km/h to 60 km/h over limit
driver.conviction.type.SP3=Speeding 60 kms or more over speed  limit
driver.conviction.type.FTS=Signalling offences
driver.conviction.type.UV=Unsafe vehicle
driver.conviction.type.OT1=Other minor conviction
driver.conviction.type.OT2=Other major conviction
driver.conviction.type.OT3=Other serious/criminal conviction