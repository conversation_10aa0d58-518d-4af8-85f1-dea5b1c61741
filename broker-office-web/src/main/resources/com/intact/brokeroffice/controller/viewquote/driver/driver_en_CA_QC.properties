#POLICY HOLDER SECTION 
driver.aboutyou.homePhone=Phone number (Home)

driver.aboutyou.civicnumber=Street number
driver.aboutyou.streetname=Street name
driver.aboutyou.apartmentno=Apartment number(if applicable)
driver.aboutyou.city=City 
driver.aboutyou.province=Province
driver.aboutyou.country=Country
driver.aboutyou.postalcode=Postal code
driver.aboutyou.previous.address=Previous address

driver.aboutyou.howlonglivedthere=How long have you been living at this address?
driver.aboutyou.savewhenvehicleandhomeareinsuredwithus=You save when you insure your vehicle and home together! Would you like a quote as if you had your home insurance contract with Intact Insurance? 
driver.aboutyou.type.residence=What type of residential insurance would you need?

#GENERAL INFORMATION SECTION
driver.aboutyou.occupation=What is your occupation?
driver.aboutyou.worksector=What is your work sector?

#DRIVING RECORD SECTION
driver.aboutyou.drivingrecord=Driving Record
driver.aboutyou.licencetype=Type of valid driver's licence
driver.aboutyou.agewhenlicenseobtained=How old were you when you got this licence? 
driver.aboutyou.trainingcourse=Have you successfully completed a driver training course?

driver.aboutyou.claimsorlosses=Have you had claims or losses (at fault or not, declared or not) in the past 6 years?
driver.aboutyou.claimamount=Claim total amount
driver.aboutyou.how.many.claimsorlosses=How many accidents or accident-related claims have you had in the last 6 years ?
driver.aboutyou.howlongagolossoccur=How long ago did your loss occur?
driver.aboutyou.typeofloss=Type of loss

driver.aboutyou.commitedinfractions=Have you committed any infractions in the past 3 years? (Do not consider parking tickets.)
driver.aboutyou.minorinfractions=How many minor infractions have you had?
driver.aboutyou.majorinfractions=How many major or serious infractions have you had?

driver.aboutyou.class6.licence=Do you also have a Class 6 licence ?

#INSURANCE HISTORY
#  for policyHolder
driver.aboutyou.quebec.start.this.insurance.coverage=As of which date would you like your vehicle(s) coverage to begin?
driver.aboutyou.quebec.licencesuspended=In the last 3 years, have you, or any driver listed on your contract, or any other person living in your household, had their driver's licence suspended or revoked?
driver.aboutyou.monthswithoutparents=Number of months per year not living with your parents
driver.aboutyou.universitydegree=Do you have an undergraduate or graduate degree (Bachelor, Master or PhD) from a university in Canada?
driver.aboutyou.only.drive.vehicle.during.weekends=Do you drive the vehicle only during weekends?
driver.aboutyou.caa=Are you a CAA-Quebec member?
driver.aboutyou.caa.membership.number=CAA-Quebec membership card number
driver.aboutyou.currentlyinsured=Are you currently insured for your vehicle or have you been in the past 3 months?
driver.aboutyou.insuredhowlong=How long have you been continuously insured? (Do not consider interruptions lasting fewer than 3 months).
driver.aboutyou.currentinsurer=Who is your current insurer?

driver.aboutyou.insurancecoverage.northamerica=Have you ever had insurance coverage in North America
driver.aboutyou.howholdwhengetinsured=How old where you when you first obtained insurance coverage
driver.aboutyou.interruptioncoverage=Has there been an interruption (gap or lapse) in your insurance coverage of more than 2 years in the last 6 years ?
driver.aboutyou.outstanding.premiums.to.any.insurer=Do you currently owe outstanding insurance premium to any insurer ?
driver.aboutyou.insurancerefusal=In the last 3 years, has your insurance contract, or that of any driver listed on your contract, or any other person living in your household, been cancelled by another insurance company?
driver.aboutyou.policyrefusalreason=Why was the contract cancelled?
driver.aboutyou.policyrefusal.because.of.nonpayment=Was it because of non-payment?	
driver.aboutyou.policyrefusal.how.many.times=How many times were you cancelled?	


driver.aboutyou.permissionscore=In order for us to make our best offer, do you authorize us to obtain your credit information from credit agencies? We may consult these agencies to make updates upon contract renewals or changes.
driver.aboutyou.homepolicynumber=Home contract number
driver.aboutyou.currentpolicynumber=Current contract number



#OTHER DRIVERS

driver.aboutothers.worksector=Work sector
driver.aboutothers.occupation=Occupation 

driver.aboutothers.only.drive.vehicle.during.weekends=Is this person only driving during weekends?

driver.aboutothers.universitydegree=Does this person have an undergraduate or graduate degree(Bachelor, Master or PhD) from a university in Canada?
driver.aboutothers.caa=Is this person a CAA-Quebec member?
driver.aboutothers.caa.membership.number=CAA-Quebec membership card number

#SECTION: DRIVING RECORD
driver.aboutothers.licencetype=Type of valid driver's licence 
driver.aboutothers.licensenumber=Driver's licence number :
driver.aboutothers.agewhenlicenseobtained=At what age did this person obtain their driver's licence?
driver.aboutothers.trainingcourse=Has this person successfully completed a driver's training course?

# Claims or losses
driver.aboutothers.claimsorlosses=Has this person had claims or losses (at fault or not, declared or not) in the past 6 years?
driver.aboutothers.howlongagolossoccur=How long ago did the loss occur?
driver.aboutothers.typeofloss=Type of loss
driver.aboutothers.claimamount=Claim total amount

driver.aboutothers.commitedinfractions=Has this person committed any infractions in the past 3 years? (Do not consider parking tickets.) 
driver.aboutothers.minorinfractions=How many minor infractions has this driver had in the past 3 years?
driver.aboutothers.majorinfractions=How many major or serious infractions has this driver had in the past 3 years?
driver.aboutothers.another.licencetype=Does this driver hold another driver's licence type?


driver.aboutothers.licencesuspended= Has this driver ever had their drivers licence under suspension or revocation at any time during the previous 6 years ?	
driver.aboutothers.accident.description.and.date= Please choose the option that best describes the accident and indicate in which month and year it occurred.
driver.aboutothers.driving.infractions= How many driving convictions has this driver had in the last 3 years? (excluding parking tickets)	
driver.aboutothers.driving.conviction.description.and.datePlease= choose the option that best describes the driving conviction and indicate in which month and year it occurred.	
driver.aboutothers.how.many.nonaccident.claims= How many non-accident insurance claims (like theft or windshield claims) has this driver made in the past 6 years?	
driver.aboutothers.option.that.best.describes.claim= Please choose the option that best describes the claim and indicate in which month and year it occurred.	


#Insurance History
driver.aboutothers.continuousyear.insurance.coverage=How many years of continuous insurance coverage has this driver had?	
driver.aboutothers.interruptioncoverage=Has there been an interruption (gap or lapse) in this driver's insurance coverage of more than 2 years in the last 6 years?	
driver.aboutothers.outstanding.premiums.to.any.insurer=Does this driver currently owe outstanding insurance premium to any insurer?	
driver.aboutothers.insurancerefusal=Does this driver ever had their contract cancelled in the last three years?	
driver.aboutothers.policyrefusal.because.of.nonpayment=Was it because of non-payment?	
driver.aboutothers.policyrefusal.how.many.times=How many times were they cancelled?	

driver.aboutothers.firstname=First name
driver.aboutothers.lastname=Last name

driver.aboutothers.monthswithoutparents=Number of months per year not living with parents

driver.postalcode=Postal code
driver.civicnumber=Street number
driver.streetname=Street name
driver.apartmentnumber=Apartment number (if applicable)
driver.city=City
driver.province=Province
driver.country=Country

driver.last.move.000=Less than 6 months
driver.last.move.006=Between 6 months and 2 years
driver.last.move.024=More than 2 years

driver.last.move.I00=Less than 1 year
driver.last.move.I12=1 year, but less than 2
driver.last.move.I24=2 years, but less than 3
driver.last.move.I36=3 years, but less than 4
driver.last.move.I48=4 years, but less than 5
driver.last.move.I60=5 years or more

driver.validation.groupcode.valid.start=As a member of 
driver.validation.groupcode.valid.end=, you benefit from belairdirect's 'Your Group Program'!

# ENUM TYPE DESCRIPTION
driver.license.P=Regular or probationary permit
driver.license.R=Regular or probationary permit
driver.license.L=Learner's permit
driver.license.U=Not licensed
driver.license.A=From another province/country

driver.claim.year.00=Less than 1 year
driver.claim.year.01=1 year, but less than 2 
driver.claim.year.02=2 years, but less than 3
driver.claim.year.03=3 years, but less than 4
driver.claim.year.04=4 years, but less than 5
driver.claim.year.05= 5 years, but less than 6
driver.claim.year.06=6 years, but less than 7,
driver.claim.year.07=7 years and more

driver.claim.nature.NA=Not-at-fault accident
driver.claim.nature.AA=At-fault accident
driver.claim.nature.AAF=50% at-fault accident 
driver.claim.nature.FV=Fire or vandalism
driver.claim.nature.HR=Hit and run
driver.claim.nature.FIR=Fire 
driver.claim.nature.GW=Glass replacement
driver.claim.nature.GR=Glass repair
driver.claim.nature.WN=Windstorm or hail storm
driver.claim.nature.VAN=Vandalism
driver.claim.nature.TH=Theft
driver.claim.nature.ONC=Fire / Theft / Vandalism / Storm

driver.type.residence.HO=Home
driver.type.residence.TN=Tenant
driver.type.residence.CO=Condo
