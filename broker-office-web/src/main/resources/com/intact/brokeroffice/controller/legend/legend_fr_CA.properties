legend.link=Légende
legend.title=Légende

activity.title=Indicateur d'activité
activity.text=Cette soumission a été active au cours des 15 dernières minutes 

system.origin.title=Provenance systèmes (P)

origin.intact.web.site= <b>i</b>
origin.broker.web.site= <b>C</b>
origin.intact.web.site.text=Site Internet Intact
origin.broker.web.site.text=Site Internet courtier

web.program.type.title=Indicateur actif (AQ/QQ/AS)
web.program.type.code.1=<b>AQ</b>
web.program.type.value.1=Programme web AutoQuote
web.program.type.code.2=<b>QQ</b>
web.program.type.value.2=Programme web AutoQuote
web.program.type.code.3=<b>AS</b>
web.program.type.value.3=Assignable pour AutoQuote et QuickQuote

web.program.type.domain=<b>Définition pour AQ et QQ</b>
web.program.type.domain.code.1=<b>X</b>
web.program.type.domain.value.1=Inactif
web.program.type.domain.code.2=<b>B</b>
web.program.type.domain.value.2=Site courtier seulement
web.program.type.domain.code.3=<b>I</b>
web.program.type.domain.value.3=Site Intact seulement
web.program.type.domain.code.4=<b>IB</b>
web.program.type.domain.value.4=Site Intact et courtier

assignable.domain=<b>Définition pour AS</b>
assignable.domain.code.1=<b>*</b>
assignable.domain.value.1=Not Assignable
assignable.domain.code.2=<b>A</b>
assignable.domain.value.2=Assignable AutoQuote
assignable.domain.code.3=<b>Q</b>
assignable.domain.value.3=Assignable QuickQuote
assignable.domain.code.4=<b>AQ</b>
assignable.domain.value.4=Assignable AutoQuote et QuickQuote

consent.title=Indicateurs de consentement
consent.consent= Le client a accepté d'être contacté.
consent.noconsent= Le client n'a pas accepté d'être contacté.

followup.title=Indicateurs de suivi
followup.notContacted=Le client n'a pas encore été contacté. (Blanc)
followup.noFollowup=Le client a été contacté mais <u>aucun</u> suivi n'est requis.
followup.followup=Le client a été contacté mais un suivi est requis.
followup.noneRequired=Le client n'a pas été contacté, aucun suivi n'est requis.

status.title=Statuts
status.quoteIncomplete=Soumission incomplète
status.quoteIncomplete.text=Le client n'a pas encore complété la soumission.
status.quoteIncomplete.nextSteps=<b>Prochaines étapes:</b> Vérifier l'indicateur de contact et communiquer avec le client s'il a donné son consentement.

status.quoteComplete=Soumission effectuée
status.quoteComplete.text=Une offre a été fournie au client.
status.quoteComplete.nextSteps=<b>Prochaines étapes:</b> Vérifier l'indicateur de contact et communiquer avec le client s'il a donné son consentement.

status.purchaseAbandoned=Achat incomplet
status.purchaseAbandoned.text=Une offre a été fournie au client. Le client a amorcé le processus d'achat mais ne l'a pas complété.
status.purchaseAbandoned.nextSteps=<b>Prochaines étapes:</b> Contacter le client pour lui confirmer que les protections ne sont pas en vigueur.

status.purchaseCompleted=Achat effectué
status.purchaseCompleted.text=Le client a sélectionné l'option d'achat en ligne et a complété le processus d'achat.
status.purchaseCompleted.nextSteps=<b>Prochaines étapes:</b> Vous pouvez commander MVR et Auto Plus. Contacter le client pour prendre les dispositions finales

status.expired=Expirée
status.expired.text=Le client a complété cette soumission il y a plus de 30 jours. 
status.expired.nextSteps=<b>Prochaines étapes:</b> Contacter le client. Il reste moins de 10 jours pour télécharger cette soumission dans Savers.

status.uploaded=Téléchargée
status.uploaded.text=La soumission a été téléchargée dans Savers.
status.uploaded.nextSteps=<b>Prochaines étapes:</b> Dans le cas où les dispositions finales ne sont pas complétées, vous pouvez récupérer la soumission dans Savers.

status.accepted=Acceptée
status.accepted.text=La soumission a été téléchargée dans goBRIO et a été acceptée pour l'émission du contrat.
status.accepted.nextSteps=<b>Prochaines étapes:</b> S'assurer que toutes les dispositions finales sont effectuées.

status.refused=Refusée
status.refused.text=La soumission a été téléchargée dans goBRIO mais n'a pas été acceptée par le client ou par Intact Assurance pour l'émission du contrat.
status.refused.nextSteps=<b>Prochaines étapes: </b> Si refusée par le client, vous pourriez vouloir faire un suivi dans quelques jours pour confirmer la décision du client. Si refusée par Intact Assurance, aucune autre soumission de ce client ne sera acceptée.

status.roadblock.text=La soumission a été bloquée.
status.roadblock.not.canadian.text=La soumission a été bloquée.  Jamais assuré au Canada.
status.roadblock.intact.client.text=La soumission a été bloquée. Déja un client Intact.

status.PDF_DOWNLOADED=PDF Téléchargé
status.PDF_DOWNLOADED.text=La version courtier de la soumission en PDF a été téléchargé.