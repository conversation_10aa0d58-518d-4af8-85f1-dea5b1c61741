#POLICY HOLDER SECTION
#LBL5200
driver.aboutyou.permissioninfos=I give Intact Insurance and my assigned broker permission to collect, use and disclose my personal information as permitted by law, for the purpose of creating a profile in my name and providing me with a quote for automobile insurance.

driver.aboutyou.licencesuspended	=	Has any driver's licence, permit or similar authorization issued to you or any other driver to be included on this quote, been or continue to be suspended, cancelled or lapsed within the previous 6 years?
driver.aboutyou.insurancerefusal	=	Has any insurer cancelled, declined or refused to renew or issue automobile insurance to you or any driver listed on this quote within the previous 3 years or has any claim been denied for material misrepresentation?
driver.aboutyou.policyrefusal.because.of.nonpayment		= Was the cancellation or refusal because of non-payment?	
driver.aboutyou.policyrefusal.how.many.times			= How many times have you or any driver listed on this quote been cancelled or refused because of non-payment?
driver.aboutyou.other.licensed.drivers 					= Are there any other licensed drivers (not already listed) in the household, or do any regular drivers reside somewhere other than in the household?
driver.aboutyou.all.drivers.own.insurance           	= Do ALL drivers have their own auto insurance?
driver.aboutyou.savewhenvehicleandhomeareinsuredwithus  = 50% of our clients have both home and auto insurance with us and save even more. Would you like to include the my Home & Auto<sup>TM</sup> (multi-line) discount with this quote?
driver.aboutyou.howlonglivedthere=How long have you been living at this address?

#GENERAL INFORMATION SECTION
driver.aboutyou.occupation=What is your occupation?
driver.aboutyou.fulltime.student = Are you currently a full-time student at a college or university in Alberta? 
driver.aboutyou.universitydegree = Do you have an undergraduate or graduate degree (Bachelor, Master or PhD) from a university in Canada?
driver.aboutyou.start.this.insurance.coverage=As of which date would you like your insurance coverage to begin?
#DRIVING RECORD SECTION
driver.aboutyou.agewhenlicenseobtained = At what age did you obtain your first Canadian driver's licence?
driver.aboutyou.licensenumber  = Driver's licence number
driver.aboutyou.licencetype    = Class of valid driver's licence
driver.aboutyou.trainingcourse = Have you successfully completed a driver training course in an approved driving school in the last 3 years?

driver.aboutyou.claimsorlosses      = Have you had any accidents or made any claims arising from the ownership or operation of any automobile during the last 10 years (whether you were responsible or not)?
driver.aboutyou.howlongagolossoccur	= How long ago was the claim made or accident occur?
driver.aboutyou.typeofloss			= Type of accident or claim
driver.aboutyou.loss.vehicle        = Please select the vehicle involved in the loss
 
driver.aboutyou.commitedinfractions = Have you had any convictions in the past 3 years? (Do not consider parking tickets.)
driver.aboutyou.conviction.date     = Conviction date
driver.aboutyou.nature.conviction   = Type of conviction

#INSURANCE HISTORY POLICY HOLDER

driver.aboutyou.insured.in.canada 		= Have you ever had insurance coverage in Canada?
driver.aboutyou.age.first.insured 		= At what age did you first obtain insurance coverage?
driver.aboutyou.interruptioncoverage	= Has there been an interruption (gap or lapse) in your insurance coverage of more than 2 years in the last 6 years ?
driver.aboutyou.outstanding.premiums.to.any.insurer	= Do you currently owe outstanding auto premiums to any insurer?
driver.aboutyou.currentinsurer						= Who is your current insurer?

#OTHER DRIVERS

driver.aboutothers.firstname=First name
driver.aboutothers.lastname=Last name
driver.aboutothers.occupation=Occupation
driver.aboutothers.universitydegree=Does this person have an undergraduate or graduate degree(Bachelor, Master or PhD) from a university in Canada?
driver.aboutothers.fulltime.student=Is this driver a full-time student at a college or university in Alberta? 

#SECTION: DRIVING RECORD and CLAIMS OR LOSSES
driver.aboutothers.licencetype=Class of valid driver's licence 
driver.aboutothers.agewhenlicenseobtained=At what age did this driver obtain their first Canadian driver's licence?
driver.aboutothers.trainingcourse=Has this person successfully completed a driver's training course from an approved driving school in the last 3 years? 

driver.aboutothers.claimsorlosses       = Has this person had any accidents or made any claims arising from the ownership or operation of any automobile during the last 10 years (whether they were responsible or not)?  
driver.aboutothers.howlongagolossoccur	= How long ago was the claim made or accident occur?
driver.aboutothers.claimamount			= Claim total amount
driver.aboutothers.typeofloss			= Type of accident or claim
driver.aboutothers.loss.vehicle       	= Please select the vehicle involved in the loss

driver.aboutothers.commitedinfractions=Has this person had any convictions in the past 3 years? (Do not consider parking tickets.) 
driver.aboutothers.conviction.date=Conviction date
driver.aboutothers.nature.conviction=Type of Conviction

#Insurance History
driver.aboutothers.insured.in.canada = Has this driver ever had insurance coverage in Canada?
driver.aboutothers.age.first.insured = At what age did this driver first obtain insurance coverage?
driver.aboutothers.interruptioncoverage = Has there been an interruption (gap or lapse) in this person's insurance coverage of more than 2 years in the last 6 years?
driver.aboutothers.outstanding.premiums.to.any.insurer=Does this driver currently owe outstanding auto premiums to any insurer?	

#GENERAL INFORMATION SECTION
driver.postalcode=Postal code
driver.civicnumber=Street number
driver.streetname=Street name
driver.apartmentnumber=Apartment number (if applicable)
driver.city=City
driver.province=Province
driver.country=Country

driver.address.type.POBOX=PO BOX
driver.address.type.RR=RR
driver.address.type.GD=GENERAL DELIVERY
driver.address.type.other=

driver.last.move.000=Less than 6 months
driver.last.move.006=Between 6 months and 2 years
driver.last.move.024=More than 2 years

driver.last.move.I00 = Less than 1 year
driver.last.move.I12 = 1 year, but less than 2
driver.last.move.I24 = 2 years, but less than 3
driver.last.move.I36 = 3 years, but less than 4
driver.last.move.I48 = 4 years, but less than 5
driver.last.move.A60 = 5 years, but less than 6
driver.last.move.A72 = 6 years, but less than 7
driver.last.move.A84 = 7 years, but less than 8
driver.last.move.A96 = 8 years, but less than 9
driver.last.move.108 = 9 years, but less than 10
driver.last.move.120 = 10 years or more

# ENUM TYPE DESCRIPTION - VALUE DOMAIN

driver.years.insured.continuously.0=Less than 1 year
driver.years.insured.continuously.1=1 year, but less than 2
driver.years.insured.continuously.2=2 years, but less than 3
driver.years.insured.continuously.3=3 years, but less than 4
driver.years.insured.continuously.4=4 years, but less than 5
driver.years.insured.continuously.5=5 years, but less than 6
driver.years.insured.continuously.6=6 years and more

driver.license.P=Regular or probationary permit
driver.license.R=Regular or probationary permit
driver.license.L=Learner's permit
driver.license.U=Not licensed
driver.license.A=From another province/country

driver.license.class.1=Class 1 (Professional - Any vehicle)
driver.license.class.2=Class 2 (Professional - Bus)
driver.license.class.3=Class 3 (3-axle plus)
driver.license.class.4=Class 4 (Professional - Taxi, Ambulance)
driver.license.class.5=Class 5 (2-axle - Cars, Light trucks, Motorhomes or Mopeds)
driver.license.class.7=Class 7 (Learners - 2-axle, Motorcycle, and Mopeds)
driver.license.class.U=Not licensed
driver.license.class.A=From another province/country

driver.claim.year.00=Less than 1 year
driver.claim.year.01=1 year, but less than 2 
driver.claim.year.02=2 years, but less than 3
driver.claim.year.03=3 years, but less than 4
driver.claim.year.04=4 years, but less than 5
driver.claim.year.05= 5 years, but less than 6
driver.claim.year.06=6 years, but less than 7
driver.claim.year.07=7 years, but less than 10

driver.claim.nature.SOV=You struck an object or vehicle
driver.claim.nature.HDS=You were hit by a driver who was disobeying a sign/signal
driver.claim.nature.SP=You struck a pedestrian
driver.claim.nature.SPS=You struck a pedestrian who was disobeying a street sign/signal
driver.claim.nature.SB=You struck a bicyclist
driver.claim.nature.SBS=You struck a bicyclist who was disobeying a street sign/signal
driver.claim.nature.YRV=You rear-ended a vehicle
driver.claim.nature.YWR=Your were rear-ended
driver.claim.nature.BAV=You backed into another vehicle
driver.claim.nature.VBY=A vehicle backed into you
driver.claim.nature.SCA=Single car accident
driver.claim.nature.WMA=You were in a multi-car accident
driver.claim.nature.OAF=Other at-fault accident
driver.claim.nature.OPA=Other partially at-fault accident
driver.claim.nature.ONF=Other not-at-fault accident
driver.claim.nature.IA=You hit a bird/animal
driver.claim.nature.GW=Windshield/glass replacement
driver.claim.nature.GR=Windshield/glass repair
driver.claim.nature.ONC=Other non-collision claim

driver.marital.status.M=Couple
driver.marital.status.C=Single

driver.conviction.type.OT3=Other serious/criminal conviction
driver.conviction.type.CN=Criminal negligence or manslaughter
driver.conviction.type.DD=Dangerous driving
driver.conviction.type.DUS=Driving while disqualified
driver.conviction.type.CD=Driving without due care and attention
driver.conviction.type.IMP=Drug/alcohol-related offences
driver.conviction.type.FTY=Failure to give way
driver.conviction.type.TS=Failure to obey traffic sign/signal
driver.conviction.type.IP=Failure to pass in safety
driver.conviction.type.FTSSA=Failure to remain at an accident
driver.conviction.type.FRA=Failure to report an accident
driver.conviction.type.FTSPO=Failure to stop for peace officer
driver.conviction.type.PSB=Failure to stop for a school bus
driver.conviction.type.TFTC=Following too closely (tailgating)
driver.conviction.type.IPSB=Improper passing - school bus
driver.conviction.type.RAC=Racing
driver.conviction.type.FTS=Signalling offences
driver.conviction.type.PSG=Speeding/passing - school zone
driver.conviction.type.SP1=Speeding
driver.conviction.type.STN=Stunting

ONE = 1
TWO_OR_MORE = 2 or more