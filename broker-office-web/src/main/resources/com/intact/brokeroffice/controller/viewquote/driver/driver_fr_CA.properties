
#Data element = Tab

driver.title.drivingRecord=Dossier de conduite
driver.title.insuranceHistory=Antécédents d'assurance

#POLICY HOLDER SECTION 
driver.title.policyHolder=Titulaire de la police

driver.aboutyou.lastname=Nom 
driver.aboutyou.firstname=Prénom 
driver.aboutyou.email=Courriel
driver.aboutyou.permissioninfos=J'autorise Intact Assurance à recueillir, utiliser, et communiquer mes renseignements personnels et à les échanger avec mon courtier assigné, dans le but de créer un profil à mon nom et de me fournir une soumission d'assurance auto.
driver.aboutyou.permissionscore=Afin de nous permettre d'accorder notre meilleure offre, nous autorisez-vous à obtenir vos informations de crédit auprès des agences d'évaluation du crédit ? Nous pourrons consulter ces agences pour faire des mises à jour lors de renouvellements ou modifications de votre police.

driver.aboutyou.homePhone=Numéro de téléphone (domicile)
driver.aboutyou.address=Adresse

driver.aboutyou.isyourhomeinsuredwithus=Votre résidence est-elle assurée chez Intact Assurance?

#GENERAL INFORMATION SECTION
driver.title.details=Information générale 
driver.aboutyou.generalinfo.tab=Information Générale
driver.aboutyou.dateofbirth=Date de naissance :
driver.aboutyou.gender=Sexe :
driver.aboutyou.maritalstatus=État civil :

#DRIVING RECORD SECTION
driver.aboutyou.drivingrecord=Dossier de conduite
# Licence type, number

driver.aboutyou.how.many.claimsorlosses=Combiens d'accidents ou sinistres liés aux accidents avez-vous eu au cours des 6 dernières années ?

driver.aboutyou.class6.licence=Avez-vous aussi un permis de classe 6 ?

#INSURANCE HISTORY
driver.aboutyou.monthswithoutparents=Nombre de mois par année où vous n'habitez pas chez vos parents
driver.aboutyou.universitydegree=Êtes-vous diplômé d'une université canadienne (bac, maîtrise ou doctorat)?

driver.aboutyou.caa.membership.number=N° de carte de membre CAA-Québec
driver.aboutyou.currentlyinsured=Actuellement, êtes-vous détenteur d'une assurance auto ou l'avez-vous été au cours des trois derniers mois?
driver.aboutyou.currentinsurer=Qui est votre assureur actuel?
driver.aboutyou.currentpolicynumber=Numéro de police actuel

driver.aboutyou.insurancecoverage.northamerica=Have you ever had insurance coverage in North America
driver.aboutyou.howholdwhengetinsured=How old where you when you first obtained insurance coverage
driver.aboutyou.interruptioncoverage=Est-ce qu'il y a eu une interruption (intervalle ou laps) dans votre protection d'assurance de plus de 2 ans au cours des 6 dernières années?
driver.aboutyou.outstanding.premiums.to.any.insurer=Avez-vous des primes d'assurances non-payés avec n'importe quel assureur présentement?
driver.aboutyou.policyrefusalreason=Quelle était la raison de l'annulation ou de la résiliation de cette police?
driver.aboutyou.policyrefusal.because.of.nonpayment=Est-ce que c'était à cause de non-paiement des primes ?
driver.aboutyou.policyrefusal.how.many.times=Combien de fois est-ce que vous avez été annulé ?
driver.aboutyou.homepolicynumber=Numéro de police habitation
driver.aboutyou.ubi=Seriez-vous intéressé à adhérer à <strong>Ma conduite</strong></a><sup>MC</sup> et voulez-vous inclure ce rabais d'inscription à votre soumission auto ?

#OTHER DRIVERS

driver.aboutothers.firstname=Prénom
driver.aboutothers.lastname=Nom

driver.aboutothers.generalinfo.tab=Profil 
driver.aboutothers.dateofbirth=Date de naissance
driver.aboutothers.gender=Sexe
driver.aboutothers.maritalstatus=État civil
driver.aboutothers.relationtopolicyholder=Lien avec {0}

driver.aboutothers.only.drive.vehicle.during.weekends=Cette personne utilisera-t-elle le véhicule uniquement durant les fins de semaine ?
driver.aboutothers.universitydegree=Cette personne est-elle diplômée d'une université canadienne (bac, maîtrise ou doctorat)?

#DRIVING RECORD
driver.aboutothers.licensenumber=N° de permis de conduire :
driver.aboutothers.agewhenlicenseobtained=À quel âge a-t-elle obtenu ce permis?
driver.aboutothers.trainingcourse=Cette personne a-t-elle réussi un cours de conduite?

driver.aboutothers.typeofloss=Nature du sinistre
driver.aboutothers.claimamount=Montant total de la réclamation

driver.aboutothers.class.6.licence=Est ce que ce conducteur a aussi un permis de classe 6 ?

driver.aboutothers.licencesuspended=Est-ce que ce conducteur a jamais eu son permis de conduire suspendu ou révoqué au cours des 6 dernières années?
driver.aboutothers.how.many.claimsorlosses=Combiens d'accidents ou sinistres liés aux accidents est-ce que ce conducteur a eu au cours des 6 dernières années ?
driver.aboutothers.accident.description.and.date=Veuillez sélectionner l'option qui mieux explique le nature de l'accident et indiquer le mois et l'année il a eu lieu.
driver.aboutothers.driving.infractions=Combiens d'infractions de conduite est-ce que ce conducteur a eu au cours des 3 dernières années ? (exclure les contraventions de stationnement)
driver.aboutothers.driving.conviction.description.and.date=Veuillez sélectionner l'option qui mieux explique le nature de l'infraction et indiquer le mois et l'année il a eu lieu.
driver.aboutothers.how.many.nonaccident.claims=Combiens de sinistres non-liés aux accidents (comme sinistres de vol ou pare-brise) est-ce que ce conducteur a eu au cours des 6 dernières années ?
driver.aboutothers.option.that.best.describes.claim=Veuillez sélectionner l'option qui mieux explique le nature du sinistre et indiquer le mois et l'année il a eu lieu.
driver.aboutothers.continuousyear.insurance.coverage=Combien d'années d'assurance continues est-ce que ce conducteur a eu?
driver.aboutothers.interruptioncoverage=Est-ce qu'il y a eu une interruption (intervalle ou laps) dans la protection d'assurance de ce conducteur de plus de 2 ans au cours des 6 dernières années?
driver.aboutothers.outstanding.premiums.to.any.insurer=Est-ce que ce conducteur a des primes d'assurances non-payés avec n'importe quel assureur présentement?
driver.aboutothers.insurancerefusal=Est-ce que ce conducteur a jamais eu son contrat d'assurance annulé au cours des 3 dernières années.
driver.aboutothers.policyrefusal.because.of.nonpayment=Est-ce que c'était à cause de non-paiement des primes ?
driver.aboutothers.policyrefusal.how.many.times=Combiens de fois est-ce que ce conducteur a était annulé.

driver.aboutothers.drivingrecord.tab=Dossier de conduite

driver.aboutothers.monthswithoutparents=Nombre de mois par année où cette personne n'habite pas chez ses parents

#Previous address

driver.postalcode=Code postal
driver.civicnumber=Numéro
driver.streetname=Nom de la rue
driver.apartmentnumber=N<span class='superscript'>o</span> d'appartement (si applicable)
driver.city=Ville
driver.province=Province
driver.country=Pays

driver.address.type.POBOX=CP
driver.address.type.RR=RR
driver.address.type.GD=POSTE RESTANTE
driver.address.type.other=

driver.last.move.000=Moins de 6 mois 
driver.last.move.006=Entre 6 mois et 2 ans 
driver.last.move.024=Plus de 2 ans

driver.last.move.I00=Moins de 1 an
driver.last.move.I12=1 an, mais moins de 2
driver.last.move.I24=2 ans, mais moins de 3
driver.last.move.I36=3 ans, mais moins que 4
driver.last.move.I48=4 ans, mais moins que 5
driver.last.move.I60=5 ans ou plus

driver.validation.groupcode.valid.start=À titre de membre de 
driver.validation.groupcode.valid.end=, vous bénéficiez des avantages de « Votre programme groupe » de belairdirect!

# pour le QC seulement
driver.error.postalCode.client=Veuillez saisir un code postal valide du Québec.


# ENUM TYPE DESCRIPTION
driver.years.insured.continuously.0=Moins de 1 an
driver.years.insured.continuously.1=1 an, mais moins de 2
driver.years.insured.continuously.2=2 ans, mais moins de 3
driver.years.insured.continuously.3=3 ans, mais moins de 4
driver.years.insured.continuously.4=4 ans, mais moins de 5
driver.years.insured.continuously.5=5 ans ou plus
driver.years.insured.continuously.6=6 ans et plus
driver.license.P=Permis régulier ou probatoire
driver.license.R=Permis régulier ou probatoire
driver.license.L=Permis d'apprenti conducteur
driver.license.U=Sans permis
driver.license.A=D'une autre province ou pays

driver.family.relationship.C=Conjoint
driver.family.relationship.E=Enfant  
driver.family.relationship.F=Frère ou soeur
driver.family.relationship.M=Père ou mère
driver.family.relationship.X=Autre

driver.gender.F= Femme
driver.gender.M= Homme

true=Oui
false=Non
driver.true=Oui
driver.false=Non
driver.=N/D

driver.claim.year.00=Moins de 1 an
driver.claim.year.01=1 an, mais moins de 2 
driver.claim.year.02=2 ans, mais moins de 3
driver.claim.year.03=3 ans, mais moins de 4
driver.claim.year.04=4 ans, mais moins de 5
driver.claim.year.05=5 ans, mais moins de 6
driver.claim.year.06=6 ans, mais moins de 7,
driver.claim.year.07=7 ans et plus

driver.province.name.AB=Alberta
driver.province.name.MB=Manitoba
driver.province.name.NL=Terre-Neuve-et-Labrador
driver.province.name.NS=Nouvelle-Écosse
driver.province.name.ON=Ontario
driver.province.name.QC=Québec
driver.province.name.YT=Yukon
driver.province.name.BC=Colombie-Britannique
driver.province.name.NB=Nouveau-Brunswick
driver.province.name.NT=Territoires du Nord-Ouest
driver.province.name.NU=Nunavut
driver.province.name.PE=Île-du-Prince Édouard
driver.province.name.SK=Saskatchewan
driver.province.name.US=États-Unis
driver.province.name.EC=Europe
driver.province.name.OT=Autre

driver.country.name.CA=Canada
driver.country.name.US=États-Unis
driver.country.name.OT=Autre

driver.marital.status.M=En couple
driver.marital.status.C=Célibataire
driver.marital.status.V=Veuf
driver.marital.status.D=Divorcé
driver.marital.status.S=Separé
driver.marital.status.F=Conjoint de fait
driver.marital.status.E=Religieux
driver.marital.status.1=Conjoint de même sexe
driver.marital.status.A=Autre