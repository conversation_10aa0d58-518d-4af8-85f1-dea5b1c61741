#Data element = Title
usage.info.title=Vehicle Usage
#Data element = Introtext
usage.info.introtext=
#Data element = Tab
usage.tabUsage=Drivers and usage
usage.na=n/a


#DE146
usage.registeredOwner=Registered owner
#DE277
usage.is.there.secondOwner=Is there a second registered owner?
#DE284
usage.who.is.secondOwner=Who is the second registered owner?
#DE42
usage.principalDriver=Principal driver
#DE261
usage.already.been.principal.driver.indicator=Are you currently insured as principal driver, or have you been in the past 3 months?
#DE55
usage.insured.period=How long have you been insured as principal driver?
#DE147
usage.primary.of.vehicle=Vehicle use

#Data element = DE39
usage.annual.km.driven=Annual kilometres driven
#DE152
usage.drive.to.work= Do you drive to work/school?
#DE40
usage.how.far.from.home.to.work=Distance between your home and your work/school 

usage.other.driver=Other driver

#ENUM TYPE DESCRIPTION
usage.category.P=Personal
usage.category.B=Business
usage.category.Z=Business (occasionally)
usage.category.O=Commercial
usage.category.D=Delivery

usage.outside.qc.lessthan21days= Not used outside Quebec more than 21 consecutive days.	
usage.outside.qc.morethan21days=Used outside Quebec more than 21 consecutive days.	
usage.annual.km.driven.outside.qc=kilometers driven annually outside Quebec 	
usage.annual.km.driven.outside.qc.2=kilometers driven annually outside Quebec (for personal and/or business/business occasionally use)

usage.year.between.00=Less than 1 year
usage.year.between.12=1 year, but less than 2
usage.year.between.24=2 years, but less than 3
usage.year.between.36=3 years, but less than 4
usage.year.between.48=4 years, but less than 5
usage.year.between.60=5 years, but less than 6
usage.year.between.72=6 years and more

true=Yes
false=No