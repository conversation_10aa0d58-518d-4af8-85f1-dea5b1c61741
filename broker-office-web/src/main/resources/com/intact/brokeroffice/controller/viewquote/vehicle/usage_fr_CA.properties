#Data element = Title
usage.info.title=Usage du véhicule
#Data element = Introtext
usage.info.introtext=
#Data element = Tab
usage.tabUsage=Détails de l'utilisation
usage.na=n/a

#DE146
usage.registeredOwner=Propriétaire immatriculé

#DE277
usage.is.there.secondOwner=Y a-t-il un second propriétaire immatriculé?
#DE284
usage.who.is.secondOwner=Qui est le second propriétaire immatriculé? 
#DE42
usage.principalDriver=Conducteur principal
#DE261
usage.already.been.principal.driver.indicator=Êtes-vous assuré à titre de conducteur principal ou l'avez-vous été au cours des trois derniers mois?
#DE55
usage.insured.period=Depuis combien de temps êtes-vous assuré à titre de conducteur principal?
#DE147
usage.primary.of.vehicle=Usage du véhicule
#Data element = DE39
usage.annual.km.driven=Kilométrage annuel
#DE152
usage.drive.to.work=Utilisez-vous le véhicule pour aller au travail ou à l'école?
#DE40
usage.how.far.from.home.to.work=À quelle distance est-ce de votre résidence?
#DE41
usage.businessAnnualKm=Kilométrage annuel pour usage d'affaires/d'affaires (occasionnellement), excluant l'aller-retour au travail
#DE727
usage.more.than.49days.in.usa=Ce véhicule est-il utilisé aux États-Unis  pendant plus de 7 semaines consécutives?

usage.other.driver=Autre conducteur

#ENUM TYPE DESCRIPTION

usage.category.P=Personnel
usage.category.B=Affaire
usage.category.Z=Affaires (occasionnellement)
usage.category.O=Commercial
usage.category.D=Livraison

usage.year.between.00=Moins de 1 an
usage.year.between.12=1 an, mais moins de 2
usage.year.between.24=2 ans, mais moins de 3
usage.year.between.36=3 ans, mais moins de 4
usage.year.between.48=4 ans, mais moins de 5
usage.year.between.60=5 ans, mais moins de 6
usage.year.between.72=6 ans et plus

true=Oui
false=Non
vehicle.true=Oui
vehicle.false=Non
vehicle.=N/D