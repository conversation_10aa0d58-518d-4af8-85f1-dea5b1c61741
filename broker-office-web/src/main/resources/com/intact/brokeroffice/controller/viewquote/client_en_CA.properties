## Important messages
client.messages.title=Important Messages

client.message.roadblock=Client was roadblocked on {0} page due to :<br> {1}
client.message.recentActivity=There has been recent activity on this quote. Please view the Activity Log below for details.
client.message.recentQuote=There are other quotes submitted by this client. To view them, go to the <b>Quote Search page</b> and search for the client by name.

client.message.consent.quote.contacted=The client has not provided their consent to be contacted with respect to this quote.
client.message.consent.marketing=The client has not provided their consent to be contacted with respect to the marketing of Intact Insurance products.

#MSG506
message.upload.to.goBrio.confirmation=The quote has been successfully uploaded.
#MSG507
message.upload.to.goBrio.error=A technical failure has occurred, please contact the Intact Help Desk at 1-877-464-3375.
#MSG527
message.quote.has.been.reassigned=The quote you were attempting to view/update/upload has been reassigned. It is no longer available for display.

#MSG5568
message.region.changed=Have you changed your region? The quote you were attempting to update/upload is not in your current region. It is no longer available for display.


## IRCA specific messages
client.message.roadblock.irca=Client was roadblocked due to :<br> {1}
client.message.consent.credit=The client did not authorize Intact Insurance to obtain information pertaining to the credit score. 

####

client.general.title=General Information
client.general.info=Client information

client.refNo=Quote Reference Number:
client.roadBlock=Road Block:
client.policyHolder=Policyholder name:
client.language=Language:
client.brokerAdvisor=Point of Sale:
client.status=Quote status:

client.quote.source=System Origin:

client.lastUpdate=Last update by customer:
client.creationDate=Quote Submission Date:
client.start.this.insurance.coverage=As of which date would you like your insurance coverage to begin?

client.followup=Follow-up status:
client.followup.notContacted=Never contacted.
client.followup.required=Contact, Follow-up Needed.
client.followup.notRequired=Contact, No Follow-up Needed.
client.followup.noneRequired=No Contact, No Follow-up Needed.
client.followup.duplicate=Duplicate/Fake

client.followup.note=Status changed from "{0}" to "{1}".
client.addNotes=Add Contact Notes:

client.filenamePDF={0} Intact Insurance Commercial Quote.pdf

client.phoneHome=Preferred Phone Number:
client.phoneCell=Phone Number (Cell):
client.phoneWork=Phone Number (Work):

client.email.address=Email
client.email.address.label=Email:

client.activityLog.title=Activity Log
client.activityLog.date=Date / Timestamp
client.activityLog.account=User Name
client.activityLog.activity=Activity
client.activityLog.notes=Notes

VIEW_QUO=Viewed Quote
UPLOAD_QUO=Uploaded Quote
CHG_FLW_ST=Follow-up
ADD_NOTE=Updated Quote
REAS_QUO=Reassigned

INITIAL=initial
VEHICLE=vehicle
VEHICLES=Vehicle(s)
OFFER=offer
BIND=bind
DRIVER=driver
VEHICLE_USAGE=vehicle usage
ADDRESS_CHANGE=address change
PURCHASE=purchase
INVALID_OFFER=invalid offer

NOT_CONTACTED=Never contacted
CONTACTED_FOLLOWUP_REQUIRED=Contact, Follow-up Needed
CONTACTED_NO_FOLLOWUP_REQUIRED=Contact, No Follow-up Needed
NOT_CONTACTED_NO_FOLLOWUP_REQUIRED=No Contact, No Follow-up Needed
CALLBACK_REQUESTED=Callback requested
DUPLICATE_FAKE=Duplicate/Fake

NOT_CONTACTED.FOLLOWUP=Customer not contacted yet
CONTACTED_FOLLOWUP_REQUIRED.FOLLOWUP=Customer contacted but a follow-up is required
CONTACTED_NO_FOLLOWUP_REQUIRED.FOLLOWUP=Customer contacted, no follow-up is required


prop.timezone=EST
fr=French
en=English

client.upload.remote.system.url = Click <a href="{0}">here</a> to access goBRIO. Contract No.: {1}
message.url.here=here
message.error.plt=This section is unavailable at the moment.  Please come back later.

INT=Intact web site
WEBBK=Broker web site


dialerSuccessMessage=Dialer file created with confirmation number:
dialerFailedMessage=The creation of the dialer file failed. Please re-try or create a file manually instead.

# Message when dialer is not match dialer_broker json file
dialer.failed.match.broker.message=The dialer file cannot be created : this quote could not be matched to a specific broker. Please escalate this error to your manager. Broker ID :
dialer.failed.province.address.message=The province cannot be found in the address provided in the quote. Please advise your manager.


# Message for assign to me
message.assign.confirmation=The quote was successfully assigned to you. 
message.assign.error=There was a problem assigning the quote to you. 
