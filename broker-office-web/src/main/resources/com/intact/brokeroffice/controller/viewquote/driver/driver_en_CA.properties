#Data element = Tab

driver.title.details=General Information

driver.title.drivingRecord=Driving Record

driver.title.insuranceHistory=Insurance History

#POLICY HOLDER SECTION 
driver.title.policyHolder=Policy Holder
driver.aboutyou.lastname=Last name 
driver.aboutyou.firstname=First name 
driver.aboutyou.email=Email
driver.aboutyou.permissioninfos=I give Intact Insurance permission to collect, use and disclose my personal information, and share with my assigned insurance broker for the purpose of creating a profile in my name and providing me with a quote for automobile insurance.
driver.aboutyou.permissionscore=In order for us to make our best offer, do you authorize us to obtain your credit information from credit agencies? We may consult these agencies to make updates upon policy renewals or changes.

driver.aboutyou.homePhone=Phone number (Home)

driver.aboutyou.address=Address

driver.aboutyou.isyourhomeinsuredwithus=Is your home insured with Intact Insurance?
driver.aboutyou.homepolicynumber=Home policy number


#GENERAL INFORMATION SECTION
driver.aboutyou.generalinfo.tab=General Information
driver.aboutyou.dateofbirth=Date of birth :
driver.aboutyou.gender=Gender :
driver.aboutyou.maritalstatus=Marital status :

#DRIVING RECORD SECTION
driver.aboutyou.drivingrecord=Driving Record
# Licence type, number
driver.aboutyou.licensenumber=Driver's licence number :

driver.aboutyou.how.many.claimsorlosses=How many accidents or accident-related claims have you had in the last 6 years ?

driver.aboutyou.class6.licence=Do you also have a Class 6 licence ?

#INSURANCE HISTORY
#  for policyHolder

driver.aboutyou.monthswithoutparents=Number of months per year not living with your parents
driver.aboutyou.universitydegree=Do you have an undergraduate or graduate degree (Bachelor, Master or PhD) from a university in Canada?

driver.aboutyou.currentlyinsured=Are you currently insured for your vehicle or have you been in the past 3 months?
driver.aboutyou.currentinsurer=Who is your current insurer?
driver.aboutyou.currentpolicynumber=Current policy number
driver.aboutyou.ubi=Would you be interested in signing up for <i>my</i> <strong>Driving Discount</a><sup>TM</sup></strong> program and including the enrollment discount in your auto insurance quote? 

driver.aboutyou.insurancecoverage.northamerica=Have you ever had insurance coverage in North America
driver.aboutyou.howholdwhengetinsured=How old where you when you first obtained insurance coverage
driver.aboutyou.interruptioncoverage=Has there been an interruption (gap or lapse) in your insurance coverage of more than 2 years in the last 6 years ?
driver.aboutyou.outstanding.premiums.to.any.insurer=Do you currently owe outstanding premium to any insurer ?
driver.aboutyou.policyrefusalreason=Why was the policy cancelled?
driver.aboutyou.policyrefusal.because.of.nonpayment=Was it because of non-payment?	
driver.aboutyou.policyrefusal.how.many.times=How many times were you cancelled?	
driver.aboutyou.homepolicynumber=Home insurance policy number

#OTHER DRIVERS

driver.aboutothers.firstname=Prénom
driver.aboutothers.lastname=Nom
driver.aboutothers.generalinfo.tab=Profile 
driver.aboutothers.dateofbirth=Date of birth
driver.aboutothers.gender=Gender
driver.aboutothers.maritalstatus=Marital status
driver.aboutothers.relationtopolicyholder=Relationship to {0}
driver.aboutothers.universitydegree=Does this person have an undergraduate or graduate degree (Bachelor, Master or PhD) from a university in Canada?

#SECTION: DRIVING RECORD
driver.aboutothers.licencetype=Type of valid driver's licence 
driver.aboutothers.licensenumber=Driver's licence number :
driver.aboutothers.agewhenlicenseobtained=At what age did this person obtain their driver's licence?
driver.aboutothers.trainingcourse=Has this person successfully completed a driver's training course?

# Claims or losses
driver.aboutothers.typeofloss=Type of loss
driver.aboutothers.claimamount=Claim total amount

driver.aboutothers.commitedinfractions=Has this person committed any infractions in the past 3 years? 
driver.aboutothers.another.licencetype=Does this driver hold another driver's licence type?

driver.aboutothers.licencesuspended= Has this driver ever had their drivers licence under suspension or revocation at any time during the previous 6 years ?	
driver.aboutothers.claimsorlosses= Has this person had claims or losses (at fault or not, declared or not) in the past 6 years?
driver.aboutothers.accident.description.and.date= Please choose the option that best describes the accident and indicate in which month and year it occurred.
driver.aboutothers.driving.infractions= How many driving convictions has this driver had in the last 3 years? (excluding parking tickets)	
driver.aboutothers.driving.conviction.description.and.datePlease= choose the option that best describes the driving conviction and indicate in which month and year it occurred.	
driver.aboutothers.how.many.nonaccident.claims= How many non-accident insurance claims (like theft or windshield claims) has this driver made in the past 6 years?	
driver.aboutothers.option.that.best.describes.claim= Please choose the option that best describes the claim and indicate in which month and year it occurred.	

#Insurance History
driver.aboutothers.continuousyear.insurance.coverage=How many years of continuous insurance coverage has this driver had?	
driver.aboutothers.interruptioncoverage=Has there been an interruption (gap or lapse) in this driver's insurance coverage of more than 2 years in the last 6 years?	
driver.aboutothers.outstanding.premiums.to.any.insurer=Does this driver currently owe outstanding premium to any insurer?	
driver.aboutothers.insurancerefusal=Does this driver ever had their policy cancelled in the last three years?	
driver.aboutothers.policyrefusal.because.of.nonpayment=Was it because of non-payment?	
driver.aboutothers.policyrefusal.how.many.times=How many times were they cancelled?	

driver.aboutothers.firstname=First name
driver.aboutothers.lastname=Last name

driver.aboutothers.monthswithoutparents=Number of months per year not living with parents

driver.postalcode=Postal code
driver.civicnumber=Street number
driver.streetname=Street name
driver.apartmentnumber=Apartment number (if applicable)
driver.city=City
driver.province=Province
driver.country=Country

driver.address.type.POBOX=PO BOX
driver.address.type.RR=RR
driver.address.type.GD=GENERAL DELIVERY
driver.address.type.other=

driver.last.move.000=Less than 6 months
driver.last.move.006=Between 6 months and 2 years
driver.last.move.024=More than 2 years

driver.last.move.I00=Less than 1 year
driver.last.move.I12=1 year, but less than 2
driver.last.move.I24=2 years, but less than 3
driver.last.move.I36=3 years, but less than 4
driver.last.move.I48=4 years, but less than 5
driver.last.move.I60=5 years or more

driver.validation.groupcode.valid.start=As a member of 
driver.validation.groupcode.valid.end=, you benefit from belairdirect's 'Your Group Program'!

# ENUM TYPE DESCRIPTION
driver.years.insured.continuously.0=Less than 1 year
driver.years.insured.continuously.1=1 year, but less than 2
driver.years.insured.continuously.2=2 years, but less than 3
driver.years.insured.continuously.3=3 years, but less than 4
driver.years.insured.continuously.4=4 years, but less than 5
driver.years.insured.continuously.5=5 years or more
driver.years.insured.continuously.6=6 years and more

driver.license.P=Regular or probationary permit
driver.license.R=Regular or probationary permit
driver.license.L=Learner's permit
driver.license.U=Not licensed
driver.license.A=From another province/country

driver.family.relationship.C=Spouse
driver.family.relationship.E=Child 
driver.family.relationship.F=Sister or brother
driver.family.relationship.M=Mother or father
driver.family.relationship.X=Other

driver.gender.F= Female
driver.gender.M= Male
true=Yes
false=No
null=N/D
driver.true=Yes
driver.false=No
driver.=N/A

driver.claim.year.00=Less than 1 year
driver.claim.year.01=1 year, but less than 2 
driver.claim.year.02=2 years, but less than 3
driver.claim.year.03=3 years, but less than 4
driver.claim.year.04=4 years, but less than 5
driver.claim.year.05= 5 years, but less than 6
driver.claim.year.06=6 years, but less than 7,
driver.claim.year.07=7 years and more

driver.province.name.AB=Alberta
driver.province.name.MB=Manitoba
driver.province.name.NL=Newfoundland and Labrador
driver.province.name.NS=Nova Scotia
driver.province.name.ON=Ontario
driver.province.name.QC=Quebec
driver.province.name.YT=Yukon
driver.province.name.BC=British Columbia
driver.province.name.NB=New Brunswick
driver.province.name.NT=Northwest Territories
driver.province.name.NU=Nunavut
driver.province.name.PE=Prince Edward Island
driver.province.name.SK=Saskatchewan
driver.province.name.US=United States
driver.province.name.EC=Europe
driver.province.name.OT=Other

driver.country.name.CA=Canada
driver.country.name.US=United States
driver.country.name.OT=Other

driver.marital.status.M=Couple
driver.marital.status.C=Single
driver.marital.status.V=Widowed
driver.marital.status.D=Divorced
driver.marital.status.S=Separated
driver.marital.status.F=Common law
driver.marital.status.E=Religious
driver.marital.status.1=Same sex partner
driver.marital.status.A=Other
