legend.link=Legend
legend.title=Legend

activity.title=Activity indicator
activity.text=This quote has had activity within the last 15 minutes.

system.origin.title=Systems Origin (O)

origin.intact.web.site= <b>i</b>
origin.broker.web.site= <b>B</b>
origin.intact.web.site.text=Intact web site
origin.broker.web.site.text=Broker web site

web.program.type.title=Active indicator (AQ/QQ/AS)
web.program.type.code.1=<b>AQ</b>
web.program.type.value.1=AutoQuote Web Program
web.program.type.code.2=<b>QQ</b>
web.program.type.value.2=AutoQuote Web Program
web.program.type.code.3=<b>AS</b>
web.program.type.value.3=Assignable for AutoQuote and QuickQuote

web.program.type.domain=<b>Definition for AQ and QQ</b>
web.program.type.domain.code.1=<b>X</b>
web.program.type.domain.value.1=Inactive
web.program.type.domain.code.2=<b>B</b>
web.program.type.domain.value.2=Broker Site only
web.program.type.domain.code.3=<b>I</b>
web.program.type.domain.value.3=Intact Site only
web.program.type.domain.code.4=<b>IB</b>
web.program.type.domain.value.4=Intact and Broker Site

assignable.domain=<b>Definition for AS</b>
assignable.domain.code.1=<b>*</b>
assignable.domain.value.1=Not Assignable
assignable.domain.code.2=<b>A</b>
assignable.domain.value.2=AutoQuote Assignable
assignable.domain.code.3=<b>Q</b>
assignable.domain.value.3=QuickQuote Assignable
assignable.domain.code.4=<b>AQ</b>
assignable.domain.value.4=AutoQuote and QuickQuote Assignable

consent.title=Consent indicators
consent.consent= Customer agreed to be contacted.
consent.noconsent= Customer did not agree to be contacted.

followup.title=Follow-up indicators
followup.notContacted=Customer never contacted.
followup.noFollowup=Customer contacted, no follow-up needed.
followup.followup=Customer contacted, a follow-up is needed.
followup.noneRequired=Customer was not contacted, no follow-up needed.
followup.noFollowup=Customer contacted but no follow-up required.
followup.noneRequired=Customer was not contacted, no follow-up needed.

status.title=Statuses
status.quoteIncomplete=Quote incomplete
status.quoteIncomplete.text=The customer has not completed the quote yet.
status.quoteIncomplete.nextSteps=<b>Next steps:</b> Look at contact indicator and contact the customer if he accepted to be contacted regarding this quote. 

status.quoteComplete=Quote complete
status.quoteComplete.text=An offer was provided to the customer.
status.quoteComplete.nextSteps=<b>Next steps:</b> Look at contact indicator and contact the customer if he accepted to be contacted regarding this quote.

status.purchaseAbandoned=Purchase incomplete
status.purchaseAbandoned.text=An offer was provided to the customer. Customer initiated the purchase process but did not finish.
status.purchaseAbandoned.nextSteps=<b>Next steps:</b> Contact the customer to confirm the coverage is not in effect.

status.purchaseCompleted=Purchase complete
status.purchaseCompleted.text=Customer clicked the purchase online button and finished the purchase process.
status.purchaseCompleted.nextSteps=<b>Next steps:</b> You can order MVR & Auto Plus. Contact the customer to make final arrangements.

status.expired=Expired
status.expired.text=Customer submitted this quote more than 30 days ago.
status.expired.nextSteps=<b>Next steps:</b> Contact the customer. Less than 10 days remain to upload the quote to Savers.

status.uploaded=Uploaded
status.uploaded.text=Quote was uploaded to Savers.
status.uploaded.nextSteps=<b>Next steps:</b> If final arrangements are not done, go to Savers and retrieve the quote from there.

status.accepted=Accepted
status.accepted.text=Quote has been uploaded to goBRIO and accepted for policy.
status.accepted.nextSteps=<b>Next steps:</b> : Ensure all final arrangements have been completed.

status.refused=Refused
status.refused.text=Quote has been uploaded to goBRIO but the policy issue has been refused by either the customer or Intact Insurance.
status.refused.nextSteps=<b>Next steps:</b> If refused by the customer, you may wish to follow-up in a few days time to see if their decision is final. If refused by Intact Insurance, no further submissions from this customer will be accepted.

status.PDF_DOWNLOADED=PDF Downloaded
status.PDF_DOWNLOADED.text=Broker version PDF of the quote has been downloaded.

status.roadblock.text=Quote has been blocked.
status.roadblock.not.canadian.text=Quote has been blocked.  No previous insurance in Canada.
status.roadblock.intact.client.text=Quote has been blocked. Already an Intact client.

