#POLICY HOLDER SECTION 

driver.aboutyou.permissioninfos=J'autorise Intact Assurance et mon courtier assigné à recueillir, utiliser, et communiquer mes renseignements personnels comme le permet la loi, dans le but de créer un profil à mon nom et de me fournir une soumission d'assurance auto.

driver.aboutyou.savewhenvehicleandhomeareinsuredwithus=Économisez jusqu'à 15% en combinant vos assurances auto et habitation! Aimeriez-vous voir ce que serait votre soumission si vous aviez également une assurance habitation auprès d'Intact Assurance?

#GENERAL INFORMATION SECTION

#DRIVING RECORD SECTION
driver.aboutyou.drivingrecord=Dossier de conduite
# Licence type
driver.aboutyou.licencetype=Classe du permis de conduire valide
driver.aboutyou.licensenumber=Numéro de permis de conduire :
driver.aboutyou.agewhenlicenseobtained=À quelle date avez-vous obtenu votre premier permis de conduire au Canada ou aux États-Unis?
driver.aboutyou.ontario.G2.obtained.when=Quand avez-vous obtenu votre permis de classe G2 de l'Ontario?
driver.aboutyou.ontario.G.obtained.when=Quand avez-vous obtenu votre permis de classe G de l'Ontario?
driver.aboutyou.trainingcourse=Avez-vous complété avec succès un cours de conduite dans une école reconnue dans les trois dernières années?

driver.aboutyou.claimsorlosses=Au cours des neuf dernières années, avez-vous fait des réclamations ou eu des accidents (responsables ou non) liés à la propriété ou à l'utilisation d'un véhicule automobile?
driver.aboutyou.how.many.claimsorlosses=Combiens d'accidents ou sinistres liés aux accidents avez-vous eu au cours des 6 dernières années ?
driver.aboutyou.howlongagolossoccur=À quand remonte la réclamation ou l'accident?
driver.aboutyou.typeofloss=Nature de l'accident ou de la réclamation

driver.aboutyou.commitedinfractions=Au cours des trois dernières années, avez-vous commis des infractions? (Ne pas tenir compte des contraventions de stationnement.) 

driver.aboutyou.conviction.date= Date de l'infraction
driver.aboutyou.nature.conviction=Type d'infraction

driver.aboutyou.class6.licence=Avez-vous aussi un permis de classe 6 ?
driver.aboutyou.ontario.licencesuspended=Au cours des six dernières années, votre permis de conduire ou celui de l'un des conducteurs inscrits à la police ou de toute autre personne habitant sous votre toit a-t-il été suspendu ou révoqué? (Ne pas tenir compte des suspensions de permis dues au non-paiement d'une contravention.)
driver.aboutyou.ontario.start.this.insurance.coverage=Pour quelle date aimeriez-vous que votre assurance entre en vigueur?
driver.aboutyou.retired.receiving.a.pension=Êtes-vous retraité ?
driver.aboutyou.fulltime.student.ontario=Êtes-vous étudiant à temps plein en Ontario?
driver.aboutyou.good.student.ontario=Maintenez-vous une moyenne de 80% ou plus (note moyenne de A ou meilleure)?
driver.aboutyou.student.type.of.school.ontario=Quel type d'établissement fréquentez-vous ?  
driver.aboutyou.school.more.than.100km.from.parentshome=Votre école est-elle située à plus de 100 kilomètres de votre résidence permanente?

driver.aboutyou.student.type.of.school.ontario.0=Collège ou Université
driver.aboutyou.student.type.of.school.ontario.1=Ecole secondaire

#INSURANCE HISTORY
driver.aboutyou.monthswithoutparents=Nombre de mois par année où vous n'habitez pas chez vos parents
driver.aboutyou.universitydegree=Êtes-vous diplômé d'une université canadienne (bac, maîtrise ou doctorat)?

driver.aboutyou.currentlyinsured=Actuellement, êtes-vous détenteur d'une assurance auto?
driver.aboutyou.insuredhowlong=Depuis combien d'années, sans interruption, êtes-vous assuré? 
driver.aboutyou.last.5years.with.same.insurer=Est-ce que les trois dernières années ont été avec le même assureur?
driver.aboutyou.currentinsurer=Qui est votre assureur le plus récent?

driver.aboutyou.insurancecoverage.northamerica=Have you ever had insurance coverage in North America
driver.aboutyou.howholdwhengetinsured=How old where you when you first obtained insurance coverage
driver.aboutyou.interruptioncoverage=Est-ce qu'il y a eu une interruption (intervalle ou laps) dans votre protection d'assurance de plus de 2 ans au cours des 6 dernières années?
driver.aboutyou.outstanding.premiums.to.any.insurer=Avez-vous des primes d'assurances non-payés avec n'importe quel assureur présentement?
driver.aboutyou.insurancerefusal=Au cours des quatre dernières années, votre police d'assurance auto ou celle de l'un des conducteurs inscrits à la police ou de toute autre personne habitant sous votre toit a-t-elle été résiliée ou annulée, ou un renouvellement a-t-il été refusé, par une compagnie d'assurance?
driver.aboutyou.policyrefusalreason=Quelle était la raison de l'annulation ou de la résiliation de cette police?
driver.aboutyou.policyrefusal.because.of.nonpayment=Est-ce que c'était à cause de non-paiement des primes ?
driver.aboutyou.policyrefusal.how.many.times=Combiens de fois est-ce que vous avez été annulé ?
driver.aboutyou.agreeToUseAutoRelyNetwork=Voulez-vous inclure ce rabais à votre soumission automobile ?


#OTHER DRIVERS

driver.aboutothers.universitydegree=Cette personne est-elle diplômée d'une université canadienne (bac, maîtrise ou doctorat)?
driver.aboutothers.retired.receiving.a.pension=Cette personne est-elle retraitée ?
driver.aboutothers.fulltime.student.ontario=Cette personne est-elle étudiante à temps plein en Ontario?
driver.aboutothers.school.more.than.100km.from.parentshome=L'école de cette personne est-elle située à plus de 100 kilomètres de sa résidence permanente?

#DRIVING RECORD

driver.aboutothers.licencetype=Classe du permis de conduire valide
driver.aboutothers.licensenumber=Numéro de permis de conduire :
driver.aboutothers.agewhenlicenseobtained=À quelle date cette personne a-t-elle obtenu son premier permis de conduire au Canada ou aux États-Unis?
driver.aboutothers.ontario.G2.obtained.when= Quand a-t-elle obtenu son permis de classe G2 de l'Ontario?	
driver.aboutothers.ontario.G.obtained.when=Quand a-t-elle obtenu son permis de classe G de l'Ontario?
driver.aboutothers.trainingcourse=Cette personne a-t-elle réussi un cours de conduite dans une école reconnue dans les trois dernières années?
driver.aboutothers.good.student.ontario=Cette personne maintient-elle une moyenne de "A" (80%) ou plus?

# CLAIMS OR LOSSES
driver.aboutothers.claimsorlosses=Au cours des neuf dernières années, cette personne a-t-elle fait des réclamations ou eu des accidents (responsables ou non, déclarés ou non) liés à la propriété ou à l'utilisation d'un véhicule automobile?
driver.aboutothers.howlongagolossoccur=À quand remonte le sinistre?
driver.aboutothers.claimamount=Montant total de la réclamation
driver.aboutothers.commitedinfractions=Au cours des trois dernières années, cette personne a-t-elle commis des infractions? (Ne pas tenir compte des contraventions de stationnement.) 

driver.aboutothers.conviction.date=Date de l'infraction
driver.aboutothers.nature.conviction=Type d'infraction

driver.aboutothers.class.6.licence=Est ce que ce conducteur a aussi un permis de classe 6 ?

driver.aboutothers.licencesuspended=Est-ce que ce conducteur a jamais eu son permis de conduire suspendu ou révoqué au cours des 6 dernières années?
driver.aboutothers.how.many.claimsorlosses=Combiens d'accidents ou sinistres liés aux accidents est-ce que ce conducteur a eu au cours des 6 dernières années ?
driver.aboutothers.accident.description.and.date=Veuillez sélectionner l'option qui mieux explique le nature de l'accident et indiquer le mois et l'année il a eu lieu.
driver.aboutothers.driving.infractions=Combiens d'infractions de conduite est-ce que ce conducteur a eu au cours des 3 dernières années ? (exclure les contraventions de stationnement)
driver.aboutothers.driving.conviction.description.and.date=Veuillez sélectionner l'option qui mieux explique le nature de l'infraction et indiquer le mois et l'année il a eu lieu.
driver.aboutothers.how.many.nonaccident.claims=Combiens de sinistres non-liés aux accidents (comme sinistres de vol ou pare-brise) est-ce que ce conducteur a eu au cours des 6 dernières années ?
driver.aboutothers.option.that.best.describes.claim=Veuillez sélectionner l'option qui mieux explique le nature du sinistre et indiquer le mois et l'année il a eu lieu.
driver.aboutothers.continuousyear.insurance.coverage=Combien d'années d'assurance continues est-ce que ce conducteur a eu?
driver.aboutothers.interruptioncoverage=Est-ce qu'il y a eu une interruption (intervalle ou laps) dans la protection d'assurance de ce conducteur de plus de 2 ans au cours des 6 dernières années?
driver.aboutothers.outstanding.premiums.to.any.insurer=Est-ce que ce conducteur a des primes d'assurances non-payés avec n'importe quel assureur présentement?
driver.aboutothers.insurancerefusal=Est-ce que ce conducteur a jamais eu son contrat d'assurance annulé au cours des 3 dernières années.
driver.aboutothers.policyrefusal.because.of.nonpayment=Est-ce que c'était à cause de non-paiement des primes ?
driver.aboutothers.policyrefusal.how.many.times=Combiens de fois est-ce que ce conducteur a était annulé.

driver.aboutothers.drivingrecord.tab=Dossier de conduite

driver.aboutothers.monthswithoutparents=Nombre de mois par année où cette personne n'habite pas chez ses parents

#Insurance History
driver.aboutothers.currentlyinsured=Cette personne est-elle présentement détentrice d'une assurance auto?
driver.aboutothers.insuredhowlong=Depuis combien d'années, sans interruption, cette personne est-elle assurée?

#Previous address

driver.postalcode=Code postal
driver.civicnumber=Numéro
driver.streetname=Nom de la rue
driver.apartmentnumber=N<span class='superscript'>o</span> d'appartement (si applicable)
driver.city=Ville
driver.province=Province
driver.country=Pays

driver.last.move.000=Moins de 6 mois 
driver.last.move.006=Entre 6 mois et 2 ans 
driver.last.move.024=Plus de 2 ans

driver.last.move.I00=Moins de 1 an
driver.last.move.I12=1 an, mais moins de 2
driver.last.move.I24=2 ans, mais moins de 3
driver.last.move.I36=3 ans, mais moins que 4
driver.last.move.I48=4 ans, mais moins que 5
driver.last.move.I60=5 ans ou plus

driver.validation.groupcode.valid.start=À titre de membre de 
driver.validation.groupcode.valid.end=, vous bénéficiez des avantages de « Votre programme groupe » de belairdirect!

# ENUM TYPE DESCRIPTION
driver.years.insured.continuously.0=Moins de 1 an
driver.years.insured.continuously.1=1 an, mais moins de 2
driver.years.insured.continuously.2=2 ans, mais moins de 3
driver.years.insured.continuously.3=3 ans, mais moins de 4
driver.years.insured.continuously.4=4 ans, mais moins de 5
driver.years.insured.continuously.5=5 ans, mais moins de 6
driver.years.insured.continuously.6=6 ans, mais moins de 7
driver.years.insured.continuously.7=7 ans, mais moins de 8
driver.years.insured.continuously.8=8 ans, mais moins de 9
driver.years.insured.continuously.9=9 ans, mais moins de 10
driver.years.insured.continuously.10=10 et plus


# pour le QC seulement
driver.error.postalCode.client=Veuillez saisir un code postal valide du Québec.


# ENUM TYPE DESCRIPTION
driver.license.P=Permis régulier ou probatoire
driver.license.R=Permis régulier ou probatoire
driver.license.L=Permis d'apprenti conducteur
driver.license.U=Sans permis
driver.license.A=D'une autre province ou pays

driver.license.class.G=Permis de classe G de l'Ontario
driver.license.class.G1=Permis de classe G1 de l'Ontario
driver.license.class.G2=Permis de classe G2 de l'Ontario


driver.license.class.R=Permis de classe G de l'Ontario
driver.license.class.L=Permis de classe G1 de l'Ontario
driver.license.class.P=Permis de classe G2 de l'Ontario
driver.license.class.U=Sans permis
driver.license.class.A=D'une autre province ou pays

driver.claim.year.00=Moins de 1 an
driver.claim.year.01=1 an, mais moins de 2 
driver.claim.year.02=2 ans, mais moins de 3
driver.claim.year.03=3 ans, mais moins de 4
driver.claim.year.04=4 ans, mais moins de 5
driver.claim.year.05=5 ans, mais moins de 6
driver.claim.year.06=6 ans, mais moins de 7,
driver.claim.year.07=7 ans, mais moins de 10

driver.claim.nature.AA	=	Accident responsable
driver.claim.nature.FIR	=	Feu
driver.claim.nature.VAN	=	Vandalisme
driver.claim.nature.TH	=	Vol
driver.claim.nature.GW	=	Remplacement de vitres
driver.claim.nature.GR	=	Réparation de vitres
driver.claim.nature.HR	=	Délit de fuite
driver.claim.nature.NA	=	Accident non responsable
driver.claim.nature.WN	=	Tempête de vent ou de grêle
driver.claim.nature.IA	=	Collision avec un animal

driver.marital.status.M=En couple
driver.marital.status.C=Célibataire

driver.month.0=Janvier
driver.month.1=Février
driver.month.2=Mars
driver.month.3=Avril
driver.month.4=Mai
driver.month.5=Juin
driver.month.6=Juillet
driver.month.7=Août
driver.month.8=Septembre
driver.month.9=Octobre
driver.month.10=Novembre
driver.month.11=Décembre

driver.conviction.type.CD=Conduite négligente
driver.conviction.type.CN=Négligence criminelle
driver.conviction.type.DD=Conduite dangeureuse
driver.conviction.type.DUS=Conduire avec un permis suspendu
driver.conviction.type.OMVNI=Conduire sans assurance
driver.conviction.type.IMP=Infraction reliée à la drogue ou à lalcool
driver.conviction.type.FCIC=Incapacité à fournir une preuve d'assurance
driver.conviction.type.TS=Omission de se conformer à la signalisation routière
driver.conviction.type.FTSSA=Omission de rester sur les lieux d'un accident
driver.conviction.type.FRA=Omission de rapporter un accident ou des dommages
driver.conviction.type.FTSPO=Omission de s'arrêter pour la police
driver.conviction.type.SB=Omission de porter la ceinture de sécurité
driver.conviction.type.TFTC=Omission de respecter une distance sécuritaire
driver.conviction.type.GNA=Non respect des conditions des permis de conduire G1 ou G2 licence infractions non relié à l'alcool
driver.conviction.type.GAL=Non respect des conditions des permis de conduire G1 ou G2 licence infractions relié à l'alcool
driver.conviction.type.PSB=Dépassement illégal d'un autobus scolaire
driver.conviction.type.RAC=Course
driver.conviction.type.PSG=Dépassement ou excès de vitesse en zone scolaire
driver.conviction.type.SP1=Excès de vitesse - 0 à 50 km/h au dessus de la limite
driver.conviction.type.SP2=Excès de vitesse - 50 à 60 km/h au dessus de la limite
driver.conviction.type.SP3=Excès de vitesse - 60 km/h ou plus au dessus de la limite
driver.conviction.type.FTS=Infraction reliée aux signalements
driver.conviction.type.UV=Véhicule non sécuritaire
driver.conviction.type.OT1=Autre infraction mineure
driver.conviction.type.OT2=Autre infraction majeure
driver.conviction.type.OT3=Autre infraction criminelle