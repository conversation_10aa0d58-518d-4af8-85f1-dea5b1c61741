<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans" 
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:p= "http://www.springframework.org/schema/p"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
						http://www.springframework.org/schema/context
						http://www.springframework.org/schema/context/spring-context-3.1.xsd">

	<context:annotation-config />
	
	<bean id="service.sanitizing.default" class="com.intact.tools.sanitization.service.mixed.MixedSanitizationApplicationService" lazy-init="true">
		<property name="services">
			<map>
				<entry key="header">
					<list>
						<ref bean="service.sanitizing.whitelist"/>	
						<ref bean="service.sanitizing.encoding"/>
					</list>
				</entry>
			</map>
		</property>
	</bean>
	
	<bean id="service.sanitizing.encoding" class="com.intact.tools.sanitization.service.encoding.ReloadableEncodingSanitizationApplicationService" lazy-init="true">
		<property name="cacheSeconds" value="3600" />
		<property name="dataService" ref="service.data.bundle" />
		<property name="encodings">
			<bean class="java.util.concurrent.ConcurrentHashMap" />
		</property>
		<property name="stringService">  
			<bean class="com.intact.tools.string.service.StringService">
				<property name="service">
					<bean class="com.intact.tools.string.service.apache.ApacheStringApplicationService" />
				</property>	
				<property name="serviceName" value="default" />
			</bean>
		
		</property>
	</bean>
	
	<bean id="service.sanitizing.whitelist" class="com.intact.tools.sanitization.service.whitelist.ReloadableWhiteListSanitizationApplicationService" lazy-init="true">
		<property name="cacheSeconds" value="3600" />
		<property name="dataService" ref="service.data.bundle" />
		<property name="whiteListings">
			<bean class="java.util.concurrent.ConcurrentHashMap" />
		</property>
	</bean>
	
	<import resource="data-exception.xml" />

	<bean id="service.data.bundle" class="com.intact.tools.data.service.props.ReloadablePropertiesDataApplicationService" lazy-init="true">
		<property name="cacheSeconds" value="3600" />
		<property name="configurations">
			<bean class="java.util.concurrent.ConcurrentHashMap" />
		</property>
	</bean>
	
</beans>