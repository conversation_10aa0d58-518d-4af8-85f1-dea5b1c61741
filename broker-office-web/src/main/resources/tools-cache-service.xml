<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

	<bean class="com.intact.tools.cache.service.ehcache.EhCacheApplicationService">
		<property name="configurations">
			<map>
				<entry key="webzone">
					<bean class="com.intact.tools.cache.service.ehcache.util.Configuration">
						<property name="configPath" value="/ehcache.xml" />
					</bean>
				</entry>
			</map>
		</property>
	</bean>
</beans>