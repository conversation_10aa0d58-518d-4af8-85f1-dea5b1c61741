<?xml version="1.0" encoding="utf-8"?>
<wsdl:definitions name="IntactWebServices" targetNamespace="http://tempuri.org/" xmlns:wsdl="http://schemas.xmlsoap.org/wsdl/" xmlns:wsap="http://schemas.xmlsoap.org/ws/2004/08/addressing/policy" xmlns:wsa10="http://www.w3.org/2005/08/addressing" xmlns:tns="http://tempuri.org/" xmlns:msc="http://schemas.microsoft.com/ws/2005/12/wsdl/contract" xmlns:soapenc="http://schemas.xmlsoap.org/soap/encoding/" xmlns:wsx="http://schemas.xmlsoap.org/ws/2004/09/mex" xmlns:soap="http://schemas.xmlsoap.org/wsdl/soap/" xmlns:wsam="http://www.w3.org/2007/05/addressing/metadata" xmlns:wsa="http://schemas.xmlsoap.org/ws/2004/08/addressing" xmlns:wsp="http://schemas.xmlsoap.org/ws/2004/09/policy" xmlns:wsaw="http://www.w3.org/2006/05/addressing/wsdl" xmlns:soap12="http://schemas.xmlsoap.org/wsdl/soap12/" xmlns:wsu="http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
	<wsp:Policy wsu:Id="BasicHttpsBinding_IIntactWebServices_policy">
		<wsp:ExactlyOne>
			<wsp:All>
				<sp:TransportBinding xmlns:sp="http://schemas.xmlsoap.org/ws/2005/07/securitypolicy">
					<wsp:Policy>
						<sp:TransportToken>
							<wsp:Policy>
								<sp:HttpsToken RequireClientCertificate="false"/>
							</wsp:Policy>
						</sp:TransportToken>
						<sp:AlgorithmSuite>
							<wsp:Policy>
								<sp:Basic256/>
							</wsp:Policy>
						</sp:AlgorithmSuite>
						<sp:Layout>
							<wsp:Policy>
								<sp:Strict/>
							</wsp:Policy>
						</sp:Layout>
					</wsp:Policy>
				</sp:TransportBinding>
			</wsp:All>
		</wsp:ExactlyOne>
	</wsp:Policy>
	<wsdl:types>
		<xs:schema elementFormDefault="qualified" targetNamespace="http://tempuri.org/" xmlns:xs="http://www.w3.org/2001/XMLSchema">
			<xs:import namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
			<xs:import namespace="http://schemas.datacontract.org/2004/07/IntactWebServices"/>
			<xs:element name="retrieveContactsNumbers">
				<xs:complexType>
					<xs:sequence>
						<xs:element minOccurs="0" name="_values" nillable="true" type="q1:ArrayOfstring" xmlns:q1="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
						<xs:element minOccurs="0" name="sDID" nillable="true" type="xs:string"/>
						<xs:element minOccurs="0" name="lContactColumnNames" nillable="true" type="q2:ArrayOfstring" xmlns:q2="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
						<xs:element minOccurs="0" name="sSearchType" nillable="true" type="xs:string"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="retrieveContactsNumbersResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element minOccurs="0" name="retrieveContactsNumbersResult" nillable="true" type="q3:ArrayOfContacts" xmlns:q3="http://schemas.datacontract.org/2004/07/IntactWebServices"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="updateValue">
				<xs:complexType>
					<xs:sequence>
						<xs:element minOccurs="0" name="I3Value" nillable="true" type="xs:string"/>
						<xs:element minOccurs="0" name="values" nillable="true" type="q4:ArrayOfKeyValueOfstringstring" xmlns:q4="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
						<xs:element minOccurs="0" name="sDID" nillable="true" type="xs:string"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="updateValueResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element minOccurs="0" name="updateValueResult" type="xs:boolean"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="setAgentStatus">
				<xs:complexType>
					<xs:sequence>
						<xs:element minOccurs="0" name="sUserId" nillable="true" type="xs:string"/>
						<xs:element minOccurs="0" name="sStatus" nillable="true" type="xs:string"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="setAgentStatusResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element minOccurs="0" name="setAgentStatusResult" type="xs:boolean"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="setCallAttributes">
				<xs:complexType>
					<xs:sequence>
						<xs:element minOccurs="0" name="_attributes" nillable="true" type="q5:ArrayOfstring" xmlns:q5="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
						<xs:element minOccurs="0" name="_values" nillable="true" type="q6:ArrayOfstring" xmlns:q6="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
						<xs:element minOccurs="0" name="interactionId" nillable="true" type="xs:string"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="setCallAttributesResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element minOccurs="0" name="setCallAttributesResult" type="xs:boolean"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="addRecordToCallingList">
				<xs:complexType>
					<xs:sequence>
						<xs:element minOccurs="0" name="columns" nillable="true" type="q7:ArrayOfKeyValueOfstringstring" xmlns:q7="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
						<xs:element minOccurs="0" name="sDID" nillable="true" type="xs:string"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="addRecordToCallingListResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element minOccurs="0" name="addRecordToCallingListResult" type="xs:long"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="getCampaignName">
				<xs:complexType>
					<xs:sequence>
						<xs:element minOccurs="0" name="sDID" nillable="true" type="xs:string"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="getCampaignNameResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element minOccurs="0" name="getCampaignNameResult" nillable="true" type="xs:string"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="test">
				<xs:complexType>
					<xs:sequence>
						<xs:element minOccurs="0" name="sPhone" nillable="true" type="xs:string"/>
						<xs:element minOccurs="0" name="sDID" nillable="true" type="xs:string"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
			<xs:element name="testResponse">
				<xs:complexType>
					<xs:sequence>
						<xs:element minOccurs="0" name="testResult" type="xs:boolean"/>
					</xs:sequence>
				</xs:complexType>
			</xs:element>
		</xs:schema>
		<xs:schema attributeFormDefault="qualified" elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/2003/10/Serialization/" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.microsoft.com/2003/10/Serialization/">
			<xs:element name="anyType" nillable="true" type="xs:anyType"/>
			<xs:element name="anyURI" nillable="true" type="xs:anyURI"/>
			<xs:element name="base64Binary" nillable="true" type="xs:base64Binary"/>
			<xs:element name="boolean" nillable="true" type="xs:boolean"/>
			<xs:element name="byte" nillable="true" type="xs:byte"/>
			<xs:element name="dateTime" nillable="true" type="xs:dateTime"/>
			<xs:element name="decimal" nillable="true" type="xs:decimal"/>
			<xs:element name="double" nillable="true" type="xs:double"/>
			<xs:element name="float" nillable="true" type="xs:float"/>
			<xs:element name="int" nillable="true" type="xs:int"/>
			<xs:element name="long" nillable="true" type="xs:long"/>
			<xs:element name="QName" nillable="true" type="xs:QName"/>
			<xs:element name="short" nillable="true" type="xs:short"/>
			<xs:element name="string" nillable="true" type="xs:string"/>
			<xs:element name="unsignedByte" nillable="true" type="xs:unsignedByte"/>
			<xs:element name="unsignedInt" nillable="true" type="xs:unsignedInt"/>
			<xs:element name="unsignedLong" nillable="true" type="xs:unsignedLong"/>
			<xs:element name="unsignedShort" nillable="true" type="xs:unsignedShort"/>
			<xs:element name="char" nillable="true" type="tns:char"/>
			<xs:simpleType name="char">
				<xs:restriction base="xs:int"/>
			</xs:simpleType>
			<xs:element name="duration" nillable="true" type="tns:duration"/>
			<xs:simpleType name="duration">
				<xs:restriction base="xs:duration">
					<xs:pattern value="\-?P(\d*D)?(T(\d*H)?(\d*M)?(\d*(\.\d*)?S)?)?"/>
					<xs:minInclusive value="-P10675199DT2H48M5.4775808S"/>
					<xs:maxInclusive value="P10675199DT2H48M5.4775807S"/>
				</xs:restriction>
			</xs:simpleType>
			<xs:element name="guid" nillable="true" type="tns:guid"/>
			<xs:simpleType name="guid">
				<xs:restriction base="xs:string">
					<xs:pattern value="[\da-fA-F]{8}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{4}-[\da-fA-F]{12}"/>
				</xs:restriction>
			</xs:simpleType>
			<xs:attribute name="FactoryType" type="xs:QName"/>
			<xs:attribute name="Id" type="xs:ID"/>
			<xs:attribute name="Ref" type="xs:IDREF"/>
		</xs:schema>
		<xs:schema elementFormDefault="qualified" targetNamespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
			<xs:complexType name="ArrayOfstring">
				<xs:sequence>
					<xs:element minOccurs="0" maxOccurs="unbounded" name="string" nillable="true" type="xs:string"/>
				</xs:sequence>
			</xs:complexType>
			<xs:element name="ArrayOfstring" nillable="true" type="tns:ArrayOfstring"/>
			<xs:complexType name="ArrayOfKeyValueOfstringanyType">
				<xs:annotation>
					<xs:appinfo>
						<IsDictionary xmlns="http://schemas.microsoft.com/2003/10/Serialization/">true</IsDictionary>
					</xs:appinfo>
				</xs:annotation>
				<xs:sequence>
					<xs:element minOccurs="0" maxOccurs="unbounded" name="KeyValueOfstringanyType">
						<xs:complexType>
							<xs:sequence>
								<xs:element name="Key" nillable="true" type="xs:string"/>
								<xs:element name="Value" nillable="true" type="xs:anyType"/>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
			</xs:complexType>
			<xs:element name="ArrayOfKeyValueOfstringanyType" nillable="true" type="tns:ArrayOfKeyValueOfstringanyType"/>
			<xs:complexType name="ArrayOfKeyValueOfstringstring">
				<xs:annotation>
					<xs:appinfo>
						<IsDictionary xmlns="http://schemas.microsoft.com/2003/10/Serialization/">true</IsDictionary>
					</xs:appinfo>
				</xs:annotation>
				<xs:sequence>
					<xs:element minOccurs="0" maxOccurs="unbounded" name="KeyValueOfstringstring">
						<xs:complexType>
							<xs:sequence>
								<xs:element name="Key" nillable="true" type="xs:string"/>
								<xs:element name="Value" nillable="true" type="xs:string"/>
							</xs:sequence>
						</xs:complexType>
					</xs:element>
				</xs:sequence>
			</xs:complexType>
			<xs:element name="ArrayOfKeyValueOfstringstring" nillable="true" type="tns:ArrayOfKeyValueOfstringstring"/>
		</xs:schema>
		<xs:schema elementFormDefault="qualified" targetNamespace="http://schemas.datacontract.org/2004/07/IntactWebServices" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:tns="http://schemas.datacontract.org/2004/07/IntactWebServices">
			<xs:import namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
			<xs:complexType name="ArrayOfContacts">
				<xs:sequence>
					<xs:element minOccurs="0" maxOccurs="unbounded" name="Contacts" nillable="true" type="tns:Contacts"/>
				</xs:sequence>
			</xs:complexType>
			<xs:element name="ArrayOfContacts" nillable="true" type="tns:ArrayOfContacts"/>
			<xs:complexType name="Contacts">
				<xs:sequence>
					<xs:element minOccurs="0" name="id" nillable="true" type="xs:string"/>
					<xs:element minOccurs="0" name="m_dicValues" nillable="true" type="q1:ArrayOfKeyValueOfstringanyType" xmlns:q1="http://schemas.microsoft.com/2003/10/Serialization/Arrays"/>
					<xs:element minOccurs="0" name="m_sTableName" nillable="true" type="xs:string"/>
				</xs:sequence>
			</xs:complexType>
			<xs:element name="Contacts" nillable="true" type="tns:Contacts"/>
		</xs:schema>
	</wsdl:types>
	<wsdl:message name="IIntactWebServices_retrieveContactsNumbers_InputMessage">
		<wsdl:part name="parameters" element="tns:retrieveContactsNumbers"/>
	</wsdl:message>
	<wsdl:message name="IIntactWebServices_retrieveContactsNumbers_OutputMessage">
		<wsdl:part name="parameters" element="tns:retrieveContactsNumbersResponse"/>
	</wsdl:message>
	<wsdl:message name="IIntactWebServices_updateValue_InputMessage">
		<wsdl:part name="parameters" element="tns:updateValue"/>
	</wsdl:message>
	<wsdl:message name="IIntactWebServices_updateValue_OutputMessage">
		<wsdl:part name="parameters" element="tns:updateValueResponse"/>
	</wsdl:message>
	<wsdl:message name="IIntactWebServices_setAgentStatus_InputMessage">
		<wsdl:part name="parameters" element="tns:setAgentStatus"/>
	</wsdl:message>
	<wsdl:message name="IIntactWebServices_setAgentStatus_OutputMessage">
		<wsdl:part name="parameters" element="tns:setAgentStatusResponse"/>
	</wsdl:message>
	<wsdl:message name="IIntactWebServices_setCallAttributes_InputMessage">
		<wsdl:part name="parameters" element="tns:setCallAttributes"/>
	</wsdl:message>
	<wsdl:message name="IIntactWebServices_setCallAttributes_OutputMessage">
		<wsdl:part name="parameters" element="tns:setCallAttributesResponse"/>
	</wsdl:message>
	<wsdl:message name="IIntactWebServices_addRecordToCallingList_InputMessage">
		<wsdl:part name="parameters" element="tns:addRecordToCallingList"/>
	</wsdl:message>
	<wsdl:message name="IIntactWebServices_addRecordToCallingList_OutputMessage">
		<wsdl:part name="parameters" element="tns:addRecordToCallingListResponse"/>
	</wsdl:message>
	<wsdl:message name="IIntactWebServices_getCampaignName_InputMessage">
		<wsdl:part name="parameters" element="tns:getCampaignName"/>
	</wsdl:message>
	<wsdl:message name="IIntactWebServices_getCampaignName_OutputMessage">
		<wsdl:part name="parameters" element="tns:getCampaignNameResponse"/>
	</wsdl:message>
	<wsdl:message name="IIntactWebServices_test_InputMessage">
		<wsdl:part name="parameters" element="tns:test"/>
	</wsdl:message>
	<wsdl:message name="IIntactWebServices_test_OutputMessage">
		<wsdl:part name="parameters" element="tns:testResponse"/>
	</wsdl:message>
	<wsdl:portType name="IIntactWebServices">
		<wsdl:operation name="retrieveContactsNumbers">
			<wsdl:input wsaw:Action="http://tempuri.org/IIntactWebServices/retrieveContactsNumbers" message="tns:IIntactWebServices_retrieveContactsNumbers_InputMessage"/>
			<wsdl:output wsaw:Action="http://tempuri.org/IIntactWebServices/retrieveContactsNumbersResponse" message="tns:IIntactWebServices_retrieveContactsNumbers_OutputMessage"/>
		</wsdl:operation>
		<wsdl:operation name="updateValue">
			<wsdl:input wsaw:Action="http://tempuri.org/IIntactWebServices/updateValue" message="tns:IIntactWebServices_updateValue_InputMessage"/>
			<wsdl:output wsaw:Action="http://tempuri.org/IIntactWebServices/updateValueResponse" message="tns:IIntactWebServices_updateValue_OutputMessage"/>
		</wsdl:operation>
		<wsdl:operation name="setAgentStatus">
			<wsdl:input wsaw:Action="http://tempuri.org/IIntactWebServices/setAgentStatus" message="tns:IIntactWebServices_setAgentStatus_InputMessage"/>
			<wsdl:output wsaw:Action="http://tempuri.org/IIntactWebServices/setAgentStatusResponse" message="tns:IIntactWebServices_setAgentStatus_OutputMessage"/>
		</wsdl:operation>
		<wsdl:operation name="setCallAttributes">
			<wsdl:input wsaw:Action="http://tempuri.org/IIntactWebServices/setCallAttributes" message="tns:IIntactWebServices_setCallAttributes_InputMessage"/>
			<wsdl:output wsaw:Action="http://tempuri.org/IIntactWebServices/setCallAttributesResponse" message="tns:IIntactWebServices_setCallAttributes_OutputMessage"/>
		</wsdl:operation>
		<wsdl:operation name="addRecordToCallingList">
			<wsdl:input wsaw:Action="http://tempuri.org/IIntactWebServices/addRecordToCallingList" message="tns:IIntactWebServices_addRecordToCallingList_InputMessage"/>
			<wsdl:output wsaw:Action="http://tempuri.org/IIntactWebServices/addRecordToCallingListResponse" message="tns:IIntactWebServices_addRecordToCallingList_OutputMessage"/>
		</wsdl:operation>
		<wsdl:operation name="getCampaignName">
			<wsdl:input wsaw:Action="http://tempuri.org/IIntactWebServices/getCampaignName" message="tns:IIntactWebServices_getCampaignName_InputMessage"/>
			<wsdl:output wsaw:Action="http://tempuri.org/IIntactWebServices/getCampaignNameResponse" message="tns:IIntactWebServices_getCampaignName_OutputMessage"/>
		</wsdl:operation>
		<wsdl:operation name="test">
			<wsdl:input wsaw:Action="http://tempuri.org/IIntactWebServices/test" message="tns:IIntactWebServices_test_InputMessage"/>
			<wsdl:output wsaw:Action="http://tempuri.org/IIntactWebServices/testResponse" message="tns:IIntactWebServices_test_OutputMessage"/>
		</wsdl:operation>
	</wsdl:portType>
	<wsdl:binding name="BasicHttpsBinding_IIntactWebServices" type="tns:IIntactWebServices">
		<wsp:PolicyReference URI="#BasicHttpsBinding_IIntactWebServices_policy"/>
		<soap:binding transport="http://schemas.xmlsoap.org/soap/http"/>
		<wsdl:operation name="retrieveContactsNumbers">
			<soap:operation soapAction="http://tempuri.org/IIntactWebServices/retrieveContactsNumbers" style="document"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="updateValue">
			<soap:operation soapAction="http://tempuri.org/IIntactWebServices/updateValue" style="document"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="setAgentStatus">
			<soap:operation soapAction="http://tempuri.org/IIntactWebServices/setAgentStatus" style="document"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="setCallAttributes">
			<soap:operation soapAction="http://tempuri.org/IIntactWebServices/setCallAttributes" style="document"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="addRecordToCallingList">
			<soap:operation soapAction="http://tempuri.org/IIntactWebServices/addRecordToCallingList" style="document"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="getCampaignName">
			<soap:operation soapAction="http://tempuri.org/IIntactWebServices/getCampaignName" style="document"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
		<wsdl:operation name="test">
			<soap:operation soapAction="http://tempuri.org/IIntactWebServices/test" style="document"/>
			<wsdl:input>
				<soap:body use="literal"/>
			</wsdl:input>
			<wsdl:output>
				<soap:body use="literal"/>
			</wsdl:output>
		</wsdl:operation>
	</wsdl:binding>
	<wsdl:service name="IntactWebServices">
		<wsdl:port name="BasicHttpsBinding_IIntactWebServices" binding="tns:BasicHttpsBinding_IIntactWebServices">
			<soap:address location="https://intactdialer.iad.ca.inet/intactdialerWebServices/IntactWebServices.svc"/>
		</wsdl:port>
	</wsdl:service>
</wsdl:definitions>