<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:ui="http://java.sun.com/jsf/facelets"
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:f="http://java.sun.com/jsf/core"
	  xmlns:p="http://primefaces.org/ui">

#{spoeController.initialize()}

<head>
	<link href="style/richComponent.css" rel="stylesheet" type="text/css" media="all" />
	<link href="style/main.css" rel="stylesheet" type="text/css" media="all" />
	<script src="javascript/search.js" type="text/javascript" />
	<script src="javascript/richComponent.js" type="text/javascript" />
	<script src="javascript/modalPanel.js" type="text/javascript"/>
</head>
<f:loadBundle var="spoe" basename="com.intact.brokeroffice.controller.spoe.spoe"/>

<!--Only for debugging in development mode. Use ctrl+shift+d -->
<ui:debug rendered="#{facesContext.application.projectStage == 'Development'}" />

<p:panel styleClass="main">

	<p:panel styleClass="header">
		<p:spacer height="100px" />
		<h:graphicImage url="/image/IntactLogo.gif" alt=""/>
	</p:panel>

	<h:form id="spoeForm" rendered="#{spoeController.isSpoeMode}">
		<p:spacer height="30"/>
		<p:panel>

			<p:panelGrid columns="1" styleClass="formSpoe" cellspacing="0" >

				<h:outputText value="#{spoe['access.level']}" />
				<h:graphicImage url="/image/question_delimiter_arrow.gif" alt=""/>
				<h:selectOneMenu id="accessLevel" value="#{spoeController.spoeBean.selectedAccessLevel}" >
					<f:selectItems value="#{spoeController.accessLevel}" />
					<p:ajax event="change" listener="#{spoeController.onAccessLevelChanged}" update="userId, masterBroker, province"/>
				</h:selectOneMenu>

				<h:outputText value="#{spoe['user.id']}" />
				<h:graphicImage url="/image/question_delimiter_arrow.gif" alt=""/>
				<h:panelGroup id="userId">
					<h:inputText value="#{spoeController.spoeBean.userId}" size="14" maxlength="12"/>
				</h:panelGroup>

				<h:outputText value="#{spoe['province']}" />
				<h:graphicImage url="/image/question_delimiter_arrow.gif" alt=""/>
				<h:panelGroup id="province">
					<h:selectOneMenu value="#{spoeController.spoeBean.selectedProvince}" rendered="#{spoeController.spoeBean.brokerAccess}">
						<f:selectItems value="#{spoeController.provinces}" var="province" itemValue="#{province.code}" itemLabel="#{spoe[province.name()]}" />
						<p:ajax event="change" listener="#{spoeController.onProvinceChanged}" update="masterBroker" />
					</h:selectOneMenu>
					<h:outputText value="N/A" rendered="#{!spoeController.spoeBean.brokerAccess}"/>
				</h:panelGroup>
				<h:outputText value="#{spoe['brokers']}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" alt=""/>
				<h:panelGroup id="masterBroker">
					<p:dataGrid value="#{spoeController.masterBrokers}" styleClass="masterBroker" var="masterBrokers" columns="1" rendered="#{spoeController.spoeBean.brokerAccess}">
						<h:selectBooleanCheckbox value="#{masterBrokers.selected}" />
						<h:outputText value="#{masterBrokers.number} #{masterBrokers.name}"/>
					</p:dataGrid>
					<h:outputText value="N/A" rendered="#{!spoeController.spoeBean.brokerAccess}"/>
				</h:panelGroup>
				<h:commandButton id="submit" value="#{spoe['submit']}" action="#{spoeController.login}" />
			</p:panelGrid>
		</p:panel>
	</h:form>

	<p:spacer height="10px" />
</p:panel>
</html>
