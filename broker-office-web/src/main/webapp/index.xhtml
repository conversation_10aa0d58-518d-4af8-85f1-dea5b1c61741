<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui">

	<f:view locale="#{languageController.locale}" />

	<f:loadBundle var="global" basename="com.intact.brokeroffice.resources.global"/>
	<f:loadBundle var="legend" basename="com.intact.brokeroffice.controller.legend.legend"/>

	<head>
		<!-- PM10280, Add compatibilty tag to insure WebZone work in IE9 and up -->
		<meta http-equiv="X-UA-Compatible" content="IE=8"/>

		<link rel="stylesheet" type="text/css" href="style/richComponent.css" media="all" />
		<link rel="stylesheet" type="text/css" href="style/main.css"  media="all" />
		<script src="javascript/richComponent.js" type="text/javascript" />
		<script src="javascript/phoneValidator.js" type="text/javascript" />
		<script src="javascript/search.js" type="text/javascript" />
		<script src="javascript/modalPanel.js" type="text/javascript"/>
		<link rel="stylesheet" type="text/css" href="style/xtra.css"  media="all" />
		<script type="text/javascript">
			var timeoutLen = 105*60*1000;
			var timeout;
			var searchClosedByLink = false;
			var searchIsOn = false;

			setTimer();

            // Used in page body. See src/main/webapp/templates/basePage.xhtml
            // noinspection JSUnusedGlobalSymbols
			function initWindowTitle() {
                // Empty method to not trigger "function is not defined" on page load
			}

			function setTimer() {
				clearTimeout(timeout);
				timeout =setTimeout('redirecttologinpage()', timeoutLen );
			}

			function redirecttologinpage(){
				window.location.href = "timeout.jsf";
			}

			document.onkeydown = function(e) {if (check4Backspace(e)== false) return false; if (check4Refresh(e)== false) return false;}
			document.onkeypress = function(e) {return check4Backspace(e);}

			function check4Backspace(event) {
				var event = event || window.event;
				if (event.keyCode == 8) {
					var elements = "HTML, BODY, TABLE, TBODY, TR, TD, DIV, BUTTON";
					var d = event.srcElement || event.target;
					var regex = new RegExp(d.tagName.toUpperCase());

					if (d.contentEditable != 'true') { //it's not REALLY true, checking the boolean value (!== true) always passes, so we can use != 'true' rather than !== true/
						if (regex.test(elements)) {
							event.preventDefault ? event.preventDefault() : event.returnValue = false;
						}
					}
				}
			}

			function check4Refresh(event) {

				var event = event || window.event;

				if (event.keyCode == 116) {
					event.returnValue = false;
					event.keyCode = 0;
					return false;
				}

				if (event.keyCode == 82) {
					if (event.ctrlKey){
						event.returnValue = false;
						event.keyCode = 0;
						return false;
					}
				}
			}
		</script>

		<title>
			<h:outputFormat value="#{global['quotes.window.title.search']}" rendered="#{searchController.isSearch}">
				<f:param value="#{searchController.index}"/>
			</h:outputFormat>

			<h:outputFormat value="#{global['quotes.window.title.webzone']}" rendered="#{not searchController.isSearch}" />
		</title>
	</head>

	<p:panel id="indexPanel" styleClass="main">

		<ui:include src="/pages/common/banner.xhtml"/>
		<ui:include src="/templates/basePage.xhtml" />

		<p:panel styleClass="header">
			<p:spacer height="100px" />
			<h:graphicImage url="#{global['global.image.relative']}/intactLogoLeftLines.png" alt=""/>
			<h:form id="logoutForm">
				<h:commandLink action="#{authentificationController.logout}" styleClass="logout" rendered="#{spoeController.isSpoeMode == 'true'}">
					<h:graphicImage url="#{global['global.image.relative']}/btnExit.png" alt=""/>
				</h:commandLink>
				<h:outputLink value="#{authentificationController.logoutUrl}" styleClass="logout" rendered="#{spoeController.isSpoeMode == 'false'}">
					<h:graphicImage url="#{global['global.image.relative']}/btnExit.png" alt=""/>
				</h:outputLink>
			</h:form>

			<h:form id="provinceForm" styleClass="province" rendered="#{provinceController.isMultiple and not searchController.isSearch}">
				<h:outputText value="#{global['subscription.company']}"/>
				<p:spacer width="10px"/>
				<h:selectOneMenu id="subscriptionCompanyOptions" value="#{provinceController.selectedCompany}" styleClass="input_province" >
					<f:selectItems value="#{provinceController.companies}" var="subscriptionCompany" itemValue="#{subscriptionCompany}"  itemLabel="#{subscriptionCompany.label}"/>
					<p:ajax event="change" update="mainTab" listener="#{searchController.modifyCompany}" />
				</h:selectOneMenu>
			</h:form>

		</p:panel>

		<p:panel styleClass="contents">
			<p:tabView styleClass="first-tabpanel" id="mainTab">

				<p:tab title="#{global['common.tab.quotes']}"
					   styleClass="tab_quotes">
					<h:graphicImage url="/image/contentTopGrey.png" styleClass="breadcrumb" alt=""/>
					<ui:include src="#{!searchController.isSearch ? '/pages/quotes/quotes.xhtml' : '/pages/quotes/search.xhtml'}" />
					<h:graphicImage url="/image/contentBot966.png" alt=""/>
				</p:tab>

			</p:tabView>

		</p:panel>

		<p:spacer height="10px" />

	</p:panel>
</html>

