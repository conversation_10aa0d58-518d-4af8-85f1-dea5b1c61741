/* DATEPICKER */
body .ui-button.ui-datepicker-trigger {
    background: var(--gray5) url('../image/calendar.png') no-repeat;
    position: relative;
    top: -4px;
    border: none;
    width: 21px;
    height: 21px;
    border-radius: 5px;
    margin-left: 5px;
    cursor: pointer;
}

button.ui-datepicker-trigger span.ui-button-text {
    display: none;
}
.errorGrid .text-align{
    position: relative;
    top: -1px;
}

.ui-datepicker {
    background-color: var(--white1);
    border: thin solid var(--gray5);
}

.ui-datepicker-header {
    background-color: var(--black);
    padding: 0.6rem;
    display: flex;
    justify-content: space-evenly;
}

.ui-datepicker .ui-datepicker-prev {
    background: transparent url('../image/btnLeftArrow.png') no-repeat center center;
    padding: 0 5px;
}

.ui-datepicker .ui-datepicker-next {
    background: transparent url('../image/btnRightArrow.png') no-repeat center center;
    padding: 0 5px;
}
.ui-datepicker-calendar-container > table > thead {
    font-size: small;
}

.ui-datepicker-calendar-container > table > tbody a {
    display: flex;
    justify-content: center;
    color: var(--black);
    padding: 4px;
    font-size: small;
}

.ui-datepicker-calendar-container > table > tbody a:hover {
    text-decoration: none;
    background-color: var(--gray5);
}

.ui-datepicker-calendar-container > table > tbody .ui-state-active  {
    background-color: var(--red2);
    color: var(--white1) !important;
}

.ui-datepicker-calendar-container > table > tbody .ui-state-active:hover  {
    background-color: var(--red2);
}

.ui-datepicker-calendar-container > table > tbody .ui-datepicker-today {
    font-weight: bold;
}

.ui-datepicker-calendar-container > table > tbody .ui-datepicker-other-month a {
    color: var(--gray5);
}

.ui-datepicker-calendar-container > table > tbody .ui-datepicker-week-end a {
    color: var(--turquoise1);
}

.ui-datepicker-buttonbar > div {
    display: flex;
    justify-content: space-between;
    padding: 0.5rem;
}

.ui-datepicker-buttonbar > div .ui-today-button,
.ui-datepicker-buttonbar > div .ui-clear-button{
    background-color: transparent;
    padding: 3px 8px;
    border: 1px solid var(--black);
    border-radius: 5px;
}

/* search form */
.search-content-tab .ui-panel-content .title{
    margin-left: 5px;
    font-size: 20px;
}

table.formSearch > tbody > tr > td.searchColumn1 {
    white-space: nowrap;
}

table.formSearch td.searchColumn1 .errorGrid{
    white-space: normal;
}

table.formSearch td.searchColumn1 ul{
    list-style: none;
    padding: 0;
    margin: 5px 0;
}

table.formSearch td.searchColumn3{
    padding-left: 0;
}

table.formSearch,
table.formSearch td.searchColumn3 table:has(table.input_clientFollowup),
table.formSearch td.searchColumn3 table:has(table.input_webAccessType),
table.formSearch td.searchColumn3 table.input_clientFollowup,
table.formSearch td.searchColumn3 table.input_webAccessType{
    width: 100%;
    font-size: 14px;
    border-collapse: collapse;
}

table.formSearch.no-border-top-td td {
    border-top: none
}

table.formSearch td.searchColumn3 .input_clientFollowup tr,
table.formSearch td.searchColumn3 .input_webAccessType tr{
    display: flex;
    justify-content: space-between;
}

table.formSearch td.searchColumn3 .input_clientFollowup input[type="radio"],
table.formSearch td.searchColumn3 .input_webAccessType input[type="radio"]{
    position: absolute;
    margin: 1px 0 0;
    padding: 0;
}

table.formSearch td.searchColumn3 .input_clientFollowup label,
table.formSearch td.searchColumn3 .input_webAccessType label{
    display: inline-block;
    padding: 0 5px 0 18px;
}

.ui-panel[data-widget=panelReassignWidget] {
    width: 600px;
    padding: 10px;
    margin: 350px auto;
    border: thin solid var(--black);
    background-color: var(--white1);
}

.ui-panel[data-widget=panelReassignWidget] form{
    margin: 0;
}

.ui-panel[data-widget=panelReassignWidget] .ui-panel-content::after {
    clear: both;
}

.reassignPanel {
    background-color: var(--gray6);
    padding: 15px;
}

.reassignPanel table,
.reassignPanel select {
    width: 100%;
    font-size: 14px;
}

.reassignPanel table td {
    padding: 5px 0;
}

.reassignPanel .reassignRow .reassignColumn1 {
    white-space: nowrap;
    padding-right: 15px;
}

/* Modals */
img.rightcorner{
    float: right;
}

.reassign-btn-group .ui-panel-content{
    display: flex;
    justify-content: space-around;
    flex-direction: row-reverse;
}

.reassign-btn-group .actionBtn{
    padding: 3px 20px 4px 15px;
}

.input_reassignToBroker {
    margin-top: 10px;
}

.modal {
    display: none; /* Hidden by default */
    position: fixed; /* Stay in place */
    z-index: 1; /* Sit on top */
    padding-top: 100px; /* Location of the box */
    left: 0;
    top: 0;
    width: 100%; /* Full width */
    height: 100%; /* Full height */
    overflow: auto; /* Enable scroll if needed */
    background-color: var(--transparentWhite); /* Fallback color */
}

/* search warning panel */
.searchWarning-panel{
    position: absolute;
    z-index: 100;
    top: 0;
    left: 0;
}

.searchWarning-panel .panel-mask{
    position: fixed;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    padding: 0;
    margin: 0;
    opacity: 0.5;
    z-index: -1;
    background-color: var(--gray6);
}

.searchWarning-panel .panel-wrap{
    position: fixed;
    z-index: 10;
    padding: 0;
    margin: 0;
    width: 100%;
    left: 32%;
    top: 30%;
    display: block;
}

.searchWarning-panel .panel-wrap .panel-border{
    position: absolute;
    width: 35%;
    border: 1px solid var(--black);
}

.searchWarning-panel .panel-wrap .panel-content{
    display: block;
    border: 10px solid var(--white1);
    background-color: var(--gray6);
    padding: 0 10px 20px;
}

.searchWarning-panel .panel-wrap .searchWarning-msg{
    display: block;
    margin: 10px 0 20px;
    font-size: 12px;
}

.searchWarning-panel .panel-wrap .actionBtn-group{
    display: flex;
    justify-content: space-around;
}

.searchWarning-panel .panel-wrap .actionBtn-wording{
    margin-right: 10px;
}


