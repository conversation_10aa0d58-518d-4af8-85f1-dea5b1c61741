/* VARIABLES */
:root {
    --black: #000000;
    --white1: #ffffff;
    --white2: #f1f1f1;
    --white3: #fffeef;
    --white4: #d8d8d8;
    --transparentWhite: rgb(216 216 216 / 50%);
    --turquoise1: #00b3be;
    --turquoise2: #27688f;
    --turquoise3: #b2dee7;
    --turquoise4: #d9f4f5;
    --turquoise5: #007b87;
    --turquoise6: #00626b;
    --brown1: #655f5d;
    --brown2: #c4c0b9;
    --gray1: #cccccc;
    --gray2: #404040;
    --gray3: #58585a;
    --gray4: #1e1e1e;
    --gray5: #BCBEC0;
    --gray6: #EEEEEF;
    --red1: #c60c30;
    --red2: #c00c30;
    --red3: #ae275f;
    --orange: #ff8c00;
    --yellow: #FFFEEF;
}

/* TAGS */
table.formSearch > tbody > tr > td {
    border-bottom: thin solid var(--gray5);
    padding: 10px 15px;
}

/* CLASSES */

/* IDS */

/* MEDIA QUERIES */

/* header & layout */

html, body {
    margin: 0;
    padding: 0;
    height: 100%;
    width: 100%;
}

img.breadcrumb{
    width: 100%;
}

body {
    background-image: url(../image/bgPage.jpg);
    background-repeat: repeat;
    background-attachment: fixed;
    background-position: center 0pt;
    font-size: 12px;
    font-family: Helvetica,Arial,Verdana,sans-serif;
}

.main {
    position: relative;
    padding: 0 8px;
    margin: auto;
    box-sizing: border-box;
    width: 1004px;
    background-image: url(../image/wrapBg.png);
    background-repeat: repeat-y;
    background-position: center 0pt;
}

.header {
    background-image: url(../image/bgHeader.jpg);
    background-repeat: no-repeat;
    background-position: right 0pt;
    position: relative;
    padding-bottom: 20px;
    margin-bottom: 10px;
}

.contents {
    padding: 0 10px;
    box-sizing: border-box;
    width: 100%;
    position: relative;
}

.province {
    position: absolute;
    right: 0;
    bottom: 30px;
}

.search-panel-tab {
    border: solid 1px var(--gray5);
    margin-bottom: 20px;
    padding-top: 25px;
    border-bottom: none;
}

.search-content-tab {
    box-sizing: border-box;
    margin: 20px auto 20px auto;
    width: 916px;
}

a {
    text-decoration: none;
    color: var(--turquoise1);
}

a:hover {
    text-decoration: underline;
    cursor: pointer;
    color: var(--brown1);
}

br {
    display: none;
}

.errorMessage {
    color: red;
}

.important-message, #roadblock_message_question{
    color: var(--red2);
}

img.important-message{
    filter: invert(12%) sepia(93%) saturate(4467%) hue-rotate(340deg) brightness(86%) contrast(98%);
    width: 10px;
    height: 10px;
    margin-right: 5px;
}
/* Banner */

.banner-container {
    gap: 14px;
    display: flex;
    background-color: var(--turquoise4);
    position: absolute;
    z-index: 1;
    padding: 6px 20px;
    border: 1px solid var(--turquoise5);
    border-radius: 6px;
    box-sizing: border-box;
    left: 0;
    right: 0;
    margin: 0 8px;
    color: var(--turquoise6);
}

.banner-container a {
    color: var(--turquoise6);
    font-weight: bold;
    text-decoration: underline;
}

.banner-container::before {
    margin-top: 8px;
    content: '';
    mask: url(../image/info-fill.401cbef0.svg);
    width: 20px;
    height: 20px;
    mask-size: cover;
    background-color: var(--turquoise6);
}

.banner-container button {
    position: absolute;
    background: none;
    border: none;
    right: 18px;
    top: 12px;
    padding: 0;
    mask: url(../image/close-heavy.7effad6f.svg);
    width: 14px;
    height: 14px;
    mask-size: cover;
    background-color: var(--turquoise6);
    cursor: pointer;
}

/* tabs */

.ui-tabs-panel {
    overflow: visible;
    background-image: url(../image/contentMid966.png);
    background-repeat: repeat-y;
}

.info-content-in-tab {
    box-sizing: border-box;
    padding: 0 20px;
    overflow: visible;
}

.ui-tabs-nav {
    position: absolute;
    top: -45px;
    list-style: none;
    padding: 0;
}

.ui-tabs-nav a {
    border: 1px solid var(--red1);
    color: var(--red1);
    display: inline-block;
    padding: 5px 40px;
    text-decoration: none;
    font-size: 11px;
    font-weight: bold;
}

.info-content-in-tab {
    margin-top: 30px;
    position: relative;
}

.leftfullpart {
    background-image: url(../image/ligneGauche.png);
    height: 8px;
    display: block;
    width: 865px;
}

.rightpart {
    position: absolute;
    top: 18px;
    right: 33px;
}

.title {
    font-size: 16px;
    font-weight: bold;
}

.info {
    font-size: 11px;
    font-weight: bold;
    margin-top: 5px;
}

.perPage {
    position: relative;
    left: 790px;
    width: 160px;
}

/* table */

.ui-datatable-tablewrapper table {
    border-collapse: collapse;
    font-size: 12px;
}

.ui-sortable-column {
    text-align: left;
    font-weight: normal;
    vertical-align: top;
}

.ui-sortable-column:nth-child(6) {
    padding-right: 30px;
}

.ui-datatable.ui-datatable-striped>.ui-datatable-tablewrapper>table>.ui-datatable-data>tr.ui-datatable-odd {
    background-color: var(--white2);
}

.prime-table-cell, [id*="subQuoteList_row_"] > td {
    padding: 4px;
}

.deselectAllBtn-, .selectAllBtn- {
    border: 0;
    cursor: pointer;
    width: 18px;
    height: 18px;
}

.deselectAllBtn- span, .selectAllBtn- span {
    display: none;
}

.deselectAllBtn- {
    background: url("../image/iconDeselectAll.png") no-repeat;
}

.selectAllBtn- {
    background: url("../image/iconSelectAll.png") no-repeat;
}

.ui-expanded-row {
    border-top: 2px solid var(--turquoise1);
    border-left: 2px solid var(--turquoise1);
    border-right: 2px solid var(--turquoise1);
}

.ui-datatable-data .prime-row-collapsed {
    height: 48px;
    border: 0;
}

.ui-datatable-data .prime-row-expanded {
    height: 48px;
}

.ui-expanded-row-content {
    display: none;
}

[id*="subQuoteList_row_"] {
    background-color: var(--white2);
    border-left: 2px solid var(--turquoise1);
    border-right: 2px solid var(--turquoise1);
}

.ui-datatable-data [id*="subQuoteList_row_"]:last-of-type {
    border-bottom: 2px solid var(--turquoise1);
}

.ui-datatable-data [id*="subQuoteList_row_"] + :not([id*="subQuoteList_row_"]) {
    border-top: 2px solid var(--turquoise1);
}

/* sorting */

.ui-datatable-tablewrapper th {
    position: relative;

}

.ui-datatable-tablewrapper thead tr th:has(.ui-icon){
    padding-right: 12px;
}

.ui-column-title {
    display: block;
}

.ui-sortable-column-icon {
    cursor: pointer;
    display: block;
    height: 100%;
    width: 100%;
    position: absolute;
    right: 0;
    top: 0;
}

.ui-sortable-column-icon::before, .ui-sortable-column-icon::after {
    content: '';
    display: block;
    height: 0;
    width: 0;
    position: absolute;
}

.ui-sortable-column-icon::before {
    right: 1px;
    top: 6px;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-bottom: 4px solid black;
}

.ui-sortable-column-icon::after {
    right: 1px;
    top: 12px;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
    border-top: 4px solid black;
}

.ui-sortable-column-icon.ui-icon-triangle-1-n::after {
    display: none;
}

.ui-sortable-column-icon.ui-icon-triangle-1-s::before {
    display: none;
}

/* loading spinner */

#contentWaiter {
    font-size: 0;
}

#contentWaiter > div > [id*="_start"] {
    background-color: var(--transparentWhite);
    position: fixed;
    bottom: 0;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1;
}

#contentWaiter > div > [id*="_start"]::after {
    background: var(--white1) url("../image/waiting.gif") no-repeat;
    background-position: center;
    background-size: 50px 50px, auto;
    border: 1px solid var(--black);
    display: block;
    content: '';
    left: 50%;
    top: 50%;
    margin-left: -40px;
    margin-top: -40px;
    position: absolute;
    height: 80px;
    width: 80px;
}

/* Accordion panel */

.ui-accordion-header {
    color: var(--turquoise2);
    background-color: var(--white1);
    border: 0;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    margin: 30px 0 10px 0;
}

/* --------------------- */

.subformQuote tr td, .subformQuote-twoCol tr td{
    padding:4px 4px 4px 12px;
}

.subformQuote tbody tr, .subformQuote-twoCol tbody tr {
    border-top: 1px solid var(--gray1);
    vertical-align: text-top;
}
.subformQuote.no-line tbody tr, .subformQuote-twoCol tbody tr {
    border-top: none;
    vertical-align: middle;
}

.subformQuote tbody tr td:first-child{
    width: 45%;
}

.subformQuote tbody tr td:nth-child(2){
    width: 5%;
}

.subformQuote tbody tr td:last-child{
    width: 50%;
}

.subformQuote .innerGrid tbody tr{
    border-top: 0;
}

.subformQuote .innerGrid tbody td{
    padding-left: 0;
}

.subformQuote .innerGrid input{
    margin-left: 0;
}

.blue-top-subsection {
    background-repeat: no-repeat;
    background-image: url(../image/contentTopBlue.png) ;
    background-color: transparent;
    background-position:left top;
    display: block;
    height: 33px;
    color: #58585A;
    font-size: 15px;
    font-weight: bold;
    padding-left: 40px;
    padding-top: 10px;
}

/* Tooltip */

.tooltip {
    background-color: var(--yellow);
    border:1px solid black;
    padding:10px;
    max-width: fit-content;
    white-space: nowrap;
    display: none;
    text-align: left;
}

.tooltip-large {
    width:500px;
}

.tooltipData {
    font-weight: bold;
    font-size: 12px;
}

.tooltip-title {
    margin-bottom: 10px;
}

/* Primefaces OverlayPanel component specific CSS */
/* See https://primefaces.github.io/primefaces/13_0_0/#/components/overlaypanel */

.ui-overlay-visible {
    visibility: visible
}

ui-overlay-visible * {
    visibility: visible !important
}

.ui-overlay-hidden {
    visibility: hidden
}

.ui-overlay-hidden * {
    visibility: hidden !important
}

.ui-overflow-hidden {
    overflow: hidden
}

.ui-connected-overlay-enter {
    opacity: 0;
    transform: scaleY(0.8)
}

.ui-connected-overlay-enter-active {
    opacity: 1;
    transform: scaleY(1);
    transition: transform .12s cubic-bezier(0, 0, 0.2, 1), opacity .12s cubic-bezier(0, 0, 0.2, 1)
}

.ui-connected-overlay-enter-done {
    transform: none
}

.ui-connected-overlay-exit {
    opacity: 1
}

.ui-connected-overlay-exit-active {
    opacity: 0;
    transition: opacity .1s linear
}

.ui-dialog.ui-overlay-hidden {
    display: block
}

.ui-overlaypanel {
    margin: 0;
    position: absolute;
    display: none
}

.ui-overlaypanel-close {
    position: absolute;
    top: -10px;
    right: -10px;
    -moz-border-radius: 10px;
    -webkit-border-radius: 10px;
    border-radius: 10px
}

.ui-overlaypanel-mask {
    position: fixed;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    user-select: none
}

/*ACTION BUTTONS: Reassign quote, search */
a.actionBtn span.right img {
    margin-left: 5px;
}

a.actionBtn:hover {
    background:url(../image/btnRightLongOver.png) right no-repeat;
}

a.actionBtn {
    border-radius: 5px;
    color: #FFF;
    cursor:pointer;
    font-weight: bold;
    font-size: 1.2em;
    background : url(../image/btnRightLong.png) no-repeat right;
    padding:3px 15px;
    text-decoration: none;
}

.actionBtn.btnArrow{
    padding-right: 30px;
    position: relative;
}

.actionBtn.btnArrow::after{
    content: '';
    background: url("../image/btnRightArrow.png");
    display: block;
    width: 13px;
    height: 13px;
    position: absolute;
    right: 10px;
    top: 4px;
}

.flex-end {
    display: flex;
    justify-content: end;
}

.link_reassignQuote {
    margin-bottom: 15px;
    display: inline-block;
}

/* PAGINATION*/
.ui-paginator-bottom {
    padding: 20px 0;
    display:flex;
    justify-content: end;
}

.ui-paginator-pages {
    margin-top: 4px;
}

.ui-paginator-prev,
.ui-paginator-next {
    border: 1px solid var(--brown2);
    padding: 3px 9px;
    margin: 0 2px;
}

.ui-paginator-page {
    border: 1px solid var(--brown1);
    padding: 3px 12px;
    margin: 0 2px;
}

.ui-paginator .ui-state-active {
    color: var(--brown1);
    padding: 3px 9px;
}

.ui-paginator .ui-paginator-prev:before {
    content: "<";
}

.ui-paginator .ui-paginator-next:before {
    content: ">";
}

.ui-paginator .ui-icon-seek-prev,
.ui-paginator .ui-icon-seek-next {
    display: none;
}

.ui-paginator .ui-state-disabled {
    color: var(--brown2);
}

.ui-paginator .ui-state-disabled:hover {
    text-decoration: none;
    cursor: auto;
}

/* view quote details */
.viewQuote-wrap{
    padding: 10px 15px;
}

table.formQuote.third-table,
.formQuote.third-table table {
    width: 100%;
}

.updateBtn {
    margin-right: 20px;
    float: right;
}

.notification {
    display: block;
    margin: 15px 0;
    color: var(--turquoise1);
}

.quote-detail-section:has(table){
    border: 2px solid var(--turquoise3);
    border-top: 0;
    margin-bottom: 15px;
}

.quote-detail-section table{
    width: 100%;
    border-collapse: collapse;
}

.quote-detail-section table.third-table{
    border-collapse: initial;
    padding-bottom: 15px;
}

.ui-commandlink.link_upload2 {
    margin-left: 10px;
}

table.formQuote.third-table th,
.formQuote.third-table table th {
    text-align: left;
}

.topLine{
    border-top: 1px solid var(--gray1);
    margin: 15px 0;
}

table.quote-logNote{
    border-collapse: initial;
    padding: 0 12px;
}

table.quote-logNote .subformQuoteCol1{
    width: 200px;
}

table.quote-logNote .subformQuoteCol2{
    width: 15px;
    padding: 0 !important;
}

table.quote-logNote .subformQuoteCol3{
    width: 50%;
}

table.quote-logNote .remaining-chars-counter {
    margin-top: 5px;
    color: var(--gray3);
    font-size: 10px;
}

table.formQuote.third-table td:not(:last-child):not(:first-child),
.formQuote.third-table table td:not(:last-child):not(:first-child) {
    padding: 4px 0;
}

table.formQuote.third-table th:nth-child(2),
.formQuote.third-table table th:nth-child(2) {
    width: 115px;
}

table.formQuote.third-table th:nth-child(3),
.formQuote.third-table table th:nth-child(3) {
    width: 120px;
}

table.formQuote.third-table th:nth-child(4),
.formQuote.third-table table th:nth-child(4){
    width: 120px;
}

.headerSection, .headerSectionR{
    display: flex;
    margin: 15px 0;
}

.headerSectionR{
    justify-content: end;
}

.headerSectionR.language{
    padding: 0 15px;
}

.headerSectionR .collapseBtn{
    cursor: pointer;
}

.black-label-payment{
    color: var(--gray3);
    font-weight: bold;
}

.quote-detail-section .paymentColumn .payment-detail{
    padding: 0 10px 8px 10px;
}

.quote-detail-section .paymentColumn .payment-detail thead{
    display: none;
}

.quote-detail-section .paymentColumn .payment-objectName{
    display: block;
}

.quote-detail-section .paymentColumn .premiumColumn1,
.quote-detail-section .paymentColumn .premiumColumn2,
.quote-detail-section .paymentColumn .premiumColumn3{
    display: inline-block;
    padding: 15px 20px 15px 0;
}

.quote-detail-section .paymentColumn .premiumColumn3{
    color: var(--red1);
    font-weight: bold;
}

.btn-top-margin-reassignQ {
    margin-top: 52px;
}

.reassignRow{
    vertical-align: top;
}

.reassignColumn1{
    width: 154px;
}

.reassignColumn2{
    width: 40px;
}

.reassignColumn3{
    width: 204px;
    text-align:left;
}