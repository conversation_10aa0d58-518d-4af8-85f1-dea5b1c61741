mainPage = false;
var searchWins = [];
currentIndex = 0;

function openNewSearch(index, type) {
    try {
        searchWins[index].close();
    } catch (err) {
    }
    var newWindow = window.open('index.jsf?searchIndex=' + index + '&searchType=' + type, '_newtab' + index);
    searchWins[index] = newWindow;
    newWindow.focus();
}

function closeSearchIfNeeded() {
    if (!mainPage) {
        callBackEnd();
    } else {
        {
            if (searchWins.length > 0) {
                for (var i = 1; i < searchWins.length; i++) {
                    if (typeof searchWins[i] != "undefined") {
                        searchWins[i].close();
                    }
                }
            }
        }
    }
}

function callBackEnd() {
    jQuery.post("closeSearch?index=" + currentIndex, function () {
    });
}