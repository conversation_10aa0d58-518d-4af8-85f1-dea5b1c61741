		

/** set focus on the next phone number field**/
function input_onchange(field, formId){ 
		  	  var maxLength = field.getAttribute('maxlength');
		  	  var fieldObject = document.getElementById(field.id);
		  	  var fieldLength = fieldObject.value.length;

		  	  if (fieldLength != maxLength){
		  			return;
		  	  }
		  	  
		  	  nextField(field, formId);
  	  
}

function nextField(field, formId) {
	  var i = 0;
  	  var formFields = document.getElementById(formId).elements;
  	  var formFieldsLength = formFields.length;
      for (i=0; i < formFieldsLength; i++) {
        if (formFields[i].id==field.id) {
            break;
        }
      }
      formFields[i+1].focus();			
}