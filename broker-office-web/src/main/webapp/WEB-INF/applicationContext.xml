<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Important notice: This software is the sole property of Intact Insurance and cannot be distributed and/or copied
  ~  without the written permission of Intact Insurance
  ~
  ~ Copyright (c) 2013 Intact Insurance, All rights reserved.
  -->
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xmlns:context="http://www.springframework.org/schema/context"
	xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
						http://www.springframework.org/schema/context
						http://www.springframework.org/schema/context/spring-context-3.0.xsd">

	<context:annotation-config />

	<import resource="classpath:capi-java-services-rating.xml" />
	<import resource="classpath:capi-java-services-policy.xml" />

	<import resource="classpath:plp-services-beans.xml" />
	<import resource="classpath:cif-services-beans.xml" />
	<import resource="classpath:brm-services-beans.xml" />

	<import resource="classpath:transaction-beans.xml" />
	<import resource="classpath:brm-transaction-beans.xml" />
	<import resource="classpath:broker-office-service.xml" />
	<import resource="classpath:broker-office-web.xml" />
	<import resource="classpath:abr-quote.xml" />
	<import resource="classpath:abr-offer.xml" />
	<import resource="classpath:tools-string-service.xml" />
	
	<bean id="plt.upload.ws.url" class="java.lang.String">
		<constructor-arg type="java.lang.String" value="${plt.upload.ws.url}"/>
	</bean>

	<bean id="plt.upload.soa.url" class="java.lang.String">
		<constructor-arg type="java.lang.String" value="${plt.upload.ws.url}"/>
	</bean>

	<bean id="plt.information.ws.url" class="java.lang.String">
		<constructor-arg type="java.lang.String" value="${plt.information.ws.url}"/>
	</bean>

	<bean id="plt.search.ws.url" class="java.lang.String">
		<constructor-arg type="java.lang.String" value="${plt.search.ws.url}"/>
	</bean>

	<bean id="webzoneClientInformationService" class="com.intact.brokeroffice.service.WebzoneClientInformationService">
		<property name="url" value="${plt.information.ws.url}" />
	</bean>

	<bean class="com.intact.plt.upload.service.client.ClientUploadApplicationService">
		<property name="url" value="${plt.upload.ws.url}" />
	</bean>

	<bean id="plt.information.soa.url" class="java.lang.String">
		<constructor-arg type="java.lang.String" value="${plt.information.ws.url}" />
	</bean>

	<bean class="com.intact.plt.search.service.client.ClientSearchApplicationService">
		<property name="url" value="${plt.search.ws.url}" />
	</bean>

	<!-- property placeholder post-processor -->
<!--	<bean id="appPlaceholderConfig" class="org.springframework.context.support.PropertySourcesPlaceholderConfigurer" >-->
<!--		<property name="locations">-->
<!--			<list>-->
<!--				<value>classpath:plp-services.properties</value>-->
<!--				<value>classpath:cif-services.properties</value>-->
<!--				<value>classpath:brm-services.properties</value>-->
<!--				<value>classpath:plt-service.properties</value>-->
<!--				<value>classpath:brokerOffice-web.properties</value>-->
<!--			</list>-->
<!--		</property>-->
<!--	</bean>-->

	<bean class="org.springframework.beans.factory.config.CustomScopeConfigurer">
		<property name="scopes">
			<map>
				<entry key="view">
					<bean class="com.intact.brokeroffice.customscope.ViewScope" />
				</entry>
			</map>
		</property>
	</bean>
</beans>

