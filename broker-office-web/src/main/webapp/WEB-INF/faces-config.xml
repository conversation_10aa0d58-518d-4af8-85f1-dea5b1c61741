<?xml version="1.0" encoding="UTF-8"?>
<faces-config xmlns="http://java.sun.com/xml/ns/javaee"
			  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
			  xsi:schemaLocation="http://java.sun.com/xml/ns/javaee http://java.sun.com/xml/ns/javaee/web-facesconfig_2_0.xsd"
			  version="2.0">
	<application>
		<el-resolver>
			org.springframework.web.jsf.el.SpringBeanFacesELResolver
		</el-resolver>

		<el-resolver>
			org.primefaces.application.exceptionhandler.PrimeExceptionHandlerELResolver
		</el-resolver>

		<view-handler>
			org.primefaces.application.DialogViewHandler
		</view-handler>

		<action-listener>org.primefaces.application.DialogActionListener</action-listener>
		<navigation-handler>org.primefaces.application.DialogNavigationHandler</navigation-handler>

		<!-- Resource bundle config -->
		<locale-config>
			<default-locale>en_CA</default-locale>
			<supported-locale>en_CA</supported-locale>
			<supported-locale>fr_CA</supported-locale>
		</locale-config>

		<message-bundle>com.intact.brokeroffice.resources.calendarLabel</message-bundle>

		<system-event-listener>
			<system-event-listener-class>
				com.intact.brokeroffice.listener.RemoveResourcesListener
			</system-event-listener-class>
			<system-event-class>
				jakarta.faces.event.PreRenderViewEvent
			</system-event-class>
		</system-event-listener>
	</application>

	<converter>
		<converter-id>nameConverter</converter-id>
		<converter-class>com.intact.brokeroffice.converter.NameConverter</converter-class>
	</converter>
	<converter>
		<converter-id>vehicleNameConverter</converter-id>
		<converter-class>com.intact.brokeroffice.converter.VehicleNameConverter</converter-class>
	</converter>
	<converter>
		<converter-id>CalendarConverter</converter-id>
		<converter-class>com.intact.brokeroffice.converter.CalendarConverter</converter-class>
	</converter>
	<converter>
		<converter-id>pointOfSaleConverter</converter-id>
		<converter-class>com.intact.brokeroffice.converter.PointOfSaleConverter</converter-class>
	</converter>
	<managed-bean>
		<managed-bean-name>currentDate</managed-bean-name>
		<managed-bean-class>java.util.Date</managed-bean-class>
		<managed-bean-scope>request</managed-bean-scope>
	</managed-bean>

	<factory>
		<exception-handler-factory>com.intact.brokeroffice.config.CustomExceptionHandlerFactory</exception-handler-factory>
	</factory>

	<lifecycle>
		<phase-listener>org.primefaces.component.lifecycle.LifecyclePhaseListener</phase-listener>
	</lifecycle>
</faces-config>
