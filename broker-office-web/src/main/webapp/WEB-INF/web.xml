<?xml version="1.0" encoding="UTF-8"?>
<web-app id="WebApp_ID" version="3.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xmlns="http://java.sun.com/xml/ns/javaee"
		 xsi:schemaLocation="http://java.sun.com/xml/ns/javaee
						http://java.sun.com/xml/ns/javaee/web-app_3_0.xsd">

	<display-name>WebZone</display-name>

	<context-param>
		<param-name>contextConfigLocation</param-name>
		<param-value>
			/WEB-INF/applicationContext.xml
		</param-value>
	</context-param>
	<context-param>
		<param-name>jakarta.faces.DEFAULT_SUFFIX</param-name>
		<param-value>.xhtml</param-value>
	</context-param>
	<context-param>
		<param-name>primefaces.THEME</param-name>
		<param-value>none</param-value>
	</context-param>

	<!-- Primefaces recommended settings to maximize performance -->
	<!-- See https://primefaces.github.io/primefaces/13_0_0/#/core/performance -->

	<!-- JSF rendering mode -->
	<context-param>
		<param-name>jakarta.faces.PROJECT_STAGE</param-name>
		<!-- Replace value with "Development" for debug mode. Do NOT use elsewhere than your local environment -->
		<param-value>Production</param-value>
	</context-param>

	<context-param>
		<param-name>jakarta.faces.STATE_SAVING_METHOD</param-name>
		<param-value>server</param-value>
	</context-param>
<!--	<context-param>-->
<!--		<param-name>jakarta.faces.PARTIAL_STATE_SAVING</param-name>-->
<!--		<param-value>true</param-value>-->
<!--	</context-param>-->
<!--	<context-param>-->
<!--		<param-name>jakarta.faces.SERIALIZE_SERVER_STATE</param-name>-->
<!--		<param-value>false</param-value>-->
<!--	</context-param>-->

	<!-- Disable watching files changed by the compiler -->
	<context-param>
		<param-name>jakarta.faces.FACELETS_REFRESH_PERIOD</param-name>
		<param-value>-1</param-value>
	</context-param>

	<!-- Remove comments from JSF page -->
	<context-param>
		<param-name>jakarta.faces.FACELETS_SKIP_COMMENTS</param-name>
		<param-value>true</param-value>
	</context-param>

	<!-- Define whether default MyFaces config parameters are logged at startup -->
	<context-param>
		<param-name>org.apache.myfaces.LOG_WEB_CONTEXT_PARAMS</param-name>
		<param-value>true</param-value>
	</context-param>

	<!-- Cache EL expressions; See: https://myfaces.apache.org/#/coreConceptsCacheElExpressions -->
	<context-param>
		<param-name>org.apache.myfaces.CACHE_EL_EXPRESSIONS</param-name>
		<param-value>alwaysRecompile</param-value>
	</context-param>

	<!-- Disable ViewState compression (better performance but more memory usage) -->
	<context-param>
		<param-name>org.apache.myfaces.COMPRESS_STATE_IN_SESSION</param-name>
		<param-value>false</param-value>
	</context-param>

	<!-- Disable duplicate IDs check in Production mode -->
	<context-param>
		<param-name>org.apache.myfaces.CHECK_ID_PRODUCTION_MODE</param-name>
		<param-value>false</param-value>
	</context-param>

	<!-- Flush the response directly after the head to allow start loading resources on the browser side -->
	<context-param>
		<param-name>org.apache.myfaces.EARLY_FLUSH_ENABLED</param-name>
		<param-value>true</param-value>
	</context-param>

	<!-- Define whether rendered html code will be formatted to be "human-readable" -->
	<context-param>
		<param-name>org.apache.myfaces.PRETTY_HTML</param-name>
		<param-value>false</param-value>
	</context-param>

	<!-- Enable cache and increase its size -->
	<context-param>
		<param-name>org.apache.myfaces.VIEW_UNIQUE_IDS_CACHE_ENABLED</param-name>
		<param-value>true</param-value>
	</context-param>
	<context-param>
		<param-name>org.apache.myfaces.COMPONENT_UNIQUE_IDS_CACHE_SIZE</param-name>
		<param-value>500</param-value>
	</context-param>

	<context-param>
		<param-name>org.apache.myfaces.ERROR_HANDLING</param-name>
		<param-value>true</param-value>
	</context-param>

	<!--	org.apache.myfaces.NUMBER_OF_VIEWS_IN_SESSION defaults to 20-->
	<context-param>
		<param-name>org.apache.myfaces.NUMBER_OF_VIEWS_IN_SESSION</param-name>
		<param-value>40</param-value>
	</context-param>

	<!-- Filters config -->
	<!-- /!\ SecureFilter must be the first filter (for some reason) /!\ -->
	<filter>
		<filter-name>SecureFilter</filter-name>
		<filter-class>com.intact.brokeroffice.filter.SecureFilter</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>SecureFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	<filter>
		<filter-name>FilterUTF8</filter-name>
		<filter-class>com.intact.brokeroffice.filter.FilterUTF8</filter-class>
	</filter>
	<filter-mapping>
		<filter-name>FilterUTF8</filter-name>
		<url-pattern>*.jsf</url-pattern>
		<url-pattern>*.xhtml</url-pattern>
	</filter-mapping>

	<filter>
		<filter-name>Spring BRM OpenEntityManagerInViewFilter</filter-name>
		<filter-class>org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter</filter-class>
		<init-param>
			<param-name>entityManagerFactoryBeanName</param-name>
			<param-value>brm-entityManagerFactory</param-value>
		</init-param>
	</filter>

	<filter>
		<filter-name>Spring PLP OpenEntityManagerInViewFilter</filter-name>
		<filter-class>org.springframework.orm.jpa.support.OpenEntityManagerInViewFilter</filter-class>
		<init-param>
			<param-name>entityManagerFactoryBeanName</param-name>
			<param-value>entityManagerFactory</param-value>
		</init-param>
	</filter>

	<filter>
		<filter-name>cifOpenSessionInViewFilter</filter-name>
		<filter-class>org.springframework.orm.hibernate5.support.OpenSessionInViewFilter</filter-class>
		<init-param>
			<param-name>sessionFactoryBeanName</param-name>
			<param-value>sessionFactory</param-value>
		</init-param>
	</filter>

	<!--	<filter>-->
	<!--		<description>BlockXhtmlFilter</description>-->
	<!--		<display-name>BlockXhtmlFilter</display-name>-->
	<!--		<filter-name>BlockXhtmlFilter</filter-name>-->
	<!--		<filter-class>com.intact.brokeroffice.filter.BlockXhtmlFilter</filter-class>-->
	<!--	</filter>-->

	<filter>
		<description>SessionTimeoutFilter</description>
		<display-name>SessionTimeoutFilter</display-name>
		<filter-name>SessionTimeoutFilter</filter-name>
		<filter-class>com.intact.brokeroffice.filter.SessionTimeoutFilter</filter-class>
	</filter>

	<filter>
		<description>AntiCSRFFilter</description>
		<display-name>AntiCSRFFilter</display-name>
		<filter-name>AntiCSRFFilter</filter-name>
		<filter-class>com.intact.brokeroffice.filter.SecurityAntiCSRFFilter</filter-class>
		<init-param>
			<param-name>BundleFile</param-name>
			<param-value>broker-office-security</param-value>
		</init-param>
	</filter>

	<filter>
		<filter-name>SecurityFilter</filter-name>
		<filter-class>com.intact.brokeroffice.filter.SecurityFilter</filter-class>
	</filter>

	<filter>
		<description>SearchRefreshFilter</description>
		<display-name>SearchRefreshFilter</display-name>
		<filter-name>SearchRefreshFilter</filter-name>
		<filter-class>com.intact.brokeroffice.filter.SearchRefreshFilter</filter-class>
	</filter>

	<filter>
		<filter-name>NoCacheFilter</filter-name>
		<filter-class>com.intact.brokeroffice.filter.NoCacheFilter</filter-class>
	</filter>

	<filter>
		<filter-name>XFrameFilter</filter-name>
		<filter-class>com.intact.brokeroffice.filter.XFrameFilter</filter-class>
	</filter>

<!--	<filter>-->
<!--		<filter-name>Extensions Filter</filter-name>-->
<!--		<filter-class>org.apache.myfaces.webapp.filter.ExtensionsFilter</filter-class>-->
<!--	</filter>-->

	<!--                          -->
	<!--  FILTER MAPPING SECTION  -->
	<!--    					  -->
	<filter-mapping>
		<filter-name>cifOpenSessionInViewFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
<!--	<filter-mapping>-->
<!--		<filter-name>Extensions Filter</filter-name>-->
<!--		<url-pattern>*.xhtml</url-pattern>-->
<!--	</filter-mapping>-->
<!--	<filter-mapping>-->
<!--		<filter-name>Extensions Filter</filter-name>-->
<!--		<servlet-name>Faces Servlet</servlet-name>-->
<!--	</filter-mapping>-->
	<filter-mapping>
		<filter-name>Spring BRM OpenEntityManagerInViewFilter</filter-name>
		<url-pattern>*.jsf</url-pattern>
		<url-pattern>*.xhtml</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>Spring PLP OpenEntityManagerInViewFilter</filter-name>
		<url-pattern>*.jsf</url-pattern>
		<url-pattern>*.xhtml</url-pattern>
	</filter-mapping>
	<!--	<filter-mapping>-->
	<!--		<filter-name>BlockXhtmlFilter</filter-name>-->
	<!--		<url-pattern>*.xhtml</url-pattern>-->
	<!--	</filter-mapping>-->
	<filter-mapping>
		<filter-name>SessionTimeoutFilter</filter-name>
		<url-pattern>*.jsf</url-pattern>
		<url-pattern>*.xhtml</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>SecurityFilter</filter-name>
		<url-pattern>*.jsf</url-pattern>
		<url-pattern>*.xhtml</url-pattern>
	</filter-mapping>

	<filter-mapping>
		<filter-name>SearchRefreshFilter</filter-name>
		<url-pattern>*.jsf</url-pattern>
		<url-pattern>*.xhtml</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>AntiCSRFFilter</filter-name>
		<url-pattern>*.jsf</url-pattern>
		<url-pattern>*.xhtml</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>cifOpenSessionInViewFilter</filter-name>
		<url-pattern>/*</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>NoCacheFilter</filter-name>
		<url-pattern>*.jsf</url-pattern>
		<url-pattern>*.xhtml</url-pattern>
	</filter-mapping>
	<filter-mapping>
		<filter-name>XFrameFilter</filter-name>
		<url-pattern>*.jsf</url-pattern>
		<url-pattern>*.xhtml</url-pattern>
	</filter-mapping>

	<!--                    -->
	<!--  LISTENER SECTION  -->
	<!--                    -->

	<listener>
		<listener-class>org.springframework.web.context.ContextLoaderListener</listener-class>
	</listener>
	<listener>
		<listener-class>org.springframework.web.context.request.RequestContextListener</listener-class>
	</listener>
	<listener>
		<listener-class>org.apache.myfaces.webapp.StartupServletContextListener</listener-class>
	</listener>

	<!-- Error Handling  -->
	<!-- For testing purposes, disable this to see the stacktrace right on the screen -->
	<error-page>
		<error-code>500</error-code>
		<location>/error.xhtml</location>
	</error-page>

	<!-- Faces Servlet -->
	<servlet>
		<servlet-name>Faces Servlet</servlet-name>
		<servlet-class>jakarta.faces.webapp.FacesServlet</servlet-class>
		<load-on-startup>1</load-on-startup>
	</servlet>

	<!-- Comparateur de primes automatisé Servlet -->
	<servlet>
		<servlet-name>quoteUploaderServlet</servlet-name>
		<servlet-class>org.springframework.web.context.support.HttpRequestHandlerServlet</servlet-class>
	</servlet>

	<servlet>
		<servlet-name>CloseSearchServlet</servlet-name>
		<servlet-class>com.intact.brokeroffice.web.servlet.CloseSearchServlet</servlet-class>
	</servlet>

	<!-- Faces Servlet Mapping -->
	<servlet-mapping>
		<servlet-name>Faces Servlet</servlet-name>
		<url-pattern>*.jsf</url-pattern>
		<url-pattern>*.xhtml</url-pattern>
	</servlet-mapping>

	<!-- Automated primes comparator Servlet Mapping-->
	<servlet-mapping>
		<servlet-name>quoteUploaderServlet</servlet-name>
		<url-pattern>/quoteUploaderServlet</url-pattern>
	</servlet-mapping>

	<servlet-mapping>
		<servlet-name>CloseSearchServlet</servlet-name>
		<url-pattern>/closeSearch</url-pattern>
	</servlet-mapping>

	<login-config>
		<auth-method>BASIC</auth-method>
	</login-config>

	<session-config>
		<session-timeout>120</session-timeout>
		<!-- Secure session cookie -->
<!--	⚠️ I've commented it out because it's causing issue with navigation after the new Primefaces version  -->
<!--		<cookie-config>-->
<!--			<http-only>true</http-only>-->
<!--			<secure>true</secure>-->
<!--			&lt;!&ndash; SameSite=strict is configured via Tomcat directly, see context.xml &ndash;&gt;-->
<!--		</cookie-config>-->
		<!-- Prevent JSESSIONID to be added in the URL on the first call to a resource -->
<!--		<tracking-mode>COOKIE</tracking-mode>-->
	</session-config>

	<mime-mapping>
		<extension>svg</extension>
		<mime-type>image/svg+xml</mime-type>
	</mime-mapping>

	<welcome-file-list>
		<welcome-file>index.jsf</welcome-file>
	</welcome-file-list>

</web-app>
