<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui">

	<f:view locale="#{languageController.locale}" >
		<h:head>
			<meta http-equiv="expires" content="0" />
			<meta http-equiv="pragma" content="no-cache" />
			<meta http-equiv="cache-control" content="private" />
			<meta http-equiv="content-type" content="text/html; charset=UTF-8" />

			<script type="text/javascript">
				showSpinner = true;
			</script>
		</h:head>

		<f:loadBundle var="global" basename="com.intact.brokeroffice.resources.global"/>
		<body onload="initWindowTitle();" onbeforeunload="closeSearchIfNeeded();">

		<!-- Spinner/loading based on ajax statuses -->
		<div id="contentWaiter" class="content">
			<p:ajaxStatus>
				<f:facet name="prestart">
					<h:outputText value="Starting..." /> </f:facet>
				<f:facet name="error"> <h:outputText value="Error" />
				</f:facet>
				<f:facet name="success"> <h:outputText value="Success" />
				</f:facet>
				<f:facet name="default"> <h:outputText value="Idle" />
				</f:facet>
				<f:facet name="start"> <h:outputText value="Please Wait" />
				</f:facet>
				<f:facet name="complete"> <h:outputText value="Done" />
				</f:facet>
			</p:ajaxStatus>
		</div>
		</body>
	</f:view>
</ui:composition>