<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:f="http://java.sun.com/jsf/core"
	  xmlns:p="http://primefaces.org/ui">

<f:view locale="#{languageController.localeUrl}" />
<h:head>
	<link href="../../style/richComponent.css" rel="stylesheet" type="text/css" media="all" />
	<link href="../../style/legend.css" rel="stylesheet" type="text/css" media="all" />
</h:head>
<f:loadBundle var="legend" basename="com.intact.brokeroffice.controller.legend.legend"/>

<p:panel styleClass="main">
	<h:panelGrid columns="1" cellspacing="0" cellpadding="0" styleClass="legendPanel">

		<p:spacer height="10px" />

		<h:outputText value="#{legend['legend.title']}" styleClass="legendSectionTitle"/>

		<p:spacer height="10px" />

		<h:outputText value="#{legend['activity.title']}" styleClass="subtitle"/>
		<p:spacer height="10px" />
		<h:panelGrid columns="2" cellspacing="0" cellpadding="0" columnClasses="legendColumn1, defaultColumn">
			<h:graphicImage url="/image/iconVuCourtier.png" alt=""/>
			<h:outputText value="#{legend['activity.text']}"/>
		</h:panelGrid>
		<p:spacer height="10px" />


		<h:outputText value="#{legend['status.autoplus']}" styleClass="subtitle" rendered="#{provinceController.company6 or provinceController.company3}"/>
		<p:spacer height="10px" rendered="#{provinceController.company6 or provinceController.company3}"/>
		<h:panelGrid columns="2" cellspacing="0" cellpadding="0" columnClasses="legendColumn1, defaultColumn"  rendered="#{provinceController.company6 or provinceController.company3}">
			<h:outputText escape="false" value="#{legend['status.autoplus.text']}" rendered="#{provinceController.company6 or provinceController.company3}"/>
			<h:outputText escape="false" value="#{legend['status.autoplus.value']}" rendered="#{provinceController.company6 or provinceController.company3}"/>
		</h:panelGrid>
		<p:spacer height="10px" rendered="#{provinceController.company6 or provinceController.company3}"/>


		<h:outputText value="#{legend['vic.title']}" styleClass="subtitle" rendered="#{provinceController.company6}"/>
		<p:spacer height="10px" rendered="#{provinceController.company6}"/>
		<h:outputText escape="false" value="#{legend['vic.message']}" rendered="#{provinceController.company6}"/>
		<p:spacer height="10px" rendered="#{provinceController.company6}"/>
		<h:panelGrid columns="2" cellspacing="0" cellpadding="0" columnClasses="legendColumn1, defaultColumn"  rendered="#{provinceController.company6}">
			<h:outputText escape="false" value="#{legend['vic.vc']}" rendered="#{provinceController.company6}"/>
			<h:outputText escape="false" value="#{legend['vic.vc.value']}" rendered="#{provinceController.company6}"/>
		</h:panelGrid>
		<h:panelGrid columns="2" cellspacing="0" cellpadding="0" columnClasses="legendColumn1, defaultColumn"  rendered="#{provinceController.company6}">
			<h:outputText escape="false" value="#{legend['vic.rc']}" rendered="#{provinceController.company6}"/>
			<h:outputText escape="false" value="#{legend['vic.rc.value']}" rendered="#{provinceController.company6}"/>
		</h:panelGrid>
		<h:panelGrid columns="2" cellspacing="0" cellpadding="0" columnClasses="legendColumn1, defaultColumn"  rendered="#{provinceController.company6}">
			<h:outputText escape="false" value="#{legend['vic.slash']}" rendered="#{provinceController.company6}"/>
			<h:outputText escape="false" value="#{legend['vic.slash.value']}" rendered="#{provinceController.company6}"/>
		</h:panelGrid>
		<p:spacer height="10px" rendered="#{provinceController.company6}"/>


		<h:outputText value="#{legend['system.origin.title']}" styleClass="subtitle"/>
		<p:spacer height="10px" />
		<h:panelGrid columns="2" cellspacing="0" cellpadding="0" columnClasses="legendColumn1, defaultColumn">
			<h:outputText escape="false" value="#{legend['origin.intact.web.site']}"/>
			<h:outputText escape="false" value="#{legend['origin.intact.web.site.text']}"/>
			<h:outputText escape="false" value="#{legend['origin.broker.web.site']}"/>
			<h:outputText escape="false" value="#{legend['origin.broker.web.site.text']}"/>
		</h:panelGrid>
		<p:spacer height="10px" />

		<h:outputText value="#{legend['web.program.type.title']}" styleClass="subtitle"/>
		<p:spacer height="10px" />
		<h:panelGrid columns="2" cellspacing="0" cellpadding="0" columnClasses="legendColumn1, defaultColumn">
			<h:outputText escape="false" value="#{legend['web.program.type.code.1']}"/>
			<h:outputText escape="false" value="#{legend['web.program.type.value.1']}"/>
			<h:outputText escape="false" value="#{legend['web.program.type.code.2']}"/>
			<h:outputText escape="false" value="#{legend['web.program.type.value.2']}"/>
			<h:outputText escape="false" value="#{legend['web.program.type.code.3']}"/>
			<h:outputText escape="false" value="#{legend['web.program.type.value.3']}"/>
		</h:panelGrid>
		<p:spacer height="10px" />
		<h:outputText escape="false" value="#{legend['web.program.type.domain']}" />
		<h:panelGrid columns="2" cellspacing="0" cellpadding="0" columnClasses="legendColumn1, defaultColumn">
			<h:outputText escape="false" value="#{legend['web.program.type.domain.code.1']}"/>
			<h:outputText escape="false" value="#{legend['web.program.type.domain.value.1']}"/>
			<h:outputText escape="false" value="#{legend['web.program.type.domain.code.2']}"/>
			<h:outputText escape="false" value="#{legend['web.program.type.domain.value.2']}"/>
			<h:outputText escape="false" value="#{legend['web.program.type.domain.code.3']}"/>
			<h:outputText escape="false" value="#{legend['web.program.type.domain.value.3']}"/>
			<h:outputText escape="false" value="#{legend['web.program.type.domain.code.4']}"/>
			<h:outputText escape="false" value="#{legend['web.program.type.domain.value.4']}"/>
		</h:panelGrid>
		<p:spacer height="10px" />
		<h:outputText escape="false" value="#{legend['assignable.domain']}"/>
		<h:panelGrid columns="2" cellspacing="0" cellpadding="0" columnClasses="legendColumn1, defaultColumn">
			<h:outputText escape="false" value="#{legend['assignable.domain.code.1']}"/>
			<h:outputText escape="false" value="#{legend['assignable.domain.value.1']}"/>
			<h:outputText escape="false" value="#{legend['assignable.domain.code.2']}"/>
			<h:outputText escape="false" value="#{legend['assignable.domain.value.2']}"/>
			<h:outputText escape="false" value="#{legend['assignable.domain.code.3']}"/>
			<h:outputText escape="false" value="#{legend['assignable.domain.value.3']}"/>
			<h:outputText escape="false" value="#{legend['assignable.domain.code.4']}"/>
			<h:outputText escape="false" value="#{legend['assignable.domain.value.4']}"/>
		</h:panelGrid>
		<p:spacer height="10px" />

		<h:outputText value="#{legend['consent.title']}" styleClass="subtitle"/>
		<p:spacer height="10px" />
		<h:panelGrid columns="2" cellspacing="0" cellpadding="0" columnClasses="legendColumn1, defaultColumn">
			<h:graphicImage url="/image/contact_consent.gif" alt=""/><h:outputText escape="false" value="#{legend['consent.consent']}"/>
			<h:graphicImage url="/image/contact_no_consent.gif" alt=""/><h:outputText escape="false" value="#{legend['consent.noconsent']}"/>
		</h:panelGrid>

		<p:spacer height="10px" rendered="#{provinceController.company3}"/>
		<h:outputText escape="false" value="#{legend['synchro.title']}" styleClass="subtitle" rendered="#{provinceController.company3}"/>
		<p:spacer height="10px" rendered="#{provinceController.company3}"/>
		<h:panelGrid columns="2" cellspacing="0" cellpadding="0" rendered="#{provinceController.company3}" columnClasses="legendColumn1, defaultColumn">
			<h:graphicImage url="/image/maison.gif" alt=""/><h:outputText escape="false" value="#{legend['synchro.text']}"/>
		</h:panelGrid>

		<p:spacer height="10px"/>

		<h:outputText value="#{legend['followup.title']}" styleClass="subtitle"/>
		<p:spacer height="10px" />
		<h:panelGrid columns="2" cellspacing="0" cellpadding="0" columnClasses="legendColumn1, defaultColumn">
			<h:outputText value="&#160;"/><h:outputText value="#{legend['followup.notContacted']}"/>
			<h:graphicImage url="/image/crochet_follow-up_required.gif" alt=""/><h:outputText escape="false" value="#{legend['followup.noFollowup']}"/>
			<h:graphicImage url="/image/crochet_follow-up.gif" alt=""/><h:outputText escape="false" value="#{legend['followup.followup']}"/>
			<h:graphicImage url="/image/no_follow-up.gif" alt=""/><h:outputText escape="false" value="#{legend['followup.noneRequired']}"/>
		</h:panelGrid>

		<p:spacer height="10px" />

		<h:outputText value="#{legend['status.title']}" styleClass="subtitle"/>
		<p:spacer height="10px" />
		<h:panelGrid columns="2" columnClasses="legendStatusColumn1, defaultColumn" cellspacing="0" cellpadding="2">

			<h:graphicImage url="/image/wrong-way.png" alt=""/>
			<h:panelGrid columns="1" cellspacing="0" cellpadding="0">
				<h:outputText value="#{legend['status.roadblock.text']}"/>
			</h:panelGrid>

			<p:spacer height="5px" /><p:spacer height="5px" />

			<h:graphicImage url="/image/not_canadian.png" alt=""/>
			<h:panelGrid columns="1" cellspacing="0" cellpadding="0">
				<h:outputText value="#{legend['status.roadblock.not.canadian.text']}"/>
			</h:panelGrid>

			<p:spacer height="5px" /><p:spacer height="5px" />

			<h:graphicImage url="/image/intact-client.png" alt=""/>
			<h:panelGrid columns="1" cellspacing="0" cellpadding="0">
				<h:outputText value="#{legend['status.roadblock.intact.client.text']}"/>
			</h:panelGrid>

			<p:spacer height="5px" /><p:spacer height="5px" />

			<h:outputText value="#{legend['status.quoteIncomplete']}"/>
			<h:panelGrid columns="1" cellspacing="0" cellpadding="0">
				<h:outputText value="#{legend['status.quoteIncomplete.text']}"/>
				<h:outputText escape="false" value="#{legend['status.quoteIncomplete.nextSteps']}"/>
			</h:panelGrid>

			<p:spacer height="5px" /><p:spacer height="5px" />

			<h:outputText value="#{legend['status.quoteComplete']}"/>
			<h:panelGrid columns="1" cellspacing="0" cellpadding="0">
				<h:outputText value="#{legend['status.quoteComplete.text']}"/>
				<h:outputText escape="false" value="#{legend['status.quoteComplete.nextSteps']}"/>
			</h:panelGrid>

			<p:spacer height="5px" /><p:spacer height="5px" />

			<h:outputText value="#{legend['status.purchaseAbandoned']}"/>
			<h:panelGrid columns="1" cellspacing="0" cellpadding="0">
				<h:outputText value="#{legend['status.purchaseAbandoned.text']}"/>
				<h:outputText escape="false" value="#{legend['status.purchaseAbandoned.nextSteps']}"/>
			</h:panelGrid>

			<p:spacer height="5px" /><p:spacer height="5px" />

			<h:outputText value="#{legend['status.purchaseCompleted']}"/>
			<h:panelGrid columns="1" cellspacing="0" cellpadding="0">
				<h:outputText value="#{legend['status.purchaseCompleted.text']}"/>
				<h:outputText escape="false" value="#{legend['status.purchaseCompleted.nextSteps']}"/>
			</h:panelGrid>

			<p:spacer height="5px" /><p:spacer height="5px" />

			<h:outputText value="#{legend['status.uploaded']}"/>
			<h:panelGrid columns="1" cellspacing="0" cellpadding="0">
				<h:outputText value="#{legend['status.uploaded.text']}"/>
				<h:outputText escape="false" value="#{legend['status.uploaded.nextSteps']}"/>
			</h:panelGrid>

			<p:spacer height="5px" /><p:spacer height="5px" />

			<h:outputText value="#{legend['status.accepted']}" rendered="#{provinceController.companyA}" />
			<h:panelGrid columns="1" cellspacing="0" cellpadding="0" rendered="#{provinceController.companyA}">
				<h:outputText value="#{legend['status.accepted.text']}"/>
				<h:outputText escape="false" value="#{legend['status.accepted.nextSteps']}"/>
			</h:panelGrid>

			<p:spacer height="5px" rendered="#{provinceController.companyA}"/><p:spacer height="5px" rendered="#{provinceController.companyA}"/>

			<h:outputText value="#{legend['status.refused']}"  rendered="#{provinceController.companyA}"/>
			<h:panelGrid columns="1" cellspacing="0" cellpadding="0" rendered="#{provinceController.companyA}" >
				<h:outputText value="#{legend['status.refused.text']}"/>
				<h:outputText escape="false" value="#{legend['status.refused.nextSteps']}"/>
			</h:panelGrid>

			<p:spacer height="5px" rendered="#{provinceController.companyA}"/><p:spacer height="5px" rendered="#{provinceController.companyA}"/>

			<h:outputText value="#{legend['status.expired']}"/>
			<h:panelGrid columns="1" cellspacing="0" cellpadding="0">
				<h:outputText value="#{legend['status.expired.text']}"/>
				<h:outputText escape="false" value="#{legend['status.expired.nextSteps']}"/>
			</h:panelGrid>
			<p:spacer height="5px" /><p:spacer height="5px" />

		</h:panelGrid>

		<p:spacer height="10px" />
		<h:outputText value="#{legend['type.of.residence.title']}" styleClass="subtitle" rendered="#{provinceController.companyA}"/>
		<p:spacer height="10px" />
		<h:panelGrid columns="2" cellspacing="0" cellpadding="0" columnClasses="legendColumn1, defaultColumn" rendered="#{provinceController.companyA}">
			<h:outputText value="&#160;"/><h:outputText escape="false" value="#{legend['type.of.residence.text.tenant']}"/>
			<h:outputText value="&#160;"/><h:outputText escape="false" value="#{legend['type.of.residence.text.condo']}"/>
			<h:outputText value="&#160;"/><h:outputText escape="false" value="#{legend['type.of.residence.text.home']}"/>
			<h:graphicImage url="/image/already_intact.png" alt=""/><h:outputText escape="false" value="#{legend['type.of.residence.text.green.ind']}"/>
			<h:graphicImage url="/image/not_interested.png" alt=""/><h:outputText escape="false" value="#{legend['type.of.residence.text.red.ind']}"/>
		</h:panelGrid>
		<p:spacer height="10px" />

	</h:panelGrid>

</p:panel>
</html>
