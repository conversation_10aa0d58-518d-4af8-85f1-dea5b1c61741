<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui">

	<f:view locale="#{languageController.localeUrl}" />

	<!--Only for debugging in development mode. Use ctrl+shift+d -->
	<ui:debug rendered="#{facesContext.application.projectStage == 'Development'}" />

	<script type="text/javascript" language="JavaScript">

		mainPage = #{!searchController.isSearch and !searchController.isSearchResult};
		currentIndex=<h:outputText value="#{searchController.index}" />;

		var refNo = '<h:outputText value="#{searchController.emailReferenceNo}" />'
		if(refNo){
		openViewQuote(refNo);
		}

		function openViewQuote(quoteRefNo){
			var urlParam = 'pages/quotes/viewQuote/viewQuoteFromPLT.jsf?referenceNo=';
			urlParm = urlParam + quoteRefNo ;
			var window1 = window.open(urlParm,'window_name','dependent=yes, menubar=no, scrollbars=yes, resizable=yes, toolbar=no');
			window1.focus();
		}
	</script>

	<h:form styleClass="info-content-in-tab" id="quoteListForm">
		<!-- The tag for securityCSRF has been moved in the controls.xhtml -->
		<p:dialog id="a_work_around_for_richfaces_modal_panel_non_render_issue"></p:dialog>

		<h:inputHidden id="index" value="#{searchController.index}" />

		<f:loadBundle var="msg_quo" basename="com.intact.brokeroffice.controller.quotes.quotes"/>
		<f:loadBundle var="msg_legend" basename="com.intact.brokeroffice.controller.legend.legend"/>

		<h:outputText value="#{msg_quo[searchController.pageTitleKey]}" styleClass="title"/>
		<p:spacer width="20px" />

		<ui:include src="/pages/quotes/controls.xhtml" />

		<p:panel styleClass="leftfullpart line865"></p:panel>
		<h:graphicImage url="/image/ligneVirgule.png" styleClass="rightpart" alt=""/>

		<br/><br/>

		<div class="info">
			<h:outputFormat escape="false"  value="#{msg_quo['info.quotes']}" rendered="#{not searchController.isSearchResult}">
				<f:param value="#{searchController.maximumRow}"/>
			</h:outputFormat>

			<h:outputFormat escape="false" value="#{msg_quo['info.search']}" rendered="#{searchController.isSearchResult and searchController.quotesBeansSize > 1}">
				<f:param value="#{searchController.quotesBeansSize}"/>
			</h:outputFormat>

			<h:outputFormat escape="false" value="#{msg_quo['info.search.one']}" rendered="#{searchController.isSearchResult and searchController.quotesBeansSize == 1}">
				<f:param value="#{searchController.quotesBeansSize}"/>
			</h:outputFormat>
		</div>

		<h:panelGroup rendered="#{searchController.isSearchFailed('C.RE')}">
			<br/>
			<h:graphicImage url="/image/iconRefused.png" alt=""/>
			<h:outputFormat escape="false" value="#{msg_quo['info.error.C.RE']}" styleClass="errorMessageBold" />
		</h:panelGroup>

		<h:panelGroup rendered="#{searchController.isSearchFailed('P.RE')}">
			<br/>
			<h:graphicImage url="/image/iconRefused.png" alt=""/>
			<h:outputFormat escape="false" value="#{msg_quo['info.error.P.RE']}" styleClass="errorMessageBold" />
		</h:panelGroup>

		<h:panelGroup rendered="#{searchController.isSearchFailed('P.AU')}">
			<br/>
			<h:graphicImage url="/image/iconRefused.png" alt=""/>
			<h:outputFormat escape="false" value="#{msg_quo['info.error.AU']}" styleClass="errorMessageBold" />
		</h:panelGroup>

		<h:panelGroup rendered="#{searchController.isSearchFailed('C.AU')}">
			<br/>
			<h:graphicImage url="/image/iconRefused.png" alt=""/>
			<h:outputFormat escape="false" value="#{msg_quo['info.error.AU']}" styleClass="errorMessageBold" />
		</h:panelGroup>

		<h:panelGroup rendered="#{searchController.isSearchFailed('REASS')}">
			<br/>
			<h:graphicImage url="/image/iconRefused.png" alt=""/>
			<h:outputFormat escape="false" value="#{msg_quo['info.error.REASS']}" styleClass="errorMessageBold" />
		</h:panelGroup>

		<br/>

		<h:panelGroup rendered="#{searchController.reassignConfirmation}">
			<br/>
			<h:outputFormat value="#{msg_quo['info.reassignConfirmation']}" styleClass="notification" />
			<br/>
		</h:panelGroup>

		<ui:param name="listName" value="quoteList"/>
		<ui:param name="scrollerPerPage" value="#{searchController.scrollerPerPage}"/>
		<ui:param name="scrollerPage" value="#{searchController.scrollerPage}"/>

		<ui:include src="/pages/common/scrollertop.xhtml" >
			<ui:param name="scrollerPerPage" value="#{scrollerPerPage}"/>
			<ui:param name="scrollerPerPageList" value="#{searchController.scrollerPerPageList}"/>
			<ui:param name="listName" value="#{listName}"/>
		</ui:include>

		<br/><br/>

		<p:dataTable id="#{listName}" value="#{searchController.quotesBeans}"
					 var="qb"
					 rowKey="#{qb.agreementNumber}"
					 expandedRow="#{qb.expanded}"
					 rows="#{searchController.scrollerPerPage}"
					 rowStyleClass="#{qb.expanded ? 'prime-row-expanded' : 'prime-row-collapsed'}"
					 tableStyleClass="prime-table tabPanelContent"
					 binding="#{list_quotes_table}"
					 rowIndexVar="rowIndex"
					 paginator="true"
					 paginatorPosition="bottom"
					 paginatorTemplate="{PreviousPageLink} {PageLinks} {NextPageLink}"
					 stripedRows="true">

			<ui:param name="expandedStyle" value="#{qb.expanded ? 'expandedRow' : ''}"/>
			<ui:param name="childCount" value="#{qb.expanded ? qb.childrens.size() : 0}"/>

			<p:column headerClass="firstColumn" styleClass="prime-table-cell firstColumn #{expandedStyle} #{qb.expanded ? 'expandedFirstColumn' : ''}"
					  rendered="#{permissionController.checkReassign}">
				<f:facet name="header">
					<p:panelGrid columns="1" style="float:left;">
						<p:commandButton id="deselectAllBtn" styleClass="deselectAllBtn-#{rowIndex}" immediate="true" action="#{searchController.deselectAllQuotes}" image="image/iconDeselectAll.png" update=":mainTab:quoteListForm,:mainTab:reassignForm"
										 onclick="checkAllCheckboxesInTable( this.id, false );"  />
						<p:commandButton id="selectAllBtn" styleClass="selectAllBtn-#{rowIndex}" immediate="true" action="#{searchController.selectAllQuotes}" image="image/iconSelectAll.png" update=":mainTab:quoteListForm,:mainTab:reassignForm"
										 onclick="checkAllCheckboxesInTable( this.id, true );"/>
						<p:tooltip for="selectAllBtn" value="#{msg_quo['select.all']}" my="left top" at="right bottom" showDelay="100" styleClass="tooltip"/>
						<p:tooltip for="deselectAllBtn" value="#{msg_quo['deselect.all']}" my="left top" at="right bottom" showDelay="100" styleClass="tooltip"/>
					</p:panelGrid>
				</f:facet>
				<h:selectBooleanCheckbox id="selected-#{rowIndex}" immediate="true" value="#{qb.selected}"
										 styleClass="cui-panelgrid-cell ui-g-12 ui-md-12"
										 valueChangeListener="#{searchController.toggleSelection}">
					<p:ajax event="change"
							update=":mainTab:quoteListForm,:mainTab:reassignForm"/>
				</h:selectBooleanCheckbox>
			</p:column>

			<p:column sortBy="#{qb.lastActivityNote.date}" styleClass="prime-table-cell notFirstColumn #{expandedStyle}" >
				<f:facet name="header">
					<p:panelGrid columns="1" >
						<h:outputText value="#{msg_quo['table.activity']}"/>
						<br/>
					</p:panelGrid>
				</f:facet>
				<p:outputPanel layout="block">
					<p:tooltip my="left top" at="right bottom" showDelay="100"
							   styleClass="tooltip" rendered="#{qb.lastActivityNote != null}">
						<h:outputText styleClass="activityDate-#{rowIndex}" value="#{qb.lastActivityNote.date}" rendered="#{!provinceController.company3}">
							<f:convertDateTime type="date" pattern="[yyyy-MM-dd HH:mm]" timeZone="US/Eastern"/>
						</h:outputText>
						<h:outputText styleClass="activityDate-#{rowIndex}" value="#{qb.lastActivityNote.date}" rendered="#{provinceController.company3}">
							<f:convertDateTime type="date" pattern="[yyyy-MM-dd HH:mm]" timeZone="Canada/Mountain"/>
						</h:outputText>
						<br/>
						<h:outputFormat styleClass="activityType-#{rowIndex}" value="#{msg_quo[qb.lastActivityNote.activity]}">
							<f:param value="#{qb.lastActivityNote.account}"/>
						</h:outputFormat>
					</p:tooltip>

					<h:graphicImage width="16" styleClass="quoteStatus-#{rowIndex}" url="/image/#{qb.openedQuoteStatus}-flag.png" rendered="#{qb.openedQuoteStatus != null and qb.lastActivityNote == null}" alt=""/>
				</p:outputPanel>
			</p:column>

			<p:column sortBy="#{qb.agreementNumber}" styleClass="prime-table-cell #{expandedStyle}" >
				<p:commandLink  id="expandQuote" action="#{searchController.expandQuote(qb)}" update="#{listName}">
					<p:graphicImage id="expandedNo" styleclass="parent-#{rowIndex}" url="/image/minusIcon.gif" rendered="#{qb.parent and qb.expanded}" alt=""/>
					<p:graphicImage id="expandedYes" styleclass="parent-#{rowIndex}" url="/image/plusIcon.gif" rendered="#{qb.parent and not qb.expanded}" alt=""/>
				</p:commandLink>
			</p:column>

			<p:column sortBy="#{qb.agreementNumber}" styleClass="prime-table-cell #{expandedStyle} refNoStyle" headerClass="link_sortByReferenceNo">
				<f:facet name="header">
					<p:panelGrid columns="1" >
						<h:outputText escape="false" value="#{msg_quo['table.referenceNo1']}" rendered="#{not searchController.isSearchResult}"/>
						<h:outputText escape="false" value="#{msg_quo['table.referenceNo']}" rendered="#{searchController.isSearchResult}"/>
						<h:outputText value="#{msg_quo['table.policyNo']}" rendered="#{searchController.isSearchResult}"/>
					</p:panelGrid>
				</f:facet>

				<h:outputLink id="viewQuoteLinkParent" value="${searchController.getPage(qb.applicationMode)}" target="_blank" rendered="#{authentificationController.canViewQuote}"
							  styleClass="link_view#{qb.agreementNumber} link_#{qb.applicationMode}" >
					<h:outputText styleClass="agreementNumber-#{rowIndex}" value="#{qb.agreementNumber}"/><br/>
					<h:outputText  styleClass="agreementLegacyNumber-#{rowIndex}" value="#{qb.agreementLegacyNumber}" rendered="#{qb.agreementLegacyNumber != null}"/>
					<f:param name="referenceNo" value="#{qb.id}"/>
					<f:param name="lineOfBusiness" value="#{qb.lineOfBusiness.code}"/>
					<f:param name="lineOfInsurance" value="#{qb.lineOfInsurance.code}"/>
					<f:param name="searchIndex" value="#{searchController.index}"/>
				</h:outputLink>

				<p:panelGrid columns="1" rendered="#{!authentificationController.canViewQuote}">
					<h:outputText styleClass="agreementNumber-#{rowIndex} link_#{qb.applicationMode}" value="#{qb.agreementNumber}"/><br/>
					<h:outputText styleClass="agreementLegacyNumber-#{rowIndex} #{qb.cssClass}" value="#{qb.agreementLegacyNumber}" rendered="#{qb.agreementLegacyNumber != null}"/>
				</p:panelGrid>
			</p:column>

			<p:column sortBy="#{qb.followUp}" styleClass="prime-table-cell notFirstColumn #{expandedStyle}" headerClass="link_sortByFollowUp">
				<f:facet name="header">
					<p:panelGrid columns="1" >
						<h:outputText escape="false" value="#{msg_quo['table.callback']}"/>
					</p:panelGrid>
				</f:facet>
				<p:outputPanel layout="block">
					<h:graphicImage id="callbackImage" styleClass="followupStatus-#{rowIndex}" url="/image/callback.png" rendered="#{qb.followUp=='CALLBACK_REQUESTED'}" alt="">
					</h:graphicImage>
				</p:outputPanel>
			</p:column>

			<p:column sortBy="#{qb.autoplusAvailable}" styleClass="prime-table-cell #{expandedStyle}" rendered="#{searchController.autoPlusDisplay}" headerClass="link_sortByLanguage">
				<f:facet name="header">
					<p:panelGrid columns="1" >
						<h:outputText escape="false" value="#{msg_quo['auto.plus.header']}"/>
					</p:panelGrid>
				</f:facet>
				<h:outputText styleClass="language-#{rowIndex}" value="#{msg_quo['auto.plus.value']}" rendered="#{qb.autoplusAvailable}" />
			</p:column>

			<p:column sortBy="#{qb.quoteSource}" styleClass="prime-table-cell notFirstColumn #{expandedStyle}" headerClass="link_sortByQuoteSource">
				<f:facet name="header">
					<p:panelGrid columns="1" >
						<h:outputText escape="false" value="#{msg_quo['table.quote.source']}"/>
					</p:panelGrid>
				</f:facet>
				<h:outputText styleClass="quoteSource-#{rowIndex}" value="#{msg_quo['quote.source.'.concat(qb.quoteSource)]}"/>
			</p:column>

			<p:column headerClass="link_sortByInfo" styleClass="prime-table-cell #{expandedStyle}">
				<f:facet name="header">
					<p:panelGrid columns="1" >
					</p:panelGrid>
				</f:facet>
				<p:outputPanel>
					<h:graphicImage id ="info-image"
									width ="15"
									styleClass ="followupStatus-#{rowIndex}"
									url ="/image/info.png"
									alt="">
					</h:graphicImage>
					<p:overlayPanel id="tooltip-id-#{rowIndex}"
                                    for="info-image"
                                    styleClass="tooltip tooltip-large"
                                    showEvent="mouseover"
                                    hideEvent="mouseout"
                                    dynamic="true"
                                    dismissable="false"
                                    showDelay="100"
                                    autoHide="false">
						<ui:include src="quoteSummaryFromPLT.xhtml">
							<ui:param name="qb" value="#{qb}"/>
						</ui:include>
                    </p:overlayPanel>
				</p:outputPanel>
			</p:column>

			<p:column sortBy="#{qb.languageCommunication}" styleClass="prime-table-cell #{expandedStyle}" rendered="#{searchController.languageCommunicationDisplay}" headerClass="link_sortByLanguage">
				<f:facet name="header">
					<p:panelGrid columns="1" >
						<h:outputText escape="false" value="#{msg_quo['table.language']}"/>
					</p:panelGrid>
				</f:facet>
				<h:outputText styleClass="language-#{rowIndex}" value="#{qb.languageCommunication.codeISO}"/>
			</p:column>

			<p:column sortBy="#{qb.customerValueIndex1}" rendered="#{searchController.CVIDisplay}" headerClass="link_sortByCVI" styleClass="prime-table-cell #{expandedStyle}">
				<f:facet name="header">
					<p:panelGrid columns="1" >
						<h:outputText escape="false" value="#{msg_quo['table.cvi']}"/>
					</p:panelGrid>
				</f:facet>
				<p:panelGrid columns="1" >
					<h:outputText styleClass="customerValueIndexBand-#{rowIndex}" value="#{msg_quo[qb.customerValueIndexBand1]} /  #{msg_quo[qb.customerValueIndexBand2]}" rendered="#{qb.customerValueIndex1 != null and qb.customerValueIndex2 != null}"/>
					<h:outputText styleClass="customerValueIndexBand-#{rowIndex}" value="#{msg_quo[qb.customerValueIndexBand1]}" rendered="#{qb.customerValueIndex1 != null and empty qb.customerValueIndex2 }"/>
					<h:outputText styleClass="customerValueIndexBand-#{rowIndex}" value="#{msg_quo[qb.customerValueIndexBand2]}" rendered="#{qb.customerValueIndex2 != null and empty qb.customerValueIndex1}"/>

				</p:panelGrid>
			</p:column>

			<p:column sortBy="#{qb.consent}" rendered="#{!provinceController.company3}" headerClass="link_sortByConsent" styleClass="prime-table-cell #{expandedStyle}">
				<f:facet name="header">
					<p:panelGrid columns="1" >
						<h:outputText escape="false" value="#{msg_quo['table.consent']}"/>
						<br/>
					</p:panelGrid>
				</f:facet>
				<h:graphicImage id="consentYes" styleClass="contactConsent-#{rowIndex}" url="/image/contact_consent.gif" rendered="#{qb.consent}" alt=""/>
				<p:tooltip styleClass="tooltip" my="left top" at="right bottom" for="consentYes" value="#{msg_legend['consent.consent']}" rendered="#{qb.consent}"/>
				<h:graphicImage id="consentNo" styleClass="contactConsent-#{rowIndex}" url="/image/contact_no_consent.gif" rendered="#{!qb.consent}" alt=""/>
				<p:tooltip styleClass="tooltip" my="left top" at="right bottom" for="consentNo" value="#{msg_legend['consent.noconsent']}" rendered="#{!qb.consent}"/>
			</p:column>

			<p:column rendered="#{provinceController.company3}" headerClass="link_sortByConsent" styleClass="prime-table-cell #{expandedStyle}">
				<f:facet name="header">
					<p:panelGrid columns="1" >
						<h:outputText escape="false" value="#{msg_quo['table.consent']}"/>
						<h:outputText value="#{msg_quo['table.sync']}"/>
					</p:panelGrid>
				</f:facet>
				<p:panelGrid columns="1" >
					<h:graphicImage id="consentYesAB" styleClass="contactConsent-#{rowIndex}" url="/image/contact_consent.gif" rendered="#{qb.consent}" alt=""/>
					<p:tooltip styleClass="tooltip" for="consentYesAB" my="left top" at="right bottom" value="#{msg_legend['consent.consent']}" rendered="#{qb.consent}" />
					<h:graphicImage id="consentNoAB" styleClass="contactConsent-#{rowIndex}" url="/image/contact_no_consent.gif" rendered="#{!qb.consent}" alt=""/>
					<p:tooltip styleClass="tooltip" for="consentNoAB" my="left top" at="right bottom" value="#{msg_legend['consent.noconsent']}" rendered="#{!qb.consent}"/>
					<h:graphicImage id="syncroAB" styleClass="syncro-#{rowIndex}" url="/image/maison.gif" rendered="#{qb.sync}" alt=""/>
					<p:tooltip styleClass="tooltip" for="syncroAB" my="left top" at="right bottom" value="#{msg_legend['synchro.text']}" escape="false" rendered="#{qb.sync}"/>
				</p:panelGrid>
			</p:column>

			<p:column sortBy="#{qb.brokerName}" width="160" headerClass="link_sortByBroker" styleClass="prime-table-cell #{expandedStyle}">
				<f:facet name="header">
					<p:panelGrid columns="1" >
						<h:outputText value="#{msg_quo['table.broker']}"/>
						<br/>
					</p:panelGrid>
				</f:facet>
				<p:panelGrid columns="1">
					<p:outputPanel layout="block">
						<h:outputText id="brokerName" styleClass="broker-#{rowIndex}" value="#{qb.brokerName}" converter="pointOfSaleConverter"/>
						<p:tooltip value="#{qb.brokerName}" for="brokerName" styleClass="tooltip" my="left top" at="right bottom"/>
					</p:outputPanel>
				</p:panelGrid>
			</p:column>

			<p:column sortBy="#{qb.followUp}" styleClass="prime-table-cell notFirstColumn #{expandedStyle}" headerClass="link_sortByFollowUp">
				<f:facet name="header">
					<p:panelGrid columns="1" >
						<h:outputText escape="false" value="#{msg_quo['table.followUp']}"/>
					</p:panelGrid>
				</f:facet>
				<p:outputPanel layout="block" rendered="#{qb.followUp != 'NOT_CONTACTED'}">
					<p:tooltip my="left top"
                               at="right bottom"
                               showDelay="100"
							   styleClass="tooltip"
                               rendered="#{qb.lastFollowupNote != null and qb.followUp != 'DUPLICATE_FAKE'}">
						<h:outputText styleClass="followupDate-#{rowIndex}" value="#{qb.lastFollowupNote.date}" rendered="#{!provinceController.company3}">
							<f:convertDateTime type="date" pattern="[yyyy-MM-dd HH:mm] - " timeZone="US/Eastern"/>
						</h:outputText>
						<h:outputText styleClass="followupDate-#{rowIndex}" value="#{qb.lastFollowupNote.date}" rendered="#{provinceController.company3}">
							<f:convertDateTime type="date" pattern="[yyyy-MM-dd HH:mm] - " timeZone="Canada/Mountain"/>
						</h:outputText>
						<h:outputText styleClass="followupAccount-#{rowIndex}" value="#{qb.lastFollowupNote.account}" /><br/>
						<h:outputText styleClass="followupNote-#{rowIndex}" value="#{qb.lastFollowupNote.note}" />
					</p:tooltip>
					<h:graphicImage id="followUpImage" styleClass="followupStatus-#{rowIndex}" url="/image/crochet_follow-up.gif" rendered="#{qb.followUp=='CONTACTED_FOLLOWUP_REQUIRED'}" alt=""/>
					<p:tooltip styleClass="tooltip" for="followUpImage" value="#{msg_legend['followup.followup']}" escape="false"/>
					<h:graphicImage id="noFollowUpImage" styleClass="followupStatus-#{rowIndex}" url="/image/crochet_follow-up_required.gif" rendered="#{qb.followUp=='CONTACTED_NO_FOLLOWUP_REQUIRED'}" alt=""/>
					<p:tooltip styleClass="tooltip" for="noFollowUpImage" value="#{msg_legend['followup.noFollowup']}" escape="false"/>
					<h:graphicImage id="noContactNoFollowUpImage" styleClass="followupStatus-#{rowIndex}" url="/image/no_follow-up.gif" rendered="#{qb.followUp=='NOT_CONTACTED_NO_FOLLOWUP_REQUIRED'}" alt=""/>
					<p:tooltip styleClass="tooltip" for="noContactNoFollowUpImage" value="#{msg_legend['followup.noneRequired']}" escape="false"/>
					<h:graphicImage id="fakeLeadImage" styleClass="followupStatus-#{rowIndex}" url="/image/fake_lead.png" rendered="#{qb.followUp=='DUPLICATE_FAKE'}" alt=""/>
					<p:tooltip styleClass="tooltip" for="fakeLeadImage" value="FAKE LEAD" />
				</p:outputPanel>
			</p:column>

			<p:column sortBy="#{qb.lastName}" headerClass="link_sortByLastName" styleClass="prime-table-cell #{expandedStyle}">
				<f:facet name="header">
					<p:panelGrid columns="1" >
						<h:outputText escape="false" value="#{msg_quo['table.firstName']}"/>
						<h:outputText value="#{msg_quo['table.lastName']}"/>
					</p:panelGrid>
				</f:facet>
				<p:panelGrid columns="1" rendered="#{qb.lineOfBusiness == 'PERSONAL_LINES'}">
					<p:outputPanel layout="block">
						<h:outputText id="firstname" styleClass="firstName-#{rowIndex}" value="#{qb.firstName}" converter="nameConverter"/>
						<p:tooltip for="firstname" value="#{qb.firstName}" my="left top" at="right bottom" styleClass="tooltip" rendered="#{qb.firstNameLong}" />
					</p:outputPanel>
					<p:outputPanel layout="block">
						<h:outputText id="lastname" styleClass="lastName-#{rowIndex}" value="#{qb.lastName}" converter="nameConverter"/>
						<p:tooltip for="lastname" value="#{qb.lastName}" my="left top" at="right bottom"  styleClass="tooltip" rendered="#{qb.lastNameLong}"/>
					</p:outputPanel>
				</p:panelGrid>

				<p:panelGrid columns="1" rendered="#{qb.lineOfBusiness == 'COMMERCIAL_LINES'}">
					<p:outputPanel layout="block">
						<h:outputText id="unstructuredName" styleClass="unstructuredName-#{rowIndex}" value="#{qb.unstructuredName}" converter="nameConverter"/>
						<p:tooltip for="unstructuredName" value="#{qb.unstructuredName}" my="left top" at="right bottom" styleClass="tooltip" rendered="#{qb.unstructuredNameLong}" />
					</p:outputPanel>
				</p:panelGrid>
			</p:column>

			<p:column sortBy="#{qb.lastUpdate}" headerClass="link_sortByLastUpdate" styleClass="prime-table-cell #{expandedStyle}">
				<f:facet name="header">
					<p:panelGrid columns="1"  >
						<h:outputText escape="false" value="#{msg_quo['table.creationDate']}"/>
						<h:outputText value="#{msg_quo['table.lastUpdate']}"/>
					</p:panelGrid>
				</f:facet>
				<p:panelGrid columns="1">
					<h:outputText styleClass="creationDate-#{rowIndex} nowrap" value="#{qb.creationDate}" rendered="#{!provinceController.company3}">
						<f:convertDateTime type="date" pattern="yyyy-MM-dd" timeZone="US/Eastern"/>
					</h:outputText>
					<h:outputText styleClass="creationDate-#{rowIndex} nowrap" value="#{qb.creationDate}" rendered="#{provinceController.company3}">
						<f:convertDateTime type="date" pattern="yyyy-MM-dd" timeZone="Canada/Mountain"/>
					</h:outputText>
					<h:outputText styleClass="lastUpdate-#{rowIndex}" value="#{qb.lastUpdate}" rendered="#{!provinceController.company3}">
						<f:convertDateTime type="date" pattern="yyyy-MM-dd HH:mm" timeZone="US/Eastern"/>
					</h:outputText>
					<h:outputText styleClass="lastUpdate-#{rowIndex}" value="#{qb.lastUpdate}" rendered="#{provinceController.company3}">
						<f:convertDateTime type="date" pattern="yyyy-MM-dd HH:mm" timeZone="Canada/Mountain"/>
					</h:outputText>
				</p:panelGrid>
			</p:column>

			<p:column sortBy="#{qb.status}" headerClass="link_sortByStatus" styleClass="prime-table-cell #{expandedStyle}">
				<f:facet name="header">
					<p:panelGrid columns="1" >
						<h:outputText value="#{msg_quo['table.status']}"/><br/>
					</p:panelGrid>
				</f:facet>
				<h:graphicImage styleClass="rbImage-#{rowIndex}" id="roadblock" url="/image/#{qb.roadBlockImage}" rendered="#{qb.roadblockCode != null}" alt="">
				</h:graphicImage>
				<h:outputText styleClass="rbMsg-#{rowIndex}" value=" " rendered="#{qb.roadblockCode != null}"/>
				<h:outputText styleClass="rbStatus-#{rowIndex}" value="#{global[qb.status.name()]}"/>
				<p:tooltip for="roadblock"
						   rendered="#{not empty qb.roadblockMessage and qb.roadblockMessage != '&amp;nbsp;'}"
						   value="#{qb.roadblockMessage}"
						   my="left top"
						   at="right bottom"
						   showDelay="100"
						   styleClass="tooltip"/>
			</p:column>

			<p:column sortBy="#{qb.inceptionDate}" rendered="#{!provinceController.companyA}" headerClass="link_sortByInceptionDate" styleClass="prime-table-cell #{expandedStyle} #{qb.expanded ? 'expandedLastColumn' : ''} nowrap">
				<f:facet name="header">
					<h:outputText escape="false" value="#{msg_quo['table.inceptionDate']}"  />
				</f:facet>
				<h:outputText styleClass="inceptionDate-#{rowIndex}" value="#{qb.inceptionDate}" rendered="#{!provinceController.company3}">
					<f:convertDateTime type="date" pattern="yyyy-MM-dd" timeZone="US/Eastern"/>
				</h:outputText>
				<h:outputText styleClass="inceptionDate-#{rowIndex}" value="#{qb.inceptionDate}" rendered="#{provinceController.company3}">
					<f:convertDateTime type="date" pattern="yyyy-MM-dd" timeZone="Canada/Mountain"/>
				</h:outputText>
			</p:column>

			<p:rowExpansion>
				<ui:include src="/pages/quotes/childQuote.xhtml">
					<ui:param name="quoteListForm" value="#{quoteListForm}"/>
					<ui:param name="reassignForm" value="#{reassignForm}"/>
				</ui:include>
			</p:rowExpansion>

			<ui:include src="/pages/common/scrollerbottom.xhtml" />

		</p:dataTable>

		<p:panel id="panelBelowTable">
			<p:panelGrid columns="1">
				<h:panelGroup rendered="#{permissionController.checkReassign and searchController.hasQuotesSelected}">
					<!--  jmarois: PM9331: insure that image is always download to avoid buttons disappear temporarily  -->
					<p:panel style="display:none">
						<h:commandLink styleClass="actionBtnRightHover" >
							<h:outputText value="invisible right" />
						</h:commandLink>

						<h:commandLink styleClass="actionBtnLeftHover">
							<h:outputText value="invisible left" />
						</h:commandLink>
					</p:panel>

					<h:outputLink value="#" onclick="Primefaces.showModalPanel('mainTab\:modalPanelReassign');" id="reassignLink" styleClass="actionBtn floatLeft link_reassignQuote">
						<span class="right">
							<h:outputText value="#{global['form.button.reassign']}"/>
		   					<h:graphicImage url="/image/btnRightArrow.png" alt=""/>
		   				</span>
					</h:outputLink>
				</h:panelGroup>
				<h:panelGroup>
					<br/>
					<h:outputLink styleClass="floatLeft link_legend" value="#"
								  onclick="window.open('pages/legend/legend.jsf', 'legendWindow', 'width=815, height=700, scrollbars=yes, dependent=yes, menubar=no, toolbar=no'); return false;">
						<h:outputText value="#{legend['legend.link']}"/>
					</h:outputLink>
					<br/>
				</h:panelGroup>

				<h:panelGroup rendered="#{!provinceController.companyA}">
					<br/>
					<h:outputText value="#{msg_quo['cvi.foot.note']}"/>
					<br/>
				</h:panelGroup>

			</p:panelGrid>
		</p:panel>

	</h:form>

	<div
			id="modalOverlay"
			class="modal">
		<p:panel id="modalPanelReassign" closable="true"
				  style="display:none"
				  widgetVar="panelReassignWidget" width="592" height="180" resizeable="false"
				  rendered="#{permissionController.checkReassign}">
			<h:form id="reassignForm">
				<h:inputHidden id="AntiCSRFToken" value="#{searchController.tokenCSRF}"/>
				<h:inputHidden id="index" value="#{searchController.index}"/>
				<ui:include src="/pages/quotes/reassign.xhtml"/>
			</h:form>
		</p:panel>
	</div>
</ui:composition>
