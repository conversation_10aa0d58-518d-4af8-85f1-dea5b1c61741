<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:c="http://java.sun.com/jstl/core"
                xmlns:fn="http://java.sun.com/jsp/jstl/functions"
                xmlns:p="http://primefaces.org/ui">


  <f:loadBundle var="msg_quo" basename="com.intact.brokeroffice.controller.quotes.quotes"/>
  <h:form styleClass="search-content-tab" id="quoteSearchForm">

    <h:inputHidden id="index" value="#{searchController.index}"/>
    <p:panel styleClass="search-panel-tab">
      <f:loadBundle var="msg_search" basename="com.intact.brokeroffice.controller.search.search"/>
      <f:loadBundle var="msg_label" basename="com.intact.brokeroffice.resources.calendarLabel"/>

      <p:spacer width="10px"/>
      <h:outputText value="#{msg_search['title']}" styleClass="title"/>
      <p:spacer width="20px"/>

      <ui:include src="/pages/quotes/controls.xhtml"/>

      <p:panel>
        <p:panelGrid columns="1" layout="tabular" styleClass="formSearch no-border-top-td">
          <h:panelGroup styleClass="errorGrid">
            <h:outputText value="#{msg_search['form.text']}"/>
            <p:messages styleClass="errorMessage" showDetail="true" globalOnly="true" layout="table"/>
            <h:panelGroup rendered="#{searchController.isSearchFailed('C.RE')}">
              <h:graphicImage url="/image/iconRefused.png" alt=""/>
              <h:outputFormat escape="false" value="#{msg_quo['info.error.C.RE']}" styleClass="errorMessageBold"/>
            </h:panelGroup>

            <h:panelGroup rendered="#{searchController.isSearchFailed('P.RE')}">
              <h:graphicImage url="/image/iconRefused.png" alt=""/>
              <h:outputFormat escape="false" value="#{msg_quo['info.error.P.RE']}" styleClass="errorMessageBold"/>
            </h:panelGroup>

            <h:panelGroup rendered="#{searchController.isSearchFailed('C.AU')}">
              <h:graphicImage url="/image/iconRefused.png" alt=""/>
              <h:outputFormat escape="false" value="#{msg_quo['info.error.C.AU']}" styleClass="errorMessageBold"/>
            </h:panelGroup>

            <h:panelGroup rendered="#{searchController.isSearchFailed('P.AU')}">
              <h:graphicImage url="/image/iconRefused.png" alt=""/>
              <h:outputFormat escape="false" value="#{msg_quo['info.error.P.AU']}" styleClass="errorMessageBold"/>
            </h:panelGroup>
          </h:panelGroup>
        </p:panelGrid>
        <p:panelGrid columns="3" layout="tabular" columnClasses="searchColumn1, searchColumn2, searchColumn3"
                     styleClass="formSearch form" cellspacing="0" cellpadding="0">

          <h:outputText value="#{msg_search['form.masterBroker']}"/>
          <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
          <h:panelGroup styleClass="errorGrid">
            <h:selectOneMenu id="master" value="#{searchController.searchBean.selectedMaster}"
                             rendered="#{not empty searchController.masters and !searchController.onlyOneMaster}"
                             styleClass="input_master">
              <f:selectItem itemLabel="#{msg_search['select.all.owner']}"/>
              <f:selectItems value="#{searchController.masters}"/>
              <p:ajax event="change" listener="#{searchController.onMasterChanged}" update="pointOfSale"/>
            </h:selectOneMenu>

            <h:outputText id="master1" value="#{searchController.searchBean.selectedDefaultMaster}"
                          rendered="#{searchController.onlyOneMaster}"/>

            <p:message styleClass="errorMessage" for="master"/>
          </h:panelGroup>

          <h:outputText value="#{msg_search['form.pointOfSale']}"/>
          <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
          <h:selectOneMenu id="pointOfSale" value="#{searchController.searchBean.selectedPointOfSale}"
                           styleClass="input_pointOfSale">
            <f:selectItem itemLabel="#{msg_search['select.all.pointofsale']}"/>

            <f:selectItems value="#{searchController.subBrokers}"/>
          </h:selectOneMenu>


          <ui:param name="count" value="#{fn:length(permissionController.availableLinesOfBusiness)}"/>
          <c:if test="#{count gt 1}">
            <h:outputText value="#{msg_search['form.lineOfBusiness']}"/>
            <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
            <h:panelGroup styleClass="errorGrid">
              <h:selectManyCheckbox id="lineOfBusiness" value="#{searchController.searchBean.selectedLinesOfBusiness}">
                <f:selectItem itemValue="P" itemLabel="#{msg_search['form.lineOfBusiness.code.P']}"/>
                <f:selectItem itemValue="C" itemLabel="#{msg_search['form.lineOfBusiness.code.C']}"/>
              </h:selectManyCheckbox>
              <p:message id="lineOfInsurance_errors" styleClass="errorMessage" for="lineOfBusiness"/>
            </h:panelGroup>
          </c:if>

          <h:outputText value="#{msg_search['form.productType']}"/>
          <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
          <h:panelGroup styleClass="errorGrid">
            <h:selectManyCheckbox id="lineOfInsurance" value="#{searchController.searchBean.selectedLinesOfInsurance}">
              <f:selectItem itemValue="AU" itemLabel="#{msg_search['form.product.type.AUTO']}"/>
              <f:selectItem itemValue="RE" itemLabel="#{msg_search['form.product.type.HOME']}"/>
            </h:selectManyCheckbox>
            <p:message id="lineOfBusiness_errors" styleClass="errorMessage" for="lineOfInsurance"/>
          </h:panelGroup>

          <h:outputText value="#{msg_search['form.referenceNo']}"/>
          <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
          <h:panelGroup styleClass="errorGrid">
            <h:inputText id="referenceNo" value="#{searchController.searchBean.agreementNumber}"
                         styleClass="input_referenceNo"/>
          </h:panelGroup>

          <h:outputText value="#{msg_search['form.telephone']}"/>
          <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
          <h:panelGroup styleClass="errorGrid">
            <h:inputText id="phoneAreaCode" size="3" maxlength="3" value="#{searchController.searchBean.phoneAreaCode}"
                         onkeyup="input_onchange(this,'quoteSearchForm');" onkeypress="return isNumberKey(event)"
                         styleClass="input_phoneField1"/>
            <p:spacer width="10px"/>
            <h:inputText id="phoneNumberPrefix" size="3" maxlength="3"
                         value="#{searchController.searchBean.phoneNumberPrefix}"
                         onkeyup="input_onchange(this,'quoteSearchForm');" onkeypress="return isNumberKey(event)"
                         styleClass="input_phoneField2"/>
            <p:spacer width="10px"/>
            <h:inputText id="phoneNumberSuffix" size="4" maxlength="4"
                         value="#{searchController.searchBean.phoneNumberSuffix}" onkeypress="return isNumberKey(event)"
                         styleClass="input_phoneField3"/>
            <br/><p:message id="phoneNumber_errors" styleClass="errorMessage" for="phoneNumberSuffix"/>
          </h:panelGroup>

          <c:if test="#{permissionController.IRCACommercialQQAccessProfile}">
            <h:outputText value="#{msg_search['form.businessName']}"/>
            <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
            <h:panelGroup styleClass="errorGrid">
              <h:inputText id="businessName" value="#{searchController.searchBean.businessName}" escape="false"
                           styleClass="input_businessName"/>
              <br/><p:message id="businessName_errors" styleClass="errorMessage" for="businessName"/>
            </h:panelGroup>
          </c:if>


          <!--
            Originally, firstname and lastname only appeared for users that  could see Personal Line quotes.
            Since Webzone do not handle PL quotes anymore, these 2 filters have to always be displayed
            (business requirement)
          -->
          <!-- Lastname -->
          <h:outputText value="#{msg_search['form.lastName']}"/>
          <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
          <h:panelGroup styleClass="errorGrid">
            <h:inputText id="lastName" value="#{searchController.searchBean.lastName}" escape="false"
                         styleClass="input_lastName"/>
            <p:message id="lastName_errors" styleClass="errorMessage" for="lastName"/>
          </h:panelGroup>

          <!-- Firstname -->
          <h:outputText value="#{msg_search['form.firstName']}"/>
          <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
          <h:panelGroup styleClass="errorGrid">
            <h:inputText id="firstName" value="#{searchController.searchBean.firstName}" styleClass="input_firstName"/>
            <p:message id="firstName_errors" styleClass="errorMessage" for="firstName"/>
          </h:panelGroup>

          <h:outputText value="#{msg_search['form.email']}"/>
          <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
          <h:panelGroup styleClass="errorGrid">
            <h:inputText id="email" maxlength="73" value="#{searchController.searchBean.email}"
                         styleClass="input_email"/>
          </h:panelGroup>

          <h:outputText value="#{msg_search['form.postalCode']}"/>
          <h:graphicImage url="/image/question_delimiter_arrow.gif"/>
          <h:panelGroup styleClass="errorGrid">
            <h:inputText id="postalCode" size="6" maxlength="6" value="#{searchController.searchBean.postalCode}"
                         styleClass="input_postalCode"/>
            <p:message id="postalCode_errors" styleClass="errorMessage" for="postalCode"/>
          </h:panelGroup>

          <!--TODO Fix converters/field validation for all four datepickers-->
          <!--Last Update (From) datepicker-->
          <h:outputLabel for="calFrom" value="#{msg_search['form.lastUpdateFrom']}"
                         rendered="#{permissionController.checkSearchAccess}"/>
          <h:graphicImage url="/image/question_delimiter_arrow.gif"
                          rendered="#{permissionController.checkSearchAccess}"/>
          <h:panelGrid styleClass="errorGrid" cellspacing="0" cellpadding="0" rows="2"
                       rendered="#{permissionController.checkSearchAccess}">
            <h:panelGrid styleClass="errorGrid" cellspacing="0" cellpadding="0" columns="3">
              <p:datePicker
                  id="calFrom"
                  value="#{searchController.searchBean.from}"
                  showIcon="true"
                  showOnFocus="false"
                  showButtonBar="true"
                  monthNavigator="true"
                  yearNavigator="true"
                  showOtherMonths="true"
                  selectOtherMonths="true"
                  mask="[9][9][9][9]-[9][9]-[9][9]"
                  keepInvalid="true"
                  converterMessage="#{msg_search['error.date.InvalidFormat']}"
              />
              <p:spacer width="5px"/>
              <h:outputText value="#{msg_search['form.dateFormat']}" styleClass="text-align"/>
            </h:panelGrid>
            <p:message id="lastUpdateFrom_errors" styleClass="errorMessage" for="calFrom"/>
          </h:panelGrid>

          <!--Last Update (To) datepicker-->
          <h:outputLabel for="calTo" value="#{msg_search['form.lastUpdateTo']}"
                         rendered="#{permissionController.checkSearchAccess}"/>
          <h:graphicImage url="/image/question_delimiter_arrow.gif"
                          rendered="#{permissionController.checkSearchAccess}"/>
          <h:panelGrid styleClass="errorGrid" cellspacing="0" cellpadding="0" rows="2"
                       rendered="#{permissionController.checkSearchAccess}">
            <h:panelGrid styleClass="errorGrid" cellspacing="0" cellpadding="0" columns="3">
              <p:datePicker
                  id="calTo"
                  value="#{searchController.searchBean.to}"
                  showIcon="true"
                  showOnFocus="false"
                  showButtonBar="true"
                  monthNavigator="true"
                  yearNavigator="true"
                  showOtherMonths="true"
                  selectOtherMonths="true"
                  mask="[9][9][9][9]-[9][9]-[9][9]"
                  keepInvalid="true"
                  converterMessage="#{msg_search['error.date.InvalidFormat']}"
              />
              <p:spacer width="5px"/>
              <h:outputText value="#{msg_search['form.dateFormat']}" styleClass="text-align"/>
            </h:panelGrid>
            <p:message id="lastUpdateTo_errors" styleClass="errorMessage" for="calTo"/>
          </h:panelGrid>

          <!--Creation Date (From) datepicker-->
          <h:outputLabel for="createFrom" value="#{msg_search['form.creationDateFrom']}"
                         rendered="#{permissionController.checkSearchAccess}"/>
          <h:graphicImage url="/image/question_delimiter_arrow.gif"
                          rendered="#{permissionController.checkSearchAccess}"/>
          <h:panelGrid styleClass="errorGrid" cellspacing="0" cellpadding="0" rows="2"
                       rendered="#{permissionController.checkSearchAccess}">
            <h:panelGrid styleClass="errorGrid" cellspacing="0" cellpadding="0" columns="3">
              <p:datePicker
                  id="createFrom"
                  value="#{searchController.searchBean.creationDateFrom}"
                  showIcon="true"
                  showOnFocus="false"
                  showButtonBar="true"
                  monthNavigator="true"
                  yearNavigator="true"
                  showOtherMonths="true"
                  selectOtherMonths="true"
                  mask="[9][9][9][9]-[9][9]-[9][9]"
                  keepInvalid="true"
                  converterMessage="#{msg_search['error.date.InvalidFormat']}"
              />
              <p:spacer width="5px"/>
              <h:outputText value="#{msg_search['form.dateFormat']}" styleClass="text-align"/>
            </h:panelGrid>
            <p:message id="createFrom_errors" styleClass="errorMessage" for="createFrom"/>
          </h:panelGrid>

          <!--Creation Date (To) datepicker-->
          <h:outputLabel for="createTo" value="#{msg_search['form.creationDateTo']}"
                         rendered="#{permissionController.checkSearchAccess}"/>
          <h:graphicImage url="/image/question_delimiter_arrow.gif"
                          rendered="#{permissionController.checkSearchAccess}"/>
          <h:panelGrid styleClass="errorGrid" cellspacing="0" cellpadding="0" rows="2"
                       rendered="#{permissionController.checkSearchAccess}">
            <h:panelGrid styleClass="errorGrid" cellspacing="0" cellpadding="0" columns="3">
              <p:datePicker
                  id="createTo"
                  value="#{searchController.searchBean.creationDateTo}"
                  showIcon="true"
                  showOnFocus="false"
                  showButtonBar="true"
                  monthNavigator="true"
                  yearNavigator="true"
                  showOtherMonths="true"
                  selectOtherMonths="true"
                  mask="[9][9][9][9]-[9][9]-[9][9]"
                  keepInvalid="true"
                  converterMessage="#{msg_search['error.date.InvalidFormat']}"
              />
              <p:spacer width="5px"/>
              <h:outputText value="#{msg_search['form.dateFormat']}" styleClass="text-align"/>
            </h:panelGrid>
            <p:message id="createTo_errors" styleClass="errorMessage" for="createTo"/>
          </h:panelGrid>


          <h:outputText value="#{msg_search['form.clientFollowUp']}"
                        rendered="#{permissionController.checkSearchAccess}"/>
          <h:graphicImage url="/image/question_delimiter_arrow.gif"
                          rendered="#{permissionController.checkSearchAccess}"/>
          <h:panelGroup styleClass="errorGrid" rendered="#{permissionController.checkSearchAccess}">
            <h:selectOneRadio styleClass="input_clientFollowup search-followup-table" id="clientFollowUps"
                              value="#{searchController.searchBean.selectedClientFollowUp}">
              <f:selectItems value="#{searchController.agreementFollowUpStatus}" var="followUp" itemValue="#{followUp}"
                             itemLabel="#{msg_search[followUp]}"/>
            </h:selectOneRadio>
          </h:panelGroup>


          <h:outputText value="#{msg_search['form.quoteSource']}" rendered="#{permissionController.checkSearchAccess}"/>
          <h:graphicImage url="/image/question_delimiter_arrow.gif"
                          rendered="#{permissionController.checkSearchAccess}"/>
          <h:panelGroup styleClass="errorGrid" rendered="#{permissionController.checkSearchAccess}">
            <p:panelGrid columns="1" styleClass="errorGrid" cellspacing="0" cellpadding="0"
                         rendered="#{permissionController.checkSearchAccess}">
              <h:selectOneRadio id="autoQuoteWebAccessType" value="#{searchController.searchBean.quoteSource}"
                                styleClass="input_webAccessType">
                <f:selectItems value="#{searchController.webAccessTypeList}" var="accessType"
                               itemValue="#{accessType.value}" itemLabel="#{msg_search[accessType.value]}"/>
              </h:selectOneRadio>
            </p:panelGrid>
          </h:panelGroup>


          <h:outputText value="#{msg_search['form.quoteStatus']}" rendered="#{permissionController.checkSearchAccess}"/>
          <h:graphicImage url="/image/question_delimiter_arrow.gif"
                          rendered="#{permissionController.checkSearchAccess}"/>
          <h:selectOneMenu id="quoteStatus" value="#{searchController.searchBean.selectedQuoteStatus}"
                           styleClass="input_quoteStatus" rendered="#{permissionController.checkSearchAccess}">
            <f:selectItem itemLabel="#{msg_search['select.default']}"/>
            <f:selectItems value="#{searchController.quoteStatusItems}" var="quoteStatusItem"
                           itemValue="#{quoteStatusItem}" itemLabel="#{msg_search[quoteStatusItem]}"/>
          </h:selectOneMenu>


          <h:outputText value="#{msg_search['form.quoteFromTerminatedBrokers']}"
                        rendered="#{permissionController.checkSearchAccess}"/>
          <h:graphicImage url="/image/question_delimiter_arrow.gif"
                          rendered="#{permissionController.checkSearchAccess}"/>
          <h:panelGroup styleClass="errorGrid" rendered="#{permissionController.checkSearchAccess}">
            <h:selectBooleanCheckbox id="closedBrokerQuotes" value="#{searchController.searchBean.closedBrokerQuotes}"/>
          </h:panelGroup>


        </p:panelGrid>
      </p:panel>
    </p:panel>

    <p:panel styleClass="marginTop flex-end">
      <!--  jmarois: PM9331: insure that image is always download to avoid buttons disappear temporarily  -->
      <p:panel style="display:none">
        <h:commandLink styleClass="actionBtnRightHover">
          <h:outputText value="invisible right"/>
        </h:commandLink>

        <h:commandLink styleClass="actionBtnLeftHover">
          <h:outputText value="invisible left"/>
        </h:commandLink>
      </p:panel>

      <p:commandLink action="#{searchController.search}" styleClass="link_submitSearch actionBtn floatRight"
                     update="mainTab">
		    	<span class="right">
		    		<h:outputText value="#{msg_search['form.submit']}"/>
		    		<h:graphicImage url="/image/btnRightArrow.png" styleClass="rightButtonArrow" alt=""/>
		    	</span>
      </p:commandLink>
    </p:panel>
  </h:form>

</ui:composition>
