<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui">

	<ui:repeat value="#{consentSection}" var="tab">
		<div class="quote-detail-section">
			<h:outputText value="#{tab.pieces[0].value}" styleClass="blue-top-subsection"/>
			<p:panelGrid id="quoteClientPanel" columns="1" styleClass="formQuote" cellspacing="0" cellpadding="0">
				<p:dataTable value="#{tab.childrens}" var="childPiece" styleClass="subformQuote-twoCol" >
	
					<p:column>
	
						<span id="#{childPiece.value}_icon">
							<h:graphicImage url="/image/iconRefused.png" rendered="#{childPiece.pieces[1].value=='No_Consent'}"/>
							<h:graphicImage url="/image/checkGreen.gif" rendered="#{childPiece.pieces[1].value=='Explicit_Consent'}"/>
							<h:graphicImage url="/image/check.gif" rendered="#{childPiece.pieces[1].value=='Implicit_Consent'}"/>
							<h:graphicImage url="/image/iconRefused.png" rendered="#{childPiece.pieces[1].value=='false'}"/>
							<h:graphicImage url="/image/checkGreen.gif" rendered="#{childPiece.pieces[1].value=='true'}"/>
						</span>
					</p:column>
	
					<p:column>
						<span id="#{childPiece.value}_txt">
							<h:outputText value="#{childPiece.pieces[0].value}" />
						</span>
					</p:column>
	
				</p:dataTable>
			</p:panelGrid>
		</div>
	</ui:repeat>

</ui:composition>
