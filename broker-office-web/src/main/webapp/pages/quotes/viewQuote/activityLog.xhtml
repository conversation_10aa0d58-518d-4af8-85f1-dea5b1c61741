<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
		"http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">

<html xmlns="http://www.w3.org/1999/xhtml"
	  xmlns:h="http://java.sun.com/jsf/html"
	  xmlns:f="http://java.sun.com/jsf/core"
	  xmlns:p="http://primefaces.org/ui">

<p:panelGrid columns="3" styleClass="subformQuote" cellspacing="0" cellpadding="0" >
	<h:outputText value="#{msgClient['client.followup']}" />
	<h:graphicImage url="/image/question_delimiter_arrow.gif" alt=""/>
	<h:selectOneRadio id="followupStatus" value="#{viewQuoteController.clientContactBean.followupStatus}" styleClass="innerGrid" layout="pageDirection">
		<f:selectItems value="#{viewQuoteController.followupStatusList}" />
	</h:selectOneRadio>
</p:panelGrid>

<p:panelGrid rendered="#{permissionController.checkAddFollowupNotes}" columns="1" styleClass="subformQuote" cellspacing="0" cellpadding="0" >
	<p:spacer width="5px" />
</p:panelGrid>

<p:panelGrid rendered="#{permissionController.checkAddFollowupNotes}" columns="3" columnClasses="subformQuoteCol1, subformQuoteCol2, subformQuoteCol3" styleClass="quote-logNote" cellspacing="0" cellpadding="0" >

	<h:outputText value="#{msgClient['client.addNotes']}" />
	<h:graphicImage url="/image/question_delimiter_arrow.gif" alt=""/>
	<h:inputTextarea id="note" value="#{viewQuoteController.clientContactBean.noteText}" rows="6"
					 onkeypress='if (this.value.length == 149 ){ if (event.keyCode == 46 || event.keyCode == 8 || event.keyCode == 37 || event.keyCode == 38 || event.keyCode == 39 || event.keyCode == 40){return true;}else{return false;}}else{return true;}'
					 onchange="this.value = this.value.substr(0, 149);"/>

	<h:message for="note"/>
</p:panelGrid>

<p:panel rendered="#{permissionController.checkAddFollowupNotes}" >
	<!--  PM9331: insure that image is always download to avoid buttons disappear temporarily  -->
	<p:panel style="display:none">
		<h:commandLink styleClass="actionBtnRightHover" >
			<h:outputText value="invisible right" />
		</h:commandLink>

		<h:commandLink styleClass="actionBtnLeftHover">
			<h:outputText value="invisible left" />
		</h:commandLink>
	</p:panel>

	<p:commandLink action="#{viewQuoteController.update(param.referenceNo, param.lineOfBusiness, param.lineOfInsurance, param.applicationMode)}" update="viewQuotePanel, ActivityLogDataTable, note">
		<span class="right" >
			<h:outputText value="#{global['form.button.update']}"/>
    		<h:graphicImage url="/image/btnRightArrow.png" alt=""/>
    		<f:param name="referenceNo" value="#{param.referenceNo}" />
    		<f:param name="lineOfBusiness" value="#{param.lineOfBusiness}" />
    		<f:param name="lineOfInsurance" value="#{param.lineOfInsurance}" />
            <f:param name="applicationMode" value="#{param.applicationMode}" />
    	</span>

	</p:commandLink>

</p:panel>
</html>
