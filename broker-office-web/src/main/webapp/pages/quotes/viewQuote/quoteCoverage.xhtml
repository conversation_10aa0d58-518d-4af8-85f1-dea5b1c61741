<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui">

	<f:loadBundle var="msg_offer" basename="com.intact.brokeroffice.controller.viewquote.offer.offer"/>


	<h:outputText value="#{msg_offer['offer.label.premiums.package.selection']}" styleClass="blue-top-subsection" rendered="#{viewQuoteController.premiumBean.ratingCalculated}"/>
	<h:outputText value="&#160;" styleClass="blue-top-subsection" rendered="#{!viewQuoteController.premiumBean.ratingCalculated}"/>

	<h:panelGrid styleClass="formQuote" columns="1" rendered="#{!viewQuoteController.premiumBean.ratingCalculated}">
		<h:outputText value="#{msg_offer['offer.no.coverage.selected']}" styleClass="sectionInfo"/>
	</h:panelGrid>

	<h:panelGrid styleClass="formQuote" columns="2" columnClasses="formCoverageCol1, formCoverageCol2" rendered="#{viewQuoteController.premiumBean.ratingCalculated}">

		<p:panel>
			<h:panelGrid columns="1" columnClasses="formCoverageCol1">
				<h:outputText value="&#160;"/>
			</h:panelGrid>
		</p:panel>

		<p:panel>
			<p:dataGrid width="540px" value="#{viewQuoteController.vehicleOfferBeanList}" columns="#{viewQuoteController.vehicleOfferSize}" var="vehicle_offer">
				<p:panel styleClass="formCoverageColx">
					<span id="vehicleYMM_label">
						<h:outputText id="vehicleYMN" value="#{vehicle_offer.year} #{vehicle_offer.make} #{vehicle_offer.model}" styleClass="sectionTitle" converter="vehicleNameConverter"/>
					</span>
					<p:tooltip for="vehicleYMN"
							   value="#{vehicle_offer.year} #{vehicle_offer.make} #{vehicle_offer.model}"
							   my="left top"
							   at="right bottom"
							   showDelay="100"
							   styleClass="tooltip"/>
				</p:panel>
			</p:dataGrid>
		</p:panel>

		<p:panel>
			<h:panelGrid columns="1" columnClasses="formCoverageCol1">
				<h:outputText id="offer_P_txt" 	value="#{msg_offer['offer.type.P']}" 	styleClass="sectionInfo" rendered="#{viewQuoteController.hasEconomyOffer}"/>
				<h:outputText id="offer_QQ_P_txt" value="#{msg_offer['offer.type.QQ.P']}" styleClass="sectionInfo" rendered="#{viewQuoteController.hasValuePackOffer}"/>
				<h:outputText id="offer_V_txt" 	value="#{msg_offer['offer.type.V']}" 	styleClass="sectionInfo" rendered="#{viewQuoteController.hasPreferredOffer}"/>
				<h:outputText id="offer_C_txt" 	value="#{msg_offer['offer.type.C']}" 	styleClass="sectionInfo" rendered="#{viewQuoteController.hasPlusOffer}"/>
				<h:outputText id="offer_O_txt" 	value="#{msg_offer['offer.type.O']}" 	styleClass="sectionInfo" rendered="#{viewQuoteController.hasCustomOffer}"/>
				<h:outputText id="offer_Y_txt" 	value="#{msg_offer['offer.type.Y']}" 	styleClass="sectionInfo" rendered="#{viewQuoteController.hasPeopleLikeYouOffer}"/>
				<h:outputText id="offer_R_txt" 	value="#{msg_offer['offer.type.R']}" 	styleClass="sectionInfo" rendered="#{viewQuoteController.hasRecommendedOffer}"/>
			</h:panelGrid>
		</p:panel>

		<p:panel>
			<p:dataGrid value="#{viewQuoteController.vehicleOfferBeanList}" rowKeyVar="rowIndex" columns="#{viewQuoteController.vehicleOfferSize}" var="vehicle_offer">
				<p:panel styleClass="formCoverageColx">
					<h:panelGrid columns="3">
						<h:outputText id="offer_P_amount_na" value="N/A" rendered="#{empty vehicle_offer.economyOffer.annualPremium}" styleClass="sectionInfo coverage-section-width economyAnnualPremium-#{rowIndex}" />
						<h:outputText id="offer_P_amount" value="#{vehicle_offer.economyOffer.annualPremium}" styleClass="sectionInfo coverage-section-width economyAnnualPremium-#{rowIndex}"  rendered="#{not empty vehicle_offer.economyOffer.annualPremium }"><f:convertNumber  maxFractionDigits="0" type="currency" /></h:outputText>
						<h:graphicImage id="offer_P_icon" url="/image/check.gif" styleClass="coverage-align-left economySelected-#{rowIndex}" rendered="#{not empty vehicle_offer.economyOffer.selected and vehicle_offer.economyOffer.selected == true}"/>
						<h:outputText value="&#160;" rendered="#{vehicle_offer.economyOffer.selected == false }"/>
						<h:outputText id="economyCustomerValueIndexBand-#{rowIndex}" value="#{msg_offer[vehicle_offer.economyOffer.customerValueIndexBand]}" rendered="#{vehicle_offer.economyOffer.customerValueIndex != null}"/>
						<h:outputText value="&#160;" rendered="#{empty vehicle_offer.economyOffer.customerValueIndex}"/>

						<h:outputText id="offer_V_amount_na" value="N/A" rendered="#{empty vehicle_offer.preferredOffer.annualPremium and viewQuoteController.hasPreferredOffer}" styleClass="sectionInfo coverage-section-width economyAnnualPremium-#{rowIndex}"/>
						<h:outputText id="offer_V_amount" value="#{vehicle_offer.preferredOffer.annualPremium}" styleClass="sectionInfo coverage-section-width economyAnnualPremium-#{rowIndex}" rendered="#{not empty vehicle_offer.preferredOffer.annualPremium and viewQuoteController.hasPreferredOffer}"><f:convertNumber  maxFractionDigits="0" type="currency" /></h:outputText>
						<h:graphicImage id="offer_V_icon" url="/image/check.gif" styleClass="coverage-align-left preferredSelected-#{rowIndex}" rendered="#{not empty vehicle_offer.preferredOffer.selected and vehicle_offer.preferredOffer.selected == true and viewQuoteController.hasPreferredOffer}"/>
						<h:outputText value="&#160;" rendered="#{vehicle_offer.preferredOffer.selected == false and viewQuoteController.hasPreferredOffer}"/>
						<h:outputText id="preferredCustomerValueIndexBand-#{rowIndex}" value="#{msg_offer[vehicle_offer.preferredOffer.customerValueIndexBand]}" rendered="#{vehicle_offer.preferredOffer.customerValueIndex != null and viewQuoteController.hasPreferredOffer}"/>
						<h:outputText value="&#160;" rendered="#{empty vehicle_offer.preferredOffer.customerValueIndex and viewQuoteController.hasPreferredOffer}"/>

						<h:outputText id="offer_C_amount_na" value="N/A" rendered="#{empty vehicle_offer.plusOffer.annualPremium and viewQuoteController.hasPlusOffer}" styleClass="sectionInfo coverage-section-width plusAnnualPremium-#{rowIndex}"/>
						<h:outputText id="offer_C_amount" value="#{vehicle_offer.plusOffer.annualPremium}" styleClass="sectionInfo plusAnnualPremium-#{rowIndex}" rendered="#{not empty vehicle_offer.plusOffer.annualPremium and viewQuoteController.hasPlusOffer}"><f:convertNumber  maxFractionDigits="0" type="currency" /></h:outputText>
						<h:graphicImage id="offer_C_icon" url="/image/check.gif" styleClass="coverage-align-left plusSelected-#{rowIndex}" rendered="#{not empty vehicle_offer.plusOffer.selected and vehicle_offer.plusOffer.selected == true and viewQuoteController.hasPlusOffer}"/>
						<h:outputText value="&#160;" rendered="#{vehicle_offer.plusOffer.selected == false and viewQuoteController.hasPlusOffer}"/>
						<h:outputText id="plusCustomerValueIndexBand-#{rowIndex}" value="#{msg_offer[vehicle_offer.plusOffer.customerValueIndexBand]}" rendered="#{vehicle_offer.plusOffer.customerValueIndex != null and viewQuoteController.hasPlusOffer}"/>
						<h:outputText value="&#160;" rendered="#{empty vehicle_offer.plusOffer.customerValueIndex and viewQuoteController.hasPlusOffer}"/>

						<h:outputText id="offer_O_amount_na" value="N/A" rendered="#{empty vehicle_offer.customOffer.annualPremium and viewQuoteController.hasCustomOffer}" styleClass="sectionInfo coverage-section-width customAnnualPremium-#{rowIndex}"/>
						<h:outputText id="offer_O_amount" value="#{vehicle_offer.customOffer.annualPremium}" styleClass="sectionInfo coverage-section-width customAnnualPremium-#{rowIndex}" rendered="#{not empty vehicle_offer.customOffer.annualPremium and viewQuoteController.hasCustomOffer}"><f:convertNumber maxFractionDigits="0" type="currency" /></h:outputText>
						<h:graphicImage id="offer_O_icon" url="/image/check.gif" styleClass="coverage-align-left customSelected-#{rowIndex}" rendered="#{not empty vehicle_offer.customOffer.selected and vehicle_offer.customOffer.selected == true and viewQuoteController.hasCustomOffer}"/>
						<h:outputText value="&#160;" rendered="#{vehicle_offer.customOffer.selected == false and viewQuoteController.hasCustomOffer}"/>
						<h:outputText id="customCustomerValueIndexBand-#{rowIndex}" value="#{msg_offer[vehicle_offer.customOffer.customerValueIndexBand]}" rendered="#{vehicle_offer.customOffer.customerValueIndex != null and viewQuoteController.hasCustomOffer}"/>
						<h:outputText value="&#160;" rendered="#{empty vehicle_offer.customOffer.customerValueIndex and viewQuoteController.hasCustomOffer}"/>

						<h:outputText id="offer_Y_amount_na" value="N/A" rendered="#{empty vehicle_offer.peopleLikeYouOffer.annualPremium and viewQuoteController.hasPeopleLikeYouOffer}" styleClass="sectionInfo coverage-section-width economyAnnualPremium-#{rowIndex}"/>
						<h:outputText id="offer_Y_amount" value="#{vehicle_offer.peopleLikeYouOffer.annualPremium}" styleClass="sectionInfo coverage-section-width economyAnnualPremium-#{rowIndex}" rendered="#{not empty vehicle_offer.peopleLikeYouOffer.annualPremium and viewQuoteController.hasPeopleLikeYouOffer}"><f:convertNumber  maxFractionDigits="0" type="currency" /></h:outputText>
						<h:graphicImage id="offer_Y_icon" url="/image/check.gif" styleClass="coverage-align-left preferredSelected-#{rowIndex}" rendered="#{not empty vehicle_offer.peopleLikeYouOffer.selected and vehicle_offer.peopleLikeYouOffer.selected == true and viewQuoteController.hasPeopleLikeYouOffer}"/>
						<h:outputText value="&#160;" rendered="#{vehicle_offer.peopleLikeYouOffer.selected == false and viewQuoteController.hasPeopleLikeYouOffer}"/>
						<h:outputText id="peopleLikeYouCustomerValueIndexBand-#{rowIndex}" value="#{msg_offer[vehicle_offer.peopleLikeYouOffer.customerValueIndexBand]}" rendered="#{vehicle_offer.peopleLikeYouOffer.customerValueIndex != null and viewQuoteController.hasPeopleLikeYouOffer}"/>
						<h:outputText value="&#160;" rendered="#{empty vehicle_offer.peopleLikeYouOffer.customerValueIndex and viewQuoteController.hasPeopleLikeYouOffer}"/>

						<h:outputText id="offer_R_amount_na" value="N/A" rendered="#{empty vehicle_offer.recommendedOffer.annualPremium and viewQuoteController.hasRecommendedOffer}" styleClass="sectionInfo coverage-section-width economyAnnualPremium-#{rowIndex}"/>
						<h:outputText id="offer_R_amount" value="#{vehicle_offer.recommendedOffer.annualPremium}" styleClass="sectionInfo coverage-section-width economyAnnualPremium-#{rowIndex}" rendered="#{not empty vehicle_offer.recommendedOffer.annualPremium and viewQuoteController.hasRecommendedOffer}"><f:convertNumber  maxFractionDigits="0" type="currency" /></h:outputText>
						<h:graphicImage id="offer_R_icon" url="/image/check.gif" styleClass="coverage-align-left preferredSelected-#{rowIndex}" rendered="#{not empty vehicle_offer.recommendedOffer.selected and vehicle_offer.recommendedOffer.selected == true and viewQuoteController.hasRecommendedOffer}"/>
						<h:outputText value="&#160;" rendered="#{vehicle_offer.recommendedOffer.selected == false and viewQuoteController.hasRecommendedOffer}"/>
						<h:outputText id="recommendedCustomerValueIndexBand-#{rowIndex}" value="#{msg_offer[vehicle_offer.recommendedOffer.customerValueIndexBand]}" rendered="#{vehicle_offer.recommendedOffer.customerValueIndex != null and viewQuoteController.hasRecommendedOffer}"/>
						<h:outputText value="&#160;" rendered="#{empty vehicle_offer.recommendedOffer.customerValueIndex and viewQuoteController.hasRecommendedOffer}"/>

					</h:panelGrid>
				</p:panel>
			</p:dataGrid>
		</p:panel>
	</h:panelGrid>


	<h:outputText value="#{msg_offer['offer.label.coverage.levels']}" styleClass="blue-top-subsection"  rendered="#{viewQuoteController.premiumBean.premiumExist == true}"/>
	<h:panelGrid styleClass="formQuote" columns="2" columnClasses="formCoverageCol1, formCoverageCol2" rendered="#{viewQuoteController.premiumBean.premiumExist == true}">
		<p:panel>
			<h:panelGrid columns="1" columnClasses="formCoverageCol1">
				<h:outputText id="cvrg_lvl_liability_txt" 			value="#{msg_offer['offer.coverage.levels.liability']}" styleClass="sectionInfo"/>
				<h:outputText id="cvrg_lvl_collision_txt" 			value="#{msg_offer['offer.coverage.levels.collision']}" styleClass="sectionInfo"/>
				<h:outputText id="cvrg_lvl_comprehensive_txt" 		value="#{msg_offer['offer.coverage.levels.comprehensive']}" styleClass="sectionInfo"/>
				<h:outputText id="cvrg_lvl_accident_benefits_txt" 	value="#{msg_offer['offer.coverage.levels.accident.benefits']}" styleClass="sectionInfo"/>
				<h:outputText id="cvrg_lvl_dcpd_txt" 				value="#{msg_offer['offer.coverage.levels.dcpd']}" rendered="#{provinceController.company6}" styleClass="sectionInfo"/>
				<h:outputText id="cvrg_lvl_family_protection_txt"	value="#{msg_offer['offer.coverage.levels.family.protection']}" rendered="#{provinceController.company3}" styleClass="sectionInfo"/>
			</h:panelGrid>
		</p:panel>

		<p:panel>
			<p:dataGrid value="#{viewQuoteController.vehicleOfferBeanList}" rowKeyVar="rowIndex" columns="#{viewQuoteController.vehicleOfferSize}" var="vehicle_offer">
				<p:panel>
					<h:panelGrid columns="2" styleClass="formCoverageColx">
						<h:outputText id="cvrg_lvl_liability_empty" styleClass="sectionInfo boldFont liability-#{rowIndex}" value="#{msg_offer['offer.not.selected']}" rendered="#{empty vehicle_offer.liability}" />
						<h:outputText id="cvrg_lvl_liability_val" value="#{vehicle_offer.liability.coverageAmount}" rendered="#{not empty vehicle_offer.liability}" styleClass="sectionInfo liability-#{rowIndex}"><f:convertNumber maxFractionDigits="0"  type="currency"  /></h:outputText>
						<h:outputText value="&#160;" />

						<h:outputText id="cvrg_lvl_collision_empty" styleClass="sectionInfo collision-#{rowIndex}" value="#{msg_offer['offer.not.selected.empty']}" rendered="#{empty vehicle_offer.collisionDeductible}" />
						<h:outputText id="cvrg_lvl_collision_val" value="#{vehicle_offer.collisionDeductible.coverageAmount}" rendered="#{not empty vehicle_offer.collisionDeductible and vehicle_offer.collisionDeductible.covered}" styleClass="sectionInfo collision-#{rowIndex}"><f:convertNumber maxFractionDigits="0"  type="currency"  /></h:outputText>
						<h:outputText id="cvrg_lvl_collision_not_covered" value="#{msg_offer['offer.not.covered']}" rendered="#{not empty vehicle_offer.collisionDeductible and !vehicle_offer.collisionDeductible.covered}" styleClass="sectionInfo collision-#{rowIndex}"/>
						<h:outputText value="&#160;" />

						<h:outputText id="cvrg_lvl_comprehensive_empty" styleClass="sectionInfo comprehensive-#{rowIndex}" value="#{msg_offer['offer.not.selected.empty']}" rendered="#{empty vehicle_offer.comprehensiveDeductible}"/>
						<h:outputText id="cvrg_lvl_comprehensive_val" value="#{vehicle_offer.comprehensiveDeductible.coverageAmount}" rendered="#{not empty vehicle_offer.comprehensiveDeductible and vehicle_offer.comprehensiveDeductible.covered}" styleClass="sectionInfo comprehensive-#{rowIndex}"><f:convertNumber  maxFractionDigits="0" type="currency"  /></h:outputText>
						<h:outputText id="cvrg_lvl_comprehensive_not_covered" value="#{msg_offer['offer.not.covered']}" rendered="#{not empty vehicle_offer.comprehensiveDeductible and !vehicle_offer.comprehensiveDeductible.covered}" styleClass="sectionInfo comprehensive-#{rowIndex}"/>
						<h:outputText value="&#160;" />

						<h:outputText id="cvrg_lvl_accident_benefits_empty" styleClass="sectionInfo accidentBenefit-#{rowIndex}" value="#{msg_offer['offer.not.selected.empty']}" rendered="#{empty vehicle_offer.accidentBenefit}"/>
						<h:graphicImage id="cvrg_lvl_accident_benefits_icon" url="/image/check.gif" rendered="#{not empty vehicle_offer.accidentBenefit}" styleClass="sectionInfo accidentBenefit-#{rowIndex}"/>
						<h:outputText value="&#160;" />

						<h:outputText id="cvrg_lvl_dcpd_empty" styleClass="sectionInfo directCompensation-#{rowIndex}" value="#{msg_offer['offer.not.selected.empty']}" rendered="#{provinceController.company6 and empty vehicle_offer.directCompensationPropertyDamage}"/>
						<h:outputText id="cvrg_lvl_dcpd_val" value="#{vehicle_offer.directCompensationPropertyDamage.coverageAmount}" rendered="#{provinceController.company6 and not empty vehicle_offer.directCompensationPropertyDamage and vehicle_offer.directCompensationPropertyDamage.covered}" styleClass="sectionInfo directCompensation-#{rowIndex}"><f:convertNumber maxFractionDigits="0"  type="currency"  /></h:outputText>
						<h:outputText id="cvrg_lvl_dcpd_not_covered" value="#{msg_offer['offer.not.covered']}" rendered="#{provinceController.company6 and not empty vehicle_offer.directCompensationPropertyDamage and !vehicle_offer.directCompensationPropertyDamage.covered }" styleClass="sectionInfo directCompensation-#{rowIndex}"/>
						<h:outputText value="&#160;" rendered="#{provinceController.company6}"/>

						<h:graphicImage id="cvrg_lvl_family_protection_icon" url="/image/check.gif" rendered="#{provinceController.company3 and vehicle_offer.offerSelected}" styleClass="sectionInfo offerSelected-#{rowIndex}"/>
						<h:outputText value="&#160;" rendered="#{provinceController.company3}" />

					</h:panelGrid>
				</p:panel>
			</p:dataGrid>
		</p:panel>
	</h:panelGrid>


	<h:outputText value="#{msg_offer['offer.label.endorsements']}" styleClass="blue-top-subsection" rendered="#{viewQuoteController.premiumBean.premiumExist == true}"/>
	<h:panelGrid styleClass="formQuote" columns="2" columnClasses="formCoverageCol1, formCoverageCol2" rendered="#{viewQuoteController.premiumBean.premiumExist == true}">
		<p:panel>
			<h:panelGrid columns="1" columnClasses="formCoverageCol1" rendered="#{not empty viewQuoteController.endorsementSortedItems}">
				<p:dataGrid styleClass="mendorsm" value="#{viewQuoteController.endorsementSortedItems}" columns="1" var="endorsementItem">
            		<span id="endorsement_txt_#{endorsementItem.coverageCode}">
            			<h:outputText  value="#{msg_offer[endorsementItem.endorsementDescriptionKey]}" styleClass="sectionInfo endorsementItemKey"/>
        			</span>
					<h:outputText value="#{msg_offer[endorsementItem.endorsementRegisteredCode]}" styleClass="coverageReserved endorsementItemCode" escape="false"/>
					<br/>
				</p:dataGrid>
			</h:panelGrid>
			<h:panelGrid columns="1" columnClasses="formCoverageCol1" rendered="#{empty viewQuoteController.endorsementSortedItems}">
				<h:outputText value="#{msg_offer['offer.no.endorsements.found']}" styleClass="sectionInfo endorsementItemCode-0"/>
			</h:panelGrid>
		</p:panel>

		<p:panel>
			<p:dataGrid value="#{viewQuoteController.vehicleOfferBeanList}" rowKeyVar="rowIndex" columns="#{viewQuoteController.vehicleOfferSize}" var="vehicle_offer">
				<p:panel>
					<h:panelGrid columns="1" styleClass="formCoverageColx">
						<p:dataGrid value="#{vehicle_offer.endorsementsSorted}" columns="1" var="endorsement" rowKeyVar="rowEndorsement" >
            				<span id="endorsement_val_#{endorsement.coverageCode}">
	               				<h:outputText value="#{msg_offer[endorsement.endorsementStatus]} #{msg_offer[endorsement.plusPacOption]}" styleClass="sectionInfo endorsement-#{rowIndex}-#{rowEndorsement}" rendered="#{vehicle_offer.offerSelected}"/>
	             				<h:outputText value="#{msg_offer[endorsement.endorsementStatus]} #{msg_offer[endorsement.plusPacOption]}" styleClass="sectionInfo boldFont endorsement-#{rowIndex}-#{rowEndorsement}" rendered="#{!vehicle_offer.offerSelected and rowEndorsement == 0}"/>
	             				<h:outputText value="&#160;" styleClass="sectionInfo boldFont" rendered="#{!vehicle_offer.offerSelected and rowEndorsement != 0}"/>
             				</span>
						</p:dataGrid>
					</h:panelGrid>
				</p:panel>
			</p:dataGrid>
		</p:panel>
	</h:panelGrid>

</ui:composition>