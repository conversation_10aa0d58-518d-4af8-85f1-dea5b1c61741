<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui">

	<f:loadBundle var="msg_client" basename="com.intact.brokeroffice.controller.viewquote.client"/>

	<ui:repeat value="#{viewQuoteController.getGeneralInfoPiece(param.referenceNo).getChildren('activity_log')}" var="tab">
		<div class="quote-detail-section">
			<h:outputText value="#{tab.pieces[0].value}" styleClass="blue-top-subsection"/>
			<p:panelGrid columns="1" styleClass="formQuote third-table" cellspacing="0" cellpadding="0">

				<p:dataTable id="ActivityLogDataTable"  rowKey="rowIndex" value="#{tab.childrens}" var="act" >

					<p:column >
						<f:facet name="header">
						</f:facet>
					</p:column>

					<p:column>
						<f:facet name="header">
							<h:outputText value="#{msg_client['client.activityLog.date']}"/>
						</f:facet>
						<h:outputText value="#{act.pieces[1].value}"/>
					</p:column>

					<p:column>
						<f:facet name="header">
							<h:outputText value="#{msg_client['client.activityLog.account']}"/>
						</f:facet>
						<h:outputText value="#{act.pieces[3].value}"/>
					</p:column>

					<p:column>
						<f:facet name="header">
							<h:outputText value="#{msg_client['client.activityLog.activity']}"/>
						</f:facet>
						<h:outputText value="#{act.pieces[5].value}"/>
					</p:column>

					<p:column>
						<f:facet name="header">
							<h:outputText value="#{msg_client['client.activityLog.notes']}"/>
						</f:facet>
						<h:outputText value="#{act.pieces[7].value}"/>
					</p:column>
				</p:dataTable>

				<p:panel rendered="#{permissionController.checkAddFollowupNotes}" styleClass="topLine"></p:panel>

				<!-- Add a note to the quote -->
				<p:panelGrid rendered="#{permissionController.checkAddFollowupNotes}"
                             columns="3"
							 layout="tabular"
                             columnClasses="subformQuoteCol1, subformQuoteCol2, subformQuoteCol3"
                             styleClass="quote-logNote"
                             cellspacing="0"
                             cellpadding="0" >

					<h:outputText value="#{msg_client['client.addNotes']}" />
					<h:graphicImage url="/image/question_delimiter_arrow.gif"/>
                    <p:panel>
                        <p:inputTextarea id="note"
                                         value="#{viewQuoteController.clientContactBean.noteText}"
                                         rows="6"
                                         maxlength="150"
                                         counter="display"
                                         counterTemplate="{1}/{2}"
                                         autoResize="false"/>
						<div class="remaining-chars-counter">
							<h:outputText id="display"/>
						</div>
                        <h:message for="note"/>
                    </p:panel>
				</p:panelGrid>

				<p:panelGrid columns="3" layout="tabular" styleClass="quote-logNote">
					<p:panel rendered="#{permissionController.checkAddFollowupNotes}" layout="tabular">
						<!--  PM9331: insure that image is always download to avoid buttons disappear temporarily  -->
						<p:panel style="display:none">
							<h:commandLink styleClass="actionBtnRightHover" >
								<h:outputText value="invisible right" />
							</h:commandLink>

							<h:commandLink styleClass="actionBtnLeftHover">
								<h:outputText value="invisible left" />
							</h:commandLink>
						</p:panel>

						<p:commandLink action="#{viewQuoteController.update(param.referenceNo, param.lineOfBusiness, param.lineOfInsurance, param.applicationMode)}" styleClass="actionBtn updateBtn" update="viewQuotePanel">
							<span class="right" >
								<h:outputText value="#{global['form.button.update']}"/>
								<h:graphicImage url="/image/btnRightArrow.png" alt=""/>
							</span>
							<f:param name="referenceNo" value="#{viewQuoteController.quote.id}" />
							<f:param name="lineOfBusiness" value="#{viewQuoteController.quote.lineOfBusiness.code}" />
							<f:param name="lineOfInsurance" value="#{viewQuoteController.quote.lineOfInsurance.code}" />
							<f:param name="applicationMode" value="#{viewQuoteController.quote.applicationMode}" />
							<f:param name="searchIndex" value="#{param.searchIndex}" />
						</p:commandLink>
					</p:panel>
				</p:panelGrid>
			</p:panelGrid>
		</div>
	</ui:repeat>

</ui:composition>
