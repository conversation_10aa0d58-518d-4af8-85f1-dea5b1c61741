<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui">

	<f:loadBundle var="global" basename="com.intact.brokeroffice.resources.global"/>
	<f:loadBundle var="msg_driver" basename="com.intact.brokeroffice.controller.viewquote.driver.driver"/>

	<ui:repeat value="#{vehiclePiece.childrens}" var="infoPiece">
		<div class="quote-detail-section">
			<h:outputText value="#{infoPiece.pieces[0].value}" styleClass="blue-top-subsection"/>
			<p:panelGrid id="quoteClientPanel" columns="1" styleClass="formQuote" cellspacing="0" cellpadding="0">
				<p:dataTable value="#{infoPiece.childrens}" var="childPiece" styleClass="subformQuote" >
					<p:column><span id="#{childPiece.value}_question"><h:outputText value="#{childPiece.pieces[0].value}" escape="false"/></span></p:column>
					<p:column><h:graphicImage url="/image/question_delimiter_arrow.gif"/></p:column>
					<p:column rendered="#{empty childPiece.pieces[1].childrens}"><span id="#{childPiece.value}_response"><h:outputText value="#{childPiece.pieces[1].value}" escape="false" /></span></p:column>
					<p:column rendered="#{not empty childPiece.pieces[1].childrens}">
					<span id="#{childPiece.value}_response"><ui:repeat value="#{childPiece.pieces[1].childrens}" var="childChildPiece">
						<h:panelGroup rendered="#{not empty childChildPiece.value}">
							<h:outputText value="#{childChildPiece.value}" escape="false" /><br/>
						</h:panelGroup>
					</ui:repeat>
					</span>
					</p:column>
				</p:dataTable>
			</p:panelGrid>
		</div>
	</ui:repeat>

</ui:composition>
