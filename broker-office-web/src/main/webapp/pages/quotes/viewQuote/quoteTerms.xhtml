<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui">

	<f:loadBundle var="msg_viewquote" basename="com.intact.brokeroffice.controller.viewquote.viewquote"/>
	<div class="quote-detail-section">
		<h:outputText value="&#160;" styleClass="blue-top-subsection"/>
		<p:panelGrid columns="1" styleClass="formQuote" cellspacing="0" cellpadding="0">
	
			<p:panelGrid columns="2" styleClass="subformQuote" columnClasses="subformQuoteTermsCol1" cellspacing="0" cellpadding="0" >
				<h:graphicImage id="checkGreenIconAntiSpam" styleClass="antiSpam" url="/image/checkGreen.gif" rendered="#{viewQuoteController.antispamConsent and !provinceController.company6}"/>
				<h:graphicImage id="checkRedIconAntiSpam" styleClass="antiSpam" url="/image/check.gif" rendered="#{viewQuoteController.antispamConsent and provinceController.company6}"/>
	
				<h:graphicImage id="refusedIconAntiSpam" styleClass="antiSpam" url="/image/iconRefused.png" rendered="#{!viewQuoteController.antispamConsent}" alt=""/>
				<h:outputText id="txtAntiSpam" escape="false" value="#{msg_viewquote['terms.consent.antispam']}"  />
			</p:panelGrid>
	
			<p:panelGrid columns="2" styleClass="subformQuote subformQuoteNoTopBorder" columnClasses="subformQuoteTermsCol1" cellspacing="0" cellpadding="0">
				<h:graphicImage id="checkGreenIconProfileConsent" styleClass="profileConsent" url="/image/checkGreen.gif" rendered="#{viewQuoteController.createProfileConsentInd and !provinceController.company6}"/>
				<h:graphicImage id="checkRedIconProfileConsent" styleClass="profileConsent" url="/image/check.gif" rendered="#{viewQuoteController.createProfileConsentInd and provinceController.company6}"/>
				<h:graphicImage id="refusedIconProfileConsent" styleClass="profileConsent" url="/image/iconRefused.png" rendered="#{!viewQuoteController.createProfileConsentInd}" alt=""/>
				<h:outputText id="txtProfileConsent" escape="false" value="#{msg_viewquote['terms.personal.info.disclosure']}" />
			</p:panelGrid>
	
			<p:panelGrid columns="2" styleClass="subformQuote" columnClasses="subformQuoteTermsCol1" cellspacing="0" cellpadding="0" rendered="#{provinceController.companyA}">
				<h:graphicImage id="checkGreenIconCreditConsent" styleClass="creditConsent" url="/image/checkGreen.gif" rendered="#{viewQuoteController.creditConsentInd}"/>
				<h:graphicImage id="refusedIconCreditConsent" styleClass="creditConsent" url="/image/iconRefused.png" rendered="#{!viewQuoteController.creditConsentInd}" alt=""/>
				<h:outputText id="txtCreditConsent" escape="false" value="#{msg_viewquote['terms.credit']}" />
			</p:panelGrid>
	
			<p:panelGrid columns="2" styleClass="subformQuote" columnClasses="subformQuoteTermsCol1" cellspacing="0" cellpadding="0" rendered="#{viewQuoteController.showUbiConsent}">
				<h:graphicImage id="checkGreenIconUBIConsent" styleClass="ubiConsent" url="/image/checkGreen.gif" rendered="#{viewQuoteController.ubiConsentInd}"/>
				<h:graphicImage id="refusedIconUBIConsent" styleClass="ubiConsent" url="/image/iconRefused.png" rendered="#{!viewQuoteController.ubiConsentInd}" alt=""/>
				<h:outputText id="txtUBIConsent" escape="false" value="#{msg_viewquote['terms.ubi']}" />
			</p:panelGrid>
	
			<p:panelGrid columns="2" styleClass="subformQuote" columnClasses="subformQuoteTermsCol1" cellspacing="0" cellpadding="0" rendered="#{!provinceController.companyA}">
				<h:graphicImage id="checkGreenIconRROConsent" styleClass="riskReportOrderingConsent" url="/image/checkGreen.gif" rendered="#{viewQuoteController.riskReportOrderingConsentInd and !provinceController.company6}"/>
				<h:graphicImage id="checkRedIconRROConsent" styleClass="riskReportOrderingConsent" url="/image/check.gif" rendered="#{viewQuoteController.riskReportOrderingConsentInd and provinceController.company6}"/>
				<h:graphicImage id="refusedIconRROConsent" styleClass="riskReportOrderingConsent" url="/image/iconRefused.png" rendered="#{!viewQuoteController.riskReportOrderingConsentInd}" alt=""/>
				<h:outputText id="txtRROConsent" escape="false" value="#{msg_viewquote['terms.risk.report.ordering']}" />
			</p:panelGrid>
	
			<p:spacer height="20px" />
		</p:panelGrid>
	</div>
</ui:composition>
