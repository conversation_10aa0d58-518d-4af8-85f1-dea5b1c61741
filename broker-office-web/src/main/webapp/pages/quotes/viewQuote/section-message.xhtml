<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui">

	<ui:repeat value="#{viewQuoteController.getGeneralInfoPiece(param.referenceNo).getChildren(section)}" var="tab">
		<div class="quote-detail-section">
			<h:outputText value="#{tab.pieces[0].value}" styleClass="blue-top-subsection"/>
			<p:panelGrid columns="1" styleClass="formQuote" cellspacing="0" cellpadding="0">
				<p:dataTable value="#{tab.childrens}" var="childPiece"  styleClass="subformQuote" >
	
					<p:column>
						<span id="#{childPiece.value}_question"><h:outputText value="#{childPiece.pieces[0].value}" /></span>
					</p:column>
	
				</p:dataTable>
			</p:panelGrid>
		</div>
	</ui:repeat>

</ui:composition>
