<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:p="http://primefaces.org/ui">

	<p:loadBundle var="msg_vehicle" basename="com.intact.brokeroffice.controller.viewquote.vehicle.vehicle"/>
	<p:loadBundle var="msg_usage" basename="com.intact.brokeroffice.controller.viewquote.vehicle.usage"/>
	<div class="quote-detail-section">
		<h:outputText value="#{msg_vehicle['vehicle.title.generalInfo']}" styleClass="blue-top-subsection"/>
			<p:panelGrid columns="1" styleClass="formQuote" cellspacing="0" cellpadding="0">
			<p:panelGrid columns="3" styleClass="subformQuote" cellspacing="0" cellpadding="0" >
	
				<h:outputText value="#{msg_vehicle['vehicle.year']}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif"/>
				<h:outputText  styleClass="vehicleYear-#{rowIndex}" value="#{vehicleBean.year}"/>
	
				<h:outputText value="#{msg_vehicle['vehicle.make']}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif"/>
				<h:outputText  styleClass="vehicleMake-#{rowIndex}" value="#{vehicleBean.make}"/>
	
				<h:outputText value="#{msg_vehicle['vehicle.model']}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif"/>
				<h:outputText  styleClass="vehicleModel-#{rowIndex}" value="#{vehicleBean.model}"/>
	
				<h:outputText value="#{msg_vehicle['vehicle.id.number']}" rendered="#{not empty vehicleBean.identificationNumber}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.identificationNumber}"/>
				<h:outputText  styleClass="vehicleIdentificationNumber-#{rowIndex}" value="#{vehicleBean.identificationNumber}" rendered="#{not empty vehicleBean.identificationNumber}"/>
	
				<h:outputText value="#{msg_vehicle['vehicle.conditionWhenBought']}" rendered="#{not empty vehicleBean.conditionWhenBought}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.conditionWhenBought}"/>
				<h:outputText  styleClass="vehicleConditionWhenBought-#{rowIndex}" value="#{msg_vehicle[vehicleBean.conditionWhenBoughtKey]}" rendered="#{not empty vehicleBean.conditionWhenBought}" />
	
				<h:outputText value="#{msg_vehicle['vehicle.monthYearWhenBought']}" rendered="#{not empty vehicleBean.aquisitionYear }"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.aquisitionYear }"/>
				<h:outputText  styleClass="vehicleAquisitionDate-#{rowIndex}" value="#{msg_vehicle[vehicleBean.aquisitionMonthKey]} #{vehicleBean.aquisitionYear}" rendered="#{not empty vehicleBean.aquisitionYear }" />
	
				<h:outputText value="#{msg_vehicle['vehicle.odometerReading']}" rendered="#{vehicleBean.odometerReading != '' }"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{vehicleBean.odometerReading != '' }"/>
				<h:outputText  styleClass="vehicleMonthYearWhenBought-#{rowIndex}" value="#{vehicleBean.odometerReading}" rendered="#{vehicleBean.odometerReading != '' }"/>
	
				<h:outputText value="#{msg_vehicle['vehicle.leased']}" rendered="#{not empty vehicleBean.leasedAsString}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.leasedAsString}"/>
				<h:outputText  styleClass="vehicleLeased-#{rowIndex}" value="#{msg_vehicle['vehicle.'.concat(vehicleBean.leasedAsString)]}" rendered="#{not empty vehicleBean.leasedAsString}" />
	
				<h:outputText value="#{msg_vehicle['vehicle.financed']}" rendered="#{not empty vehicleBean.financed}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.financed}"/>
				<h:outputText  styleClass="vehicleFinanced-#{rowIndex}" value="#{msg_vehicle[vehicleBean.financed]}" rendered="#{not empty vehicleBean.financed}" />
	
				<h:outputText value="#{msg_vehicle['vehicle.leasing.or.financing.company']}" rendered="#{!vehicleBean.financingCompanyOther and (vehicleBean.financed or vehicleBean.leased )}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{!vehicleBean.financingCompanyOther and (vehicleBean.financed or vehicleBean.leased )}"/>
				<h:outputText  styleClass="vehicleFinancingCompany-#{rowIndex}" value="#{vehicleBean.financingCompanyDescFr}" rendered="#{!vehicleBean.financingCompanyOther and (vehicleBean.financed or vehicleBean.leased ) and languageController.localeQuote.language == 'fr'}"/>
				<h:outputText  styleClass="vehicleFinancingCompany-#{rowIndex}" value="#{vehicleBean.financingCompanyDescEn}" rendered="#{!vehicleBean.financingCompanyOther and (vehicleBean.financed or vehicleBean.leased ) and languageController.localeQuote.language == 'en'}"/>
	
				<h:outputText value="#{msg_vehicle['vehicle.leasing.or.financing.company.other']}" rendered="#{not empty vehicleBean.nameOfLienholderLessor}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.nameOfLienholderLessor}"/>
				<h:outputText  styleClass="vehicleNameOfLienholderLessor-#{rowIndex}" value="#{vehicleBean.nameOfLienholderLessor}" rendered="#{not empty vehicleBean.nameOfLienholderLessor}"/>
	
				<h:outputText value="#{msg_vehicle['vehicle.address']}" rendered="#{not empty vehicleBean.nameOfLienholderLessor}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.nameOfLienholderLessor}"/>
				<h:outputText  styleClass="vehicleAddress1-#{rowIndex}" value="#{vehicleBean.civicNumber}  #{msg_vehicle[vehicleBean.arterialRoadKey]}  #{vehicleBean.addressPOBox}"  rendered="#{not empty vehicleBean.nameOfLienholderLessor}"/>
	
				<h:outputText value="&#160;" rendered="#{not empty vehicleBean.nameOfLienholderLessor}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.nameOfLienholderLessor}"/>
				<h:outputText styleClass="vehicleAddress2-#{rowIndex}" value="#{vehicleBean.cityMunicipality}, #{vehicleBean.province}  #{vehicleBean.postalCode}" rendered="#{not empty vehicleBean.cityMunicipality or not empty vehicleBean.province or not empty vehicleBean.postalCode}"/>
	
				<h:outputText value="#{msg_vehicle['vehicle.trackingSystem.01']}" rendered="#{not empty vehicleBean.trackingSystem}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.trackingSystem}"/>
				<h:outputText  styleClass="vehicleTrackingSystem-#{rowIndex}" value="#{msg_vehicle[vehicleBean.trackingSystem]}" rendered="#{not empty vehicleBean.trackingSystem }" />
	
				<h:outputText value="#{msg_vehicle['vehicle.trackingSystemCode']}" rendered="#{not empty vehicleBean.trackingSystemCode}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.trackingSystemCode}"/>
				<h:outputText  styleClass="vehicleTrackingSystemDesc-#{rowIndex}" value="#{vehicleBean.trackingSystemDescFr}" rendered="#{not empty vehicleBean.trackingSystemCode and languageController.localeQuote.language == 'fr'}"/>
				<h:outputText  styleClass="vehicleTrackingSystemDesc-#{rowIndex}" value="#{vehicleBean.trackingSystemDescEn}" rendered="#{not empty vehicleBean.trackingSystemCode and languageController.localeQuote.language == 'en'}"/>
	
	
				<h:outputText value="#{msg_vehicle['vehicle.intenseEngraving.01']}" rendered="#{not empty vehicleBean.intenseEngraving}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.intenseEngraving}"/>
				<h:outputText styleClass="vehicleIntenseEngraving-#{rowIndex}" value="#{msg_vehicle[vehicleBean.intenseEngraving]}" rendered="#{not empty vehicleBean.intenseEngraving }" />
	
				<h:outputText value="#{msg_vehicle['vehicle.intenseEngravingCode']}" rendered="#{not empty vehicleBean.intenseEngravingCode and provinceController.companyA}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.intenseEngravingCode  and provinceController.companyA}"/>
				<h:outputText  styleClass="vehicleIntenseEngravingDesc-#{rowIndex}" value="#{vehicleBean.intenseEngravingDescFr}" rendered="#{not empty vehicleBean.intenseEngravingCode and languageController.localeQuote.language == 'fr' and provinceController.companyA}"/>
				<h:outputText  styleClass="vehicleIntenseEngravingDesc-#{rowIndex}" value="#{vehicleBean.intenseEngravingDescEn}" rendered="#{not empty vehicleBean.intenseEngravingCode and languageController.localeQuote.language == 'en' and provinceController.companyA}"/>
	
	
				<h:outputText value="#{msg_vehicle['vehicle.modified']}" rendered="#{not empty vehicleBean.modifiedAsString}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.modifiedAsString}"/>
				<h:outputText  styleClass="vehicleModified-#{rowIndex}" value="#{msg_vehicle['vehicle.'.concat(vehicleBean.modifiedAsString)]}" rendered="#{not empty vehicleBean.modifiedAsString}" />
	
				<h:outputText value="#{msg_vehicle['vehicle.modifiedForPerformance']}" rendered="#{not empty vehicleBean.modifiedForPerformance}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.modifiedForPerformance}"/>
				<h:outputText  styleClass="vehicleModifiedForPerformance-#{rowIndex}" value="#{msg_vehicle[vehicleBean.modifiedForPerformance]}" rendered="#{not empty vehicleBean.modifiedForPerformance }"/>
	
				<h:outputText value="#{msg_vehicle['vehicle.modificationWorth']}" rendered="#{not empty vehicleBean.modificationWorth}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.modificationWorth}"/>
				<h:outputText  styleClass="vehicleModificationWorth-#{rowIndex}" value="#{msg_vehicle[vehicleBean.modificationWorthKey]}" rendered="#{not empty vehicleBean.modificationWorth}"/>
	
				<h:outputText value="#{msg_vehicle['vehicle.multi.discount']}" rendered="#{not empty vehicleBean.multiVehicleDiscount}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.multiVehicleDiscount}"/>
				<h:outputText  styleClass="vehicleMulti-#{rowIndex}" value="#{msg_vehicle[vehicleBean.multiVehicleDiscount]}" rendered="#{not empty vehicleBean.multiVehicleDiscount}"/>
	
			</p:panelGrid>
		</p:panelGrid>
	</div>

	<h:outputText value="#{msg_vehicle['vehicle.title.use']}" styleClass="blue-top-subsection"/>
	<div>
		<p:panelGrid columns="1" styleClass="formQuote" cellspacing="0" cellpadding="0">
			<p:panelGrid columns="3" styleClass="subformQuote" cellspacing="0" cellpadding="0" >
	
				<h:outputText value="#{msg_usage['usage.registeredOwner']}" rendered="#{not empty vehicleBean.ownerName}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.ownerName}"/>
				<h:outputText  styleClass="vehicleOwnerName-#{rowIndex}" value="#{vehicleBean.ownerName}" rendered="#{not empty vehicleBean.ownerName}"/>
	
				<h:outputText value="#{msg_usage['usage.is.there.secondOwner']}" rendered="#{not empty vehicleBean.hasAnotherOwner}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.hasAnotherOwner}"/>
				<h:outputText styleClass="vehicleHasAnotherUser-#{rowIndex}" value="#{msg_vehicle[vehicleBean.hasAnotherOwner]}" rendered="#{not empty vehicleBean.hasAnotherOwner}" />
	
				<h:outputText value="#{msg_usage['usage.who.is.secondOwner']}" rendered="#{not empty vehicleBean.secondRegisteredOwnerName}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.secondRegisteredOwnerName}"/>
				<h:outputText  styleClass="vehicleSecondRegisteredOwnerName-#{rowIndex}" value="#{vehicleBean.secondRegisteredOwnerName}" rendered="#{not empty vehicleBean.secondRegisteredOwnerName and !vehicleBean.secondRegisteredOwnerInd}"/>
				<h:outputText  styleClass="vehicleSecondRegisteredOwnerName-#{rowIndex}" value="#{msg_vehicle['other']}" rendered="#{not empty vehicleBean.secondRegisteredOwnerName and vehicleBean.secondRegisteredOwnerInd}"/>
	
				<h:outputText value="#{msg_usage['usage.principalDriver']}" rendered="#{not empty vehicleBean.principalDriverName}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.principalDriverName}"/>
				<h:outputText styleClass="vehiclePrincipalDriverName-#{rowIndex}" value="#{vehicleBean.principalDriverName}" rendered="#{not empty vehicleBean.principalDriverName}"/>
	
				<h:outputText value="#{msg_usage['usage.already.been.principal.driver.indicator']}" rendered="#{not empty vehicleBean.alreadyBeenPrincipalDriverIndicator}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.alreadyBeenPrincipalDriverIndicator}"/>
				<h:outputText  styleClass="vehicleAlreadyBeenPrincipalDriverIndicator-#{rowIndex}" value="#{msg_vehicle[vehicleBean.alreadyBeenPrincipalDriverIndicator]}" rendered="#{not empty vehicleBean.alreadyBeenPrincipalDriverIndicator}"/>
	
				<h:outputText value="#{msg_usage['usage.insured.period']}" rendered="#{not empty vehicleBean.principalInsuredSinceCode}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.principalInsuredSinceCode}"/>
				<h:outputText styleClass="vehiclePrincipalInsuredSince-#{rowIndex}" value="#{msg_usage[vehicleBean.principalInsuredSinceKey]}" rendered="#{not empty vehicleBean.principalInsuredSinceCode}"/>
	
				<h:outputText value="#{msg_usage['usage.primary.of.vehicle']}" rendered="#{not empty vehicleBean.useOfVehicleCategory}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.useOfVehicleCategory}" alt=""/>
				<h:outputText styleClass="vehicleUseOfVehicleCategory-#{rowIndex}" value="#{msg_usage[vehicleBean.useOfVehicleCategoryKey]}" rendered="#{not empty vehicleBean.useOfVehicleCategory}" />
	
				<h:outputText value="#{msg_usage['usage.annual.km.driven']}" rendered="#{not empty vehicleBean.annualKilometers}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.annualKilometers}"/>
				<h:outputText styleClass="vehicleUseOfVehicleCategory-#{rowIndex}" value="#{vehicleBean.annualKilometers}" rendered="#{not empty vehicleBean.annualKilometers}"/>
	
				<h:outputText value="#{msg_usage['usage.drive.to.work']}" rendered="#{not empty vehicleBean.driveToWork}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.driveToWork}"/>
				<h:outputText styleClass="vehicleDriveToWork-#{rowIndex}" value="#{msg_usage[vehicleBean.driveToWork]}" rendered="#{not empty vehicleBean.driveToWork}"/>
	
				<h:outputText value="#{msg_usage['usage.how.far.from.home.to.work']}" rendered="#{not empty vehicleBean.numberOfKilometersToDriveToWork and vehicleBean.numberOfKilometersToDriveToWork gt 0}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.numberOfKilometersToDriveToWork and vehicleBean.numberOfKilometersToDriveToWork gt 0}"/>
				<h:outputText styleClass="vehicleNumberOfKilometersToDriveToWork-#{rowIndex}" value="#{vehicleBean.numberOfKilometersToDriveToWork}" rendered="#{not empty vehicleBean.numberOfKilometersToDriveToWork and vehicleBean.numberOfKilometersToDriveToWork gt 0}"/>
	
				<h:outputText value="#{msg_usage['usage.businessAnnualKm']}" rendered="#{not empty vehicleBean.annualBusinessKilometers}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.annualBusinessKilometers}"/>
				<h:outputText styleClass="vehicleAnnualBusinessKilometers-#{rowIndex}" value="#{vehicleBean.annualBusinessKilometers}" rendered="#{not empty vehicleBean.annualBusinessKilometers}"/>
	
				<h:outputText value="#{msg_usage['usage.more.than.49days.in.usa']}" rendered="#{not empty vehicleBean.usedOutsideProvinceOrCountryIndicatorAsString}"/>
				<h:graphicImage url="/image/question_delimiter_arrow.gif" rendered="#{not empty vehicleBean.usedOutsideProvinceOrCountryIndicatorAsString}"/>
				<h:outputText styleClass="vehicleUsedOutsideProvinceOrCountryIndicator-#{rowIndex}" value="#{msg_vehicle['vehicle.'.concat(vehicleBean.usedOutsideProvinceOrCountryIndicatorAsString)]}" rendered="#{not empty vehicleBean.usedOutsideProvinceOrCountryIndicatorAsString}"/>
	
			</p:panelGrid>
	
		</p:panelGrid>
	</div>

</ui:composition>
