<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui">

	<f:loadBundle var="msg_payment" basename="com.intact.brokeroffice.controller.viewquote.payment.payment"/>
	<div class="quote-detail-section">
		<h:outputText value="&#160;" styleClass="blue-top-subsection"/>

		<h:panelGrid styleClass="formQuote" columns="1" rendered="#{!viewQuoteController.premiumBean.allOfferedSelected}">
		<h:outputText value="#{msg_payment['payment.not.applicable']}" styleClass="sectionInfo payment"/>
		</h:panelGrid>

		<h:panelGrid columns="2" columnClasses="paymentColumn, paymentColumn2" styleClass="formQuote" rendered="#{viewQuoteController.premiumBean.allOfferedSelected}">
		<p:panel styleClass="payment-detail">
			<h:panelGrid columns="1" >
				<p:dataTable var="vehicle_offer2" rowKey="rowIndex"  value="#{viewQuoteController.vehicleOfferBeanList}">

					<p:column colspan="3" styleClass="payment-objectName">
						<h:outputText id="vehicle_offer_#{rowIndex}" styleClass="black-label-payment vehicle-#{rowIndex}" value="#{vehicle_offer2.year} #{vehicle_offer2.make} #{vehicle_offer2.model}"/>
					</p:column>

					<p:column colspan="3" breakBefore="true" styleClass="payment-objectName">
							<span id="selected_coverage_#{rowIndex}">
	        	        		<h:outputText styleClass="black-label-payment vehiclePayment-#{rowIndex}" value="#{msg_payment['payment.not.applicable']}" rendered="#{empty vehicle_offer2.offerSelected or vehicle_offer2.offerSelected == false}"/>
	            	    		<h:outputText styleClass="black-label-payment vehiclePayment-#{rowIndex}" value="#{msg_payment[vehicle_offer2.selectedOfferTypeKey]}" rendered="#{not empty vehicle_offer2.offerSelected and vehicle_offer2.offerSelected == true}"/>
          					</span>
					</p:column>

					<p:column breakBefore="true" styleClass="black-label-payment premiumColumn1" rendered="#{not empty vehicle_offer2.offerSelected and vehicle_offer2.offerSelected == true}">
						<h:outputText value="#{msg_payment['payment.content.price.premium.1years']}" styleClass="premium-#{rowIndex}" />
					</p:column>

					<p:column styleClass="premiumColumn2" rendered="#{not empty vehicle_offer2.offerSelected and vehicle_offer2.offerSelected == true}">
                 			<span id="annual_premium_with_taxes_#{rowIndex}">
                 				<h:outputText styleClass="annualpremiumWithTaxes-#{rowIndex}" value="#{vehicle_offer2.selectedOfferBean.annualPremiumWithTaxes}"><f:convertNumber  type="currency" /></h:outputText>
                 			</span>
					</p:column>

					<p:column styleClass="red-label-payment premiumColumn3" rendered="#{not empty vehicle_offer2.offerSelected and vehicle_offer2.offerSelected == true and viewQuoteController.paymentBean.monthlyPaymentsEligible == true}">
                    		<span id="monthly_premium_with_taxes_#{rowIndex}">
	                    		(<h:outputText styleClass="monthlypremiumWithTaxes-#{rowIndex}" value="#{vehicle_offer2.selectedOfferBean.monthlyPremiumWithTaxes}" rendered="#{not empty vehicle_offer2.offerSelected and vehicle_offer2.offerSelected == true}"><f:convertNumber type="currency" /></h:outputText>
	                    		<h:outputText id="test6_" value=" / #{msg_payment['payment.content.month']}"/>)
                    		</span>
					</p:column>
				</p:dataTable>
			</h:panelGrid>
			<h:panelGrid columns="1" >

				<p:columnGroup rendered="#{viewQuoteController.premiumBean.premiumExist == true}">
					<p:column colspan="3">
						<h:graphicImage url="/image/ligneGauche.png" styleClass="line500"/>
					</p:column>
				</p:columnGroup>

				<p:columnGroup rendered="#{viewQuoteController.premiumBean.premiumExist == true}">
					<p:column styleClass="black-label-payment premiumColumn1">
						<h:outputText value="#{msg_payment['payment.grand.total.premium']}" />
					</p:column>

					<p:column styleClass="premiumColumn2">
           	   				<span id="total_annual_premium">
               					<h:outputText styleClass="annualAmountWithTaxes" value="#{viewQuoteController.paymentBean.annualAmountWithTaxes}"><f:convertNumber  	type="currency"  /></h:outputText>
               				</span>
					</p:column>

					<p:column styleClass="red-label-payment premiumColumn3" rendered="#{viewQuoteController.paymentBean.monthlyPaymentsEligible == true}">
                   	 		<span id="total_montly_premium">
	                   	 		(<h:outputText styleClass="totalMonthlyPremium" value="#{viewQuoteController.paymentBean.totalMonthlyPremium}"><f:convertNumber 	type="currency"  /></h:outputText>
	                   	 		<h:outputText value=" / #{msg_payment['payment.content.month']}"/>)
                   	 		</span>
					</p:column>
				</p:columnGroup>
			</h:panelGrid>
		</p:panel>

		<p:panel>
			<h:panelGroup rendered="#{viewQuoteController.paymentBean.paymentBank or ( provinceController.companyA and viewQuoteController.paymentBean.twoYearsPolicyEligible )}">

				<h:panelGrid styleClass="grey-bgrnd-cell payment-black-border" columnClasses="payment-no-border, payment-no-border" columns="2" border="1" cellpadding="0" cellspacing="10" rendered="#{viewQuoteController.premiumBean.premiumExist == true and viewQuoteController.paymentBean.monthlyPaymentsEligible == true}">

					<h:outputText value="#{msg_payment['payment.title']}" styleClass="sectionTitle payment-title-margin"/><p:spacer/>

					<h:outputText value="#{msg_payment['payment.annually.premium']}" rendered="#{!provinceController.companyA}"/>
					<h:outputText styleClass="paymentAnnualAmountWithTaxes" value="#{viewQuoteController.paymentBean.annualAmountWithTaxes}" rendered="#{!provinceController.companyA}"><f:convertNumber  type="currency" /></h:outputText>

					<h:outputText value="#{msg_payment['payment.monthly.premium']}"/>
					<h:outputText styleClass="paymentTotalMonthlyPremium" value="#{viewQuoteController.paymentBean.totalMonthlyPremium}"><f:convertNumber  type="currency" /></h:outputText>

					<h:outputText value="#{msg_payment['payment.first.payment']}"/>
					<h:outputText styleClass="paymentMonthlyFirstPayment" value="#{viewQuoteController.paymentBean.monthlyFirstPayment}" ><f:convertNumber  type="currency" /></h:outputText>

					<h:outputText value="#{msg_payment['payment.option']}"/>
					<h:outputText styleClass="paymentType" value="#{msg_payment['payment.by.bank.withdraw']}"/>

					<h:outputText value="#{msg_payment['payment.name.of.account.holder']}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentMonthlyBank and viewQuoteController.paymentBean.paymentMonthlyBank.accountHolderExist}"/>
					<h:outputText styleClass="paymentBankAccountHolderName" value="#{viewQuoteController.paymentBean.paymentMonthlyBank.bankAccountHolderName}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentMonthlyBank and viewQuoteController.paymentBean.paymentMonthlyBank.accountHolderExist}"/>

					<h:outputText value="#{msg_payment['payment.institution']}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentMonthlyBank and viewQuoteController.paymentBean.paymentMonthlyBank.institutionExist}"/>
					<h:outputText styleClass="paymentBankInstitution" value="#{viewQuoteController.paymentBean.paymentMonthlyBank.institution}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentMonthlyBank and viewQuoteController.paymentBean.paymentMonthlyBank.institutionExist}"/>

					<h:outputText value="#{msg_payment['payment.transit']}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentMonthlyBank and viewQuoteController.paymentBean.paymentMonthlyBank.transitExist}"/>
					<h:outputText styleClass="paymentBankTransit" value="#{viewQuoteController.paymentBean.paymentMonthlyBank.transit}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentMonthlyBank and viewQuoteController.paymentBean.paymentMonthlyBank.transitExist}"/>

					<h:outputText value="#{msg_payment['payment.account.number']}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentMonthlyBank and viewQuoteController.paymentBean.paymentMonthlyBank.accountNoExist}"/>
					<h:outputText styleClass="paymentBankAccountNumber"  value="#{viewQuoteController.paymentBean.paymentMonthlyBank.accountNumber}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentMonthlyBank and viewQuoteController.paymentBean.paymentMonthlyBank.accountNoExist}"/>

				</h:panelGrid>
			</h:panelGroup>

			<h:panelGroup rendered="#{viewQuoteController.paymentBean.paymentCC}">
				<h:panelGrid styleClass="grey-bgrnd-cell payment-black-border" columnClasses="payment-no-border, payment-no-border" columns="2" border="1" cellpadding="0" cellspacing="10" rendered="#{viewQuoteController.premiumBean.premiumExist == true and not empty viewQuoteController.paymentBean.paymentFullCC and not empty viewQuoteController.paymentBean.paymentFullCC.creditCardCompany}">

					<h:outputText value="#{msg_payment['payment.title']}" styleClass="sectionTitle payment-title-margin"/><p:spacer/>

					<h:outputText value="#{msg_payment['payment.annually.premium']}" rendered="#{!provinceController.companyA}"/>
					<h:outputText styleClass="paymentAnnualAmountWithTaxes" value="#{viewQuoteController.paymentBean.annualAmountWithTaxes}" rendered="#{!provinceController.companyA}"><f:convertNumber  type="currency" /></h:outputText>

					<h:outputText value="#{msg_payment['payment.option']}"/>
					<h:outputText styleClass="paymentType" value="#{msg_payment['payment.by.credit.card']}"/>

					<h:outputText value="#{msg_payment['payment.cc.type']}" rendered="#{not empty viewQuoteController.paymentBean.paymentFullCC.creditCardCompany}"/>
					<h:outputText styleClass="paymentCCType" value="#{msg_payment[viewQuoteController.paymentBean.paymentFullCC.creditCardType]}" rendered="#{not empty viewQuoteController.paymentBean.paymentFullCC.creditCardCompany}"/>

					<h:outputText value="#{msg_payment['payment.cc.expiry.date']}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentFullCC.cardExpiryMonth}"/>
					<h:outputText styleClass="paymentCCExpiryDate" value="#{viewQuoteController.paymentBean.paymentFullCC.cardExpiryMonth}/#{viewQuoteController.paymentBean.paymentFullCC.cardExpiryYear}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentFullCC.cardExpiryMonth}"/>

					<h:outputText value="#{msg_payment['payment.cc.card.masked.number']}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentFullCC.cardMaskedNumber}"/>
					<h:outputText styleClass="paymentCCCardMaskedNumber" value="#{viewQuoteController.paymentBean.paymentFullCC.cardMaskedNumber}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentFullCC.cardMaskedNumber}"/>

					<h:outputText value="#{msg_payment['payment.name.of.card.holder']}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentFullCC.cardHolderName}"/>
					<h:outputText styleClass="paymentCCCardHolderName" value="#{viewQuoteController.paymentBean.paymentFullCC.cardHolderName}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentFullCC.cardHolderName}"/>

					<h:outputText value="#{msg_payment['payment.cc.token']}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentFullCC.cardToken}"/>
					<h:outputText styleClass="paymentCCCardToken" value="#{viewQuoteController.paymentBean.paymentFullCC.cardToken}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentFullCC.cardToken}"/>

				</h:panelGrid>

				<h:panelGrid styleClass="grey-bgrnd-cell payment-black-border" columnClasses="payment-no-border, payment-no-border" columns="2" border="1" cellpadding="0" cellspacing="10" rendered="#{viewQuoteController.premiumBean.premiumExist == true and not empty viewQuoteController.paymentBean.paymentMonthlyCC and not empty viewQuoteController.paymentBean.paymentMonthlyCC.creditCardCompany}">

					<h:outputText value="#{msg_payment['payment.title']}" styleClass="sectionTitle payment-title-margin"/><p:spacer/>

					<h:outputText value="#{msg_payment['payment.monthly.premium']}"/>
					<h:outputText styleClass="paymentTotalMonthlyPremium" value="#{viewQuoteController.paymentBean.totalMonthlyPremium}"><f:convertNumber  type="currency" /></h:outputText>

					<h:outputText value="#{msg_payment['payment.first.payment']}"/>
					<h:outputText styleClass="paymentMonthlyFirstPayment" value="#{viewQuoteController.paymentBean.monthlyFirstPayment}" ><f:convertNumber  type="currency" /></h:outputText>

					<h:outputText value="#{msg_payment['payment.option']}"/>
					<h:outputText styleClass="paymentType" value="#{msg_payment['payment.by.credit.card']}"/>

					<h:outputText value="#{msg_payment['payment.cc.type']}" rendered="#{not empty viewQuoteController.paymentBean.paymentMonthlyCC.creditCardCompany}"/>
					<h:outputText styleClass="paymentCCType" value="#{msg_payment[viewQuoteController.paymentBean.paymentMonthlyCC.creditCardType]}" rendered="#{not empty viewQuoteController.paymentBean.paymentMonthlyCC.creditCardCompany}"/>

					<h:outputText value="#{msg_payment['payment.cc.expiry.date']}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentMonthlyCC.cardExpiryMonth}"/>
					<h:outputText styleClass="paymentCCExpiryDate" value="#{viewQuoteController.paymentBean.paymentMonthlyCC.cardExpiryMonth}/#{viewQuoteController.paymentBean.paymentMonthlyCC.cardExpiryYear}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentMonthlyCC.cardExpiryMonth}"/>

					<h:outputText value="#{msg_payment['payment.cc.card.masked.number']}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentMonthlyCC.cardMaskedNumber}"/>
					<h:outputText styleClass="paymentCCCardMaskedNumber" value="#{viewQuoteController.paymentBean.paymentMonthlyCC.cardMaskedNumber}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentMonthlyCC.cardMaskedNumber}"/>

					<h:outputText value="#{msg_payment['payment.name.of.card.holder']}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentMonthlyCC.cardHolderName}"/>
					<h:outputText styleClass="paymentCCCardHolderName" value="#{viewQuoteController.paymentBean.paymentMonthlyCC.cardHolderName}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentMonthlyCC.cardHolderName}"/>

					<h:outputText value="#{msg_payment['payment.cc.token']}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentMonthlyCC.cardToken}"/>
					<h:outputText  styleClass="paymentCCCardToken" value="#{viewQuoteController.paymentBean.paymentMonthlyCC.cardToken}" rendered="#{!viewQuoteController.quoteUploaded and not empty viewQuoteController.paymentBean.paymentMonthlyCC.cardToken}"/>
				</h:panelGrid>
			</h:panelGroup>
		</p:panel>


		<h:outputText styleClass="padding-left-payment paymentTaxes" colspan="2" value="#{msg_payment['payment.includes.taxes']}" rendered="#{provinceController.companyA}"/>

		<p:panel>
			<h:panelGroup rendered="#{provinceController.company6}">
				<h:panelGrid columns="2" cellpadding="0" cellspacing="10">
					<h:outputText value="#{msg_payment['payment.preferred.method']}"/>
					<h:outputText value="#{msg_payment['payment.preferred.method.na']}" rendered="#{empty viewQuoteController.paymentBean.preferredPaymentMethod}"/>
					<h:outputText value="#{msg_payment['payment.preferred.method.A']}" rendered="#{viewQuoteController.paymentBean.preferredPaymentMethod == 'A'}"/>
					<h:outputText value="#{msg_payment['payment.preferred.method.B']}" rendered="#{viewQuoteController.paymentBean.preferredPaymentMethod == 'B'}"/>
					<h:outputText value="#{msg_payment['payment.preferred.method.E']}" rendered="#{viewQuoteController.paymentBean.preferredPaymentMethod == 'E'}"/>
				</h:panelGrid>

			</h:panelGroup>
		</p:panel>

		<!-- QC -->
		<h:outputText styleClass="padding-left-payment paymentSurcharge" colspan="2" value="#{msg_payment['payment.includes.surcharges']}" rendered="#{viewQuoteController.paymentBean.paymentBank and provinceController.companyA }" />

		<!-- AB -->
		<h:outputText styleClass="padding-left-payment paymentSurcharge2" colspan="2" value="#{msg_payment['payment.includes.surcharges2']}" rendered="#{viewQuoteController.paymentBean.paymentBank and viewQuoteController.paymentBean.insuranceHomeAndAutoInd  and (provinceController.company3)}" />
		<h:outputText styleClass="padding-left-payment paymentSurcharge1" colspan="2" value="#{msg_payment['payment.includes.surcharges1']}" rendered="#{viewQuoteController.paymentBean.paymentBank and !viewQuoteController.paymentBean.insuranceHomeAndAutoInd and (provinceController.company3)}" />

		<!-- ON -->
		<h:outputText styleClass="padding-left-payment paymentSurcharge2" colspan="2" value="#{msg_payment['payment.includes.surcharges2']}" rendered="#{!viewQuoteController.paymentBean.newInterest and viewQuoteController.paymentBean.paymentBank and viewQuoteController.paymentBean.insuranceHomeAndAutoInd  and (provinceController.company6)}" />
		<h:outputText styleClass="padding-left-payment paymentSurcharge1" colspan="2" value="#{msg_payment['payment.includes.surcharges1']}" rendered="#{!viewQuoteController.paymentBean.newInterest and viewQuoteController.paymentBean.paymentBank and !viewQuoteController.paymentBean.insuranceHomeAndAutoInd and (provinceController.company6)}" />
		<h:outputText styleClass="padding-left-payment paymentSurcharge2" colspan="2" value="#{msg_payment['payment.includes.surcharges1.3']}" rendered="#{viewQuoteController.paymentBean.newInterest and viewQuoteController.paymentBean.paymentBank and viewQuoteController.paymentBean.insuranceHomeAndAutoInd  and (provinceController.company6)}" />
		<h:outputText styleClass="padding-left-payment paymentSurcharge1" colspan="2" value="#{msg_payment['payment.includes.surcharges1.3']}" rendered="#{viewQuoteController.paymentBean.newInterest and viewQuoteController.paymentBean.paymentBank and !viewQuoteController.paymentBean.insuranceHomeAndAutoInd and (provinceController.company6)}" />



	</h:panelGrid>
	</div>


</ui:composition>
