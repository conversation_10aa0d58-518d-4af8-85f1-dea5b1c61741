<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui">

	<ui:repeat value="#{viewQuoteController.getGeneralInfoPiece(param.referenceNo).getChildren(section)}" var="tab">
		<div class="quote-detail-section">
		<h:outputText value="#{tab.pieces[0].value}" styleClass="blue-top-subsection"/>
			<p:panelGrid columns="1" styleClass="formQuote" cellspacing="0" cellpadding="0">
				<p:dataTable value="#{tab.childrens}" var="childPiece" styleClass="subformQuote" >
	
					<p:column>
						<span id="#{childPiece.value}_question"><h:outputText value="#{childPiece.pieces[0].value}" rendered="#{childPiece.value ne 'follow_up_status'}"/></span>
						<span id="viewQuoteForm:#{childPiece.value}_question"><h:outputText value="#{childPiece.pieces[0].value}" rendered="#{childPiece.value eq 'follow_up_status'}"/></span>
					</p:column>
	
					<p:column>
						<h:graphicImage url="/image/question_delimiter_arrow.gif" alt=""/>
					</p:column>
	
					<p:column>
						<span id="#{childPiece.value}_response">
							<h:outputText value="#{childPiece.pieces[1].value}" rendered="#{childPiece.value ne 'follow_up_status'}"/>
	
	
						<h:selectOneRadio id="followupStatus" rendered="#{permissionController.checkAddFollowupNotes and childPiece.value eq 'follow_up_status'}" value="#{viewQuoteController.followUpStatus}" styleClass="innerGrid" layout="pageDirection">
							 <f:selectItems value="#{viewQuoteController.followupStatusList}" />
						</h:selectOneRadio>
	
	
	
						</span>
					</p:column>
	
				</p:dataTable>
			</p:panelGrid>
		</div>
	</ui:repeat>



</ui:composition>
