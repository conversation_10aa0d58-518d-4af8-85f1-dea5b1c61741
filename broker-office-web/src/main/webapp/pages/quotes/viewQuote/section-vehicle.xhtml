<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui">

    <ui:repeat value="#{viewQuoteController.getGeneralInfoPiece(param.referenceNo).getChildren('vehicules')}" var="tab">
        <div class="quote-detail-section">
            <h:outputText value="#{msg_client['VEHICLES']}" styleClass="blue-top-subsection"/>
            <p:panelGrid columns="1" styleClass="formQuote" cellspacing="0" cellpadding="0">
                <p:dataTable value="#{tab.childrens}" var="childPiece" styleClass="subformQuote" >
    
                    <p:column>
                        <span id="#{childPiece.value}_question">
                            <h:outputText value="#{childPiece.pieces[1].value}"/><p:spacer height="1" width="2" />
                            <h:outputText value="#{childPiece.pieces[3].value}"/><p:spacer height="1" width="2" />
                            <h:outputText value="#{childPiece.pieces[5].value}"/>
                        </span>
                    </p:column>
    
                </p:dataTable>
            </p:panelGrid>
        </div>
    </ui:repeat>



</ui:composition>
