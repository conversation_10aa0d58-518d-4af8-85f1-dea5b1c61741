<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui">

	<f:loadBundle var="msgClient" basename="com.intact.brokeroffice.controller.viewquote.client"/>
	<ui:param name="timeZoneSelected" value="#{msgClient['prop.timezone']}"/>

	<div class="quote-detail-section">
		<h:outputText value="#{msgClient['client.messages.title']}" styleClass="blue-top-subsection" rendered="#{viewQuoteController.clientContactBean.hasMessages}"/>
		<p:panelGrid columns="1" styleClass="formQuote" cellspacing="0" cellpadding="0" rendered="#{viewQuoteController.clientContactBean.hasMessages}">
	
			<p:dataTable value="#{viewQuoteController.clientContactBean.redMessages}" var="rmessage" width="100%" styleClass="subformQuote">
				<p:column>
					<h:graphicImage url="/image/arrow-right.svg" styleClass="important-message" alt=""/>
					<h:outputText styleClass="important-message" escape="false" value="#{rmessage}"/>
				</p:column>
			</p:dataTable>
	
			<p:dataTable value="#{viewQuoteController.clientContactBean.messages}" var="message" width="100%" styleClass="subformQuote">
				<p:column>
					<h:graphicImage url="/image/question_delimiter_arrow.gif" alt=""/>
					<h:outputText id="important_message" escape="false" value="#{message}"/>
				</p:column>
			</p:dataTable>
	
		</p:panelGrid>
	</div>

	<div class="quote-detail-section">
		<h:outputText value="#{generalPiece.pieces[0].value}" styleClass="blue-top-subsection"/>
		<p:panelGrid id="quoteClientPanel" columns="1" styleClass="formQuote" cellspacing="0" cellpadding="0">
			<p:dataTable value="#{generalPiece.childrens}" var="childPiece"  styleClass="subformQuote" >
				<p:column><span id="#{childPiece.value}_question"><h:outputText value="#{childPiece.pieces[0].value}" /></span></p:column>
				<p:column><h:graphicImage url="/image/question_delimiter_arrow.gif" alt=""/></p:column>
				<p:column rendered="#{empty childPiece.pieces[1].childrens}">
          <span id="#{childPiece.value}_response"><h:outputText value="#{childPiece.pieces[1].value}" /></span>
          <span id="#{childPiece.value}_response-ldo"><h:outputText value="LDO" /></span>
        </p:column>
				<p:column rendered="#{not empty childPiece.pieces[1].childrens}">
					<span id="#{childPiece.value}_response">
					<ui:repeat value="#{childPiece.pieces[1].childrens}" var="childChildPiece">
						<h:panelGroup rendered="#{not empty childChildPiece.value}">
							<h:outputText value="#{childChildPiece.value}" escape="false" /><br/>
						</h:panelGroup>
					</ui:repeat>
					</span>
				</p:column>
			</p:dataTable>
			<p:panelGrid columns="3" layout="tabular" styleClass="subformQuote" cellspacing="0" cellpadding="0" >
				<h:outputText id="follow_up_status_question" value="#{msgClient['client.followup']}" />
				<h:graphicImage url="/image/question_delimiter_arrow.gif" alt=""/>
				<h:selectOneRadio id="followupStatus" value="#{viewQuoteController.clientContactBean.followupStatus}" styleClass="innerGrid" layout="pageDirection">
					<f:selectItems value="#{viewQuoteController.followupStatusList}" />
				</h:selectOneRadio>
			</p:panelGrid>
		</p:panelGrid>
	</div>

	<div class="quote-detail-section">
		<h:outputText value="#{msgClient['client.activityLog.title']}" styleClass="blue-top-subsection"/>
		<p:panelGrid columns="1" styleClass="formQuote third-table" cellspacing="0" cellpadding="0">
			<p:dataTable id="ActivityLogDataTable" rowIndexVar="rowIndex" value="#{viewQuoteController.clientContactBean.notes}" var="act" width="100%">
	
				<p:column >
					<f:facet name="header">
					</f:facet>
				</p:column>
	
				<p:column>
					<f:facet name="header">
						<h:outputText value="#{msgClient['client.activityLog.date']}"/>
					</f:facet>
					<h:outputText value="#{act.date}" rendered="#{!provinceController.company3}" styleClass="activityDate-#{rowIndex}">
						<f:convertDateTime type="date" pattern="yyyy-MM-dd HH:mm" timeZone="US/Eastern"/>
					</h:outputText>
					 <h:outputText value="#{act.date}" rendered="#{provinceController.company3}" styleClass="activityDate-#{rowIndex}">
						<f:convertDateTime type="date" pattern="yyyy-MM-dd HH:mm" timeZone="Canada/Mountain"/>
					</h:outputText>
				</p:column>
	
				<p:column>
					<f:facet name="header">
						<h:outputText value="#{msgClient['client.activityLog.account']}"/>
					</f:facet>
					<h:outputText value="#{act.account}" styleClass="activityAccount-#{rowIndex}"/>
				</p:column>
	
				<p:column>
					<f:facet name="header">
						<h:outputText value="#{msgClient['client.activityLog.activity']}"/>
					</f:facet>
					<h:outputText value="#{msgClient[act.activity.code]}" styleClass="activityType-#{rowIndex}"/>
				</p:column>
	
				<p:column>
					<f:facet name="header">
						<h:outputText value="#{msgClient['client.activityLog.notes']}"/>
					</f:facet>
					<h:outputText value="#{act.note}" styleClass="activityNote-#{rowIndex}"/>
				</p:column>
			</p:dataTable>

			<p:panel rendered="#{permissionController.checkAddFollowupNotes}" styleClass="topLine" ></p:panel>

			<!-- Add a note to the quote -->
			<p:panelGrid rendered="#{permissionController.checkAddFollowupNotes}"
						 columns="3"
						 layout="tabular"
						 columnClasses="subformQuoteCol1, subformQuoteCol2, subformQuoteCol3"
						 styleClass="quote-logNote"
						 cellspacing="0"
						 cellpadding="0">
	
				<h:outputText value="#{msgClient['client.addNotes']}" />
				<h:graphicImage url="/image/question_delimiter_arrow.gif" alt=""/>
				<p:panel>
					<p:inputTextarea id="note"
									 value="#{viewQuoteController.clientContactBean.noteText}"
									 rows="6"
									 maxlength="150"
									 counter="display"
									 counterTemplate="{1}/{2}"
									 autoResize="false"/>
					<div class="remaining-chars-counter">
						<h:outputText id="display"/>
					</div>
					<h:message for="note"/>
				</p:panel>
			</p:panelGrid>
			<p:panelGrid columns="3" layout="tabular" styleClass="quote-logNote">
				<p:panel rendered="#{permissionController.checkAddFollowupNotes}" layout="tabular" >
					<!--  PM9331: insure that image is always download to avoid buttons disappear temporarily  -->
					<p:panel style="display:none">
						<h:commandLink styleClass="actionBtnRightHover" >
							<h:outputText value="invisible right" />
						</h:commandLink>

						<h:commandLink styleClass="actionBtnLeftHover">
							<h:outputText value="invisible left" />
						</h:commandLink>
					</p:panel>

					<p:commandLink action="#{viewQuoteController.update(param.referenceNo, param.lineOfBusiness, param.lineOfInsurance, param.applicationMode)}" styleClass="actionBtn updateBtn" update="viewQuotePanel">
							<span class="right" >
								<h:outputText value="#{global['form.button.update']}"/>
								<h:graphicImage url="/image/btnRightArrow.png" alt=""/>
							</span>
						<f:param name="referenceNo" value="#{viewQuoteController.quote.id}" />
						<f:param name="lineOfBusiness" value="#{viewQuoteController.quote.lineOfBusiness.code}" />
						<f:param name="lineOfInsurance" value="#{viewQuoteController.quote.lineOfInsurance.code}" />
						<f:param name="applicationMode" value="#{viewQuoteController.quote.applicationMode}" />
						<f:param name="searchIndex" value="#{param.searchIndex}" />
					</p:commandLink>

				</p:panel>
			</p:panelGrid>
	
		</p:panelGrid>
	</div>


</ui:composition>
