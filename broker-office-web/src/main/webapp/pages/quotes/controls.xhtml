<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:p="http://primefaces.org/ui">

    <h:inputHidden id="AntiCSRFToken" value="#{searchController.tokenCSRF}"/>
    <p:spacer width="60px" rendered="#{!searchController.isSearch}"/>
    <p:commandLink id="refreshListLink" value="#{msg_quo['link.refresh']}"
                   action="#{searchController.refresh}" update="mainTab"
                   styleClass="link_refreshList"
                   rendered="#{!searchController.isSearch}"/>

    <h:panelGroup id="panelSearchLink">

        <!--		FIXME: investigate if this setting is really needed with Primefaces-->
        <!--		<script type="text/javascript">-->
        <!--			A4J.AJAX.Submit = function(formId, event , options ) {winOpen=false;var domEvt = A4J.AJAX.CloneEvent(event);var query = A4J.AJAX.PrepareQuery(formId, domEvt, options);if (query) {var queue = A4J.AJAX.EventQueue.getOrCreateQueue(options, formId);if(queue){queue.push(query, options, domEvt);}else{A4J.AJAX.SubmitQuery(query, options, domEvt);} return false;}}-->
        <!--		</script>-->

        <p:spacer width="60px" rendered="#{!searchController.isSearchResult and !searchController.isSearch}"/>
        <p:commandLink id="searchLink"
                       update="newPage,searchWarning"
                       action="#{searchController.makeNewSearch}"
                       rendered="#{!authentificationController.userWithoutSubbrokers and !searchController.isSearchResult and !searchController.isSearch}">
            <h:outputText value="#{msg_quo[searchController.searchLinkKey]}"/>
        </p:commandLink>
    </h:panelGroup>

    <h:panelGroup id="newPage">
        <h:panelGroup rendered="#{searchController.nextIndex.type eq 'new'}">
            <script type="text/javascript">
                openNewSearch('#{searchController.nextIndex.index}', '#{searchController.nextIndex.type}');
            </script>
        </h:panelGroup>
    </h:panelGroup>

    <h:panelGroup id="searchWarning" styleClass="searchWarning-panel">
        <h:panelGroup styleClass="#{(searchController.nextIndex.type eq 'force') ? 'panel-mask' : 'hidden'}">
        </h:panelGroup>
        <h:panelGroup id="maxSearchPopup" styleClass="#{(searchController.nextIndex.type eq 'force') ? 'panel-wrap' : 'hidden'}">
            <h:panelGroup styleClass="panel-border">
                <h:panelGroup styleClass="panel-content">
                    <h:outputText value="#{msg_quo['title.max.search']}" styleClass="title"/>
                    <p:spacer height="30px"/>

                    <h:panelGrid columns="1" cellspacing="0" cellpadding="0">
                        <h:outputText value="#{msg_quo['max.search.warning']}" styleClass="searchWarning-msg"/>
                    </h:panelGrid>
                    <h:panelGroup styleClass="actionBtn-group">
                        <p:commandLink id="newSearchNo" styleClass="link_newSearchNo actionBtn"
                                       onclick="document.getElementById('mainTab:quoteListForm:searchWarning').classList.add('hidden')">
                            <h:outputText value="#{global['button.no']}" styleClass="actionBtn-wording"/>
                            <h:graphicImage url="/image/btnRightArrow.png"/>
                        </p:commandLink>
                        <p:commandLink id="newSearchYes"
                                       action="#{searchController.makeForceNewSearch}"
                                       update="searchWarning,newPage"
                                       styleClass="link_newSearchYes actionBtn">
                            <h:outputText value="#{global['button.yes']}" styleClass="actionBtn-wording"/>
                            <h:graphicImage url="/image/btnRightArrow.png"/>
                        </p:commandLink>
                    </h:panelGroup>
                </h:panelGroup>

            </h:panelGroup>

        </h:panelGroup>

    </h:panelGroup>

    <p:spacer width="60px" rendered="#{searchController.isSearchResult}"/>
    <p:commandLink id="backToSearch" value="#{msg_quo['link.back.to.search']}"
                   action="#{searchController.backToSearch}"
                   update="mainTab" style="#{searchController.isSearchResult ? '' : 'visibility: hidden'}"/>

    <p:spacer width="60px" rendered="#{searchController.isSearchResult}"/>
    <p:commandLink id="returnToListQuotes" value="#{msg_quo['link.return']}" onclick="window.close();"
                   rendered="#{searchController.isSearch or searchController.isSearchResult}"/>
    <p:spacer width="60px"/>
</ui:composition>
