<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui">

	<h:graphicImage styleClass="activityVu-#{rowKeyVar}"
					url="/image/iconVuCourtier.png"
					rendered="#{qb.lastActivityNote != null}" alt=""/>
	<h:graphicImage styleClass="activityVu-#{rowKeyVar}" url="/image/iconVuCourtier.png" rendered="#{qb.lastActivityNote != null}" alt=""/>

	<p:subTable id="subQuoteList"
				value="#{qb.childrens}" var="child" width="100%"
				rendered="#{qb.expanded}"
				rowIndexVar="subRowIndex">

		<ui:param name="childStyle" value="#{subRowIndex eq childCount - 1 ? 'lastChild' : ''}"/>

		<p:column headerClass="firstColumn" styleClass="firstColumn #{childStyle}" rendered="#{permissionController.checkReassign}">
			<h:outputText value="#{childMaster}" />
			<h:selectBooleanCheckbox id="selected" immediate="true" value="#{child.selected}" styleClass="checkbox_selectQuote#{child.agreementNumber} selected-#{rowKeyVar}"
									 valueChangeListener="#{searchController.toggleSelection}">
				<p:ajax event="change"
						update=":mainTab:quoteListForm,:mainTab:reassignForm"/>
			</h:selectBooleanCheckbox>
		</p:column>

		<p:column sortBy="#{child.lastActivityNote.date}" styleClass="notFirstColumn #{childStyle}" headerClass="link_sortLastActivityNote #{permissionController.checkReassign?'':'firstColumn'}">

			<p:outputPanel layout="block">
				<p:tooltip my="left top" at="right bottom" showDelay="100"
						   styleClass="tooltip" rendered="#{child.lastActivityNote != null}">
					<h:outputText styleClass="activityDate-#{rowKeyVar}" value="#{child.lastActivityNote.date}" rendered="#{!provinceController.company3}">
						<f:convertDateTime type="date" pattern="[yyyy-MM-dd HH:mm]" timeZone="US/Eastern"/>
					</h:outputText>
					<h:outputText styleClass="activityDate-#{rowKeyVar}" value="#{child.lastActivityNote.date}" rendered="#{provinceController.company3}">
						<f:convertDateTime type="date" pattern="[yyyy-MM-dd HH:mm]" timeZone="Canada/Mountain"/>
					</h:outputText>
					<br/>
					<h:outputFormat styleClass="activityType-#{rowKeyVar}" value="#{msg_quo[child.lastActivityNote.activity]}">
						<f:param value="#{child.lastActivityNote.account}"/>
					</h:outputFormat>
				</p:tooltip>

				<h:graphicImage width="16" styleClass="quoteStatus-#{rowKeyVar}" url="/image/#{child.openedQuoteStatus}-flag.png" rendered="#{child.openedQuoteStatus != null and child.lastActivityNote == null}" alt=""/>
				<h:graphicImage width="16" styleClass="activityVu-#{rowKeyVar}" url="/image/iconVuCourtier.png" rendered="#{child.lastActivityNote != null}" alt=""/>
			</p:outputPanel>
		</p:column>

		<p:column sortBy="#{child.agreementNumber}" headerClass="link_sortByReferenceNo" styleClass="#{childStyle}">

		</p:column>

		<p:column sortBy="#{child.agreementNumber}" headerClass="link_sortByReferenceNo" styleClass="#{childStyle}">

			<p:spacer width="6px" rendered="#{child.parent}" />

			<h:outputLink id="viewQuoteLink" value="${searchController.getPage(child.applicationMode)}" target="_blank" rendered="#{authentificationController.canViewQuote}"
						  styleClass="link_view#{child.agreementNumber} link_#{qb.applicationMode}" >
				<h:outputText styleClass="agreementNumber-#{rowKeyVar}" value="#{child.agreementNumber}"/><br/>
				<h:outputText  styleClass="agreementLegacyNumber-#{rowKeyVar}" value="#{child.agreementLegacyNumber}" rendered="#{child.agreementLegacyNumber != null}"/>
				<f:param name="referenceNo" value="#{child.id}"/>
				<f:param name="lineOfBusiness" value="#{child.lineOfBusiness.code}"/>
				<f:param name="lineOfInsurance" value="#{child.lineOfInsurance.code}"/>
				<f:param name="searchIndex" value="#{searchController.index}"/>
			</h:outputLink>

			<p:panelGrid columns="1" rendered="#{!authentificationController.canViewQuote}">
				<h:outputText styleClass="agreementNumber-#{rowKeyVar}" value="#{child.agreementNumber}"/><br/>
				<h:outputText styleClass="agreementLegacyNumber-#{rowKeyVar}" value="#{child.agreementLegacyNumber}" rendered="#{child.agreementLegacyNumber != null}"/>
			</p:panelGrid>
		</p:column>

		<p:column  styleClass="#{childStyle}" /> <!-- empty column in the child table [corresponding to the {.followUp} field of the parent] -->

		<p:column sortBy="#{qb.autoplusAvailable}" styleClass="notFirstColumn #{childStyle}" rendered="#{searchController.autoPlusDisplay}" headerClass="link_sortByLanguage">

			<h:outputText styleClass="language-#{rowKeyVar}" value="#{msg_quo['auto.plus.value']}" rendered="#{qb.autoplusAvailable}" />
		</p:column>

		<p:column sortBy="#{child.quoteSource}" styleClass="notFirstColumn #{childStyle}" headerClass="link_sortByQuoteSource">

			<h:outputText styleClass="quoteSource-#{rowKeyVar}" value="#{msg_quo['quote.source.'.concat(qb.quoteSource)]}"/>
		</p:column>

		<p:column headerClass="link_sortByInfo" styleClass="notFirstColumn #{childStyle}">
			<p:outputPanel onmouseover="showSpinner=false;" onmouseout="showSpinner=true;">
				<h:graphicImage id="info-image"
								width="15"
								styleClass="infoStatus-#{subRowIndex}"
								url="/image/info.png"
								alt="">
				</h:graphicImage>
				<p:overlayPanel id="child-tooltip-id-#{child.agreementNumber}"
								for="info-image"
                                styleClass="tooltip tooltip-large"
								showEvent="mouseover"
								hideEvent="mouseout"
                                dynamic="true"
								dismissable="false"
								showDelay="100"
								autoHide="false">
                    <ui:include src="quoteSummaryFromPLT.xhtml">
                        <ui:param name="qb" value="#{child}"/>
                    </ui:include>
				</p:overlayPanel>
			</p:outputPanel>
		</p:column>


		<p:column sortBy="#{child.languageCommunication}" rendered="#{searchController.languageCommunicationDisplay}" headerClass="link_sortByLanguage" styleClass="#{childStyle}">
			<h:outputText styleClass="language-#{rowKeyVar}" value="#{child.languageCommunication.codeISO}"/>
		</p:column>

		<p:column sortBy="#{child.customerValueIndex1}" rendered="#{searchController.CVIDisplay}" headerClass="link_sortByCVI" styleClass="#{childStyle}">

			<p:panelGrid columns="1" >
				<h:outputText styleClass="customerValueIndexBand-#{rowKeyVar}" value="#{msg_quo[child.customerValueIndexBand1]} /  #{msg_quo[child.customerValueIndexBand2]}" rendered="#{child.customerValueIndex1 != null and child.customerValueIndex2 != null}"/>
				<h:outputText styleClass="customerValueIndexBand-#{rowKeyVar}" value="#{msg_quo[child.customerValueIndexBand1]}" rendered="#{child.customerValueIndex1 != null and empty child.customerValueIndex2 }"/>
				<h:outputText styleClass="customerValueIndexBand-#{rowKeyVar}" value="#{msg_quo[child.customerValueIndexBand2]}" rendered="#{child.customerValueIndex2 != null and empty child.customerValueIndex1}"/>

			</p:panelGrid>
		</p:column>

		<p:column sortBy="#{child.consent}" rendered="#{!provinceController.company3}" headerClass="link_sortByConsent" styleClass="#{childStyle}">
			<h:graphicImage id="consentYesChild" styleClass="contactConsent-#{rowKeyVar}" url="/image/contact_consent.gif" rendered="#{child.consent}" alt=""/>
			<p:tooltip styleClass="tooltip" for="consentYesChild" value="#{msg_legend['consent.consent']}"/>
			<h:graphicImage id="consentNoChild" styleClass="contactConsent-#{rowKeyVar}" url="/image/contact_no_consent.gif" rendered="#{!child.consent}" alt=""/>
			<p:tooltip styleClass="tooltip" for="consentNoChild" value="#{msg_legend['consent.noconsent']}"/>
		</p:column>

		<p:column rendered="#{provinceController.company3}" headerClass="link_sortByConsent" styleClass="#{childStyle}">

			<p:panelGrid columns="1" >
				<h:graphicImage id="consentYesChildAB" styleClass="contactConsent-#{rowKeyVar}" url="/image/contact_consent.gif" rendered="#{child.consent}" alt=""/>
				<p:tooltip styleClass="tooltip" for="consentYesChildAB" value="#{msg_legend['consent.consent']}"/>
				<h:graphicImage id="consentNoChildAB" styleClass="contactConsent-#{rowKeyVar}" url="/image/contact_no_consent.gif" rendered="#{!child.consent}" alt=""/>
				<p:tooltip styleClass="tooltip" for="consentNoChildAB" value="#{msg_legend['consent.noconsent']}"/>
				<h:graphicImage id="syncroChildAB" styleClass="syncro-#{rowKeyVar}" url="/image/maison.gif" rendered="#{child.sync}" alt=""/>
				<p:tooltip styleClass="tooltip" for="syncroChildAB" value="#{msg_legend['synchro.text']}" escape="false"/>
			</p:panelGrid>
		</p:column>

		<p:column sortBy="#{child.brokerName}" width="160" headerClass="link_sortByBroker" styleClass="#{childStyle}">
			<p:panelGrid columns="1">
				<p:outputPanel layout="block">
					<h:outputText id="brokerName" styleClass="broker-#{rowKeyVar}" value="#{child.brokerName}" converter="pointOfSaleConverter"/>
					<p:tooltip for="brokerName" value="#{child.brokerName}" direction="bottom-right" styleClass="tooltip" layout="block" />
				</p:outputPanel>
			</p:panelGrid>
		</p:column>

		<p:column sortBy="#{child.followUp}" styleClass="notFirstColumn #{childStyle}" headerClass="link_sortByFollowUp">

			<p:outputPanel layout="block" rendered="#{child.followUp != 'NOT_CONTACTED'}">
				<p:tooltip my="left top" at="right bottom" showDelay="100"
						   styleClass="tooltip" rendered="#{child.lastFollowupNote != null}">
					<h:outputText styleClass="followupDate-#{rowKeyVar}" value="#{child.lastFollowupNote.date}" rendered="#{!provinceController.company3}">
						<f:convertDateTime type="date" pattern="[yyyy-MM-dd HH:mm] - " timeZone="US/Eastern"/>
					</h:outputText>
					<h:outputText styleClass="followupDate-#{rowKeyVar}" value="#{child.lastFollowupNote.date}" rendered="#{provinceController.company3}">
						<f:convertDateTime type="date" pattern="[yyyy-MM-dd HH:mm] - " timeZone="Canada/Mountain"/>
					</h:outputText>
					<h:outputText styleClass="followupAccount-#{rowKeyVar}" value="#{child.lastFollowupNote.account}" /><br/>
					<h:outputText styleClass="followupNote-#{rowKeyVar}" value="#{child.lastFollowupNote.note}" />
				</p:tooltip>
				<h:graphicImage id="followUpImage" styleClass="followupStatus-#{rowKeyVar}" url="/image/crochet_follow-up.gif" rendered="#{child.followUp=='CONTACTED_FOLLOWUP_REQUIRED'}" alt=""/>
				<p:tooltip styleClass="tooltip" for="followUpImage" value="#{msg_legend['followup.followup']}" escape="false"/>
				<h:graphicImage id="noFollowUpImage" styleClass="followupStatus-#{rowKeyVar}" url="/image/crochet_follow-up_required.gif" rendered="#{child.followUp=='CONTACTED_NO_FOLLOWUP_REQUIRED'}" alt=""/>
				<p:tooltip styleClass="tooltip" for="noFollowUpImage" value="#{msg_legend['followup.noFollowup']}" escape="false"/>
				<h:graphicImage id="noContactNoFollowUpImage" styleClass="followupStatus-#{rowKeyVar}" url="/image/no_follow-up.gif" rendered="#{child.followUp=='NOT_CONTACTED_NO_FOLLOWUP_REQUIRED'}" alt=""/>
				<p:tooltip styleClass="tooltip" for="noContactNoFollowUpImage" value="#{msg_legend['followup.noneRequired']}" escape="false"/>
				<h:graphicImage id="fakeLeadImage" styleClass="followupStatus-#{rowKeyVar}" url="/image/fake_lead.png" rendered="#{child.followUp=='DUPLICATE_FAKE'}" alt=""/>
				<p:tooltip styleClass="tooltip" for="fakeLeadImage" value="FAKE LEAD" />

			</p:outputPanel>
		</p:column>

		<p:column sortBy="#{child.lastName}" headerClass="link_sortByLastName" styleClass="#{childStyle}">

			<p:panelGrid columns="1" rendered="#{child.lineOfBusiness == 'PERSONAL_LINES'}">
				<p:outputPanel layout="block">
					<h:outputText id="firstname" styleClass="firstName-#{rowKeyVar}" value="#{child.firstName}" converter="nameConverter"/>
					<p:tooltip for="firstname" value="#{child.firstName}" direction="bottom-right" styleClass="tooltip" layout="block" rendered="#{child.firstNameLong}" />
				</p:outputPanel>
				<p:outputPanel layout="block">
					<h:outputText id="lastname" styleClass="lastName-#{rowKeyVar}" value="#{child.lastName}" converter="nameConverter"/>
					<p:tooltip for="lastname" value="#{child.lastName}" direction="bottom-right"  styleClass="tooltip" layout="block" rendered="#{child.lastNameLong}"/>
				</p:outputPanel>
			</p:panelGrid>

			<p:panelGrid columns="1" rendered="#{child.lineOfBusiness == 'COMMERCIAL_LINES'}">
				<p:outputPanel layout="block">
					<h:outputText id="unstructuredName" styleClass="unstructuredName-#{rowKeyVar}" value="#{child.unstructuredName}" converter="nameConverter"/>
					<p:tooltip for="unstructuredName" value="#{child.unstructuredName}" direction="bottom-right" styleClass="tooltip" layout="block" rendered="#{child.unstructuredNameLong}" />
				</p:outputPanel>
			</p:panelGrid>

		</p:column>

		<p:column sortBy="#{child.lastUpdate}" headerClass="link_sortByLastUpdate" styleClass="#{childStyle}">
			<p:panelGrid columns="1">
				<h:outputText styleClass="creationDate-#{rowKeyVar} nowrap" value="#{child.creationDate}" rendered="#{!provinceController.company3}">
					<f:convertDateTime type="date" pattern="yyyy-MM-dd" timeZone="US/Eastern"/>
				</h:outputText>
				<h:outputText styleClass="creationDate-#{rowKeyVar} nowrap" value="#{child.creationDate}" rendered="#{provinceController.company3}">
					<f:convertDateTime type="date" pattern="yyyy-MM-dd" timeZone="Canada/Mountain"/>
				</h:outputText>
				<h:outputText styleClass="lastUpdate-#{rowKeyVar}" value="#{child.lastUpdate}" rendered="#{!provinceController.company3}">
					<f:convertDateTime type="date" pattern="yyyy-MM-dd HH:mm" timeZone="US/Eastern"/>
				</h:outputText>
				<h:outputText styleClass="lastUpdate-#{rowKeyVar}" value="#{child.lastUpdate}" rendered="#{provinceController.company3}">
					<f:convertDateTime type="date" pattern="yyyy-MM-dd HH:mm" timeZone="Canada/Mountain"/>
				</h:outputText>
			</p:panelGrid>
		</p:column>

		<p:column sortBy="#{global[child.status.name()]}" headerClass="link_sortByStatus" styleClass="#{childStyle}">

			<h:graphicImage styleClass="rbImage-#{rowKeyVar}" id="child_roadblock" url="/image/#{child.roadBlockImage}" rendered="#{child.roadblockCode != null}" alt=""/>
			<h:outputText styleClass="rbMsg-#{rowKeyVar}" value=" " rendered="#{child.roadblockCode != null}"/>
			<h:outputText styleClass="rbStatus-#{rowKeyVar}" value="#{global[child.status.name()]}"/>
			<p:tooltip for="child_roadblock" value="#{child.roadblockMessage}" my="left top" at="right bottom" showDelay="100" styleClass="tooltip"/>
		</p:column>

		<p:column sortBy="#{child.inceptionDate}" rendered="#{!provinceController.companyA}" headerClass="link_sortByInceptionDate" styleClass="#{childStyle} nowrap">

			<h:outputText styleClass="inceptionDate-#{rowKeyVar}" value="#{child.inceptionDate}" rendered="#{!provinceController.company3}">
				<f:convertDateTime type="date" pattern="yyyy-MM-dd" timeZone="US/Eastern"/>
			</h:outputText>
			<h:outputText styleClass="inceptionDate-#{rowKeyVar}" value="#{child.inceptionDate}" rendered="#{provinceController.company3}">
				<f:convertDateTime type="date" pattern="yyyy-MM-dd" timeZone="Canada/Mountain"/>
			</h:outputText>
		</p:column>
	</p:subTable>
</ui:composition>	    	                                  
