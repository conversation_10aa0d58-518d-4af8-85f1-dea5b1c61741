<ui:composition xmlns="http://www.w3.org/1999/xhtml"
                xmlns:ui="http://java.sun.com/jsf/facelets"
                xmlns:h="http://java.sun.com/jsf/html"
                xmlns:f="http://java.sun.com/jsf/core"
                xmlns:p="http://primefaces.org/ui">

    <!--@elvariable id="qb" type="com.intact.brokeroffice.business.quote.QuotesBean"-->

    <h:panelGrid id="quoteSummaryForm_#{qb.id}">
        <f:event type="preRenderComponent"
                 listener="#{quoteToolTipController.setQuote(qb.id, qb.lineOfBusiness.code, qb.lineOfInsurance.code, qb.agreementNumber)}"/>

        <ui:repeat var="summary" value="#{quoteToolTipController.tooltipInfo.childrens}">
            <!-- Quote number -->
            <div class="tooltip-title">
                <h:outputText value="#{summary.pieces[0].value}" styleClass="tooltipData"/>
            </div>
            <h:panelGrid>
                <ui:repeat var="summaryHeader" value="#{summary.childrens}">
                    <!-- Firstname lastname -->
                    <h:outputText styleClass="tooltipData" value="#{summaryHeader.pieces[0].value}"/>
                    <h:outputText value=""/>
                    <h:outputText value=""/>
                    <p:dataTable value="#{summaryHeader.childrens}" var="childPiece">
                        <p:column styleClass="prime-table-cell"><h:outputText
                                value="#{childPiece.pieces[0].value}"/></p:column>
                        <p:column styleClass="prime-table-cell"><h:graphicImage
                                url="/image/question_delimiter_arrow.gif" alt=""/></p:column>
                        <p:column styleClass="prime-table-cell"><h:outputText
                                value="#{childPiece.pieces[1].value}"/></p:column>
                    </p:dataTable>
                </ui:repeat>
            </h:panelGrid>
        </ui:repeat>
    </h:panelGrid>
</ui:composition>