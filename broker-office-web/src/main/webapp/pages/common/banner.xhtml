<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN"
        "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"
      xmlns:h="http://xmlns.jcp.org/jsf/html"
      xmlns:ui="http://xmlns.jcp.org/jsf/facelets"
>


<ui:composition>
    <div class="banner-container">
        <div class="banner-content">
            <div>
                <p>A new version of Webzone has been released. For more information, check out our
                    <h:outputLink styleClass="link_release_note"
                                  onclick="window.open('#{request.contextPath}/pages/release-note/release-note.jsf', 'legendWindow', 'width=815, height=700, scrollbars=yes, dependent=yes, menubar=no, toolbar=no'); return false;">
                    <h:outputText value="release notes"/>
                </h:outputLink>.
                </p>
                <p>Une nouvelle version de Webzone a été publiée. Pour plus d'informations, consultez nos
                    <h:outputLink styleClass="link_release_note"
                                  onclick="window.open('#{request.contextPath}/pages/release-note/release-note.jsf', 'legendWindow', 'width=815, height=700, scrollbars=yes, dependent=yes, menubar=no, toolbar=no'); return false;">
                        <h:outputText value="notes de publication"/>
                    </h:outputLink>.
                </p>
            </div>
            <div>
                <button onclick="$('.banner-container').hide()" title="close"></button>
            </div>
        </div>
    </div>
</ui:composition>
</html>
