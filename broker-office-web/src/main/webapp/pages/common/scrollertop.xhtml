<ui:composition xmlns="http://www.w3.org/1999/xhtml"
				xmlns:ui="http://java.sun.com/jsf/facelets"
				xmlns:h="http://java.sun.com/jsf/html"
				xmlns:f="http://java.sun.com/jsf/core"
				xmlns:p="http://primefaces.org/ui">

	<ui:param name="ds" value="#{listName}dsFooterIndex"/>
	<p:panelGrid styleClass="perPage" cellspacing="0" cellpadding="0" columns="1">
		<h:panelGroup>
			<h:outputText value="#{global['ds.header.display']}" />
			<p:spacer width="5px" />
			<h:selectOneMenu id="#{listName}Header" value="#{scrollerPerPage}" immediate="true" class="input_numberItemsPerPage">
				<f:selectItems value="#{scrollerPerPageList}" />
				<p:ajax update="#{listName}"/>
			</h:selectOneMenu>
			<p:spacer width="5px" />
			<h:outputText value="#{global['ds.header.perPage']}" />
		</h:panelGroup>
	</p:panelGrid>

</ui:composition>