*{
	margin: 0pt;
	padding: 0pt;
}

.td1 {
	width:250px;
}

html, body{
	height: 100%;
}

body{
	background-color: transparent;
	background-image: url(../image/bgPage.jpg);
	background-repeat: repeat;
	background-attachment: fixed;
	background-position: center 0pt;
	color: #000000;
	position: relative;
	font-size: 12px;
	font-family: Helvetica,Arial,Verdana,sans-serif;
}

a{
	text-decoration: none;
	color: #00b3be;
}

a:hover{
	text-decoration: underline;
	cursor: pointer;
	color: #655F5D;
}

table{
	font-size: 12px;
	font-family: Helvetica, Arial, Verdana, sans-serif;
}

image {
	border: 0px;
	padding: 0px;
}

img.rightcorner {
   width: 70px;
   height: 9px;
   border: none;
   display: block !important;
   vertical-align: top;
   float: right;
   position: relative;
   top: -1px;
}

img.rightpart {
   width: 51px;
   height: 8px;
   border: none;
   margin-right:10px;
   float:right;
}
img.rightpartIn {
   width: 51px;
   height: 8px;
   border: none;
   margin-right:9px;
   float:right;
}

.leftfullpart {
   background-repeat: repeat-x;
   background-image: url(../image/ligneGauche.png) ;
   background-color: transparent;
   background-position:left top;
   height: 8px;
   border: none;
   display: block !important;
   float: left;
}

.line865{
 width: 865px;
}

.line863{
 width: 863px;
}

.line845{
 width: 845px;
}

.line500{
 width: 400px;
}

.breadcrumb {
  background-color: transparent;
  width: 966px;
}


img.rightcorner.togglePanel{
   top: 9px;
}

input, select, textarea, buftton, keygen, isindex {
	vertical-align:top;
}

.main{
	background-color: transparent;
	background-image: url(../image/wrapBg.png);
	background-repeat: repeat-y;
	background-attachment: scroll;
	background-position: center 0pt;
	margin-right: auto;
	margin-left: auto;
	width: 1004px;
	min-height: 100%;
	border:0px;
}

.header{
	width: 988px;
	background-color: transparent;
	background-image: url(../image/bgHeader.jpg);
	background-repeat: no-repeat;
	background-attachment: scroll;
	background-position: right 0pt;
	height: 168px;
	border:0px;
	margin-right: 8px;
	margin-left: 8px;
}

.footer{
	width: 950px;
	background-color: transparent;
	background-image: url(../../image/footerBg.png);
	background-repeat: no-repeat;
	background-attachment: scroll;
	background-position: right 0pt;
	height: 25px;
	border:0px;
	margin-right: 0px;
	margin-left: 0px;
}

.language{
	float: right;
	padding-right: 20px;
}

.contents{
	width: 988px;
	border:0px;
	margin-right: 8px;
	margin-left: 8px;
}

.form {
}

.form tr td{
	border-top-width: 1px;
	border-top-style: solid;
	border-top-color: #cccccc;
	border-left-width: 0px;
	border-right-width: 0px;
	padding-top: 4px;
	padding-right: 4px;
	padding-bottom: 4px;
	padding-left: 12px;
	vertical-align: top;
}

.form tr:first-child td{
	border-top: none;
}

.hourTable tr td{
	border-top: none;
	border-left: none;
	border-right: none;
	text-align: center;
	vertical-align: top;
	padding-top: 4px;
	padding-bottom: 4px;
	width: 11%;
}

.hourTable tr {
	border-top: none;
	border-left: none;
	border-right: none;
	padding: 4px 4px 4px 12px;
}

.hourTable-col1{
	float: left;
	margin-left: -12px;
}

.formSearch {
	width: 917px;
}

.form2 {
	width: 256px;
}

.form1 tr:first-child td{
	border-top: none;
	width: 258px;
	text-align: left;
}

.form2 tr td{
	border-top-width: 1px;
	border-top-style: solid;
	border-top-color: #cccccc;
	border-left-width: 0px;
	border-right-width: 0px;
	vertical-align: top;
	padding-top: 4px;
	padding-right: 0px;
	padding-bottom: 4px;
	padding-left: 4px;
	width: 246px;
}

.form2 tr:first-child td{
	border-top: none;
	width: 5px;
	text-align: left;
}

.form1 tr td{
	border-top-width: 1px;
	border-top-style: solid;
	border-top-color: #cccccc;
	border-left-width: 0px;
	border-right-width: 0px;
	vertical-align: top;
	padding-top: 4px;
	padding-right: 0px;
	padding-bottom: 4px;
	padding-left: 4px;
	text-align: left;
}

.subbroker-form {
	width: 900px;
}

.subbroker-form-inner {
	width: 828px;
}

.formQuote {
	width: 100%;
	background-color: #ffffff;
	/*background-image: url(../image/gradient.png);*/
	background-repeat: repeat-x;
	background-attachment: scroll;
	background-position: center bottom;
	border-top:none;
	border-left: 2px solid #B2DEE7;
	border-right: 2px solid #B2DEE7;
	border-bottom: 2px solid #B2DEE7;
}

.formQuoteClient {
	width: 800px;
}

.formQuote tr td {
	border-top: none;
}

.formViewQuote {
	padding-left:20px;
	padding-top:10px;
	padding-right:20px
}

.subformQuote {
	width: 100%;
}

.subformQuote tr td{
	border-top-width: 1px;
	border-top-style: solid;
	border-top-color: #cccccc;
	border-left-width: 0px;
	border-right-width: 0px;
	vertical-align: top;
	padding:4px 4px 4px 12px;
}

.viewQuote-update-btn {
	width: 100%;
}

.viewQuote-update-btn tr td{
	border: none;
	vertical-align: top;
	padding:4px 4px 4px 12px;
}

.subformQuote tr td{
	border-top-width: 1px;
	border-top-style: solid;
	border-top-color: #cccccc;
	border-left-width: 0px;
	border-right-width: 0px;
	vertical-align: top;
	padding:4px 4px 4px 12px;
}

.subformQuoteList{
}

.subformQuoteNoTopBorder tr td {
	border-top: none;
}

.subformQuoteTermsCol1{
	width:30px;
}

.title{
	font-size: 16px;
	font-weight: bold;
}

.redTitle{
	font-size: 16px;
	font-weight: bold;
	color:#C60C30;
	width: 922px;
}

.notification{
	color:#00B3BE;
	font-size: 11px;
}

.subtitle{
	color: #404040;
	font-size: 16px;
	font-weight: bold;
}

.sectionTitle{
	color: #58585A;
	font-size: 15px;
	font-weight: bold;
	padding-left: 10px;
}

.black-label-payment{
	color: #58585A;
	font-size: 12px;
	font-weight: bold;
}

.red-label-payment{
	color: #c60c30;
	font-size: 12px;
	font-weight: bold;
}

.padding-left-payment{
	padding-left: 10px;
	width:100%;
}

.button{
	color: transparent;
}

.perPage{
	float:right;
}

.redButton{
	color: transparent;
	float:right;
}

a.redButton{
	color: transparent;
	float:right;
}

a.actionBtn{
	border: 0 ;
	cursor:pointer;
	font-weight: bold;
	background : url(../image/btnRightLong.png) no-repeat right;
	font-size: 1.2em;
	padding:0 24px 0 0;
	text-decoration: none;
	text-align: center;
	vertical-align: middle;
	display:inline-block;
}

a.actionBtn span{
	position: relative;
	display:block;
	white-space: nowrap;
	padding:0 0 0 20px;
	margin:0 0 0 -4px;
	height: 31px;
	line-height: 28px;
	background:url(../image/btnLeftShort.png) left no-repeat;
	color: #FFF;
}

a.actionBtn span.left img {
	margin: 0 5px -2px -12px;
}
a.actionBtn span.right img {
	margin: 0 -12px -2px 5px;
}

a.actionBtn:hover {
	background:url(../image/btnRightLongOver.png) right no-repeat;
}
a.actionBtn:hover span {
	background:url(../image/btnLeftShortOver.png) left no-repeat;
}




a.actionBtnRightHover{
	border: 0 ;
	cursor:pointer;
	font-weight: bold;
	background : url(../image/btnRightLongOver.png) no-repeat right;
	font-size: 1.2em;
	padding:0 24px 0 0;
	text-decoration: none;
	text-align: center;
	vertical-align: middle;
	display:inline-block;
}

a.actionBtnLeftHover{
	border: 0 ;
	cursor:pointer;
	font-weight: bold;
	background : url(../image/btnLeftShortOver.png) no-repeat right;
	font-size: 1.2em;
	padding:0 24px 0 0;
	text-decoration: none;
	text-align: center;
	vertical-align: middle;
	display:inline-block;
}




a.actionBtn:active{
	background: url(../image/btnRightLongOver.png) no-repeat right ;
}

a.floatLeft {
	float:left;
	margin-left:4px;
}
a.floatRight {
	float:right;
	margin-right:2px;
}

a.floatCenter {
	float:left;
	margin-left : 413px;
}

a.marginTop{
	margin-top: 10px;
}

img.rightButtonArrow{
	text-decoration: none;
	margin-left: 20px;
	margin-top: 5px;
	border:none;
}

.reassignPanel{
	position: relative;
	width: 593px;
	height: 177px;
	vertical-align: top;
}

.reassignPanel-border{
	border: none;
}

panel-border {
	border: none;
}

.prime-table {
	.ui-datatable-odd {
		background-color: #FFFFFF;
	}
}

.prime-table {
	.ui-datatable-even {
		background-color: #F1F1F1;
	}
}

.prime-row {
	height: 48px;
}

.notFirstColumn {
	text-align:center;
}

.childRow{
	background-color: #F1F1F1;
	border-left-color: #00b3be;
	border-left-width: 2px;
	border-left-style: solid;
	border-right-color: #00b3be;
	border-right-width: 2px;
	border-right-style: solid;
}

.expandedRow {
	border-top-color: #00b3be;
	border-top-width: 2px;
	border-top-style: solid;
}

.expandedFirstColumn {
	border-left-color: #00b3be;
	border-left-width: 2px;
	border-left-style: solid;
}

.expandedLastColumn {
	border-right-color: #00b3be;
	border-right-width: 2px;
	border-right-style: solid;
}

.lastChild {
	border-bottom-color: #00b3be;
	border-bottom-width: 2px;
	border-bottom-style: solid;
}

.tabPanelContent {
	width: 926px;
}

.subtabPanelContent {
	width: 926px;
}

.rich-tabpanel-content-form{
	background-image: url(../image/gradient.png);
	background-repeat: repeat-x;
	background-attachment: scroll;
	background-position: center bottom;
}

.rich-tabpanel-broker{
	width: 906px;
	border-left:1px solid #C4C0B9;
    border-right:1px solid #C4C0B9;
    border-top:1px solid #C4C0B9;
    border-bottom:1px solid #C4C0B9;
}

.tooltip{
    background-color: #FFFEEF;
    border-width:1px;
    padding:0px;
    text-align:left;
}

.tooltipData{
    font-size: 12px;
}

.innerGrid tr td {
	border-top: none;
	padding:0px;
}

.errorGrid tr td {
	border-top: none;
	padding:0px;
}

.errorMessage {
	color: red;
}

.errorMessageBold {
	color: red;
	font-weight: bold;
}

.successMessage {
	color: green;
}


.errorMsg-from{
	width: 348px;
}

.errorMsg-to{
	width: 400px;
}

.errorMsg-from-kpi{
	width: 279px;
}

.errorMsg-to-kpi{
	width: 400px;
}

.calendar {
	width: 200px;
}

.calendar tr td:first-child {
	width: 24px;
}

subformQuoteColFull {
	width: 100%;
}

.subformQuoteCol1 {
	width: 600px;
}

.subformQuoteCol2 {
	width: 40px;
}

.subformQuoteCol3 {
	width: 600px;
}

.subformQuoteActCol1 {
	width: 140px;
}

.subformQuoteActCol2 {
	width: 170px;
}

.subformQuoteActCol3 {
	width: 180px;
}

.subformQuoteActCol4 {
	width: 550px;
}

.sectionInfo{
	padding-left: 10px;
}

.paymentColumn {
	width: 50%;
}

.paymentColumn2 {
	width: 50%;
	padding-left:40px;
	padding-top: 40px;
	padding-bottom: 40px;
	border-left:1px dotted #C4C0B9;
    border-right:0px;
    border-top:0px;
    border-bottom:0px;
}



.premiumColumn1 {
	width: 220px;
	text-align:left;
}

.premiumColumn2 {
	width: 116px;;
	text-align:left;
}

.premiumColumn3 {
	width: 194px;
	text-align:left;
}

.searchColumn1 {
	width: 380px;
}

.searchColumn2 {
	width: 37px;
}

.searchColumn3 {
	width: 500px;
	vertical-align: center;
}

.form2 tr:first-child td{
	border-top: none;
	width: 10px;
	width: 240px;
}

.accountColumn {
	vertical-align: top;
}

.firstColumn{
	padding-left: 10px;
}

.defaultColumn {
}

.masterBroker tr td{
	border-top-width: 0px;
	border-top-style: solid;
	border-top-color: #cccccc;
	border-left-width: 0px;
	border-right-width: 0px;
	vertical-align: top;
	padding-top: 0px;
	padding-right: 0px;
	padding-bottom: 0px;
	padding-left: 0px;
	text-align: left;
}

.logout {
   float: right;
   position: relative;
   border-width:0px;
   color: transparent;
   margin-top: -90px;
}

select.province {
   float: right;
   position: relative;
   border-width:0px;
   margin-top: -22px;
}

form.province {
   float: right;
   position: relative;
   border-width:0px;
   margin-top: -22px;
}

.black-bgrnd-cell{
	background-color: #000000;
	color: #FFFFFF;
}

.green-bgrnd-cell{
	background-color: #00b3be;
	color: #c60c30;
}

.grey-bgrnd-cell{
	border-top-width: 2px;
	border-left-width: 2px;
	border-right-width: 2px;
	border-bottom-width: 2px;
	border-style: solid;
	border-color: #00b3be;
	background-color: #d8d8d8;
	color: #00b3be;
}

.blue-bgrnd-cell{
	background-color: #00b3be;
	color: #FFFFFF;
}

.red-bgrnd-cell{
	background-color: #c60c30;
	color: #FFFFFF;
}

.black-border-cell{
	border-style: solid;
	border-left-width: 1px;
	border-right-width: 1px;
	border-color: #000000;
}

.red-bgrnd-cell{
	background-color: #c60c30;
	color: #FFFFFF;
}

.red-bgrnd-with-border-cell{
	border-top-width: 0px;
	border-left-width: 1px;
	border-color: #000000;
	background-color: #c60c30;
	color: #FFFFFF;
}

.white-bgrnd-no-border-cell{
	background-color: #FFFFFF;
	color: #000000;
}

.white-bgrnd-b-border-cell{
	border-top-width: 0px;
	border-left-width: 0px;
	border-right-width: 0px;
	border-bottom-width: 2px;
	border-color: #00b3be;
	border-style: solid;
	background-color: #FFFFFF;
	color: #000000;
}

.white-bgrnd-border-cell{
	border-top-width: 2px;
	border-left-width: 2px;
	border-right-width: 2px;
	border-bottom-width: 2px;
	border-color: #00b3be;
	border-style: solid;
	background-color: #FFFFFF;
	color: #000000;
}

.green-bgrnd-top-border-cell{
	background-color: #00b3be;
	color: #c60c30;
	border-top-width: 2px;
	border-left-width: 2px;
	border-right-width: 2px;
	border-bottom-width: 0px;
	border-color: #00b3be;
	border-style: solid;
}

.white-bgrnd-lr-border-cell{
	border-left-width: 2px;
	border-right-width: 2px;
	border-top-width: 0px;
	border-bottom-width: 0px;
	border-color: #00b3be;
	border-style: solid;
	background-color: #FFFFFF;
	color: #000000;
}

.white-bgrnd-rb-border-cell{
	border-left-width: 0px;
	border-right-width: 2px;
	border-top-width: 0px;
	border-bottom-width: 2px;
	border-color: #00b3be;
	border-style: solid;
	background-color: #FFFFFF;
	color: #000000;
}


.formSpoe {
	padding-left:20px;
	padding-top:10px;
	padding-right:20px
}

.formSpoe tr td{
	vertical-align: top;
}

.contentsSpoe{
	padding-left:13px;
}

.followupTable tr td {
	border-top: none;
}

.followupTable tr:first-child td{
	border-top: none;
	padding-left: 9px;
}

.search-followup-table tr td {
	border-top: none;
	padding:0px 0px 0px 1.4em;
	width: 25%;
	text-indent: -1.4em;
}

/* Only for IE7 */
*:first-child+html .search-followup-table tr td {
	border-top: none;
	padding:0px 0px 0px 2em;
	width: 25%;
	text-indent: -2em;
}

.search-followup-table tr:first-child td{
	border-top: none;
	width: 25%;
}

.search-source-table tr td {
	border-top: none;
	padding:0px 0px 0px 0px;
	width: 33%;
}

.search-source-table tr:first-child td{
	border-top: none;
	width: 33%;
}

.reassignRow{
	vertical-align: top;
}

.reassignColumn1{
	width: 154px;
}

.reassignColumn2{
	width: 40px;
}

.reassignColumn3{
	width: 204px;
	text-align:left;
}

.first-tabpanel{
	margin-top:-31px;
	width:100%;
}

.second-tabpanel{
	width:500px;
}

.info-content-in-tab{
	padding-top:30px;
	padding-right:20px;
	padding-left:20px;
	width:926px;
}



.search-content-tab{
	margin-top: 20px;
	margin-left: 25px;
	width:916px;
}

.search-panel-tab{
	margin-top: 20px;
	padding-top: 30px;
	border-left:solid 1px #BCBEC0;
	border-right:solid 1px #BCBEC0;
	border-top:solid 1px #BCBEC0;
	border-bottom:solid 1px #BCBEC0;
	width:916px;
}

.report-content-tab{
	padding-top:15px;
	padding-right:20px;
	padding-left:20px;
	width:906px;
}

.quote-subtitle{
	color: #27688F;
	background-color: #FFFFFF;
	border-left-width: 0px;
	border-right-width: 0px;
	border-top-width: 0px;
	border-bottom-width: 0px;
}

.red-menu-inactive{
	background: url(../image/redTab.jpg) no-repeat scroll right 0 transparent;
	color:#FFFFFF;
	border: 1px solid #C60C30;
}

.red-menu-active{
	font-weight: bold;
	color:#C60C30;
	border: 1px solid #C60C30;
	background: #fff url(../image/whiteTab.jpg) right bottom no-repeat;
	cursor:default;
}

.rich-tabpanel-remove-border-content{
	border-width:0px;
}

.border-mid-content{
	border-width:0px;
	background-image: url(../image/contentMid966.png);
	background-repeat: repeat-y;
	background-position:left top;
	background-attachment: scroll;
}


.reportTabPanel{
	margin-left:0px;
	border-width:0px;
}

.blue-top-subsection {
	margin-top: 15px;
	background-repeat: no-repeat;
	background-image: url(../image/contentTopBlue.png) ;
  	background-color: transparent;
	background-position:left top;
	display: block;
  	width: 924px;
  	height: 33px;
	border-left-width:  2px;
	border-right-width: 2px;
	border-top-width:   0px;
	border-bottom-width: 0px;
	border-color: #B2DEE7;
	border-style: solid ;
    color: #58585A;
	font-size: 15px;
	font-weight: bold;
	padding-left: 40px;
	padding-top: 10px;
}


img.bluetop-subsection {
	margin-top: 25px;
  	width: 924px;
  	height: 33px;
	border-left-width:  2px;
	border-right-width: 2px;
	border-top-width:   0px;
	border-bottom-width: 0px;
	border-color: #B2DEE7;
	border-style: solid ;
}

.formCoverageCol1{
	width: 400px;
}

.formCoverageCol2{
	width: 540px;
}

.coverage-section-width{
	width: 170px;
}

.coverage-align-left{
	text-align:left;
	padding-left: 2px;
}

.formCoverageColx{
	width: 260px;
}

.coverageReserved{
	vertical-align: super;
	font-size: 7px;
	text-align:left;
}

.successMessage{
	color: #00b3be;
}
.underline-text{
	text-decoration:underline
}
.marginTop{
	margin-top: 10px;
}

.payment-no-border {
	border-left-width:  0px;
	border-right-width: 0px;
	border-top-width:   0px;
	border-bottom-width: 0px;
	width: 150px;
}

.payment-black-border {
	border-color: #C4C0B9;
	padding-right: 40px;
}

.payment-title-margin {
	padding-top: -40px;
}

.padding-space {
	padding-top: 10px;
}

.alignRight{
	text-align:right;
}

.alignLeft{
	text-align:left;
}

.alignCenter{
	text-align:center;
}

.boldFont{
	font-weight: bold;
}

.accessdenied-text{
	padding-top:30px;
	text-align:left;
	width: 926px;
}

.accessDeniedPadding{
	padding: 10px;
}

.consentDeniedPadding{
	padding-top: 15px;
	padding-bottom: 10px;
}

.btn-top-margin-reassignQ {
	margin-top: 52px;
}

.btn-top-margin-25{
	margin-top: 25px;
}

.info-accessdenied-in-tab{
	padding-top:30px;
	padding-right:20px;
	padding-left:50px;
	width:926px;
}

.subtitle-accessdenied{
	color: #404040;
	font-size: 12px;
	font-weight: bold;
	width:900px;
}

img.margin-right-accessdenied{
	margin-right : 30px;
}

.form-upload-modalpanel{
	background-color: #FFFFFF;
	border: none;
	text-align:center;

}

.upload-modalpanel{
	border:1px solid #00b3be;
}

.upload-cmpnt-control{
	border: none;
	background-color: #00b3be;
}

.form-upload-outputpanel{
	text-align:center;
}

.important-message{
	color:#C00C30;
}

.upload-txt{
	color: #655F5D;
}

.pagination:hover{
	text-decoration: underline;
	cursor: pointer;
	color: #655F5D;
}

.cell-spacing-table{
	border-collapse: collapse;
	border-spacing: 5px;
}

.calendar-footer-cell{
	width:33%;
}

.calendar-footer-clear-cell{
	width:33%;
	text-align:center;
	border-left:1px solid #c4c0b9;
	border-right: 1px solid #c4c0b9;
	border-top:   none;
	border-bottom: none;
}

.calendar-footer-center-cell{
	width:33%;
	text-align:center;
}

.search {
	vertical-align: middle;
}

.fsa-table-col1{
	width: 200px;
}

.fsa-table-col2{
	width: 200px;
}

.fsa-table-col3{
	width: 110px;
}

.fsa-table-col4{
	width: 110px;
}

.fsa-table-col5{
	width: 110px;
}

.list-form{
	padding-left: 10px;
	padding-right: 10px;
	padding-top: 10px;
}


.user-column-1{
	width: 200px;
	text-align : left;
}

.user-column-2{
	text-align : left;
}

.perf-period-to-from{
	width: 540px;
}


.perf-quote-source-col1{
	width: 312px;

}

.perf-expand-collapse{
	width: 52px;
}

.mendorsm{
	padding : 0px;
	margin : 0px;
}

.techErrorGrid{
	width: 920px;
	font-size:14px;
	line-height:150%;
	margin-left: 16px;
}

.techErrorClosePanel{
	width: 95%;
	margin-top: 10px;
	margin-right: 20px;
	margin-bottom: 10px;
	text-align: right;
}


.fsa-rep-col1{
	width: 68px;
}

.fsa-rep-col2{
	width: 79px;
}

.fsa-rep-col3{
	width: 50px;
}

.fsa-rep-col4{
	width: 70px;
}

.fsa-rep-col5{
	width: 75px;
}

.fsa-rep-col6{
	width: 75px;
}

.fsa-rep-col7{
	width: 60px;
}

.fsa-rep-col8{
	width: 55px;
}

.fsa-rep-col9{
	width: 300px;
}

.fsa-rep-in-tab{
	padding-left: 20px;
    padding-right: 20px;
    padding-top: 30px;
    width: 926px;
}

.control-not-visible{
	display: none;
}

.driver_ {
	display:none;
}

/* Quotes list links colors */
.link_QF {
	color:#27688F !important;
}

.link_QF:hover {
	color:#655F5D !important;
}

.link_QH {
	color:#1E1E1E !important;
}

.link_QH:hover {
	color:#655F5D !important;
}

.link_QT {
	color:#1E1E1E !important;
}

.link_QT:hover {
	color:#655F5D !important;
}

.link_QC {
	color:#1E1E1E !important;
}

.link_QC:hover {
	color:#655F5D !important;
}

.link_IR {
	color:#AE275F !important;
}

.link_IR:hover {
	color:#655F5D !important;
}

.link_PC {
	color:#FF8C00 !important;
}

.link_PC:hover {
	color:#655F5D !important;
}

.nowrap {
	white-space: nowrap;
}

.deleteBtn {
    margin-left: 7px;
    padding: 2px 7px 2px 7px;
    border: 1px solid darkgrey;
}

.imgConsent {
    margin-left: -575px;
}

#home_implicit_consent_question {
	float: right;
}

td #home_implicit_consent_question {
    width: 200%;
    margin-right: -400px;
}

#home_credit_consent_question {
	float: right;
}

td #home_credit_consent_question {
    width: 200%;
    margin-right: -400px;
}

td #important_message_question {
    width: 200%;
    margin-right: -400px;
}

#important_message_question {
	float: right;
}

#roadblock_message_question {
	float: left;
	color:red;
}

