# webzone-app [![Build Status](https://githubifc.iad.ca.inet/lab-se/webzone-app/actions/workflows/ci.yml/badge.svg?branch=develop)](https://githubifc.iad.ca.inet/lab-se/webzone-app/actions/workflows/ci.yml)

## Util links

- [Documentation](https://confluence.tooling.intactfc.cloud/display/MARS/Webzone)
- [Test resources](https://confluence.tooling.intactfc.cloud/display/MARS/Resources+Needed)
- [Local install instructions](https://confluence.tooling.intactfc.cloud/display/MARS/Webzone+-+Local+Install)

## Quickstart

To run the application locally, run the maven command : `mvn clean install -P local`. This will copy the content of
`broker-admin-local-config` module into the war. To change any local properties, update them in the
broker-admin-local-config module and run the maven command again.

### IntelliJ setup

In intellij, create a local tomcat 9 launcher as follows:

- Configure server tab by linking to your local tomcat installation

![](docs/intellij_setup_tomcat_1.png)

- Configure deployment tab by adding `broker-office-web:war exploded` artifact and setting app context to `/webzone`.
  This means that once deployed, the app will be served at http://localhost:8080/webzone

![](docs/intellij_setup_tomcat_2.png)

### Manual setup

Build the app using
Maven: `mvn clean install -P local && cp broker-office-web/target/webzone.war <tomcat location>/webapps/`. Then navigate
to your tomcat folder, and then go inside the bin folder and run `./catalina.sh run`

Access the application locally via the following url: http://localhost:8080/webzone/spoe.jsf

## Technical documentation

This project contains the BrokerAdmin application running into tomcat and using GIT Actions.
This project has been copied from https://githubifc.iad.ca.inet/Intact/broker-office-project.
The old project was running in websphere. We created a new repository in GIT, moved and cleaned the code
to make it running with tomcat.

run `mvn clean install -P local && cp broker-office-web/target/broker-office-web.war <tomcat location>/webapps/`

## Local Deployment Script

make sure the tomcat server isn't running and then run `npm start` or `./local_deploy.sh`

